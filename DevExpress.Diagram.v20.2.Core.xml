<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.Diagram.v20.2.Core</name>
  </assembly>
  <members>
    <member name="N:DevExpress.Diagram.Core">
      <summary>
        <para>Contains common classes shared by the Diagram controls for different platforms.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ActionType">
      <summary>
        <para>Lists values that indicate whether the diagram item text has been changed directly or using the Undo/Redo operations.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ActionType.Direct">
      <summary>
        <para>The text has been changed in the editing mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ActionType.Redo">
      <summary>
        <para>The text has been changed using the Redo operation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ActionType.Undo">
      <summary>
        <para>The text has been changed using the Undo operation.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.AdjustBoundaryBehavior">
      <summary>
        <para>Lists the values used to specify the behavior when the end-user moves items close to the container boundaries.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.AdjustBoundaryBehavior.AutoAdjust">
      <summary>
        <para>When the end-user attempts to move items close to the container boundaries, the boundaries are automatically expanded to fit items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.AdjustBoundaryBehavior.DisableOutOfBounds">
      <summary>
        <para>When the end-user attempts to move items close to the container boundaries, the item position is automatically adjusted to fit inside the container.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.AdjustBoundaryBehavior.None">
      <summary>
        <para>Neither the items position nor the container boundaries are auto adjusted when the end-user attempts to move items close to the container boundaries.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Alignment">
      <summary>
        <para>Lists the values used to specify how the tree layout algorithm arranges shapes relatively to the layout axis.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Alignment.Center">
      <summary>
        <para>Items are aligned along the layout axis.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Alignment.Far">
      <summary>
        <para>In a left-to-right layout, items are aligned to the right from the layout axis. In a right-to-left layout, items are aligned to the left.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Alignment.Near">
      <summary>
        <para>In a left-to-right layout, items are aligned to the left from the layout axis. In a right-to-left layout, items are aligned to the right.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.BringIntoViewMode">
      <summary>
        <para>Lists the values used to specify whether all items should be brought into view by <see cref="M:DevExpress.XtraDiagram.DiagramControl.BringItemsIntoView(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.BringIntoViewMode)"/> and <see cref="M:DevExpress.Xpf.Diagram.DiagramControl.BringItemsIntoView(System.Collections.Generic.IEnumerable{DevExpress.Xpf.Diagram.DiagramItem},DevExpress.Diagram.Core.BringIntoViewMode)"/> methods when other items are partially visible.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.BringIntoViewMode.AllowPartialVisibility">
      <summary>
        <para>Do not bring all specified items into view if at least one of them is already visible.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.BringIntoViewMode.ShowAll">
      <summary>
        <para>Bring all specified items into view.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.CanvasSizeMode">
      <summary>
        <para>Lists the values used to specify whether the canvas size is automatically changed to fit the current shapes layout.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.CanvasSizeMode.AutoSize">
      <summary>
        <para>The canvas is automatically resized to fit the current shapes layout.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.CanvasSizeMode.Fill">
      <summary>
        <para>The canvas fills the entire visible area.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.CanvasSizeMode.None">
      <summary>
        <para>The canvas size does not change on moving shapes outside of it.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.CollapseButtonVisibilityMode">
      <summary>
        <para>Lists the values used to specify whether the expand-collapse button is visible.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.CollapseButtonVisibilityMode.Always">
      <summary>
        <para>The collapse button is always visible.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.CollapseButtonVisibilityMode.HasSubordinates">
      <summary>
        <para>Collapse button is visible if a shape has subordinates.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.CollapseButtonVisibilityMode.Never">
      <summary>
        <para>The collapse button is hidden.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ConnectionElementState">
      <summary>
        <para>Lists values that specify whether to enable connection to an item and show a visual indication.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ConnectionElementState.Disabled">
      <summary>
        <para>Connection is disabled. Visual indication (red border around an item/connector) is shown.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ConnectionElementState.Hidden">
      <summary>
        <para>Connection is disabled with no visual indication.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ConnectionElementState.Visible">
      <summary>
        <para>Connection is enabled.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ConnectionPoint">
      <summary>
        <para>Represents a connection point.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.ConnectionPoint.#ctor(System.Windows.Point,System.Int32,DevExpress.Diagram.Core.ConnectionElementState)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.ConnectionPoint"/> class.</para>
      </summary>
      <param name="point"></param>
      <param name="index">An integer value that represents the connection point index.</param>
      <param name="state">Specifies whether to enable connection to an item and show a visual indication.</param>
    </member>
    <member name="P:DevExpress.Diagram.Core.ConnectionPoint.Index">
      <summary>
        <para>Gets the connection point index.</para>
      </summary>
      <value>An integer value that represents the connection point index.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.ConnectionPoint.Point">
      <summary>
        <para>Gets the connection point position.</para>
      </summary>
      <value>A System.Windows.Point value.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.ConnectionPoint.State">
      <summary>
        <para>Specifies whether to enable connection to an item and show a visual indication.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ConnectionElementState enumeration value.</value>
    </member>
    <member name="T:DevExpress.Diagram.Core.ConnectorPointRestrictions">
      <summary>
        <para>Lists the values used to specify the restrictions applied to the connector&#39;s begin and end points.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ConnectorPointRestrictions.KeepConnected">
      <summary>
        <para>The connector point should always be attached to an item&#39;s connection point.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ConnectorPointRestrictions.KeepConnectedToCurrentItem">
      <summary>
        <para>The connector point should always be attached to a connection point of the current item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ConnectorPointRestrictions.KeepDisconnected">
      <summary>
        <para>The connector point cannot be attached to an item&#39;s connection point.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ConnectorPointRestrictions.None">
      <summary>
        <para>Restrictions are not applied.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ConnectorPointType">
      <summary>
        <para>Lists values that specify whether the connector point is the begin or end point.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ConnectorPointType.Begin">
      <summary>
        <para>The connector&#39;s start point.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ConnectorPointType.End">
      <summary>
        <para>The connector&#39;s end point.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ConnectorType">
      <summary>
        <para>Represents a connector type.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.ConnectorType.Create(System.String,System.Func{System.String})">
      <summary>
        <para>Creates an instance of the <see cref="T:DevExpress.Diagram.Core.ConnectorType"/> class with the specified parameters.</para>
      </summary>
      <param name="id">A string value that is the connector type id.</param>
      <param name="getTypeName">A delegate function that returns the connector type name.</param>
      <returns>A ConnectorType instance.</returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.ConnectorType.Create(System.String,System.Func{System.String},System.Func{DevExpress.Diagram.Core.Native.ConnectorProxy,DevExpress.Diagram.Core.ConnectorPointType,System.Windows.Point})">
      <summary>
        <para>Creates an instance of the <see cref="T:DevExpress.Diagram.Core.ConnectorType"/> class with the specified parameters.</para>
      </summary>
      <param name="id">A string value that is the connector type id.</param>
      <param name="getTypeName">A delegate function that returns the connector type name.</param>
      <param name="actualPoint">A delegate function that returns the actual begin and end points when the connector is attached to an item.</param>
      <returns>A ConnectorType instance.</returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.ConnectorType.Curved">
      <summary>
        <para>Returns the curved connector type.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ConnectorType object that represents a curved connector.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.ConnectorType.Id">
      <summary>
        <para>Returns the connector type id.</para>
      </summary>
      <value>A string value that is the connector type id.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.ConnectorType.OrgChart">
      <summary>
        <para>Returns the OrgChart connector type.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ConnectorType object that represents an OrgChart connector.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.ConnectorType.RegisteredTypes">
      <summary>
        <para>Returns the collection of registered connector types.</para>
      </summary>
      <value>A collection of ConnectorType objects.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.ConnectorType.RightAngle">
      <summary>
        <para>Returns the right angle connector type.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ConnectorType object that represents a right angle connector.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.ConnectorType.Straight">
      <summary>
        <para>Returns the straight connector type.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ConnectorType object that represents a straight connector.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.ConnectorType.ToString">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.ConnectorType.TypeName">
      <summary>
        <para>Returns the connector type name.</para>
      </summary>
      <value>A string value that is the connector type name.</value>
    </member>
    <member name="T:DevExpress.Diagram.Core.ContainerDragMode">
      <summary>
        <para>Lists the values used to specify whether containers can be dragged by any point or only by the header and bounds.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ContainerDragMode.ByAnyPoint">
      <summary>
        <para>Containers can be dragged by any point.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ContainerDragMode.ByHeaderAndBounds">
      <summary>
        <para>Containers can be dragged only by the header and bounds.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.DefaultBarItemNames">
      <summary>
        <para>Stores the names of the default Ribbon items.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.A3PageSize">
      <summary>
        <para>Returns &quot;bA3PageSize&quot;. Corresponds to the A3 item of the Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.A4PageSize">
      <summary>
        <para>Returns &quot;bA4PageSize&quot;. Corresponds to the A4 item of the Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.A5PageSize">
      <summary>
        <para>Returns &quot;bA5PageSize&quot;. Corresponds to the A5 item of the Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ArrangeGroup">
      <summary>
        <para>Returns &quot;rgArrange&quot;. Corresponds to the Arrange ribbon group.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.AutoSize">
      <summary>
        <para>Returns &quot;bAutoSize&quot;. Corresponds to the Auto Size ribbon menu that allows end-users to specify whether the canvas is auto resized or fills the entire viewport area.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.AutoSizeMode">
      <summary>
        <para>Returns &quot;bAutoSize&quot;. Corresponds to the Auto Size item of the Auto Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.B4PageSize">
      <summary>
        <para>Returns &quot;bB4PageSize&quot;. Corresponds to the B4 item of the Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.B5PageSize">
      <summary>
        <para>Returns &quot;bB5PageSize&quot;. Corresponds to the B5 item of the Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.BackgroundColor">
      <summary>
        <para>Returns &quot;bBackgroundColor&quot;. Corresponds to the Background ribbon item that allows end-users to pick the background color for selected items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.BringForward">
      <summary>
        <para>Returns &quot;bBringForward&quot;. Corresponds to the Bring Forward item of the Bring to Front ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.BringToFront">
      <summary>
        <para>Returns &quot;bBringToFront&quot;. Corresponds to the Bring to Front item of the Bring to Front ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.BringToFrontContainer">
      <summary>
        <para>Returns &quot;bBringToFrontContainer&quot;. Corresponds to the Bring to Front ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ChangeConnectorType">
      <summary>
        <para>Returns &quot;bChangeConnectorType&quot;. Corresponds to the Connectors ribbon menu that allows end-users to change the type of selected connectors.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ClipboardRibbonGroup">
      <summary>
        <para>Returns &quot;rgClipboard&quot;. Corresponds to the Clipboard ribbon group.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.CollapseSelectedContainers">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ConnectorTool">
      <summary>
        <para>Returns &quot;bConnectorTool&quot;. Corresponds to the Connector ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerHeaderPadding">
      <summary>
        <para>Returns &quot;bContainerHeaderPadding&quot;. Corresponds to the Header Padding ribbon menu item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerHeaderPadding_0px">
      <summary>
        <para>Returns &quot;bContainerHeaderPadding_0px&quot;. Corresponds to the 0 px. item of the Header Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerHeaderPadding_12px">
      <summary>
        <para>Returns &quot;bContainerHeaderPadding_12px&quot;. Corresponds to the 12 px. item of the Header Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerHeaderPadding_16px">
      <summary>
        <para>Returns &quot;bContainerHeaderPadding_16px&quot;. Corresponds to the 16 px. item of the Header Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerHeaderPadding_24px">
      <summary>
        <para>Returns &quot;bContainerHeaderPadding_24px&quot;. Corresponds to the 24 px. item of the Header Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerHeaderPadding_32px">
      <summary>
        <para>Returns &quot;bContainerHeaderPadding_32px&quot;. Corresponds to the 32 px. item of the Header Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerHeaderPadding_4px">
      <summary>
        <para>Returns &quot;bContainerHeaderPadding_4px&quot;. Corresponds to the 4 px. item of the Header Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerHeaderPadding_8px">
      <summary>
        <para>Returns &quot;bContainerHeaderPadding_8px&quot;. Corresponds to the 8 px. item of the Header Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerPadding">
      <summary>
        <para>Returns &quot;bContainerPadding&quot;. Corresponds to the Padding ribbon menu item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerPadding_0px">
      <summary>
        <para>Returns &quot;bContainerPadding_0px&quot;. Corresponds to the 0 px. item of the Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerPadding_12px">
      <summary>
        <para>Returns &quot;bContainerPadding_12px&quot;. Corresponds to the 12 px. item of the Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerPadding_16px">
      <summary>
        <para>Returns &quot;bContainerPadding_16px&quot;. Corresponds to the 16 px. item of the Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerPadding_24px">
      <summary>
        <para>Returns &quot;bContainerPadding_24px&quot;. Corresponds to the 24 px. item of the Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerPadding_32px">
      <summary>
        <para>Returns &quot;bContainerPadding_32px&quot;. Corresponds to the 32 px. item of the Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerPadding_4px">
      <summary>
        <para>Returns &quot;bContainerPadding_4px&quot;. Corresponds to the 4 px. item of the Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerPadding_8px">
      <summary>
        <para>Returns &quot;bContainerPadding_8px&quot;. Corresponds to the 8 px. item of the Padding ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerSizeGroup">
      <summary>
        <para>Returns &quot;rgContainerSizeGroup&quot;. Corresponds to the Size ribbon group.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerStyles">
      <summary>
        <para>Returns &quot;bContainerStyles&quot;. Corresponds to the ribbon menu that displays available container styles.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerStylesGroup">
      <summary>
        <para>Returns &quot;rgContainerStylesGroup&quot;. Corresponds to the Container Styles ribbon group.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerToolsPageCategory">
      <summary>
        <para>Returns &quot;rContainerToolsPageCategory&quot;. Corresponds to the Container Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ContainerToolsRibbonPage">
      <summary>
        <para>Returns &quot;rpContainerTools&quot;. Corresponds to the Format ribbon page.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.Copy">
      <summary>
        <para>Returns &quot;bCopy&quot;. Corresponds to the Copy ribbon item that allows end-users to copy the selected diagram items to the clipboard.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.CurvedConnectorType">
      <summary>
        <para>Returns &quot;bCurvedConnectorType&quot;. Corresponds to the Curved item that is contained within the Connectors ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.Cut">
      <summary>
        <para>Returns &quot;bCut&quot;. Corresponds to the Cut ribbon item that allows end-users to remove the selected diagram items from the canvas and add them to the clipboard.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.DefaultPageCategory">
      <summary>
        <para>Returns &quot;rDefaultPageCategory&quot;. Corresponds to the default ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.DesignRibbonPage">
      <summary>
        <para>Returns &quot;rpDesign&quot;. Corresponds to the Design ribbon page.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.DiagramPartsRibbonGroup">
      <summary>
        <para>Returns &quot;rgDiagramParts&quot;. Corresponds to the Diagram Parts ribbon group.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.EllipseTool">
      <summary>
        <para>Returns &quot;bEllipse&quot;. Corresponds to the Ellipse item of the Rectangle ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ExecutivePageSize">
      <summary>
        <para>Returns &quot;bExecutivePageSize&quot;. Corresponds to the Executive item of the Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ExportAs">
      <summary>
        <para>Returns &quot;bExportAs&quot;. Corresponds to the Export As menu item of the ribbon application menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ExportDiagramBMP">
      <summary>
        <para>Returns &quot;bExportAsBMP&quot;. Corresponds to the Windows Bitmap item of the Export As ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ExportDiagramGIF">
      <summary>
        <para>Returns &quot;bExportAsGIF&quot;. Corresponds to the Graphics Interchange Format item of the Export As ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ExportDiagramJPEG">
      <summary>
        <para>Returns &quot;bExportAsJPEG&quot;. Corresponds to the JPEG Fileinterchange Format item of the Export As ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ExportDiagramPDF">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ExportDiagramPNG">
      <summary>
        <para>Returns &quot;bExportAsPNG&quot;. Corresponds to the Portable Network Graphics item of the Export As ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ExportDiagramSVG">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FillAutoSizeMode">
      <summary>
        <para>Returns &quot;bFillAutoSizeMode&quot;. Corresponds to the Fill item of the Auto Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FitToDrawing">
      <summary>
        <para>Returns &quot;bFitToDrawing&quot;. Corresponds to the Fit to Drawing item of the Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FitToWidth">
      <summary>
        <para>Returns &quot;bFitToWidth&quot;. Corresponds to the Page Width ribbon item that sets the diagram zoom factor value to fit the entire diagram width.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FitToWindow">
      <summary>
        <para>Returns &quot;bFitToWindow&quot;. Corresponds to the Fit to Window ribbon item that sets the diagram zoom factor value to fit the entire diagram.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontBold">
      <summary>
        <para>Returns &quot;bFontBold&quot;. Corresponds to the Bold ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontFamily">
      <summary>
        <para>Returns &quot;bFontFamily&quot;. Corresponds to the Font ribbon menu item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontItalic">
      <summary>
        <para>Returns &quot;bFontItalic&quot;. Corresponds to the Italic ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontRibbonGroup">
      <summary>
        <para>Returns &quot;rgFont&quot;. Corresponds to the Font ribbon group.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontRibbonGroupSeparator">
      <summary>
        <para>Returns &quot;rsFontSeparator&quot;. Corresponds to the Font ribbon group separator.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontSize">
      <summary>
        <para>Returns &quot;bFontSize&quot;. Corresponds to the Font Size ribbon menu item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontSizeAndFamilyButtonGroup">
      <summary>
        <para>Returns &quot;bgFontSizeAndFamily&quot;. Corresponds to the ribbon group that contains the Font Size, Font Family, Increase Font Size and Decrease Font Size items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontSizeDecrease">
      <summary>
        <para>Returns &quot;bFontSizeDecrease&quot;. Corresponds to the Decrease Font Size ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontSizeIncrease">
      <summary>
        <para>Returns &quot;bFontSizeIncrease&quot;. Corresponds to the Increase Font Size ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontStrikethrough">
      <summary>
        <para>Returns &quot;bFontStrikethrough&quot;. Corresponds to the Strikethrough ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontTypeAndColorButtonGroup">
      <summary>
        <para>Returns &quot;bgFontTypeAndColor&quot;. Corresponds to the ribbon group that contains the Bold, Italic, Underline, Strikethrough, Bar Item Separator and Font Color items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontUnderline">
      <summary>
        <para>Returns &quot;bFontUnderline&quot;. Corresponds to the Underline ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ForegroundColor">
      <summary>
        <para>Returns &quot;bForegroundColor&quot;. Corresponds to the Font Color ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DefaultBarItemNames.GetFieldNameFromFieldValue(System.String)">
      <summary>
        <para>Returns the name of the property that corresponds to the specified value.</para>
      </summary>
      <param name="fieldValue">A string value that is returned by the desired property.</param>
      <returns>A System.String value that is the property name.</returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DefaultBarItemNames.GetPropertyNameFromPropertyValue(System.String)">
      <summary>
        <para>Returns the name of the property that corresponds to the specified value.</para>
      </summary>
      <param name="propertyValue">A string value that is returned by the desired property.</param>
      <returns>A System.String value that is the property name.</returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.HexagonTool">
      <summary>
        <para>Returns &quot;bHexagon&quot;. Corresponds to the Hexagon item of the Rectangle ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.HomeRibbonPage">
      <summary>
        <para>Returns &quot;rpHome&quot;. Corresponds to the Home ribbon page.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.HorizontalPageOrientation">
      <summary>
        <para>Returns &quot;bHorizontalPageOrientation&quot;. Corresponds to the Landscape item of the Orientation ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.HorizontalTextAlignmentButtonGroup">
      <summary>
        <para>Returns &quot;bgHorizontalTextAlignment&quot;. Corresponds to the ribbon group that contains the Align Left, Alight Center, Align Right and Justify items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.HorizontalTextAlignmentCenter">
      <summary>
        <para>Returns &quot;bHorizontalTextAlignmentCenter&quot;. Corresponds to the Align Center ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.HorizontalTextAlignmentJustify">
      <summary>
        <para>Returns &quot;bHorizontalTextAlignmentJustify&quot;. Corresponds to the Justify ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.HorizontalTextAlignmentLeft">
      <summary>
        <para>Returns &quot;bHorizontalTextAlignmentLeft&quot;. Corresponds to the Align Left ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.HorizontalTextAlignmentRight">
      <summary>
        <para>Returns &quot;bHorizontalTextAlignmentLeft&quot;. Corresponds to the Align Right ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsArrangeGroup">
      <summary>
        <para>Returns &quot;rgImageToolsArrangeGroup&quot;. Corresponds to the Arrange ribbon group that is placed in the Image Tools ribbon category and contains Bring to Frond and Send to Back items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsBringForward">
      <summary>
        <para>Returns &quot;bImageToolsBringForward&quot;. Corresponds to the Bring Forward item of the Bring to Front menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsBringToFront">
      <summary>
        <para>Returns &quot;bImageToolsBringForward&quot;. Corresponds to the Bring to Front item of the Bring to Front menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsBringToFrontContainer">
      <summary>
        <para>Returns &quot;bImageToolsBringToFrontContainer&quot;. Corresponds to the Bring to Front menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsFlipHorizontal">
      <summary>
        <para>Returns &quot;bImageToolsFlipHorizontal&quot;. Corresponds to the Flip Horizontal item of the Rotate menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsFlipVertical">
      <summary>
        <para>Returns &quot;bImageToolsFlipVertical&quot;. Corresponds to the Flip Vertical item of the Rotate menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsImagePictureGroup">
      <summary>
        <para>Returns &quot;rgImageToolsImagePictureGroup&quot;. Corresponds to the Picture ribbon group that is placed in the Image Tools ribbon category and contains Rotate, Set Stretch Mode, Set Image Scale, Reset Image and Change Image items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsLoadImage">
      <summary>
        <para>Returns &quot;bImageToolsLoadImage&quot;. Corresponds to the Change Image item that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsPageCategory">
      <summary>
        <para>Returns &quot;rImageToolsPageCategory&quot;. Corresponds to the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsResetToOriginalSize">
      <summary>
        <para>Returns &quot;bImageToolsResetToOriginalSize&quot;. Corresponds to the Reset Image item that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsRibbonPage">
      <summary>
        <para>Returns &quot;rpImageTools&quot;. Corresponds to the Format ribbon page.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsRotate">
      <summary>
        <para>Returns &quot;bImageToolsRotate&quot;. Corresponds to the Rotate menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsRotateLeft90">
      <summary>
        <para>Returns &quot;bImageToolsRotateLeft90&quot;. Corresponds to the Rotate Left 90 item of the Rotate menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsRotateRight90">
      <summary>
        <para>Returns &quot;bImageToolsRotateRight90&quot;. Corresponds to the Rotate Right 90 item of the Rotate menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsSendBackward">
      <summary>
        <para>Returns &quot;bImageToolsSendBackward&quot;. Corresponds to the Send Backward item of the Send to Back menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsSendToBack">
      <summary>
        <para>Returns &quot;bImageToolsSendToBack&quot;. Corresponds to the Send to Back item of the Send to Back menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsSendToBackContainer">
      <summary>
        <para>Returns &quot;bImageToolsSendToBackContainer&quot;. Corresponds to the Send to Back menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsSetImageScale">
      <summary>
        <para>Returns &quot;bImageToolsSetImageScale&quot;. Corresponds to the Set Image Scale menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsSetImageScale_0_25">
      <summary>
        <para>Returns &quot;bImageToolsSetImageScale_0_25&quot;. Corresponds to the 25 % item of the Set Image Scale menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsSetImageScale_0_5">
      <summary>
        <para>Returns &quot;bImageToolsSetImageScale_0_5&quot;. Corresponds to the 50 % item of the Set Image Scale menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsSetImageScale_0_75">
      <summary>
        <para>Returns &quot;bImageToolsSetImageScale_0_75&quot;. Corresponds to the 75 % item of the Set Image Scale menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsSetImageScale_1">
      <summary>
        <para>Returns &quot;bImageToolsSetImageScale_1&quot;. Corresponds to the 100 % item of the Set Image Scale menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsSetImageScale_1_5">
      <summary>
        <para>Returns &quot;bImageToolsSetImageScale_1_5&quot;. Corresponds to the 150 % item of the Set Image Scale menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsSetImageScale_2">
      <summary>
        <para>Returns &quot;bImageToolsSetImageScale_2&quot;. Corresponds to the 200 % item of the Set Image Scale menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsSetImageScale_4">
      <summary>
        <para>Returns &quot;bImageToolsSetImageScale_4&quot;. Corresponds to the 400 % item of the Set Image Scale menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsStretchMode">
      <summary>
        <para>Returns &quot;bImageToolsStretchMode&quot;. Corresponds to the Set Stretch Mode menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsStretchModeStretch">
      <summary>
        <para>Returns &quot;bImageToolsStretchModeStretch&quot;. Corresponds to the Stretch item of the Set Stretch Mode menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsStretchModeUniform">
      <summary>
        <para>Returns &quot;bImageToolsStretchModeUniform&quot;. Corresponds to the Uniform item of the Set Stretch Mode menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ImageToolsStretchModeUniformToFill">
      <summary>
        <para>Returns &quot;bImageToolsStretchModeUniformToFill&quot;. Corresponds to the Uniform to fill item of the Set Stretch Mode menu that is displayed in the Image Tools ribbon category.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.InsertContainer">
      <summary>
        <para>Returns &quot;bInsertContainer&quot;. Corresponds to the Container ribbon menu item that allows end-users to add containers to the diagram.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.InsertList">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.InsertRibbonPage">
      <summary>
        <para>Returns &quot;rpInsert&quot;. Corresponds to the Insert ribbon page.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.LayoutGroup">
      <summary>
        <para>Returns &quot;rgLayoutGroup&quot;. Corresponds to the Layout ribbon group.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.LegalPageSize">
      <summary>
        <para>Returns &quot;bLegalPageSize&quot;. Corresponds to the Legal item of the Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.LetterPageSize">
      <summary>
        <para>Returns &quot;bLetterPageSize&quot;. Corresponds to the Letter item of the Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ListOrientation">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ListOrientation_Horizontal">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ListOrientation_Vertical">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.MenuSeparator">
      <summary>
        <para>Returns &quot;bMenuSeparator&quot;. Corresponds to the separator in the ribbon application menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.MorePageSize">
      <summary>
        <para>Returns &quot;bMorePageSize&quot;. Corresponds to the More Page Sizes item of the Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.NewFile">
      <summary>
        <para>Returns &quot;bNewFile&quot;. Corresponds to the New item of the ribbon application menu that closes the current diagram, asking to save unsaved changes and creating a new diagram.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.NoneAutoSizeMode">
      <summary>
        <para>Returns &quot;bNoneAutoSizeMode&quot;. Corresponds to the None item of the Auto Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.OpenFile">
      <summary>
        <para>Returns &quot;bOpenFile&quot;. Corresponds to the Open File item of the ribbon application menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.OptionsGroup">
      <summary>
        <para>Returns &quot;rgOptionsGroup&quot;. Corresponds to the Options ribbon group that contains the Snap to items and Snap to grid items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.PageOrientation">
      <summary>
        <para>Returns &quot;bPageOrientation&quot;. Corresponds to the Orientation ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.PageSetupGroup">
      <summary>
        <para>Returns &quot;rgPageSetup&quot;. Corresponds to the Page Setup ribbon group that contains the Orientation, Size and Auto Size items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.PageSize">
      <summary>
        <para>Returns &quot;bPageSize&quot;. Corresponds to the Size ribbon menu item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.PageSizeSeparator">
      <summary>
        <para>Returns &quot;rsPageSizeSeparator&quot;. Corresponds to the separator displayed within the Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.PanAndZoomPanel">
      <summary>
        <para>Returns &quot;bPanAndZoomPanel&quot;. Corresponds to the Pan and Zoom ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.Panes">
      <summary>
        <para>Returns &quot;bPanes&quot;. Corresponds to the Panes ribbon menu item that allows end-users to show or hide the Shapes and Properties panels.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.PanTool">
      <summary>
        <para>Returns &quot;bPanTool&quot;. Corresponds to the Pan tool ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.Paste">
      <summary>
        <para>Returns &quot;bPaste&quot;. Corresponds to the Paste ribbon item that allows end-users to add diagram items from the clipboard to the canvas.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.Picture">
      <summary>
        <para>Returns &quot;bPicture&quot;. Corresponds to the Picture ribbon item that invokes the Insert Picture dialog window.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.PointerTool">
      <summary>
        <para>Returns &quot;bPointerTool&quot;. Corresponds to the Pointer ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.Print">
      <summary>
        <para>Returns &quot;bPrint&quot;. Corresponds to the Print item of the Print ribbon menu that invokes the Print dialog window.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.PrintMenu">
      <summary>
        <para>Returns &quot;bPrintMenu&quot;. Corresponds to the Print ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.PropertiesPanel">
      <summary>
        <para>Returns &quot;bPropertiesPanel&quot;. Corresponds to the Properties Panel item of the Panes ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.QuickPrint">
      <summary>
        <para>Returns &quot;bQuickPrint&quot;. Corresponds to the Quick Print item of the Print ribbon menu that sends the diagram to the default printer.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.RectangleTool">
      <summary>
        <para>Returns &quot;bRectangle&quot;. Corresponds to the Rectangle item of the Rectangle ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.Redo">
      <summary>
        <para>Returns &quot;bRedo&quot;. Corresponds to the Redo ribbon item in the quick access toolbar that allows end-users to reapply the last action that was undone in the diagram control.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayout">
      <summary>
        <para>Returns &quot;bReLayout&quot;. Corresponds to the Re-Layout Page ribbon menu that allows end-users to apply an automatic layout algorithm.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutCircular">
      <summary>
        <para>Returns &quot;bReLayoutCircular&quot;. Corresponds to the Circular item of the Re-Layout Page ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutCircularHeader">
      <summary>
        <para>Returns &quot;bReLayoutCircularHeader&quot;. Corresponds to the header item in the Re-Layout Page ribbon menu that displays the Circular text.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutMindMapTreeHeader">
      <summary>
        <para>Returns &quot;ReLayoutMindMapTreeHeader&quot;. Corresponds to the header item in the Re-Layout Page ribbon menu that displays the MindMap text.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutMindMapTreeHorizontal">
      <summary>
        <para>Returns &quot;bReLayoutMindMapTreeHorizontal&quot;. Corresponds to the Horizontal item that is contained within the Re-Layout Page ribbon menu. It allows end-users to apply the mindmap tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.OrientationKind.Horizontal"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutMindMapTreeVertical">
      <summary>
        <para>Returns &quot;bReLayoutMindMapTreeVertical&quot;. Corresponds to the Vertical item that is contained within the Re-Layout Page ribbon menu. It allows end-users to apply the mindmap tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.OrientationKind.Vertical"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutParts">
      <summary>
        <para>Returns &quot;bReLayoutParts&quot;. Corresponds to the Re-Layout Subordinates ribbon menu that allows end-users to apply an automatic layout algorithm.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutPartsMindMapTreeHeader">
      <summary>
        <para>Returns &quot;ReLayoutPartsMindMapTreeHeader&quot;. Corresponds to the header item in the Re-Layout Subordinates ribbon menu that displays the MindMap text.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutPartsMindMapTreeHorizontal">
      <summary>
        <para>Returns &quot;bReLayoutPartsMindMapTreeHorizontal&quot;. Corresponds to the Horizontal item that is contained within the Re-Layout Subordinates ribbon menu. It allows end-users to apply the mindmap tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.OrientationKind.Horizontal"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutPartsMindMapTreeVertical">
      <summary>
        <para>Returns &quot;bReLayoutPartsMindMapTreeVertical&quot;. Corresponds to the Vertical item that is contained within the Re-Layout Subordinates ribbon menu. It allows end-users to apply the mindmap tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.OrientationKind.Vertical"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutPartsTipOverTreeHeader">
      <summary>
        <para>Returns &quot;ReLayoutPartsTipOverTreeHeader&quot;. Corresponds to the header item in the Re-Layout Subordinates ribbon menu that displays the TipOver text.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutPartsTipOverTreeLeftToRight">
      <summary>
        <para>Returns &quot;ReLayoutPartsTipOverTreeLeftToRight&quot;. Corresponds to the Left To Right item that is contained within the Re-Layout Subordinates ribbon menu. It allows end-users to apply the tip-over tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.TipOverDirection.LeftToRight"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutPartsTipOverTreeRightToLeft">
      <summary>
        <para>Returns &quot;ReLayoutPartsTipOverTreeRightToLeft&quot;. Corresponds to the Right To Left item that is contained within the Re-Layout Subordinates ribbon menu. It allows end-users to apply the tip-over tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.TipOverDirection.RightToLeft"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutPartsTreeBottomToTop">
      <summary>
        <para>Returns &quot;bReLayoutPartsTreeBottomToTop&quot;. Corresponds to the Bottom To Top item that is contained within the Re-Layout Subordinates ribbon menu. It allows end-users to apply the tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.LayoutDirection.BottomToTop"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutPartsTreeHeader">
      <summary>
        <para>Returns &quot;ReLayoutPartsTree&quot;. Corresponds to the header item in the Re-Layout Subordinates ribbon menu that displays the Hierarchy text.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutPartsTreeLeftToRight">
      <summary>
        <para>Returns &quot;bReLayoutPartsTreeLeftToRight&quot;. Corresponds to the Left To Right item that is contained within the Re-Layout Subordinates ribbon menu. It allows end-users to apply the tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.LayoutDirection.LeftToRight"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutPartsTreeRightToLeft">
      <summary>
        <para>Returns &quot;bReLayoutPartsTreeRightToLeft&quot;. Corresponds to the Right To Left item that is contained within the Re-Layout Subordionates ribbon menu. It allows end-users to apply the tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.LayoutDirection.RightToLeft"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutPartsTreeTopToBottom">
      <summary>
        <para>Returns &quot;bReLayoutPartsTreeTopToBottom&quot;. Corresponds to the Top To Bottom item that is contained within the Re-Layout Subordinates ribbon menu. It allows end-users to apply the tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.LayoutDirection.TopToBottom"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutSugiyama">
      <summary>
        <para>Returns &quot;bReLayoutSugiyama&quot;. Corresponds to the header item in the Re-Layout Page ribbon menu that displays the Layered text.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutSugiyamaBottomToTop">
      <summary>
        <para>Returns &quot;bReLayoutSugiyamaBottomToTop&quot;. Corresponds to the Bottom To Top item that is contained within the Re-Layout Page ribbon menu. It allows end-users to apply the Sugyiama (layered) layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.LayoutDirection.BottomToTop"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutSugiyamaLeftToRight">
      <summary>
        <para>Returns &quot;bReLayoutSugiyamaLeftToRight&quot;. Corresponds to the Left To Right item that is contained within the Re-Layout Page ribbon menu. It allows end-users to apply the Sugyiama (layered) layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.LayoutDirection.LeftToRight"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutSugiyamaRightToLeft">
      <summary>
        <para>Returns &quot;bReLayoutSugiyamaRightToLeft&quot;. Corresponds to the Right To Left item that is contained within the Re-Layout Page ribbon menu. It allows end-users to apply the Sugyiama (layered) layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.LayoutDirection.RightToLeft"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutSugiyamaTopToBottom">
      <summary>
        <para>Returns &quot;bReLayoutSugiyamaTopToBottom&quot;. Corresponds to the Top To Bottom item that is contained within the Re-Layout Page ribbon menu. It allows end-users to apply the Sugyiama (layered) layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.LayoutDirection.TopToBottom"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutTipOverTreeHeader">
      <summary>
        <para>Returns &quot;ReLayoutTipOverTreeHeader&quot;. Corresponds to the header item in the Re-Layout Page ribbon menu that displays the TipOver text.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutTipOverTreeLeftToRight">
      <summary>
        <para>Returns &quot;ReLayoutTipOverTreeLeftToRight&quot;. Corresponds to the Left To Right item that is contained within the Re-Layout Page ribbon menu. It allows end-users to apply the tip-over tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.TipOverDirection.LeftToRight"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutTipOverTreeRightToLeft">
      <summary>
        <para>Returns &quot;ReLayoutTipOverTreeRightToLeft&quot;. Corresponds to the Right To Left item that is contained within the Re-Layout Page ribbon menu. It allows end-users to apply the tip-over tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.TipOverDirection.RightToLeft"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutTree">
      <summary>
        <para>Returns &quot;ReLayoutTree&quot;. Corresponds to the header item in the Re-Layout Page ribbon menu that displays the Hierarchy text.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutTreeBottomToTop">
      <summary>
        <para>Returns &quot;bReLayoutTreeBottomToTop&quot;. Corresponds to the Bottom To Top item that is contained within the Re-Layout Page ribbon menu. It allows end-users to apply the tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.LayoutDirection.BottomToTop"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutTreeLeftToRight">
      <summary>
        <para>Returns &quot;bReLayoutTreeLeftToRight&quot;. Corresponds to the Left To Right item that is contained within the Re-Layout Page ribbon menu. It allows end-users to apply the tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.LayoutDirection.LeftToRight"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutTreeRightToLeft">
      <summary>
        <para>Returns &quot;bReLayoutTreeRightToLeft&quot;. Corresponds to the Right To Left item that is contained within the Re-Layout Page ribbon menu. It allows end-users to apply the tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.LayoutDirection.RightToLeft"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ReLayoutTreeTopToBottom">
      <summary>
        <para>Returns &quot;bReLayoutTreeTopToBottom&quot;. Corresponds to the Top To Bottom item that is contained within the Re-Layout Page ribbon menu. It allows end-users to apply the tree layout algorithm with the <see cref="F:DevExpress.Diagram.Core.Layout.LayoutDirection.TopToBottom"/> direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.Ribbon">
      <summary>
        <para>Returns &quot;rParent&quot;. Corresponds to the diagram&#39;s Ribbon.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.RightAngleConnectorType">
      <summary>
        <para>Returns &quot;bRightAngleConnectorType&quot;. Corresponds to the Right Angle item that is contained within the Connectors ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.RightTriangleTool">
      <summary>
        <para>Returns &quot;bRightTriangle&quot;. Corresponds to the Right Triangle item of the Rectangle ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.Save">
      <summary>
        <para>Returns &quot;bSave&quot;. Corresponds to the Save ribbon item in the quick access toolbar that saves the current diagram. If the diagram has not been previously saved to a file, the Save File As dialog window is invoked.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.SaveAsFile">
      <summary>
        <para>Returns &quot;bSaveAsFile&quot;. Corresponds to the Save As item of the ribbon application menu that invokes the Save File As dialog window.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.SaveFile">
      <summary>
        <para>Returns &quot;bSaveFile&quot;. Corresponds to the Save item of the ribbon application menu that saves the current diagram. If the diagram has not been previously saved to a file, the Save File As dialog window is invoked.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.SendBackward">
      <summary>
        <para>Returns &quot;bSendBackward&quot;. Corresponds to the Send Backward item of the Send to Back ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.SendToBack">
      <summary>
        <para>Returns &quot;bSendToBack&quot;. Corresponds to the Send to Back item of the Send to Back ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.SendToBackContainer">
      <summary>
        <para>Returns &quot;bSendToBackContainer&quot;. Corresponds to the Send to Back ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ShapesPanel">
      <summary>
        <para>Returns &quot;bShapesPanel&quot;. Corresponds to the Shapes Panel item of the Panes ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ShapeStyles">
      <summary>
        <para>Returns &quot;bShapeStyles&quot;. Corresponds to the ribbon menu that allows end-users to apply a predefined style to selected diagram items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ShapeStylesGroup">
      <summary>
        <para>Returns &quot;rgShapeStyles&quot;. Corresponds to the ribbon group that contains the shape styles ribbon menu, Background and Stroke items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ShowContainerHeader">
      <summary>
        <para>Returns &quot;bShowContainerHeader&quot;. Corresponds to the Show Header ribbon item that allows end-users to toggle the visibility of headers for the selected containers.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ShowGrid">
      <summary>
        <para>Returns &quot;bShowGrid&quot;. Corresponds to the Grid ribbon item that allows end-users to toggle the visibility of grid lines.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ShowGroup">
      <summary>
        <para>Returns &quot;rgShowGroup&quot;. Corresponds to the ribbon group that contains the Ruler, Grid, Page Breaks and Panes items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ShowPageBreaks">
      <summary>
        <para>Returns &quot;bShowGrid&quot;. Corresponds to the Grid ribbon item that allows end-users to toggle the visibility of page breaks.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ShowPrintPreview">
      <summary>
        <para>Returns &quot;bShowPrintPreview&quot;. Corresponds to the Show Print Preview item of the ribbon application menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ShowRulers">
      <summary>
        <para>Returns &quot;bShowGrid&quot;. Corresponds to the Grid ribbon item that allows end-users to toggle the visibility of rulers.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.SnapToGrid">
      <summary>
        <para>Returns &quot;bSnapToGrid&quot;. Corresponds to the Snap to grid ribbon item that allows end-users to toggle snapping to the grid lines.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.SnapToItems">
      <summary>
        <para>Returns &quot;bSnapToGrid&quot;. Corresponds to the Snap to irid ribbon item that allows end-users to toggle snapping to the diagram items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.StatementPageSize">
      <summary>
        <para>Returns &quot;bStatementPageSize&quot;. Corresponds to the Statement item of the Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.StatusBar">
      <summary>
        <para>Returns &quot;rsbStatusBar&quot;. Corresponds to the status bar that displays the shape info and zoom slider.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.StatusBarAngle">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.StatusBarHeight">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.StatusBarShapeInfo">
      <summary>
        <para>Returns &quot;rsbShapeParameters&quot;. Corresponds to the shape info displayed within the status bar.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.StatusBarWidth">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.StatusBarZoomEditor">
      <summary>
        <para>Returns &quot;rsbZoomEditor&quot;. Corresponds to the zoom slider displayed within the status bar.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.StraightConnectorType">
      <summary>
        <para>Returns &quot;bStraightConnectorType&quot;. Corresponds to the Straight item that is contained within the Connectors ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.StrokeColor">
      <summary>
        <para>Returns &quot;bStrokeColor&quot;. Corresponds to the Stroke ribbon item that allows end-users to pick the stroke color for selected items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.TabloidPageSize">
      <summary>
        <para>Returns &quot;bTabloidPageSize&quot;. Corresponds to the Tabloid item of the Size ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.TextTool">
      <summary>
        <para>Returns &quot;bTextTool&quot;. Corresponds to the Text tool ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.TextToolsGroup">
      <summary>
        <para>Returns &quot;rgText&quot;. Corresponds to the ribbon group that contains the <see cref="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontSizeAndFamilyButtonGroup"/> and <see cref="P:DevExpress.Diagram.Core.DefaultBarItemNames.FontTypeAndColorButtonGroup"/> groups.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.Themes">
      <summary>
        <para>Returns &quot;bThemes&quot;. Corresponds to the ribbon menu that allows end-users to apply a predefined theme to the diagram.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ThemesGroup">
      <summary>
        <para>Returns &quot;rgPageGroup&quot;. Corresponds to the ribbon group that contains the themes ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ToolsContainer">
      <summary>
        <para>Returns &quot;bToolsContainer&quot;. Corresponds to the Rectangle ribbon menu that contains Rectangle, Ellipse, Right Triangle and Hexagon items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ToolsGroup">
      <summary>
        <para>Returns &quot;rgTools&quot;. Corresponds to the ribbon group that contains the Pointer tool, Connector and Rectangle items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.Undo">
      <summary>
        <para>Returns &quot;bUndo&quot;. Corresponds to the Undo ribbon item in the quick access toolbar that allows end-users to undo the most recent action in the diagram control.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.VerticalPageOrientation">
      <summary>
        <para>Returns &quot;bVerticalPageOrientation&quot;. Corresponds to the Portrait item of the Orientation ribbon menu.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.VerticalTextAlignmentBottom">
      <summary>
        <para>Returns &quot;bVerticalTextAlignmentBottom&quot;. Corresponds to the Align Bottom ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.VerticalTextAlignmentButtonGroup">
      <summary>
        <para>Returns &quot;bgVerticalTextAlignment&quot;. Corresponds to the ribbon group that contains the Align Top, Alight Middle and Align Bottom items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.VerticalTextAlignmentCenter">
      <summary>
        <para>Returns &quot;bVerticalTextAlignmentCenter&quot;. Corresponds to the Align Middle ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.VerticalTextAlignmentTop">
      <summary>
        <para>Returns &quot;bVerticalTextAlignmentTop&quot;. Corresponds to the Align Top ribbon item.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ViewRibbonPage">
      <summary>
        <para>Returns &quot;rpView&quot;. Corresponds to the View ribbon page that contains Show and Zoom groups.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DefaultBarItemNames.ZoomGroup">
      <summary>
        <para>Returns &quot;rgZoom&quot;. Corresponds to the ribbon group that contains the Fit to Window and Page Width items.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Diagram.Core.DiagramActionStage">
      <summary>
        <para>Lists values that indicate whether the drawing operation has just started, is continuing or has been finished or canceled.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramActionStage.Canceled">
      <summary>
        <para>The drawing operation is canceled.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramActionStage.Continue">
      <summary>
        <para>The drawing operation is ongoing.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramActionStage.Finished">
      <summary>
        <para>The drawing operation is finalized.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramActionStage.Start">
      <summary>
        <para>The drawing operation has just started.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.DiagramCommandsBase">
      <summary>
        <para>Serves as a base for classes providing access to Diagram commands.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramCommandsBase.#ctor(DevExpress.Diagram.Core.IDiagramControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.DiagramCommandsBase"/> class.</para>
      </summary>
      <param name="diagram">An object implementing the DevExpress.Diagram.Core.IDiagramControl interface.</param>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.BringForwardCommand">
      <summary>
        <para>Bring the selected diagram items forward.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.BringToFrontCommand">
      <summary>
        <para>Bring the selected diagram items to the front.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.CancelCommand">
      <summary>
        <para>Exit the input state (i.e., editing a text within a diagram item) or, if the control is not in the input state, deselect all diagram items.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramCommandsBase.CanExecute(DevExpress.Diagram.Core.DiagramCommand)">
      <summary>
        <para>Gets whether the specified command can be executed in its current state.</para>
      </summary>
      <param name="command">A DevExpress.Diagram.Core.DiagramCommand object.</param>
      <returns>true if the specified command can be executed; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramCommandsBase.CanExecute``1(DevExpress.Diagram.Core.DiagramCommand{``0},``0)">
      <summary>
        <para>Gets whether the specified command can be executed in its current state.</para>
      </summary>
      <param name="command">A DevExpress.Diagram.Core.DiagramCommand object.</param>
      <param name="parameter">A command&#39;s parameter.</param>
      <typeparam name="T"></typeparam>
      <returns>true if the specified command can be executed; otherwise, false.</returns>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ChangeConnectorTypeCommand">
      <summary>
        <para>Change the type of the selected connector.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.CircularLayoutCommand">
      <summary>
        <para>Realign diagram items according to the circular layout algorithm.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.CollapseSelectedContainersCommand">
      <summary>
        <para>Collapse the selected containers.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Diagram.Core.DiagramCommandsBase.CommandHotkeys">
      <summary>
        <para>Represents hotkeys used in the Diagram control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramCommandsBase.CommandHotkeys.#ctor(System.Windows.Input.Key,System.Windows.Input.ModifierKeys)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.DiagramCommandsBase.CommandHotkeys"/> class.</para>
      </summary>
      <param name="key">A System.Windows.Input.Key value that represents the shortcut key.</param>
      <param name="modifierKeys">A System.Windows.Input.ModifierKeys value that represents the shortcut key modifiers.</param>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramCommandsBase.CommandHotkeys.GetHashCode">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramCommandsBase.CommandHotkeys.Key">
      <summary>
        <para>Gets the shortcut key.</para>
      </summary>
      <value>A System.Windows.Input.Key value that represents the shortcut key.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramCommandsBase.CommandHotkeys.ModifierKeys">
      <summary>
        <para>Gets the shortcut key modifiers.</para>
      </summary>
      <value>A System.Windows.Input.ModifierKeys value that represents the shortcut key modifiers.</value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.CopyCommand">
      <summary>
        <para>Copy the selected diagram items.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.CutCommand">
      <summary>
        <para>Remove the selected diagram items and copy them to the Clipboard.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.DecreaseFontSizeCommand">
      <summary>
        <para>Decrease the font size for the text within the selected diagram items.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramCommandsBase.DefaultCommands">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.DeleteCommand">
      <summary>
        <para>Remove the selected diagram items.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.EditCommand">
      <summary>
        <para>Edit the text within the selected shape.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.EmptyCommand">
      <summary>
        <para>An empty command.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramCommandsBase.Execute(DevExpress.Diagram.Core.DiagramCommand,DevExpress.Diagram.Core.Native.IMouseArgs)">
      <summary>
        <para>Executes the specified diagram command.</para>
      </summary>
      <param name="command">A diagram command to be executed.</param>
      <param name="args"></param>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramCommandsBase.Execute``1(DevExpress.Diagram.Core.DiagramCommand{``0},``0,DevExpress.Diagram.Core.Native.IMouseArgs)">
      <summary>
        <para>Executes the specified diagram command.</para>
      </summary>
      <param name="command">A diagram command to be executed.</param>
      <param name="parameter">Data used by the command. If the command does not require data to be passed, this object can be set to a null reference.</param>
      <param name="args"></param>
      <typeparam name="T"></typeparam>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ExportDiagramCommand">
      <summary>
        <para>Export the diagram in the specified format to a file.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ExportToPdfCommand">
      <summary>
        <para>Exports the diagram to a PDF document.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ExportToSvgCommand">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.FitToDrawingCommand">
      <summary>
        <para>Change the page size to fit the diagram contents.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.FitToPageCommand">
      <summary>
        <para>Set the diagram zoom factor value to fit all pages with content within the canvas.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.FitToWidthCommand">
      <summary>
        <para>Set the diagram zoom factor value to fit the entire diagram width.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.FlipImageCommand">
      <summary>
        <para>Flip the selected image item.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.FocusNextControlCommand">
      <summary>
        <para>Focuses the next control after the Diagram control in the visual tree.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.FocusPrevControlCommand">
      <summary>
        <para>Focuses the previous control after the Diagram control in the visual tree.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramCommandsBase.GetCommandHotkeys(DevExpress.Diagram.Core.DiagramCommandBase,System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="command"></param>
      <param name="parameter"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramCommandsBase.GetKeyDownCommand(System.Windows.Input.Key,System.Windows.Input.ModifierKeys)">
      <summary>
        <para>Gets the command associated with pressing down the specified key or key combination.</para>
      </summary>
      <param name="key">A key.</param>
      <param name="modifiers">A set of modifier keys.</param>
      <returns>The associated command.</returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramCommandsBase.GetKeyUpCommand(System.Windows.Input.Key,System.Windows.Input.ModifierKeys)">
      <summary>
        <para>Gets the command associated with releasing the specified key or key combination.</para>
      </summary>
      <param name="key">A key.</param>
      <param name="modifiers">A set of modifier keys.</param>
      <returns>The associated command.</returns>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.IncreaseFontSizeCommand">
      <summary>
        <para>Increase the font size for the text within the selected diagram items.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.InsertImageCommand">
      <summary>
        <para>Invoke the &quot;Insert Picture&quot; dialog window.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.LoadImageCommand">
      <summary>
        <para>Invoke the Insert Picture dialog to change the selected image item.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.MindMapTreeLayoutCommand">
      <summary>
        <para>Realign diagram items to form a mind map tree diagram.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.MindMapTreeLayoutForSubordinatesCommand">
      <summary>
        <para>Realign the subordinate items of the selected diagram item to form a mind map tree.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.MoveSelectionCommand">
      <summary>
        <para>Move the selected diagram items the specified distance from its current position in the specified direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.MoveSelectionNoSnapCommand">
      <summary>
        <para>Move the selected diagram items the specified distance from its current position in the specified direction without snapping them to the grid.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.NewFileCommand">
      <summary>
        <para>Close the current file and create a new one. If the current file contains unsaved changes, the end-user is prompted to save them.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.OpenFileCommand">
      <summary>
        <para>Create a new diagram file and open it.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.PasteCommand">
      <summary>
        <para>Inserts the diagram items from the Clipboard onto the canvas.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.PrintCommand">
      <summary>
        <para>Invoke the Print dialog window.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.QuickPrintCommand">
      <summary>
        <para>Print the diagram with default print settings.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.RedoCommand">
      <summary>
        <para>Reapply the last action that was undone in the diagram control.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramCommandsBase.RegisterHandlers(System.Action{DevExpress.Diagram.Core.IHandlersRegistrator},System.Boolean)">
      <summary>
        <para>Allows you to override the default commands.</para>
      </summary>
      <param name="registerHandlers">An action delegate that registers a custom method to be called when executing the specified command.</param>
      <param name="updateCommandsOnRequerySuggested">true, to update the commands UI after each diagram action; otherwise, false. By default, false.</param>
      <returns>A collection of custom command handlers.</returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramCommandsBase.RegisterHotKeys(System.Action{DevExpress.Diagram.Core.IHotKeysRegistrator})">
      <summary>
        <para>Allows you to clear the existing shortcuts or define new ones.</para>
      </summary>
      <param name="registerHotKeys">An action delegate that clears the existing shortcuts or registers custom ones.</param>
      <returns>A collection of command handlers.</returns>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ResetSelectedImagesCommand">
      <summary>
        <para>Reset the selected images to their original state.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.RotateCommand">
      <summary>
        <para>Rotate the selected item counterclockwise around its anchor point by the specified angle.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SaveFileAsCommand">
      <summary>
        <para>Invoke the Save File As dialog window.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SaveFileCommand">
      <summary>
        <para>Save changes made to the diagram. If the diagram has not been saved to a file before, the Save File As dialog window is invoked.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SelectAllCommand">
      <summary>
        <para>Select all items within the diagram.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SelectConnectorToolCommand">
      <summary>
        <para>Select the tool used to create connectors.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SelectEllipseToolCommand">
      <summary>
        <para>Select the tool used to create ellipse shapes.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SelectHexagonToolCommand">
      <summary>
        <para>Select the tool used to create hexagonal shapes.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramCommandsBase.SelectionCommands">
      <summary>
        <para>Provides access to selection commands.</para>
      </summary>
      <value>An object implementing the <see cref="T:System.Collections.IEnumerable"/> interface.</value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SelectNextItemCommand">
      <summary>
        <para>Select the next diagram item in the order they were added to the diagram.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SelectPanToolCommand">
      <summary>
        <para>Select the tool used to navigate the diagram.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SelectPointerToolCommand">
      <summary>
        <para>Select the tool used to select diagram items.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SelectPrevItemCommand">
      <summary>
        <para>Select the previous diagram item in the order they were added to the diagram.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SelectRectangleToolCommand">
      <summary>
        <para>Select the tool used to create rectangular shapes.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SelectRightTriangleToolCommand">
      <summary>
        <para>Select the tool used to create triangular shapes.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SelectTextToolCommand">
      <summary>
        <para>Select the tool used to create text shapes.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SelectToolCommand">
      <summary>
        <para>Select the specified diagram tool.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SendBackwardCommand">
      <summary>
        <para>Send the selected diagram items backward.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SendToBackCommand">
      <summary>
        <para>Send the selected diagram items to the back.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SetHorizontalAlignmentCommand">
      <summary>
        <para>Set the horizontal alignment of the text within the selected shapes.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SetPageParametersCommand">
      <summary>
        <para>Invoke the Page Setup dialog window.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SetSelectedContainersHeaderPaddingCommand">
      <summary>
        <para>Set the specified header padding value for the selected container items.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SetSelectedContainersPaddingCommand">
      <summary>
        <para>Set the specified padding value for the selected container items.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SetSelectedContainersShapeCommand">
      <summary>
        <para>Set the specified shape style for the selected container items.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SetSelectedImagesScaleCommand">
      <summary>
        <para>Set the specified scale factor for the selected image items.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SetSelectedImagesStretchModeCommand">
      <summary>
        <para>Set the specified stretch mode for the selected image items.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SetSelectedItemsStyleCommand">
      <summary>
        <para>Set the specified style (id) for the selected diagram items.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SetSelectedListsOrientationCommand">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SetVerticalAlignmentCommand">
      <summary>
        <para>Set the vertical alignment of the text within the selected shapes.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SetZoomCommand">
      <summary>
        <para>Apply a zoom factor value to a document.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ShowContainerHeaderCommand">
      <summary>
        <para>Toggle the visibility of headers of the selected containers.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ShowPopupMenuCommand">
      <summary>
        <para>Invoke the popup menu for the selected item.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ShowPrintPreviewCommand">
      <summary>
        <para>Invoke the Print Preview.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ShowPropertiesPanelCommand">
      <summary>
        <para>Invoke the &#39;Properties&#39; panel.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.StartDragToolAlternateCommand">
      <summary>
        <para>Start a drag-and-drop operation for the tool passed to the command as the parameter.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.StartDragToolCommand">
      <summary>
        <para>Select the tool used to drag diagram items.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.SugiyamaLayoutCommand">
      <summary>
        <para>Realign diagram items according to the layered graph drawing (Sugiyama-style graph drawing) algorithm.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.TipOverTreeLayoutCommand">
      <summary>
        <para>Realign diagram items according to the tip-over tree layout algorithm.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.TipOverTreeLayoutForSubordinatesCommand">
      <summary>
        <para>Realign the subordinate items of the selected diagram item according to the tip-over tree layout algorithm.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ToggleFontBoldCommand">
      <summary>
        <para>Toggle bold font for the text within the selected shapes.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ToggleFontItalicCommand">
      <summary>
        <para>Toggle italic font for the text within the selected shapes.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ToggleFontStrikethroughCommand">
      <summary>
        <para>Toggle strikethrough for the text within the selected shapes.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ToggleFontUnderlineCommand">
      <summary>
        <para>Toggle underline for the text within the selected shapes.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ToggleSubordinatesVisibilityCommand">
      <summary>
        <para>Toggle the visibility of the child items of the selected diagram item.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.TreeLayoutCommand">
      <summary>
        <para>Realign diagram items to form a tree diagram.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.TreeLayoutForSubordinatesCommand">
      <summary>
        <para>Realign the subordinate items of the selected diagram item according to the tree layout algorithm.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.UndoCommand">
      <summary>
        <para>Cancel changes resulting from the last operation.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramCommandsBase.UndoRedoCommands">
      <summary>
        <para>Provides access to undo and redo commands.</para>
      </summary>
      <value>An object implementing the <see cref="T:System.Collections.IEnumerable"/> interface.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramCommandsBase.UpdateCommands(System.Collections.Generic.IEnumerable{DevExpress.Diagram.Core.DiagramCommandBase})">
      <summary>
        <para></para>
      </summary>
      <param name="commands"></param>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.UseToolCommand">
      <summary>
        <para>Use a diagram tool.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramCommandsBase.ZoomCommands">
      <summary>
        <para>Provides access to zoom commands.</para>
      </summary>
      <value>An object implementing the <see cref="T:System.Collections.IEnumerable"/> interface.</value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ZoomInCommand">
      <summary>
        <para>Increase the current zoom factor of a diagram.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramCommandsBase.ZoomOutCommand">
      <summary>
        <para>Decrease the current zoom factor of a diagram.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Diagram.Core.DiagramConnectorsSeparationMode">
      <summary>
        <para>Lists values that specify whether to automatically split the overlapping right-angle connectors.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramConnectorsSeparationMode.AllLines">
      <summary>
        <para>Overlapping right-angle connectors are automatically split.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramConnectorsSeparationMode.NoLines">
      <summary>
        <para>Right-angle connectors can be overlapped.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.DiagramExportFormat">
      <summary>
        <para>Lists the values used to specify the file format in which to export the diagram.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramExportFormat.BMP">
      <summary>
        <para>The diagram is exported in BMP format.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramExportFormat.GIF">
      <summary>
        <para>The diagram is exported in GIF format.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramExportFormat.JPEG">
      <summary>
        <para>The diagram is exported in JPEG format.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramExportFormat.PDF">
      <summary>
        <para>The diagram is exported in PDF format.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramExportFormat.PNG">
      <summary>
        <para>The diagram is exported in PNG format.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramExportFormat.SVG">
      <summary>
        <para>The diagram is exported in vector (SVG) format.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.DiagramFontEffects">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramFontEffects.#ctor(System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.DiagramFontEffects"/> class with specified settings.</para>
      </summary>
      <param name="isFontBold"></param>
      <param name="isFontItalic"></param>
      <param name="isFontUnderline"></param>
      <param name="isFontStrikethrough"></param>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramFontEffects.IsFontBold">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramFontEffects.IsFontItalic">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramFontEffects.IsFontStrikethrough">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramFontEffects.IsFontUnderline">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Diagram.Core.DiagramImageExportFormat">
      <summary>
        <para>Lists the values used to specify the image format in which to export the diagram.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramImageExportFormat.BMP">
      <summary>
        <para>The diagram is exported in BMP format.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramImageExportFormat.GIF">
      <summary>
        <para>The diagram is exported in GIF format.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramImageExportFormat.JPEG">
      <summary>
        <para>The diagram is exported in JPEG format.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramImageExportFormat.PNG">
      <summary>
        <para>The diagram is exported in PNG format.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.DiagramItemTypeRegistrator">
      <summary>
        <para>Provides methods used to create serializable custom diagram item types.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramItemTypeRegistrator.Create(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="itemKind"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramItemTypeRegistrator.GetItemKind(DevExpress.Diagram.Core.IDiagramItem)">
      <summary>
        <para></para>
      </summary>
      <param name="item"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramItemTypeRegistrator.GetItemKind(System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="itemType"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramItemTypeRegistrator.Register(System.Type[])">
      <summary>
        <para></para>
      </summary>
      <param name="itemTypes"></param>
    </member>
    <member name="T:DevExpress.Diagram.Core.DiagramScrollMode">
      <summary>
        <para>Lists the values used to specify the diagram scroll mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramScrollMode.Content">
      <summary>
        <para>The scrolling is enabled only if the diagram items are not fully visible.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DiagramScrollMode.Page">
      <summary>
        <para>The diagram canvas is scrollable regardless of the content.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.DiagramStencil">
      <summary>
        <para>Represents a set of shapes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.#ctor(System.String,System.Func{System.String})">
      <summary>
        <para></para>
      </summary>
      <param name="id"></param>
      <param name="getName"></param>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.#ctor(System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="id"></param>
      <param name="name"></param>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.#ctor(System.String,System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="id"></param>
      <param name="name"></param>
      <param name="isVisible"></param>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramStencil.ContainerShapes">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.ContainsShape(DevExpress.Diagram.Core.ShapeDescriptionBase)">
      <summary>
        <para></para>
      </summary>
      <param name="shape"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.ContainsShape(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="shapeId"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.ConvertShapesToXml(System.Collections.IDictionary,System.IO.Stream)">
      <summary>
        <para></para>
      </summary>
      <param name="resourceDictionary"></param>
      <param name="stream"></param>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.Create(System.String,System.String,System.Collections.Generic.IEnumerable{DevExpress.Diagram.Core.ShapeDescriptionBase},System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="stencilId"></param>
      <param name="stencilName"></param>
      <param name="shapes"></param>
      <param name="isVisible"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.Create(System.String,System.String,System.Collections.Generic.IEnumerable{DevExpress.Diagram.Core.Shapes.ShapeTemplateBase},System.Func{System.String,System.String},System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="stencilId"></param>
      <param name="stencilName"></param>
      <param name="shapes"></param>
      <param name="getShapeName"></param>
      <param name="isVisible"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.Create(System.String,System.String,System.Collections.IDictionary,System.Func{System.String,System.String},System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="stencilId"></param>
      <param name="stencilName"></param>
      <param name="shapesDictionary"></param>
      <param name="getShapeName"></param>
      <param name="isVisible"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.Create(System.String,System.String,System.IO.Stream,System.Func{System.String,System.String},System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="stencilId"></param>
      <param name="stencilName"></param>
      <param name="shapesStream"></param>
      <param name="getShapeName"></param>
      <param name="isVisible"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.GetContainerShape(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="shapeId"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.GetShape(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="shapeId"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.GetTool(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="toolId"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramStencil.Id">
      <summary>
        <para>Gets the stencil identifier.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value identifying the stencil.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramStencil.IsVisible">
      <summary>
        <para>Gets whether the stencil is available in the Shapes Panel.</para>
      </summary>
      <value>true, if the stencil is available in the Shapes Panel; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramStencil.Name">
      <summary>
        <para>Gets the stencil name displayed in the Shapes Panel.</para>
      </summary>
      <value>A string value that is the name of the stencil.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.RegisterShape(DevExpress.Diagram.Core.ShapeDescriptionBase)">
      <summary>
        <para>Adds the specified shape to the stencil.</para>
      </summary>
      <param name="shape">A <see cref="T:DevExpress.Diagram.Core.ShapeDescriptionBase"/> descendant representing the shape to add to the stencil.</param>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.RegisterTool(DevExpress.Diagram.Core.ItemTool)">
      <summary>
        <para>Adds the specified item tool to the stencil.</para>
      </summary>
      <param name="tool">A DevExpress.Diagram.Core.ItemTool descendant.</param>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramStencil.Shapes">
      <summary>
        <para>Gets the collection of shapes associated with the stencil.</para>
      </summary>
      <value>A collection of objects implementing the DevExpress.Diagram.Core.IDiagramShape interface.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramStencil.Tools">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.UnregisterShape(DevExpress.Diagram.Core.ShapeDescriptionBase)">
      <summary>
        <para>Removes the specified shape from the stencil.</para>
      </summary>
      <param name="shape">A <see cref="T:DevExpress.Diagram.Core.ShapeDescriptionBase"/> descendant representing the shape to add to the stencil.</param>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.UnregisterTool(DevExpress.Diagram.Core.ItemTool)">
      <summary>
        <para></para>
      </summary>
      <param name="tool"></param>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramStencil.UnregisterTool(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="toolId"></param>
    </member>
    <member name="T:DevExpress.Diagram.Core.DiagramTheme">
      <summary>
        <para>Represents a diagram theme.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramTheme.#ctor(System.String,System.Func{System.String},DevExpress.Diagram.Core.Themes.DiagramColorPalette,DevExpress.Diagram.Core.Themes.DiagramEffectCollection,DevExpress.Diagram.Core.DiagramFontSettings)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.DiagramTheme"/> class.</para>
      </summary>
      <param name="themeId">A <see cref="T:System.String"/> value identifying the theme.</param>
      <param name="getThemeName">A function that returns the theme name.</param>
      <param name="colorPalette">A <see cref="T:DevExpress.Diagram.Core.Themes.DiagramColorPalette"/> object representing the color scheme.</param>
      <param name="effectCollection">A <see cref="T:DevExpress.Diagram.Core.Themes.DiagramEffectCollection"/> object that stores theme, variant and connector effects.</param>
      <param name="fontSettings">A DevExpress.Diagram.Core.DiagramFontSettings object representing the font settings for shapes and connectors.</param>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramTheme.#ctor(System.String,System.String,DevExpress.Diagram.Core.Themes.DiagramColorPalette,DevExpress.Diagram.Core.Themes.DiagramEffectCollection,DevExpress.Diagram.Core.DiagramFontSettings,DevExpress.Diagram.Core.DiagramFontSettings)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.DiagramTheme"/> class.</para>
      </summary>
      <param name="themeId">A <see cref="T:System.String"/> value identifying the theme.</param>
      <param name="themeName">A <see cref="T:System.String"/> value representing the name of the theme.</param>
      <param name="colorPalette">A <see cref="T:DevExpress.Diagram.Core.Themes.DiagramColorPalette"/> object representing the color scheme.</param>
      <param name="effectCollection">A <see cref="T:DevExpress.Diagram.Core.Themes.DiagramEffectCollection"/> object that stores theme, variant and connector effects.</param>
      <param name="fontSettings">A DevExpress.Diagram.Core.DiagramFontSettings object representing the font settings for shapes and connectors.</param>
      <param name="containerFontSettings">A DevExpress.Diagram.Core.DiagramFontSettings object representing the font settings for containers.</param>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramTheme.ColorPalette">
      <summary>
        <para>Gets the theme&#39;s color scheme.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Themes.DiagramColorPalette"/> object representing the theme&#39;s color scheme.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramTheme.ConnectorStyles">
      <summary>
        <para>Gets the collection of connector styles.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Diagram.Core.Themes.DiagramItemStyle"/> objects.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramTheme.ContainerFontSettings">
      <summary>
        <para>Gets the font settings for containers.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.DiagramFontSettings object that stores font settings.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramTheme.EffectCollection">
      <summary>
        <para>Gets the effects collection that specifies how colors are used in the theme.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Themes.DiagramEffectCollection"/> object.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramTheme.FontSettings">
      <summary>
        <para>Gets the font settings for shapes and connectors.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.DiagramFontSettings object that stores font settings.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramTheme.GetDiagramItemStyle(DevExpress.Diagram.Core.DiagramItemStyleId)">
      <summary>
        <para>Returns the diagram item style corresponding to the specified identifier.</para>
      </summary>
      <param name="id">A DevExpress.Diagram.Core.DiagramItemStyleId object that identifies the diagram item style.</param>
      <returns>A <see cref="T:DevExpress.Diagram.Core.Themes.DiagramItemStyle"/> object.</returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramTheme.Id">
      <summary>
        <para>Gets the theme identifier.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value identifying the theme.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramTheme.Name">
      <summary>
        <para>Gets the name of the theme.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the name of the theme.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramTheme.ThemeShapeStyles">
      <summary>
        <para>Gets the collection of shape styles.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Diagram.Core.Themes.DiagramItemStyle"/> objects.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramTheme.ToString">
      <summary>
        <para>Returns the name of the <see cref="T:DevExpress.Diagram.Core.DiagramTheme"/>.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value.</returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramTheme.VariantShapeStyles">
      <summary>
        <para>Gets the collection of shape styles.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Diagram.Core.Themes.DiagramItemStyle"/> objects.</value>
    </member>
    <member name="T:DevExpress.Diagram.Core.DiagramTool">
      <summary>
        <para>The base class for classes that represent diagram tools.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramTool.ActivationAction(DevExpress.Diagram.Core.IDiagramControl)">
      <summary>
        <para></para>
      </summary>
      <param name="diagram"></param>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramTool.CreateActiveInputState(DevExpress.Diagram.Core.IDiagramControl,DevExpress.Diagram.Core.Native.IInputElement,DevExpress.Diagram.Core.Native.IMouseButtonArgs,DevExpress.Diagram.Core.IDiagramItem)">
      <summary>
        <para></para>
      </summary>
      <param name="diagram"></param>
      <param name="item"></param>
      <param name="mouseArgs"></param>
      <param name="previouslyCreatedItem"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramTool.CreateFeedbackHelper(DevExpress.Diagram.Core.IDiagramControl)">
      <summary>
        <para></para>
      </summary>
      <param name="diagram"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramTool.CreateMouseInputState(DevExpress.Diagram.Core.IDiagramControl,DevExpress.Diagram.Core.Native.IInputElement,DevExpress.Diagram.Core.Native.IMouseButtonArgs,DevExpress.Diagram.Core.IDiagramItem)">
      <summary>
        <para></para>
      </summary>
      <param name="diagram"></param>
      <param name="item"></param>
      <param name="mouseArgs"></param>
      <param name="previouslyCreatedItem"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramTool.DefaultAction(DevExpress.Diagram.Core.IDiagramControl)">
      <summary>
        <para></para>
      </summary>
      <param name="diagram"></param>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramTool.GetDefaultInputStateCursor(DevExpress.Diagram.Core.IDiagramControl,DevExpress.Diagram.Core.Native.IInputElement,DevExpress.Diagram.Core.Native.IInputArgs,DevExpress.Diagram.Core.IDiagramItem)">
      <summary>
        <para></para>
      </summary>
      <param name="diagram"></param>
      <param name="item"></param>
      <param name="mouseArgs"></param>
      <param name="previouslyCreatedItem"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramTool.ShowFullSelectionUI">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramTool.StartDrag(DevExpress.Diagram.Core.IDiagramControl,DevExpress.Diagram.Core.Native.IMouseArgs,DevExpress.Diagram.Core.MouseButtonKind)">
      <summary>
        <para></para>
      </summary>
      <param name="diagram"></param>
      <param name="mouse"></param>
      <param name="button"></param>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramTool.ToolId">
      <summary>
        <para>Returns the identifier of the tool.</para>
      </summary>
      <value>A System.String value that is the identifier of the tool.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramTool.ToolName">
      <summary>
        <para>Returns the name of the tool.</para>
      </summary>
      <value>A System.String value that is the name of the tool.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramTool.ToString">
      <summary>
        <para>Returns a string representation of the diagram tool.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> that represents the current <see cref="T:DevExpress.Diagram.Core.DiagramTool"/> object.</returns>
    </member>
    <member name="T:DevExpress.Diagram.Core.DiagramToolboxRegistrator">
      <summary>
        <para>Provides methods for modifying the available shapes in the Shapes Panel.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramToolboxRegistrator.GetStencil(System.String)">
      <summary>
        <para>Returns the stencil with the specified id.</para>
      </summary>
      <param name="stencilId">A string value that is the stencil&#39;s id.</param>
      <returns>A <see cref="T:DevExpress.Diagram.Core.DiagramStencil"/> object.</returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramToolboxRegistrator.GetStencilByShape(DevExpress.Diagram.Core.ShapeDescriptionBase)">
      <summary>
        <para>Returns the stencil that contains the specified shape.</para>
      </summary>
      <param name="shape">A <see cref="T:DevExpress.Diagram.Core.ShapeDescriptionBase"/> descendant that represents a diagram shape.</param>
      <returns>A <see cref="T:DevExpress.Diagram.Core.DiagramStencil"/> object.</returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramToolboxRegistrator.RegisterShapes(System.String,System.Func{System.String},System.Collections.IDictionary,System.Func{System.String,System.String})">
      <summary>
        <para></para>
      </summary>
      <param name="stencilId"></param>
      <param name="getStencilName"></param>
      <param name="shapesDictionary"></param>
      <param name="getShapeName"></param>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramToolboxRegistrator.RegisterStencil(DevExpress.Diagram.Core.DiagramStencil)">
      <summary>
        <para>Adds the specified stencil to the Shapes Panel.</para>
      </summary>
      <param name="stencil">A <see cref="T:DevExpress.Diagram.Core.DiagramStencil"/> object that is the stencil to register.</param>
    </member>
    <member name="E:DevExpress.Diagram.Core.DiagramToolboxRegistrator.StencilListChanged">
      <summary>
        <para>Fires each time the list of available stencils is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Diagram.Core.DiagramToolboxRegistrator.Stencils">
      <summary>
        <para>Returns the list of stencils available in the Shapes Panel.</para>
      </summary>
      <value>A list of <see cref="T:DevExpress.Diagram.Core.DiagramStencil"/> objects.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.DiagramToolboxRegistrator.UnregisterStencil(DevExpress.Diagram.Core.DiagramStencil)">
      <summary>
        <para>Removes the specified stencil from the Shapes Panel.</para>
      </summary>
      <param name="stencil">A <see cref="T:DevExpress.Diagram.Core.DiagramStencil"/> object that is the stencil to remove.</param>
    </member>
    <member name="T:DevExpress.Diagram.Core.Direction">
      <summary>
        <para>Specifies the flow direction for diagram items when laying out shapes automatically.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Direction.Down">
      <summary>
        <para>The flow direction is top to bottom.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Direction.Left">
      <summary>
        <para>The flow direction is right to left.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Direction.Right">
      <summary>
        <para>The flow direction is left to right.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Direction.Up">
      <summary>
        <para>The flow direction is bottom to top.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.DragActionKind">
      <summary>
        <para>Lists values that specify if and how the drag-and-drop operation should continue.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragActionKind.Cancel">
      <summary>
        <para>Cancel the drag-and-drop operation and discard changes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragActionKind.Continue">
      <summary>
        <para>Continue the drag-and-drop operation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragActionKind.Drop">
      <summary>
        <para>Finalize the drag-and-drop operation.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.DragDropCursor">
      <summary>
        <para>Lists values that specify the appearance of the mouse pointer during drag-and-drop operations.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropCursor.Custom">
      <summary>
        <para>Custom cursor is displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropCursor.Diagram">
      <summary>
        <para>Default diagram cursor is displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropCursor.System">
      <summary>
        <para>System cursor is displayed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.DragDropEffectsKind">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropEffectsKind.All">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropEffectsKind.Copy">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropEffectsKind.Link">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropEffectsKind.Move">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropEffectsKind.None">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropEffectsKind.Scroll">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.DragDropKeyState">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropKeyState.AltKey">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropKeyState.ControlKey">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropKeyState.LeftMouseButton">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropKeyState.MiddleMouseButton">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropKeyState.None">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropKeyState.RightMouseButton">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.DragDropKeyState.ShiftKey">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ExpandSubordinatesButtonMode">
      <summary>
        <para>Lists values that specify the visibility of the expand-collapse button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ExpandSubordinatesButtonMode.AlwaysVisible">
      <summary>
        <para>The expand button is always visible.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ExpandSubordinatesButtonMode.Default">
      <summary>
        <para>If the entire diagram is generated, the expand/collapse buttons are hidden.If the generation and/or expansion depth is set to a non-default value, expand/collapse buttons are displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ExpandSubordinatesButtonMode.LookupChildrenInSource">
      <summary>
        <para>A shape displays the expand/collapse button if it has hidden children shapes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ExpandSubordinatesButtonMode.None">
      <summary>
        <para>Expand/collapse buttons are hidden</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.FactoryConnectorTool">
      <summary>
        <para>Allows you to create custom connector tools.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.FactoryConnectorTool.#ctor(System.String,System.Func{System.String},System.Func{DevExpress.Diagram.Core.IDiagramControl,DevExpress.Diagram.Core.IDiagramConnector})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.FactoryConnectorTool"/> class.</para>
      </summary>
      <param name="id">A System.String value that is the tool&#39;s identifier.</param>
      <param name="getName">A function that returns the name of the tool.</param>
      <param name="createItem">A function that creates the connector.</param>
    </member>
    <member name="P:DevExpress.Diagram.Core.FactoryConnectorTool.ToolId">
      <summary>
        <para>Returns the identifier of the tool.</para>
      </summary>
      <value>A System.String value that is the identifier of the tool.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.FactoryConnectorTool.ToolName">
      <summary>
        <para>Returns the name of the tool.</para>
      </summary>
      <value>A System.String value that is the name of the tool.</value>
    </member>
    <member name="T:DevExpress.Diagram.Core.FactoryItemTool">
      <summary>
        <para>Allows you to create custom item tools.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.FactoryItemTool.#ctor(System.String,System.Func{System.String},System.Func{DevExpress.Diagram.Core.IDiagramControl,DevExpress.Diagram.Core.IDiagramItem},System.Nullable{System.Windows.Size},System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.FactoryItemTool"/> class.</para>
      </summary>
      <param name="id">A System.String value that is the tool&#39;s identifier.</param>
      <param name="getName">A function that returns the name of the tool.</param>
      <param name="createItem">A function that creates the item.</param>
      <param name="defaultSize">The default item size.</param>
      <param name="isQuick">true to display the item in the Quick Shapes category of the Shapes Panel; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Diagram.Core.FactoryItemTool.#ctor(System.String,System.Func{System.String},System.Func{DevExpress.Diagram.Core.IDiagramControl,DevExpress.Diagram.Core.ItemUsage,DevExpress.Diagram.Core.IDiagramItem},System.Nullable{System.Windows.Size},System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.FactoryItemTool"/> class.</para>
      </summary>
      <param name="id">A System.String value that is the tool&#39;s identifier.</param>
      <param name="getName">A function that returns the name of the tool.</param>
      <param name="createItem">A function that creates the item.</param>
      <param name="defaultSize">The default item size.</param>
      <param name="isQuick">true to display the item in the Quick Shapes category of the Shapes Panel; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Diagram.Core.FactoryItemTool.DefaultItemSize">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.FactoryItemTool.IsQuick">
      <summary>
        <para>Gets whether to display the shape in the Quick Shapes category of the Shapes Panel.</para>
      </summary>
      <value>true to display the shape in the Quick Shapes category of the Shapes Panel; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.FactoryItemTool.ToolId">
      <summary>
        <para>Returns the identifier of the tool.</para>
      </summary>
      <value>A System.String value that is the identifier of the tool.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.FactoryItemTool.ToolName">
      <summary>
        <para>Returns the name of the tool.</para>
      </summary>
      <value>A System.String value that is the name of the tool.</value>
    </member>
    <member name="T:DevExpress.Diagram.Core.GeometryKind">
      <summary>
        <para>Lists the values that specify how to draw a segment in the ShapeTemplate.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.GeometryKind.Closed">
      <summary>
        <para>Draw a straight line from the last segment to the first segment.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.GeometryKind.ClosedFilled">
      <summary>
        <para>Draw a straight line from the last segment to the first segment and fill the segments with the FillColor.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.GeometryKind.Filled">
      <summary>
        <para>Fill the segments with the FillColor.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.GeometryKind.None">
      <summary>
        <para>Draw the segments without additional modifications.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ImageFlipMode">
      <summary>
        <para>Lists values that specify how the image is flipped.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ImageFlipMode.Both">
      <summary>
        <para>The image is flipped both vertically and horizontally.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ImageFlipMode.Horizontal">
      <summary>
        <para>The image is flipped horizontally.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ImageFlipMode.None">
      <summary>
        <para>The image is not flipped.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ImageFlipMode.Vertical">
      <summary>
        <para>The image is flipped vertically.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ItemsActionKind">
      <summary>
        <para>Lists values that represent the kind of the action that raised an event.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ItemsActionKind.Copy">
      <summary>
        <para>The event was raised by copying items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ItemsActionKind.Delete">
      <summary>
        <para>The event was raised by deleting items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ItemsActionKind.Move">
      <summary>
        <para>The event was raised by moving items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ItemsActionKind.MoveCopy">
      <summary>
        <para>The event was raised by moving and copying items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ItemsActionKind.Resize">
      <summary>
        <para>The event was raised by resizing items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ItemsActionKind.Rotate">
      <summary>
        <para>The event was raised by rotating items.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ItemsActionSource">
      <summary>
        <para>Lists values that represent the source of the action that raised an event.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ItemsActionSource.Key">
      <summary>
        <para>The event was raised by pressing a key.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ItemsActionSource.Mouse">
      <summary>
        <para>The event was raised by a mouse operation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ItemsActionSource.PropertyChanging">
      <summary>
        <para>The event was raised using the Properties panel.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ItemsChangedAction">
      <summary>
        <para>Lists values that indicate whether a diagram item has been added or removed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ItemsChangedAction.Added">
      <summary>
        <para>Item has been added.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ItemsChangedAction.Removed">
      <summary>
        <para>Item has been removed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ItemUsage">
      <summary>
        <para>Lists values that indicate whether the item is a part of the diagram or toolbox preview.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ItemUsage.Diagram">
      <summary>
        <para>Indicates whether the item has been created in the diagram.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ItemUsage.ToolboxPreview">
      <summary>
        <para>The item has been created in the toolbox preview.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Diagram.Core.Layout">
      <summary>
        <para>Contains classes that relate to the diagram item arrangement.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Layout.CircularLayoutOrder">
      <summary>
        <para>Lists the values used to specify how the circular layout algorithm arranges shapes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.CircularLayoutOrder.Clockwise">
      <summary>
        <para>Arrange shapes clockwise.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.CircularLayoutOrder.Counterclockwise">
      <summary>
        <para>Arrange shapes counterclockwise.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.CircularLayoutOrder.Optimal">
      <summary>
        <para>Arrange shapes minimizing the number of crossings of connection lines.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Layout.CircularLayoutSettings">
      <summary>
        <para>Contains circular layout settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Layout.CircularLayoutSettings.#ctor(System.Double,DevExpress.Diagram.Core.Layout.CircularLayoutOrder,System.Double)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Layout.CircularLayoutSettings"/> class.</para>
      </summary>
      <param name="nodesSpacing">A System.Double value that specifies the spacing between adjacent shapes.</param>
      <param name="order">A <see cref="T:DevExpress.Diagram.Core.Layout.CircularLayoutOrder"/> enumeration value.</param>
      <param name="startAngle">A System.Double value that specifies the starting angle in degrees in the counterclockwise direction.</param>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.CircularLayoutSettings.NodesSpacing">
      <summary>
        <para>Specifies the distance between adjacent shapes.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.CircularLayoutSettings.Order">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.CircularLayoutSettings.StartAngle">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Diagram.Core.Layout.LayoutDirection">
      <summary>
        <para>Lists the values used to specify the layout direction when applying an automatic layout algorithm.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.LayoutDirection.BottomToTop">
      <summary>
        <para>The layout direction is bottom-to-top.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.LayoutDirection.LeftToRight">
      <summary>
        <para>The layout direction is left-to-right.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.LayoutDirection.RightToLeft">
      <summary>
        <para>The layout direction is right-to-left.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.LayoutDirection.TopToBottom">
      <summary>
        <para>The layout direction is top-to-bottom.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode">
      <summary>
        <para>Specifies which diagram items are realigned when performing automatic relayout.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode.AllComponents">
      <summary>
        <para>Realign all items on the canvas.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode.NonTrivialComponentsOnly">
      <summary>
        <para>Realign only items that are connected to at least one other item.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Layout.SubTreeDefaultSplitMode">
      <summary>
        <para>Specifies the criteria for arranging the mind map branches.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.SubTreeDefaultSplitMode.Area">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.SubTreeDefaultSplitMode.Breadth">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.SubTreeDefaultSplitMode.RootChildrenCount">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.SubTreeDefaultSplitMode.TreeNodesCount">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings">
      <summary>
        <para>Contains Sugiyama layout settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings.#ctor(System.Double,System.Double,DevExpress.Diagram.Core.Layout.LayoutDirection)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings"/> class.</para>
      </summary>
      <param name="columnSpacing">A System.Double value that is the distance between shapes on the same level of hierarchy.</param>
      <param name="layerSpacing">A System.Double value that is the distance between layers of hierarchy.</param>
      <param name="layoutDirection">A <see cref="T:DevExpress.Diagram.Core.Layout.LayoutDirection"/> enumeration value that is the flow direction for the automatic layout.</param>
    </member>
    <member name="M:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings.#ctor(System.Double,System.Double,System.Windows.Size,DevExpress.Diagram.Core.Direction,DevExpress.Diagram.Core.ThicknessInfo)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings"/> class.</para>
      </summary>
      <param name="columnSpacing"></param>
      <param name="layerSpacing"></param>
      <param name="pageSize"></param>
      <param name="direction"></param>
      <param name="margin"></param>
    </member>
    <member name="M:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings.#ctor(System.Double,System.Double,System.Windows.Size,DevExpress.Diagram.Core.Direction,System.Windows.Point)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings"/> class.</para>
      </summary>
      <param name="columnSpacing"></param>
      <param name="layerSpacing"></param>
      <param name="pageSize"></param>
      <param name="direction"></param>
      <param name="margin"></param>
    </member>
    <member name="P:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings.BreadthOrientation">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings.ColumnSpacing">
      <summary>
        <para>The distance between shapes on the same level of hierarchy.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings.DepthOrientation">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings.Direction">
      <summary>
        <para>The flow direction.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Direction"/> enumeration value.</value>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings.LayerSpacing">
      <summary>
        <para>The distance between layers of hierarchy.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings.LayoutDirection">
      <summary>
        <para>The flow direction for the automatic layout.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings.LogicalDirection">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings.Margin">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings.PageSize">
      <summary>
        <para>The diagram page size.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Diagram.Core.Layout.TipOverDirection">
      <summary>
        <para>Lists the values used to specify how the tip-over tree layout algorithm arranges shapes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.TipOverDirection.LeftToRight">
      <summary>
        <para>The layout direction is left-to-right.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.TipOverDirection.RightToLeft">
      <summary>
        <para>The layout direction is right-to-left.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Layout.TipOverOffsetMode">
      <summary>
        <para>Lists the values used to specify the offset mode for the tip-over tree layout algorithm.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.TipOverOffsetMode.Center">
      <summary>
        <para>The offset is applied from the center of parent shapes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.TipOverOffsetMode.Edge">
      <summary>
        <para>The offset is applied from the edge of parent shapes.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Layout.TipOverTreeLayoutSettings">
      <summary>
        <para>Contains tip-over tree layout settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Layout.TipOverTreeLayoutSettings.#ctor(DevExpress.Diagram.Core.Layout.TipOverDirection,DevExpress.Diagram.Core.Layout.TipOverOffsetMode,System.Double,System.Double)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Layout.TipOverTreeLayoutSettings"/> class.</para>
      </summary>
      <param name="direction">A <see cref="T:DevExpress.Diagram.Core.Layout.TipOverDirection"/> enumeration value.</param>
      <param name="offsetMode">A <see cref="T:DevExpress.Diagram.Core.Layout.TipOverOffsetMode"/> enumeration value that specifies whether the offset is applied from the center or the edge of parent shapes. .</param>
      <param name="offset">A System.Double value that specifies the offset.</param>
      <param name="verticalSpacing">A System.Double value that specifies the vertical spacing between shapes.</param>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.TipOverTreeLayoutSettings.Direction">
      <summary>
        <para>Specifies the tip-over layout direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.TipOverTreeLayoutSettings.Offset">
      <summary>
        <para>Specifies the offset applied to the next level of hierarchy.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.TipOverTreeLayoutSettings.OffsetMode">
      <summary>
        <para>Specifies how the resulting offset is calculated.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.TipOverTreeLayoutSettings.VerticalSpacing">
      <summary>
        <para>Specifies the distance between layers of hierarchy.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Diagram.Core.Layout.TreeConnectorsRouting">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.TreeConnectorsRouting.CalculatePointIndex">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.TreeConnectorsRouting.Default">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Layout.TreeLayoutSettings">
      <summary>
        <para>Contains tree layout settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Layout.TreeLayoutSettings.#ctor(System.Double,System.Double,DevExpress.Diagram.Core.Direction,DevExpress.Diagram.Core.ThicknessInfo)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Layout.TreeLayoutSettings"/> class.</para>
      </summary>
      <param name="horizontalSpacing">A System.Double value that specifies the horizontal spacing between shapes.</param>
      <param name="verticalSpacing">A System.Double value that specifies the vertical spacing between shapes.</param>
      <param name="direction">A <see cref="T:DevExpress.Diagram.Core.Direction"/> enumeration value.</param>
      <param name="margin">A DevExpress.Diagram.Core.ThicknessInfo object that is used to set the additional spacing between items.</param>
    </member>
    <member name="M:DevExpress.Diagram.Core.Layout.TreeLayoutSettings.#ctor(System.Double,System.Double,DevExpress.Diagram.Core.Direction,System.Double)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Layout.TreeLayoutSettings"/> class.</para>
      </summary>
      <param name="horizontalSpacing">A System.Double value that specifies the horizontal spacing between shapes.</param>
      <param name="verticalSpacing">A System.Double value that specifies the vertical spacing between shapes.</param>
      <param name="direction">A System.Double enumeration value.</param>
      <param name="margin">A DevExpress.Diagram.Core.ThicknessInfo object that is used to set the additional spacing between items.</param>
    </member>
    <member name="M:DevExpress.Diagram.Core.Layout.TreeLayoutSettings.#ctor(System.Double,System.Double,DevExpress.Diagram.Core.Layout.LayoutDirection,System.Boolean,DevExpress.Diagram.Core.Alignment)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Layout.TreeLayoutSettings"/> class.</para>
      </summary>
      <param name="horizontalSpacing">A System.Double value that specifies the horizontal spacing between shapes.</param>
      <param name="verticalSpacing">A System.Double value that specifies the vertical spacing between shapes.</param>
      <param name="layoutDirection">A <see cref="T:DevExpress.Diagram.Core.Layout.LayoutDirection"/> enumeration value.</param>
      <param name="isCompact">true to enable the compact tree layout; otherwise, false.</param>
      <param name="alignment">An <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value that specifies how the tree layout algorithm arranges shapes relatively to the layout axis.</param>
    </member>
    <member name="P:DevExpress.Diagram.Core.Layout.TreeLayoutSettings.Alignment">
      <summary>
        <para>Returns the value that specifies how the tree layout algorithm arranges shapes relatively to the layout axis.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Layout.TreeLayoutSettings.BreadthOrientation">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Layout.TreeLayoutSettings.DepthOrientation">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Layout.TreeLayoutSettings.Direction">
      <summary>
        <para>Returns the tree layout direction.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Direction"/> enumeration value.</value>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.TreeLayoutSettings.IsCompact">
      <summary>
        <para>Specifies whether the automatic tree layout arranges shapes minimizing the space between them.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.TreeLayoutSettings.LayoutDirection">
      <summary>
        <para>Specifies the tree layout direction.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Layout.TreeLayoutSettings.LogicalDirection">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Layout.TreeLayoutSettings.Margin">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Layout.TreeLayoutSettings.PageMargin">
      <summary>
        <para>Gets the amount of space between the page&#39;s borders and its contents.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.Layout.TreeLayoutSettings.Spacing">
      <summary>
        <para>Specifies the distance between adjacent shapes.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Diagram.Core.LineJumpPlacement">
      <summary>
        <para>Specifies which connector lines display jumps in intersections.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.LineJumpPlacement.HorizontalLines">
      <summary>
        <para>Line jumps are added to horizontal lines.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.LineJumpPlacement.None">
      <summary>
        <para>Line jumps are not shown.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.LineJumpPlacement.VerticalLines">
      <summary>
        <para>Line jumps are added to vertical lines.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Diagram.Core.Localization">
      <summary>
        <para>Contains classes that provide the localization capabilities of the Diagram control.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Localization.DiagramControlLocalizer">
      <summary>
        <para>A base class that provides necessary functionality for custom localizers of the Diagram Control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Localization.DiagramControlLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Localization.DiagramControlLocalizer"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Diagram.Core.Localization.DiagramControlLocalizer.Active">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Diagram.Core.Localization.DiagramControlLocalizer.CreateResXLocalizer">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.Localization.DiagramControlLocalizer.GetString(DevExpress.Diagram.Core.Localization.DiagramControlStringId)">
      <summary>
        <para></para>
      </summary>
      <param name="id"></param>
      <returns></returns>
    </member>
    <member name="T:DevExpress.Diagram.Core.MeasureUnit">
      <summary>
        <para>Represents a unit of measure used by the <see cref="T:DevExpress.XtraDiagram.DiagramControl"/> and <see cref="T:DevExpress.Xpf.Diagram.DiagramControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.MeasureUnit.#ctor(System.Double,System.Int32,System.String,DevExpress.Diagram.Core.TickStepsData[],System.Func{System.String})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.MeasureUnit"/> class with specified settings.</para>
      </summary>
      <param name="dpi"></param>
      <param name="multiplier"></param>
      <param name="name"></param>
      <param name="stepsData"></param>
      <param name="getSuffix"></param>
    </member>
    <member name="F:DevExpress.Diagram.Core.MeasureUnit.Dpi">
      <summary>
        <para>Specifies the base resolution.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.MeasureUnit.Multiplier">
      <summary>
        <para>Specifies the multiplier for the base resolution.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.MeasureUnit.Name">
      <summary>
        <para>Specifies the name of the measure unit.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.MeasureUnit.StepsData">
      <summary>
        <para>Specifies the array of the DevExpress.Diagram.Core.TickStepsData objects used to render the grid and ruler.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.MeasureUnit.Suffix">
      <summary>
        <para>Specifies the symbol for the measure unit.</para>
      </summary>
      <value>A string value that is displayed next to the numeric value in UI.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.MeasureUnit.ToString">
      <summary>
        <para>Returns the name of the <see cref="T:DevExpress.Diagram.Core.MeasureUnit"/>.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value.</returns>
    </member>
    <member name="T:DevExpress.Diagram.Core.MeasureUnits">
      <summary>
        <para>Contains units of measurement available for the <see cref="T:DevExpress.XtraDiagram.DiagramControl"/> and <see cref="T:DevExpress.Xpf.Diagram.DiagramControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.MeasureUnits.FromDeviceIndependentPixels(DevExpress.Diagram.Core.MeasureUnit,System.Double)">
      <summary>
        <para>Converts the value in device-independent pixels to the value in the unit of measure.</para>
      </summary>
      <param name="unit">A unit of measure for the desired value.</param>
      <param name="value">A value to convert.</param>
      <returns>The value in the unit of measure.</returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.MeasureUnits.FromDeviceIndependentPixels(DevExpress.Diagram.Core.MeasureUnit,System.Windows.Size)">
      <summary>
        <para></para>
      </summary>
      <param name="unit"></param>
      <param name="size"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.MeasureUnits.FromPixels(DevExpress.Diagram.Core.MeasureUnit,System.Double)">
      <summary>
        <para>Converts the value in pixels to the value in the unit of measure.</para>
      </summary>
      <param name="unit">A unit of measure for the desired value.</param>
      <param name="value">A value to convert.</param>
      <returns>The value in the unit of measure.</returns>
    </member>
    <member name="F:DevExpress.Diagram.Core.MeasureUnits.Inches">
      <summary>
        <para>Represents inches as the measure unit.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.MeasureUnits.Millimeters">
      <summary>
        <para>Represents millimeters as the measure unit.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Diagram.Core.MeasureUnits.Pixels">
      <summary>
        <para>Represents pixels as the measure unit.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Diagram.Core.MeasureUnits.ToDeviceIndependentPixels(DevExpress.Diagram.Core.MeasureUnit,System.Double)">
      <summary>
        <para>Converts the value in the unit of measure to the value in device-independent pixels.</para>
      </summary>
      <param name="unit">A unit of measure for the initial value.</param>
      <param name="value">A value to convert.</param>
      <returns>The value in device-independent pixels.</returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.MeasureUnits.ToDeviceIndependentPixels(DevExpress.Diagram.Core.MeasureUnit,System.Windows.Size)">
      <summary>
        <para></para>
      </summary>
      <param name="unit"></param>
      <param name="size"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.MeasureUnits.ToPixels(DevExpress.Diagram.Core.MeasureUnit,System.Double)">
      <summary>
        <para>Converts the value in the unit of measure to the value in pixels.</para>
      </summary>
      <param name="unit">A unit of measure for the initial value.</param>
      <param name="value">A value to convert.</param>
      <returns>The value in pixels.</returns>
    </member>
    <member name="T:DevExpress.Diagram.Core.ModifySelectionMode">
      <summary>
        <para>Lists the values used to specify whether to add or replace the existing selection with selected diagram items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ModifySelectionMode.AddToSelection">
      <summary>
        <para>Selected diagram items are added to the existing selection.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ModifySelectionMode.ReplaceSelection">
      <summary>
        <para>The existing selection is replaced with selected diagram items.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Diagram.Core.Native">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Native.SerializationKind">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Native.SerializationKind.CopyPaste">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Native.SerializationKind.Recreate">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.Native.SerializationKind.SaveLoadDocument">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.OrientationKind">
      <summary>
        <para>Lists values that specify the orientation of a diagram.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.OrientationKind.Horizontal">
      <summary>
        <para>The diagram has a horizontal orientation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.OrientationKind.Vertical">
      <summary>
        <para>The diagram has a vertical orientation.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.PageSetupTabs">
      <summary>
        <para>Lists the values used to specify the default tab of the Page Setup window.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.PageSetupTabs.LayoutAndRouting">
      <summary>
        <para>The default tab is Layout and Routing.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.PageSetupTabs.PageSize">
      <summary>
        <para>The default tab is Page Size.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.PointerToolDragMode">
      <summary>
        <para>Lists values that specify whether dragging the pointer tool pans across the canvas or draws the selection rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.PointerToolDragMode.Pan">
      <summary>
        <para>Dragging the pointer pans across the canvas.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.PointerToolDragMode.Selection">
      <summary>
        <para>Dragging the pointer draws the selection rectangle.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.PrintExportMode">
      <summary>
        <para>Lists values that specify whether to preserve the diagram content placement relative to the document boundaries when printing or exporting the diagram.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.PrintExportMode.Content">
      <summary>
        <para>This mode ignores the diagram content placement relative to the document boundaries. When exporting to a file, the resulting image is cropped to include only diagram items. When printing, the diagram content is shifted to the top left corner of the first page.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.PrintExportMode.Page">
      <summary>
        <para>This mode preserves the diagram content placement relative to the document boundaries.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.PropertiesPanelVisibility">
      <summary>
        <para>Lists the values used to specify the Properties panel display mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.PropertiesPanelVisibility.Closed">
      <summary>
        <para>The Properties panel is closed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.PropertiesPanelVisibility.Collapsed">
      <summary>
        <para>The Properties panel is hidden and can be invoked by the end-user by hovering the Properties tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.PropertiesPanelVisibility.Visible">
      <summary>
        <para>The Properties panel is fully expanded.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ResizingMode">
      <summary>
        <para>Lists the values used to specify whether diagram items change their size in real time during resizing.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ResizingMode.Live">
      <summary>
        <para>Diagram items change its size in real time during resizing.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ResizingMode.Preview">
      <summary>
        <para>Diagram items change size after the end-user exits the resizing mode by releasing the left mouse button.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.SelectionMode">
      <summary>
        <para>Lists values that specify the selection mode for diagram items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.SelectionMode.Extended">
      <summary>
        <para>Multiple items can be selected and deselected by clicking them while holding the SHIFT or CTRL key.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.SelectionMode.Multiple">
      <summary>
        <para>Multiple items can be selected and deselected by clicking them.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.SelectionMode.None">
      <summary>
        <para>Item selection is disabled.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.SelectionMode.Single">
      <summary>
        <para>Only one item can be selected.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ShapeDescription">
      <summary>
        <para>Contains basic diagram shape options.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.ShapeDescription.#ctor(System.String,System.Func{System.String},System.Func{System.Windows.Size},DevExpress.Diagram.Core.ShapeGetter,DevExpress.Diagram.Core.ShapeConnectionPointsGetter,System.Func{DevExpress.Diagram.Core.ParameterCollection},DevExpress.Diagram.Core.EditorBoundsGetter,System.Func{DevExpress.Diagram.Core.DiagramItemStyleId},System.Func{System.Boolean},System.Func{System.Boolean})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.ShapeDescription"/> class.</para>
      </summary>
      <param name="id">A System.String value that is the shape&#39;s identifier.</param>
      <param name="getName">A function that returns the name of the shape.</param>
      <param name="getDefaultSize">A function that returns the default size of the shape.</param>
      <param name="getShape">A delegate function that returns the width, height and parameters of the shape.</param>
      <param name="getConnectionPoints">A delegate function that returns connection points.</param>
      <param name="getParameterCollection">A function that returns shape parameters.</param>
      <param name="getEditorBounds">A delegate function that returns the boundaries of the shape&#39;s editor.</param>
      <param name="getStyleId">A function that returns the identifier of a style to apply to the shape.</param>
      <param name="getIsQuick">A function that returns whether to display the shape within the Quick Shapes category of the toolbox.</param>
      <param name="getUseBackgroundAsForeground">A function that returns whether to paint the shape with the background color.</param>
    </member>
    <member name="M:DevExpress.Diagram.Core.ShapeDescription.Create(System.String,DevExpress.Diagram.Core.Localization.DiagramControlStringId,System.Func{System.Windows.Size},DevExpress.Diagram.Core.ShapeGetter,DevExpress.Diagram.Core.ShapeConnectionPointsGetter,DevExpress.Diagram.Core.EditorBoundsGetter,DevExpress.Diagram.Core.DiagramItemStyleId,System.Boolean,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="id"></param>
      <param name="stringId"></param>
      <param name="getDefaultSize"></param>
      <param name="getShape"></param>
      <param name="getConnectionPoints"></param>
      <param name="getEditorBounds"></param>
      <param name="styleId"></param>
      <param name="isQuick"></param>
      <param name="useBackgroundAsForeground"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.ShapeDescription.CreateSvgShape(System.String,System.String,System.IO.Stream,System.Boolean,System.Func{System.Windows.Size,System.Collections.Generic.IEnumerable{System.Windows.Point}},System.String,System.String)">
      <summary>
        <para>Creates a diagram shape from a stream that contains an SVG image.</para>
      </summary>
      <param name="shapeId">A string value that identifies the shape.</param>
      <param name="name">A string value that is the name of the shape.</param>
      <param name="svgStream">A System.IO.Stream object that contains the SVG image.</param>
      <param name="isQuick">true to display the shape within the Quick Shapes category of the Shapes toolbox; otherwise, false.</param>
      <param name="getConnectionPoints">A function that specifies the coordinates of connection points.</param>
      <param name="backgroundColorCode">A string value that represents the code of the color to be swapped with the theme&#39;s background color.</param>
      <param name="strokeColorCode">A string value that represents the code of the color to be swapped with the theme&#39;s foreground color.</param>
      <returns>A <see cref="T:DevExpress.Diagram.Core.ShapeDescription"/> object that represents the shape.</returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.ShapeDescription.CreateTemplateShape(System.String,System.Func{System.String},System.Func{DevExpress.Diagram.Core.Shapes.ShapeTemplate})">
      <summary>
        <para>This method supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <param name="shapeId"></param>
      <param name="getName"></param>
      <param name="getTemplateCore"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.ShapeDescription.CreateTemplateShape(System.String,System.String,DevExpress.Diagram.Core.Shapes.ShapeTemplate)">
      <summary>
        <para>Creates a shape from the specified template.</para>
      </summary>
      <param name="shapeId">A System.String value that is the identifier of the template.</param>
      <param name="name">A System.String value that is the name of the template.</param>
      <param name="template">A DevExpress.Diagram.Core.Shapes.ShapeTemplate object that contains shape description.</param>
      <returns>A <see cref="T:DevExpress.Diagram.Core.ShapeDescription"/> instance.</returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.ShapeDescription.DefaultParameters">
      <summary>
        <para>Returns the default shape parameters.</para>
      </summary>
      <value>A list of System.Double values that represent shape parameters.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.ShapeDescription.GetDefaultShape">
      <summary>
        <para>Returns the shape with the default size and parameters.</para>
      </summary>
      <returns>A DevExpress.Diagram.Core.ShapeGeometry object that is the shape with the default size and parameters.</returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.ShapeDescription.IsQuick">
      <summary>
        <para>Gets whether to display the shape in the Quick Shapes category of the Shapes Panel.</para>
      </summary>
      <value>true to display the shape in the Quick Shapes category of the Shapes Panel; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.ShapeDescription.ParameterCollection">
      <summary>
        <para>Stores the shape parameters.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ParameterCollection object that lists the shape parameters.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.ShapeDescription.ToString">
      <summary>
        <para>Returns the name of the <see cref="T:DevExpress.Diagram.Core.ShapeDescription"/>.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value.</returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.ShapeDescription.Update(System.String,System.Func{System.String},System.Func{System.Windows.Size},DevExpress.Diagram.Core.ShapeGetter,DevExpress.Diagram.Core.ShapeConnectionPointsGetter,System.Func{DevExpress.Diagram.Core.ParameterCollection},DevExpress.Diagram.Core.EditorBoundsGetter,System.Func{DevExpress.Diagram.Core.DiagramItemStyleId},System.Func{System.Boolean},System.Func{System.Boolean})">
      <summary>
        <para>Returns the <see cref="T:DevExpress.Diagram.Core.ShapeDescription"/> instance with updated properties.</para>
      </summary>
      <param name="id">A System.String value that is the shape&#39;s identifier.</param>
      <param name="getName">A function that returns the name of the shape.</param>
      <param name="getDefaultSize">A function that returns the default size of the shape.</param>
      <param name="getShape">A delegate function that returns the width, height and parameters of the shape.</param>
      <param name="getConnectionPoints">A delegate function that returns connection points.</param>
      <param name="getParameterCollection">A function that returns shape parameters.</param>
      <param name="getEditorBounds">A delegate function that returns the boundaries of the shape&#39;s editor.</param>
      <param name="getStyleId">A function that returns the identifier of a style to apply to the shape.</param>
      <param name="getIsQuick">A function that returns whether to display the shape within the Quick Shapes category of the toolbox.</param>
      <param name="getUseBackgroundAsForeground">A function that returns whether to paint the shape with the background color.</param>
      <returns>A <see cref="T:DevExpress.Diagram.Core.ShapeDescription"/> instance with updated properties.</returns>
    </member>
    <member name="T:DevExpress.Diagram.Core.ShapeDescriptionBase">
      <summary>
        <para>Serves as the base for classes that represent diagram shapes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Diagram.Core.ShapeDescriptionBase.BackgroundColorCode">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.ShapeDescriptionBase.DefaultSize">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Diagram.Core.ShapeDescriptionBase.GetConnectionPoints(System.Windows.Size,System.Collections.Generic.IEnumerable{System.Double})">
      <summary>
        <para></para>
      </summary>
      <param name="size"></param>
      <param name="parameters"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.ShapeDescriptionBase.GetEditorBounds(System.Windows.Size,System.Collections.Generic.IEnumerable{System.Double})">
      <summary>
        <para></para>
      </summary>
      <param name="size"></param>
      <param name="parameters"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.ShapeDescriptionBase.GetShape(System.Windows.Size,System.Collections.Generic.IEnumerable{System.Double})">
      <summary>
        <para></para>
      </summary>
      <param name="size"></param>
      <param name="parameters"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.ShapeDescriptionBase.Id">
      <summary>
        <para>Gets the shape identifier.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value identifying the shape.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.ShapeDescriptionBase.Name">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.ShapeDescriptionBase.StrokeColorCode">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.ShapeDescriptionBase.StyleId">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.ShapeDescriptionBase.UseBackgroundAsForeground">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="N:DevExpress.Diagram.Core.Shapes">
      <summary>
        <para>Contains classes that are used to define shape geometries in the Diagram Control.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Shapes.Arc">
      <summary>
        <para>Represents an arc with the size and the direction.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Shapes.Arc.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Shapes.Arc"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Arc.Direction">
      <summary>
        <para>Specifies the direction of the arc segment.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.SweepDirectionKind enumeration value.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Arc.Size">
      <summary>
        <para>Specifies the size of the segment.</para>
      </summary>
      <value>A DevExpress.Data.Filtering.CriteriaOperator expression that uses the CreateSize method.</value>
    </member>
    <member name="T:DevExpress.Diagram.Core.Shapes.Line">
      <summary>
        <para>Represents a line.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Shapes.Line.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Shapes.Line"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Shapes.Parameter">
      <summary>
        <para>Represents a parameter that is used when defining shape templates to enable shape transformations.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Shapes.Parameter.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Shapes.Parameter"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Shapes.Parameter.CreateParameterDescription(System.Func{System.Windows.Size,System.Double[],System.Double[]})">
      <summary>
        <para></para>
      </summary>
      <param name="normalizeParameters"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Parameter.DefaultValue">
      <summary>
        <para>Specifies the initial value of the parameter.</para>
      </summary>
      <value>A numerical value that is the initial value of the parameter.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Parameter.Max">
      <summary>
        <para>Specifies the maximum parameter value that can be set by the end-user.</para>
      </summary>
      <value>A numerical value that is the maximum value of the parameter.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Parameter.Min">
      <summary>
        <para>Specifies the minimum parameter value that can be set by the end-user.</para>
      </summary>
      <value>A numerical value that is the minimum parameter value.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Parameter.Point">
      <summary>
        <para>Specifies the coordinates of the parameter&#39;s handle using the CreatePoint method.</para>
      </summary>
      <value>The coordinates of the parameter&#39;s handle.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Parameter.Value">
      <summary>
        <para>Specifies the current value of the parameter.</para>
      </summary>
      <value>A numerical value that is the current value of the parameter.</value>
    </member>
    <member name="T:DevExpress.Diagram.Core.Shapes.Start">
      <summary>
        <para>Represents the start point of the geometry and provides customization options.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Shapes.Start.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Shapes.Start"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Start.FillBrightness">
      <summary>
        <para>Specifies the brightness level of the color used to fill in the geometry.</para>
      </summary>
      <value>A numerical value that ranges from &#0045;1 through 1, where &#0045;1 represents black and 1 represents white.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Start.FillColor">
      <summary>
        <para>Specifies the color used to fill in the geometry.</para>
      </summary>
      <value>A Nullable&lt;<see cref="T:System.Drawing.Color"/>&gt; value that represents a color.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Start.IsNewShape">
      <summary>
        <para>Specifies whether to form a single shape with the previous geometry.</para>
      </summary>
      <value>true, to separate the new geometry from the previous one; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Start.IsSmoothJoin">
      <summary>
        <para>Specifies whether to round the corners of the geometry.</para>
      </summary>
      <value>true, to round the corners of the geometry; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Start.Kind">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Start.StrokeColor">
      <summary>
        <para>Gets or sets the color used to paint the outline of the geometry.</para>
      </summary>
      <value>A System.Drawing.Color value.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Start.StrokeDashArray">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Shapes.Start.StrokeThickness">
      <summary>
        <para>Gets or sets the thickness of the geometry&#39;s outline.</para>
      </summary>
      <value>A System.Double value that is the thickness of the geometry&#39;s outline.</value>
    </member>
    <member name="T:DevExpress.Diagram.Core.StretchMode">
      <summary>
        <para>Specifies whether to preserve the aspect ratio of the source image when resizing the image item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.StretchMode.Stretch">
      <summary>
        <para>Fit the image item while preserving the native aspect ratio of the source image.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.StretchMode.Uniform">
      <summary>
        <para>Stretch the source image to fill the diagram image item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.StretchMode.UniformToFill">
      <summary>
        <para>Fill the image item while preserving the native aspect ratio of the source image. The image is clipped if its aspect ratio differs from the image item.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.TextAlignmentKind">
      <summary>
        <para>Lists values that specify the horizontal alignment of the text within a diagram shape.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.TextAlignmentKind.Center">
      <summary>
        <para>Text is centered</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.TextAlignmentKind.Justify">
      <summary>
        <para>Text is justified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.TextAlignmentKind.Left">
      <summary>
        <para>Text is aligned to the left.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.TextAlignmentKind.Right">
      <summary>
        <para>Text is aligned to the right.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.ThemeRegistrator">
      <summary>
        <para>Provides methods that are used to manage the collection of diagram themes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.ThemeRegistrator.ConvertThemesToXml(System.Collections.IDictionary,System.IO.Stream)">
      <summary>
        <para>Converts themes to XML and writes to the specified stream.</para>
      </summary>
      <param name="resourceDictionary">An object implementing the System.Collections.IDictionary interface that is the collection of DevExpress.Diagram.Core.Themes.Theme objects.</param>
      <param name="stream">A System.IO.Stream object.</param>
    </member>
    <member name="M:DevExpress.Diagram.Core.ThemeRegistrator.GetTheme(System.String)">
      <summary>
        <para>Gets the diagram theme corresponding to the specified theme id.</para>
      </summary>
      <param name="themeId">A theme id.</param>
      <returns>A <see cref="T:DevExpress.Diagram.Core.DiagramTheme"/> object that represents the theme corresponding to the specified theme id.</returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.ThemeRegistrator.LoadThemesFromStream(System.IO.Stream)">
      <summary>
        <para>Loads themes from the specified stream.</para>
      </summary>
      <param name="stream">A System.IO.Stream object that contains themes.</param>
      <returns>A list of DevExpress.Diagram.Core.Themes.Theme objects retrieved from the stream..</returns>
    </member>
    <member name="M:DevExpress.Diagram.Core.ThemeRegistrator.RegisterTheme(DevExpress.Diagram.Core.DiagramTheme)">
      <summary>
        <para>Registers the specified theme.</para>
      </summary>
      <param name="theme">A diagram theme to register.</param>
    </member>
    <member name="M:DevExpress.Diagram.Core.ThemeRegistrator.RegisterThemes(System.Collections.Generic.IEnumerable{DevExpress.Diagram.Core.Themes.Theme},System.Func{System.String,System.String})">
      <summary>
        <para>Registers the specified themes.</para>
      </summary>
      <param name="themes">A collection of DevExpress.Diagram.Core.Themes.Theme objects.</param>
      <param name="getThemeName">A function that returns the theme name.</param>
    </member>
    <member name="M:DevExpress.Diagram.Core.ThemeRegistrator.RegisterThemes(System.Collections.IDictionary,System.Func{System.String,System.String})">
      <summary>
        <para>Registers the specified themes.</para>
      </summary>
      <param name="themesDictionary">An object implementing the System.Collections.IDictionary interface that is the collection of DevExpress.Diagram.Core.Themes.Theme objects.</param>
      <param name="getThemeName">A function that returns the theme name.</param>
    </member>
    <member name="M:DevExpress.Diagram.Core.ThemeRegistrator.RegisterThemes(System.IO.Stream,System.Func{System.String,System.String})">
      <summary>
        <para>Registers the specified themes.</para>
      </summary>
      <param name="stream">A System.IO.Stream object that contains themes to register.</param>
      <param name="getThemeName">A function that returns the theme name.</param>
    </member>
    <member name="P:DevExpress.Diagram.Core.ThemeRegistrator.Themes">
      <summary>
        <para>Provides access to the collection of currently registered themes.</para>
      </summary>
      <value>A collection of available themes.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.ThemeRegistrator.UnregisterTheme(DevExpress.Diagram.Core.DiagramTheme)">
      <summary>
        <para>Unregisters the specified theme.</para>
      </summary>
      <param name="theme">A diagram theme to unregister.</param>
    </member>
    <member name="N:DevExpress.Diagram.Core.Themes">
      <summary>
        <para>Contains classes that support the themes functionality in the Diagram Control.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.Themes.DiagramColorPalette">
      <summary>
        <para>Represents a color palette used to paint diagram items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Themes.DiagramColorPalette.#ctor(System.Drawing.Color[],System.Drawing.Color,System.Drawing.Color)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Themes.DiagramColorPalette"/> class.</para>
      </summary>
      <param name="accents">A collection of accent colors represented by System.Drawing.Color objects.</param>
      <param name="light">A System.Drawing.Color object representing the Light color.</param>
      <param name="dark">A System.Drawing.Color object representing the Dark color.</param>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramColorPalette.Accents">
      <summary>
        <para>Provides access to a collection of additional colors that can be applied to shapes.</para>
      </summary>
      <value>A read-only collection of accent colors represented by System.Drawing.Color objects.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramColorPalette.Dark">
      <summary>
        <para>Gets the palette&#39;s Dark color.</para>
      </summary>
      <value>A System.Drawing.Color object.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.Themes.DiagramColorPalette.GetColorByColorId(DevExpress.Diagram.Core.DiagramThemeColorId)">
      <summary>
        <para>Gets the color corresponding to the specified ID.</para>
      </summary>
      <param name="colorId">A DevExpress.Diagram.Core.DiagramThemeColorId enumeration value.</param>
      <returns>A System.Drawing.Color object representing the color.</returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramColorPalette.Light">
      <summary>
        <para>Gets the palette&#39;s Light color.</para>
      </summary>
      <value>A System.Drawing.Color object.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramColorPalette.ThemeColors">
      <summary>
        <para>Provides access to a collection of colors that can be applied to shapes.</para>
      </summary>
      <value>A read-only collection of theme colors represented by System.Drawing.Color objects.</value>
    </member>
    <member name="T:DevExpress.Diagram.Core.Themes.DiagramEffect">
      <summary>
        <para>Provides access to effects that you can use to customize the appearance of diagram items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Themes.DiagramEffect.#ctor(DevExpress.Diagram.Core.Themes.DiagramItemLineSettings,System.Func{DevExpress.Diagram.Core.Themes.EffectContext,DevExpress.Diagram.Core.Themes.DiagramItemBrush},DevExpress.Diagram.Core.DiagramFontEffects)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Themes.DiagramEffect"/> class with specified settings.</para>
      </summary>
      <param name="lineSettings">An object that provides access to the effects applied to the item&#39;s outline.</param>
      <param name="getItemBrush">A function that specifies the brush used to paint the item.</param>
      <param name="fontEffects">An object that provides access to the font effects.</param>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramEffect.FontEffects">
      <summary>
        <para>Provides access to the font effects.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.DiagramFontEffects object that provides access to the font effects.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.Themes.DiagramEffect.GetItemBrush(DevExpress.Diagram.Core.Themes.EffectContext)">
      <summary>
        <para>Specifies the brush used to paint the diagram item.</para>
      </summary>
      <param name="context">A palette&#39;s color identifier.</param>
      <returns>A DevExpress.Diagram.Core.Themes.DiagramItemBrush that paints the diagram item.</returns>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramEffect.LineSettings">
      <summary>
        <para>Provides access to the effects applied to the item&#39;s outline.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.Themes.DiagramItemLineSettings object that provides access to the outline options.</value>
    </member>
    <member name="T:DevExpress.Diagram.Core.Themes.DiagramEffectCollection">
      <summary>
        <para>Represents a collection of styles that can be applied to diagram items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Themes.DiagramEffectCollection.#ctor(DevExpress.Diagram.Core.Themes.DiagramEffect[],DevExpress.Diagram.Core.Themes.DiagramEffect[],DevExpress.Diagram.Core.Themes.DiagramEffect[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Themes.DiagramEffectCollection"/> class.</para>
      </summary>
      <param name="variantEffects">A collection of Variant effects.</param>
      <param name="themeEffects">A collection of Theme effects.</param>
      <param name="connectorEffects">A collection of connector effects.</param>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramEffectCollection.ConnectorEffects">
      <summary>
        <para>Provides access to the collection of effects that can be applied to connectors.</para>
      </summary>
      <value>A read-only collection of effects.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramEffectCollection.ThemeEffects">
      <summary>
        <para>Provides access to the collection of effects that can be applied to shapes and are displayed in the &quot;Theme Styles&quot; ribbon group.</para>
      </summary>
      <value>A read-only collection of effects.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramEffectCollection.VariantEffects">
      <summary>
        <para>Provides access to the collection of effects that can be applied to shapes and are displayed in the &quot;Variant Styles&quot; ribbon group.</para>
      </summary>
      <value>A read-only collection of effects.</value>
    </member>
    <member name="T:DevExpress.Diagram.Core.Themes.DiagramItemBrush">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Themes.DiagramItemBrush.#ctor(System.Drawing.Color,System.Drawing.Color,System.Drawing.Color)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Themes.DiagramItemBrush"/> class with specified settings.</para>
      </summary>
      <param name="foreground"></param>
      <param name="background"></param>
      <param name="stroke"></param>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramItemBrush.Background">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramItemBrush.Foreground">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramItemBrush.Stroke">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Diagram.Core.Themes.DiagramItemLineSettings">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Themes.DiagramItemLineSettings.#ctor(System.Double,DevExpress.Diagram.Core.DiagramDoubleCollection)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Themes.DiagramItemLineSettings"/> class with specified settings.</para>
      </summary>
      <param name="strokeThickness"></param>
      <param name="strokeDashArray"></param>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramItemLineSettings.StrokeDashArray">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramItemLineSettings.StrokeThickness">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Diagram.Core.Themes.DiagramItemStyle">
      <summary>
        <para>Contains the style settings for diagram items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Themes.DiagramItemStyle.#ctor(DevExpress.Diagram.Core.Themes.DiagramItemBrush,DevExpress.Diagram.Core.DiagramFontSettings,DevExpress.Diagram.Core.Themes.DiagramItemLineSettings)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Themes.DiagramItemStyle"/> class.</para>
      </summary>
      <param name="shapeBrush">A DevExpress.Diagram.Core.Themes.DiagramItemBrush object that is the brush used to paint the diagram item.</param>
      <param name="fontSettings">A DevExpress.Diagram.Core.DiagramFontSettings object that specifies the font settings applied to the text displayed within the diagram item.</param>
      <param name="lineSettings">A DevExpress.Diagram.Core.Themes.DiagramItemLineSettings object that specifies the line settings.</param>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramItemStyle.Brush">
      <summary>
        <para>Returns the brush used to paint the diagram item.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.Themes.DiagramItemBrush object.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramItemStyle.FontSettings">
      <summary>
        <para>Returns the font settings applied to the text displayed within the diagram item.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.DiagramFontSettings object that specifies the font settings.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.DiagramItemStyle.LineSettings">
      <summary>
        <para>Returns the line settings.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.Themes.DiagramItemLineSettings object that specifies the line settings.</value>
    </member>
    <member name="M:DevExpress.Diagram.Core.Themes.DiagramItemStyle.WithBrush(DevExpress.Diagram.Core.Themes.DiagramItemBrush)">
      <summary>
        <para>Returns the style whose Brush property is set to the specified brush.</para>
      </summary>
      <param name="brush">A DevExpress.Diagram.Core.Themes.DiagramItemBrush object.</param>
      <returns>A <see cref="T:DevExpress.Diagram.Core.Themes.DiagramItemStyle"/> class instance that uses the specified brush.</returns>
    </member>
    <member name="T:DevExpress.Diagram.Core.Themes.EffectContext">
      <summary>
        <para>Provides palette data for the  method.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Diagram.Core.Themes.EffectContext.#ctor(DevExpress.Diagram.Core.Themes.DiagramColorPalette,System.Drawing.Color,System.Drawing.Color)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Diagram.Core.Themes.EffectContext"/> class with specified settings.</para>
      </summary>
      <param name="palette">A color palette used to paint the diagram item.</param>
      <param name="accent">The base accent color.</param>
      <param name="light">The Light color.</param>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.EffectContext.Accent">
      <summary>
        <para>The base accent color.</para>
      </summary>
      <value>A System.Drawing.Color that represents the base accent color.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.EffectContext.Accent1">
      <summary>
        <para>An additional accent color.</para>
      </summary>
      <value>A System.Drawing.Color that represents an additional accent color.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.EffectContext.Accent2">
      <summary>
        <para>An additional accent color.</para>
      </summary>
      <value>A System.Drawing.Color that represents an additional accent color.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.EffectContext.Accent3">
      <summary>
        <para>An additional accent color.</para>
      </summary>
      <value>A System.Drawing.Color that represents an additional accent color.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.EffectContext.Accent4">
      <summary>
        <para>An additional accent color.</para>
      </summary>
      <value>A System.Drawing.Color that represents an additional accent color.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.EffectContext.Accent5">
      <summary>
        <para>An additional accent color.</para>
      </summary>
      <value>A System.Drawing.Color that represents an additional accent color.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.EffectContext.Accent6">
      <summary>
        <para>An additional accent color.</para>
      </summary>
      <value>A System.Drawing.Color that represents an additional accent color.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.EffectContext.Dark">
      <summary>
        <para>The Dark color.</para>
      </summary>
      <value>A System.Drawing.Color that represents the Dark color.</value>
    </member>
    <member name="P:DevExpress.Diagram.Core.Themes.EffectContext.Light">
      <summary>
        <para>The Light color.</para>
      </summary>
      <value>A System.Drawing.Color that represents the Light color.</value>
    </member>
    <member name="T:DevExpress.Diagram.Core.ToolboxVisibility">
      <summary>
        <para>Lists the values used to specify the Shapes panel display mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ToolboxVisibility.Closed">
      <summary>
        <para>The Shapes panel is hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ToolboxVisibility.Compact">
      <summary>
        <para>The Shapes panel is in the compact mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.ToolboxVisibility.Full">
      <summary>
        <para>The Shapes panel is fully expanded.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Diagram.Core.VerticalAlignmentKind">
      <summary>
        <para>Lists values that specify the vertical alignment of the text within a diagram shape.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.VerticalAlignmentKind.Bottom">
      <summary>
        <para>The text is aligned to the bottom of the shape.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.VerticalAlignmentKind.Center">
      <summary>
        <para>The text is aligned to the center of the shape.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.VerticalAlignmentKind.Stretch">
      <summary>
        <para>The text is stretched to fill the vertical space within the shape.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Diagram.Core.VerticalAlignmentKind.Top">
      <summary>
        <para>The text is aligned to the top of the shape.</para>
      </summary>
    </member>
  </members>
</doc>