<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.XtraTreeMap.v20.2</name>
  </assembly>
  <members>
    <member name="N:DevExpress.XtraTreeMap">
      <summary>
        <para>Contains all required classes for the functioning of the <see cref="T:DevExpress.XtraTreeMap.TreeMapControl"/> and <see cref="T:DevExpress.XtraTreeMap.SunburstControl"/>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.BorderBase">
      <summary>
        <para>Represents the base class for all border objects.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.BorderBase.ActualColor">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.BorderBase.ActualThickness">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraTreeMap.BorderBase.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.BorderBase"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.BorderBase"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.BorderBase.Color">
      <summary>
        <para>Gets or sets the border color.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> structure which specifies the border color.</value>
    </member>
    <member name="M:DevExpress.XtraTreeMap.BorderBase.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current <see cref="T:DevExpress.XtraTreeMap.BorderBase"/> instance.</para>
      </summary>
      <param name="obj">The object to be compared with the current object.</param>
      <returns>true if the specified object is equal to the current <see cref="T:DevExpress.XtraTreeMap.BorderBase"/> instance; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.BorderBase.GetHashCode">
      <summary>
        <para>Gets the hash code (a number) that corresponds to the value of the current <see cref="T:DevExpress.XtraTreeMap.BorderBase"/> object.</para>
      </summary>
      <returns>An integer value representing the hash code for the current object.</returns>
    </member>
    <member name="P:DevExpress.XtraTreeMap.BorderBase.IsColorDefault">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.BorderBase.Thickness">
      <summary>
        <para>Gets or sets the border&#39;s thickness.</para>
      </summary>
      <value>An integer value which specifies the border&#39;s thickness, in pixels.</value>
    </member>
    <member name="M:DevExpress.XtraTreeMap.BorderBase.ToString">
      <summary>
        <para>Returns the textual representation of the axis.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value which is the textual representation of the axis.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.BorderBase.UpdateSkinColor(System.Drawing.Color)">
      <summary>
        <para></para>
      </summary>
      <param name="color"></param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.BorderBase.Visible">
      <summary>
        <para>Specifies whether the border is visible.</para>
      </summary>
      <value>true, if the border is visible; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.ColorizerChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraTreeMap.ITreeMapColorizer.ColorizerChanged"/>, <see cref="E:DevExpress.XtraTreeMap.ISunburstColorizer.ColorizerChanged"/>, <see cref="E:DevExpress.XtraTreeMap.TreeMapColorizerBase.ColorizerChanged"/> and <see cref="E:DevExpress.XtraTreeMap.SunburstColorizerBase.ColorizerChanged"/> events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.ColorizerChangedEventArgs.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.ColorizerChangedEventArgs"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.ColorizerChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeMap.ITreeMapColorizer.ColorizerChanged"/> and <see cref="E:DevExpress.XtraTreeMap.ISunburstColorizer.ColorizerChanged"/> events.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies an object that implements <see cref="T:DevExpress.XtraTreeMap.ITreeMapColorizer"/> or <see cref="T:DevExpress.XtraTreeMap.ISunburstColorizer"/> and raised the event.</param>
      <param name="e">The event data.</param>
    </member>
    <member name="T:DevExpress.XtraTreeMap.DataAdapterChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraTreeMap.ITreeMapDataAdapter.DataAdapterChanged"/>, <see cref="E:DevExpress.XtraTreeMap.ISunburstDataAdapter.DataAdapterChanged"/>, <see cref="E:DevExpress.XtraTreeMap.TreeMapDataAdapterBase.DataAdapterChanged"/> and <see cref="E:DevExpress.XtraTreeMap.SunburstDataAdapterBase.DataAdapterChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.DataAdapterChangedEventArgs.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.DataAdapterChangedEventArgs"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.DataAdapterChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeMap.ITreeMapDataAdapter.DataAdapterChanged"/> and <see cref="E:DevExpress.XtraTreeMap.ISunburstDataAdapter.DataAdapterChanged"/> events.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies an object that implements <see cref="T:DevExpress.XtraTreeMap.ITreeMapDataAdapter"/> or <see cref="T:DevExpress.XtraTreeMap.ISunburstDataAdapter"/> and raised the event.</param>
      <param name="e">The event data.</param>
    </member>
    <member name="T:DevExpress.XtraTreeMap.ElementSelectionMode">
      <summary>
        <para>Lists selection modes available for the end-user.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.ElementSelectionMode.Extended">
      <summary>
        <para>Extended single selection mode, allowing you to select several items with the SHIFT key held down.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.ElementSelectionMode.Multiple">
      <summary>
        <para>Several items can be selected at the same time.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.ElementSelectionMode.None">
      <summary>
        <para>Selection is disabled.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.ElementSelectionMode.Single">
      <summary>
        <para>One item can be selected at a time.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.FillStyleBase">
      <summary>
        <para>The base class for treemap fill styles.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraTreeMap.FillStyleBase.PropertyChanged">
      <summary>
        <para>Occurs after a property value has been changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.FillStyleBase.ToString">
      <summary>
        <para>Gets the textual representation of the current <see cref="T:DevExpress.Utils.SuperToolTip"/>.</para>
      </summary>
      <returns>A string which specifies the tooltip&#39;s textual representation.</returns>
    </member>
    <member name="T:DevExpress.XtraTreeMap.GradientColorizerMode">
      <summary>
        <para>Lists modes that define how to distribute a gradient across sunburst items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.GradientColorizerMode.ByGroupLevel">
      <summary>
        <para>Items have an equal color if they belong to the same top-level item. A color&#39;s transparency increases from the top-level items to low-level items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.GradientColorizerMode.ByItemIndex">
      <summary>
        <para>Items in the same group have an equal base color. A color&#39;s transparency increases with increasing an item index in this group.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.GroupDataMemberCollection">
      <summary>
        <para>The collection of names of data members that are used to group values.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.GroupDataMemberCollection.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.GroupDataMemberCollection"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.GroupInfo">
      <summary>
        <para>The treemap / sunburst group information storage.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.GroupInfo.ChildGroups">
      <summary>
        <para>Returns the collection of child groups.</para>
      </summary>
      <value>The collection of child groups.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.GroupInfo.GroupValue">
      <summary>
        <para>Returns the value by which <see cref="P:DevExpress.XtraTreeMap.GroupInfo.SourceItems"/> and <see cref="P:DevExpress.XtraTreeMap.GroupInfo.ChildGroups"/> are grouped.</para>
      </summary>
      <value>The value by which <see cref="P:DevExpress.XtraTreeMap.GroupInfo.SourceItems"/> and <see cref="P:DevExpress.XtraTreeMap.GroupInfo.ChildGroups"/> are grouped.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.GroupInfo.SourceItems">
      <summary>
        <para>Returns a list of items from a data source that the group contains directly.</para>
      </summary>
      <value>The list of items from a data source.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.HatchFillStyle">
      <summary>
        <para>Specifies the hatch fill style.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.HatchFillStyle.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.HatchFillStyle"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.HatchFillStyle.Color2">
      <summary>
        <para>Gets or sets the second color used to hatch the treemap item background.</para>
      </summary>
      <value>A color used to hatch treemap items.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.HatchFillStyle.HatchStyle">
      <summary>
        <para>Gets or sets the pattern used to hatch treemap items.</para>
      </summary>
      <value>A pattern that applies to items.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.HierarchicalDataMapping">
      <summary>
        <para>Information about data object mapping to a hierarchical item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.HierarchicalDataMapping.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.HierarchicalDataMapping"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.HierarchicalDataMapping.ChildrenDataMember">
      <summary>
        <para>Gets or sets the name of the data field whose values are used to specify child items of current level tree map or sunburst items.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.HierarchicalDataMapping.LabelDataMember">
      <summary>
        <para>Gets or sets the name of the data field whose values are used to specify the tree map or sunburst items&#39; labels.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="M:DevExpress.XtraTreeMap.HierarchicalDataMapping.ToString">
      <summary>
        <para>Returns the textual representation of the <see cref="T:DevExpress.XtraTreeMap.HierarchicalDataMapping"/>.</para>
      </summary>
      <returns>The <see cref="T:DevExpress.XtraTreeMap.HierarchicalDataMapping"/> textual representation.</returns>
    </member>
    <member name="P:DevExpress.XtraTreeMap.HierarchicalDataMapping.Type">
      <summary>
        <para>Gets or sets the data object type on the current nested level.</para>
      </summary>
      <value>A <see cref="T:System.Type"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.HierarchicalDataMapping.ValueDataMember">
      <summary>
        <para>Gets or sets the name of the data member whose data are used to specify tree map or sunburst items&#39; values.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.HierarchicalItemCollectionBase`1">
      <summary>
        <para>The base class for a hierarchical chart&#39;s item collections.</para>
      </summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:DevExpress.XtraTreeMap.HierarchicalItemCollectionBase`1.AddRange(DevExpress.TreeMap.IHierarchicalItem[])">
      <summary>
        <para>Adds a collection of items to the end of the collection.</para>
      </summary>
      <param name="items">An array of items to append to the collection.</param>
    </member>
    <member name="T:DevExpress.XtraTreeMap.HierarchicalItemStyle">
      <summary>
        <para>The base class for treemap and sunburst item appearance settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.HierarchicalItemStyle.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.HierarchicalItemStyle"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.HierarchicalItemStyle.#ctor(System.Drawing.Color,System.Drawing.Color,System.Drawing.Font,System.Drawing.Color)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.HierarchicalItemStyle"/> class with the specified settings.</para>
      </summary>
      <param name="fill">An item&#39;s background color.</param>
      <param name="textColor">An item&#39;s text color.</param>
      <param name="font">An item&#39;s text font.</param>
      <param name="borderColor">An item&#39;s border color.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.HierarchicalItemStyle.BorderColor">
      <summary>
        <para>Gets or sets a sunburst item&#39;s border color.</para>
      </summary>
      <value>The <see cref="T:System.Drawing.Color"/> value that specifies an item border color.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.HierarchicalItemStyle.Fill">
      <summary>
        <para>Gets or sets the color that is used to fill a treemap / sunburst item.</para>
      </summary>
      <value>The <see cref="T:System.Drawing.Color"/> value that specifies a treemap / sunburst item&#39;s fill color.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.HierarchicalItemStyle.Font">
      <summary>
        <para>Gets or sets a treemap / sunburst item text font.</para>
      </summary>
      <value>The value that specifies a text font.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.HierarchicalItemStyle.TextColor">
      <summary>
        <para>Gets or sets a treemap / sunburst item text color.</para>
      </summary>
      <value>The text color.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.IColorizerValueProvider">
      <summary>
        <para>The interface that should be implemented by classes used to provide colorizers with values. Colorizer uses these values to determine item colors.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.IColorizerValueProvider.GetValue(DevExpress.TreeMap.IHierarchicalItem,System.Int32)">
      <summary>
        <para>Returns a value based on an item and its index.</para>
      </summary>
      <param name="item">A treemap/sunburst item.</param>
      <param name="itemIndex">An item index.</param>
      <returns>A value that the colorizer uses to determine an item color.</returns>
    </member>
    <member name="T:DevExpress.XtraTreeMap.InsideRectangularBorder">
      <summary>
        <para>Contains the inside border settings of rectangular elements within a TreeMap control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.InsideRectangularBorder.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current object.</para>
      </summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>true if the specified object is equal to the current object; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.InsideRectangularBorder.GetHashCode">
      <summary>
        <para>Gets the hash code (a number) that corresponds to the value of the current <see cref="T:DevExpress.XtraTreeMap.InsideRectangularBorder"/> object.</para>
      </summary>
      <returns>An integer value representing the hash code for the current object.</returns>
    </member>
    <member name="T:DevExpress.XtraTreeMap.ISunburstColorizer">
      <summary>
        <para>The interface that a colorizer class should implement.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraTreeMap.ISunburstColorizer.ColorizerChanged">
      <summary>
        <para>Occurs after a colorizer&#39;s property changes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.ISunburstColorizer.GetItemColor(DevExpress.TreeMap.ISunburstItem,DevExpress.XtraTreeMap.SunburstItemGroupInfo)">
      <summary>
        <para>Returns the specified item color in the specified group.</para>
      </summary>
      <param name="item">The sunburst item.</param>
      <param name="group">The sunburst item group.</param>
      <returns>The sunburst item color.</returns>
    </member>
    <member name="T:DevExpress.XtraTreeMap.ISunburstDataAdapter">
      <summary>
        <para>The interface that a data provider class should implement.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraTreeMap.ISunburstDataAdapter.DataAdapterChanged">
      <summary>
        <para>Occurs after a data adapter&#39;s property changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.ISunburstDataAdapter.Items">
      <summary>
        <para>Returns the collection of sunburst items the adapter provides.</para>
      </summary>
      <value>An enumerable that contains DevExpress.TreeMap.ISunburstItem objects.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.ITreeMapColorizer">
      <summary>
        <para>The interface that should be implemented by a colorizer class.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraTreeMap.ITreeMapColorizer.ColorizerChanged">
      <summary>
        <para>Occurs after a colorizer&#39;s property changes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.ITreeMapColorizer.GetItemColor(DevExpress.TreeMap.ITreeMapItem,DevExpress.XtraTreeMap.TreeMapItemGroupInfo)">
      <summary>
        <para>Returns the color for the specified item with the specified tree location.</para>
      </summary>
      <param name="item">An object of a class implementing the <see cref="T:DevExpress.TreeMap.ITreeMapItem"/> interface. It is the object for which the color is obtained.</param>
      <param name="group">A <see cref="T:DevExpress.Xpf.TreeMap.TreeMapItemGroupInfo"/> value specifying information about the item position in a group.</param>
      <returns>A <see cref="T:System.Drawing.Color"/> value that is the color of the specified item.</returns>
    </member>
    <member name="T:DevExpress.XtraTreeMap.ITreeMapDataAdapter">
      <summary>
        <para>The interface that should be implemented by a data provider class.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraTreeMap.ITreeMapDataAdapter.DataAdapterChanged">
      <summary>
        <para>Occurs after a data adapter&#39;s property changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.ITreeMapDataAdapter.Items">
      <summary>
        <para>Returns the collection of tree map items provided by this adapter.</para>
      </summary>
      <value>An enumerable, containing <see cref="T:DevExpress.TreeMap.ITreeMapItem"/> objects.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.ITreeMapLayoutAlgorithm">
      <summary>
        <para>The interface that should be implemented by a layout algorithm class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.ITreeMapLayoutAlgorithm.Calculate(System.Collections.Generic.IEnumerable{DevExpress.TreeMap.ITreeMapItemLayout},System.Double,System.Double,System.Int32)">
      <summary>
        <para>Arranges the specified list of tree map items.</para>
      </summary>
      <param name="items">A list of objects whose classes implement the <see cref="T:DevExpress.Xpf.TreeMap.ITreeMapLayoutItem"/> interface and that should be arranged.</param>
      <param name="width">The width of the parent layout in which items should be arranged.</param>
      <param name="height">The height of the parent layout in which items should be arranged.</param>
      <param name="groupLevel">The nested level of the parent layout.</param>
    </member>
    <member name="E:DevExpress.XtraTreeMap.ITreeMapLayoutAlgorithm.LayoutAlgorithmChanged">
      <summary>
        <para>Occurs after a layout algorithm&#39;s property changes.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.LayoutAlgorithmChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraTreeMap.TreeMapLayoutAlgorithmBase.LayoutAlgorithmChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.LayoutAlgorithmChangedEventArgs.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.LayoutAlgorithmChangedEventArgs"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.LayoutAlgorithmChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeMap.ITreeMapLayoutAlgorithm.LayoutAlgorithmChanged"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies an object that implements <see cref="T:DevExpress.XtraTreeMap.ITreeMapLayoutAlgorithm"/> and raised the event.</param>
      <param name="e">The event data.</param>
    </member>
    <member name="T:DevExpress.XtraTreeMap.Palette">
      <summary>
        <para>Represents a palette (collection of colors) used to draw a TreeMap.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.Palette.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.Palette"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.Palette.#ctor(System.Drawing.Color[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.Palette"/> class with the specified array of palette colors.</para>
      </summary>
      <param name="colors">An array of <see cref="T:System.Drawing.Color"/> objects specifying palette colors. An individual color can be accessed using the <see cref="P:DevExpress.XtraTreeMap.Palette.Item(System.Int32)"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraTreeMap.Palette.Add(System.Drawing.Color)">
      <summary>
        <para>Adds a color to the palette.</para>
      </summary>
      <param name="color">A <see cref="T:System.Drawing.Color"/> value.</param>
    </member>
    <member name="M:DevExpress.XtraTreeMap.Palette.AddRange(System.Drawing.Color[])">
      <summary>
        <para>Adds several colors to the palette.</para>
      </summary>
      <param name="colors">An array of <see cref="T:System.Drawing.Color"/> values.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.BlueGreenPalette">
      <summary>
        <para>Gets the BlueGreenPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the BlueGreenPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.BlueIIPalette">
      <summary>
        <para>Gets the BlueIIPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the BlueIIPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.BluePalette">
      <summary>
        <para>Gets the BluePalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the BluePalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.BlueWarmPalette">
      <summary>
        <para>Gets the BlueWarmPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the BlueWarmPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.ChameleonPalette">
      <summary>
        <para>Gets the ChameleonPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the ChameleonPalette palette.</value>
    </member>
    <member name="M:DevExpress.XtraTreeMap.Palette.CreatePalette(System.Drawing.Color[])">
      <summary>
        <para>Creates a palette from the specified colors.</para>
      </summary>
      <param name="colors">An array of <see cref="T:System.Drawing.Color"/> values initializing the palette.</param>
      <returns>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the newly created palette.</returns>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.DXTreeMapPalette">
      <summary>
        <para>Gets the DXTreeMapPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the DXTreeMapPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.GreenPalette">
      <summary>
        <para>Gets the GreenPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the GreenPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.GreenYellowPalette">
      <summary>
        <para>Gets the GreenYellowPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the GreenYellowPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.InAFogPalette">
      <summary>
        <para>Gets the InAFogPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the InAFogPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.IsFromSkin">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.IsPredefined">
      <summary>
        <para>Returns the value indicating whether or not the palette is predefined.</para>
      </summary>
      <value>true if a palette is predefined; otherwise false</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.Item(System.Int32)">
      <summary>
        <para>Provides indexed access to individual items in the palette.</para>
      </summary>
      <param name="index">A <see cref="T:System.Int32"/> value specifying the zero-based index of the item to be accessed.</param>
      <value>A <see cref="T:System.Drawing.Color"/> object which represents the color in the chart palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.MarqueePalette">
      <summary>
        <para>Gets the MarqueePalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the MarqueePalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.NatureColorsPalette">
      <summary>
        <para>Gets the NatureColorsPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the NatureColorsPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.NorthernLightsPalette">
      <summary>
        <para>Gets the NorthernLightsPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the NorthernLightsPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.Office2013Palette">
      <summary>
        <para>Gets the Office2013Palette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the Office2013Palette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.Office2016Palette">
      <summary>
        <para>Gets the Office2016Palette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the Office2016Palette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.Office2019Palette">
      <summary>
        <para>Returns the Office2019Palette palette.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the Office2019Palette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.OfficePalette">
      <summary>
        <para>Gets the OfficePalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the OfficePalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.OrangePalette">
      <summary>
        <para>Gets the OrangePalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the OrangePalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.OrangeRedPalette">
      <summary>
        <para>Gets the OrangeRedPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the OrangeRedPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.PastelKitPalette">
      <summary>
        <para>Gets the PastelKitPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the PastelKitPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.RedOrangePalette">
      <summary>
        <para>Gets the RedOrangePalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the RedOrangePalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.RedPalette">
      <summary>
        <para>Gets the RedPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the RedPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.RedVioletPalette">
      <summary>
        <para>Gets the RedVioletPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the RedVioletPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.SlipstreamPalette">
      <summary>
        <para>Gets the SlipstreamPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the SlipstreamPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.TerracottaPiePalette">
      <summary>
        <para>Gets the TerracottaPiePalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the TerracottaPiePalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.TheTreesPalette">
      <summary>
        <para>Gets the TheTreesPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the TheTreesPalette palette.</value>
    </member>
    <member name="M:DevExpress.XtraTreeMap.Palette.ToString">
      <summary>
        <para>Returns the textual representation of the axis.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value which is the textual representation of the axis.</returns>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.VioletIIPalette">
      <summary>
        <para>Gets the VioletIIPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the VioletIIPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.VioletPalette">
      <summary>
        <para>Gets the VioletPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the VioletPalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.YellowOrangePalette">
      <summary>
        <para>Gets the YellowOrangePalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the YellowOrangePalette palette.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Palette.YellowPalette">
      <summary>
        <para>Gets the YellowPalette palette of colors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object that is the YellowPalette palette.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.Palettes">
      <summary>
        <para>A class that allows you to access the list of predefined treemap palettes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.Palettes.GetNames">
      <summary>
        <para>Returns a list of built-in palettes&#39; names.</para>
      </summary>
      <returns>A list of palette names.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.Palettes.GetPalette(System.String)">
      <summary>
        <para>Returns a palette by its name.</para>
      </summary>
      <param name="name">The palette name.</param>
      <returns>The palette with the name you pass. Returns null (Nothing in Visual Basic) if a palette with the given name is not found.</returns>
    </member>
    <member name="N:DevExpress.XtraTreeMap.Printing">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.Printing.PrintOptions">
      <summary>
        <para>The hierarchical chart&#39;s printing options.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.Printing.PrintOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.Printing.PrintOptions"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.Printing.PrintOptions.SizeMode">
      <summary>
        <para>Gets or sets the size mode that the Control uses by default to be printed.</para>
      </summary>
      <value>The value that specifies the size mode that the Control uses by default to be printed.</value>
    </member>
    <member name="M:DevExpress.XtraTreeMap.Printing.PrintOptions.ToString">
      <summary>
        <para>Returns the print options object&#39;s textual representation.</para>
      </summary>
      <returns>The print options object&#39;s textual representation.</returns>
    </member>
    <member name="T:DevExpress.XtraTreeMap.PrintSizeMode">
      <summary>
        <para>Lists the values specifying size modes used when a map is printed or exported.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.PrintSizeMode.Normal">
      <summary>
        <para>A hierarchical chart is printed in the identical size it appears on the form.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.PrintSizeMode.Stretch">
      <summary>
        <para>A hierarchical chart is stretched or shrunk to fit the page on which it is printed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.PrintSizeMode.Zoom">
      <summary>
        <para>A hierarchical chart is resized proportionally (without clipping), to best fit the page on which it is printed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.RectangularBorder">
      <summary>
        <para>Contains border settings of rectangular elements within a TreeMap control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.RectangularBorder.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current object.</para>
      </summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>true if the specified object is equal to the current object; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.RectangularBorder.GetHashCode">
      <summary>
        <para>Gets the hash code (a number) that corresponds to the value of the current <see cref="T:DevExpress.XtraTreeMap.RectangularBorder"/> object.</para>
      </summary>
      <returns>An integer value representing the hash code for the current object.</returns>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SelectedGroupCollection">
      <summary>
        <para>The selected group path collection.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectedGroupCollection.Add(System.Collections.Generic.IEnumerable{System.Object})">
      <summary>
        <para>Adds a group that is under the specified path (group values from the root to the required group).</para>
      </summary>
      <param name="groupPath">An object implementing the <see cref="T:System.Collections.Generic.IEnumerable`1"/> interface that is a path to a group (group values from the root to the group).</param>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectedGroupCollection.Add(System.Object[])">
      <summary>
        <para>Adds a group that is under the specified path (group values from the root to the required group).</para>
      </summary>
      <param name="groupPath">A path to a group (group values from the root to the group).</param>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectedGroupCollection.Clear">
      <summary>
        <para>Removes all items from the collection.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectedGroupCollection.Contains(System.Collections.Generic.IEnumerable{System.Object})">
      <summary>
        <para>Determines whether the collection contains a group under the specified path (group values from the root to the group).</para>
      </summary>
      <param name="groupPath">The path (group values from the root to the group) to the group.</param>
      <returns>true, if the collection contains a group otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectedGroupCollection.Contains(System.Object[])">
      <summary>
        <para>Determines whether the collection contains a group under the specified path (group values from the root to the group).</para>
      </summary>
      <param name="groupPath">The path (group values from the root to the group) to the group.</param>
      <returns>true, if the collection contains a group otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectedGroupCollection.CopyTo(System.Collections.Generic.IEnumerable{System.Object}[],System.Int32)">
      <summary>
        <para>Copies all elements of the current collection to the specified one-dimensional array.</para>
      </summary>
      <param name="array">The one-dimensional array that is the destination of elements copied from the current collection.</param>
      <param name="arrayIndex">The index in the collection at which copying begins.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SelectedGroupCollection.Count">
      <summary>
        <para>Gets the number of selected groups which a collection stores.</para>
      </summary>
      <value>A number of items in the collection.</value>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectedGroupCollection.IndexOf(System.Collections.Generic.IEnumerable{System.Object})">
      <summary>
        <para>Returns an index under the which the collection contains a group under the specified path (group values from the root to the group).</para>
      </summary>
      <param name="groupPath">The path (group values from the root to the group) to the group.</param>
      <returns>The zero-based index of the first occurrence of the group; otherwise, -1.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectedGroupCollection.IndexOf(System.Object[])">
      <summary>
        <para>Returns an index under the which the collection contains a group under the specified path (group values from the root to the group).</para>
      </summary>
      <param name="groupPath">The path (group values from the root to the group) to the group.</param>
      <returns>The zero-based index of the first occurrence of the group; otherwise, -1.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectedGroupCollection.Insert(System.Int32,System.Collections.Generic.IEnumerable{System.Object})">
      <summary>
        <para>Inserts a group under the specified path (group values from the root to the group) to the collection at the specified index.</para>
      </summary>
      <param name="index">The zero-based index at which a group should be inserted.</param>
      <param name="groupPath">The group path (group values from the root to the group).</param>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectedGroupCollection.Insert(System.Int32,System.Object[])">
      <summary>
        <para>Inserts a group under the specified path (group values from the root to the group) to the collection at the specified index.</para>
      </summary>
      <param name="index">The zero-based index at which a group should be inserted.</param>
      <param name="groupPath">The group path (group values from the root to the group).</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SelectedGroupCollection.IsReadOnly">
      <summary>
        <para>Gets a value indicating whether the collection is read-only.</para>
      </summary>
      <value>Always false.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SelectedGroupCollection.Item(System.Int32)">
      <summary>
        <para>Gets or sets a series point by its zero-based index.</para>
      </summary>
      <param name="index">The zero-based index.</param>
      <value>The path (group values from the root to the requested group) to a group.</value>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectedGroupCollection.Remove(System.Collections.Generic.IEnumerable{System.Object})">
      <summary>
        <para>Removes a group under the specified path (group values from the root to the group) from the selected groups.</para>
      </summary>
      <param name="groupPath">An object implementing the <see cref="T:System.Collections.Generic.IEnumerable`1"/> interface that is a path to a group (group values from the root to the group).</param>
      <returns>true if the item is successfully removed; otherwise, false. This method also returns false if an item was not found in the collection.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectedGroupCollection.Remove(System.Object[])">
      <summary>
        <para>Removes a group under the specified path (group values from the root to the group) from the selected groups.</para>
      </summary>
      <param name="groupPath">A path to a group (group values from the root to the group).</param>
      <returns>true if the item is successfully removed; otherwise, false. This method also returns false if an item was not found in the collection.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectedGroupCollection.RemoveAt(System.Int32)">
      <summary>
        <para>Removes a group under the specified index from a collection of selected groups.</para>
      </summary>
      <param name="index">The index of a group to remove.</param>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SelectionChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraTreeMap.HierarchicalChartControlBase.SelectionChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SelectionChangedEventArgs.#ctor(System.Collections.IList,DevExpress.XtraTreeMap.SelectedGroupCollection)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SelectionChangedEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="selectedItems">A collection of data objects that leaf items represent.</param>
      <param name="selectedGroups">A collection of objects that provide paths to selected groups.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SelectionChangedEventArgs.SelectedGroups">
      <summary>
        <para>Returns the collection of selected group paths.</para>
      </summary>
      <value>Collection of selected group paths.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SelectionChangedEventArgs.SelectedItems">
      <summary>
        <para>Returns the collection of selected items.</para>
      </summary>
      <value>The collection of selected hierarchical items.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SelectionChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeMap.HierarchicalChartControlBase.SelectionChanged"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraTreeMap.HierarchicalChartControlBase"/> that raised the event.</param>
      <param name="e">The event data.</param>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SolidFillStyle">
      <summary>
        <para>Specifies a fill style that paints treemap items with a solid color.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SolidFillStyle.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SolidFillStyle"/> class.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstCenterLabel">
      <summary>
        <para>The Sunburst&#39;s center label.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstCenterLabel.Assign(DevExpress.XtraTreeMap.SunburstElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.SunburstCenterLabel"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.SunburstCenterLabel"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.SunburstElement"/> descendant) whose settings are assigned to the current object. 
If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstCenterLabel.BackgroundColor">
      <summary>
        <para>Gets or sets the center label&#39;s foreground color.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value that specifies the background color.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstCenterLabel.ForegroundColor">
      <summary>
        <para>Gets or sets the center label&#39;s foreground color.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value that specifies the foreground color.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstCenterLabel.TextFont">
      <summary>
        <para>Gets or sets the center label text&#39;s font.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Font"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstCenterLabel.TextPattern">
      <summary>
        <para>Gets or sets the pattern that formats the sunburst&#39;s center label text.</para>
      </summary>
      <value>The format string that configures the sunburst&#39;s center label text.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstCenterLabel.Visible">
      <summary>
        <para>Specifies the sunburst center label visibility.</para>
      </summary>
      <value>true, if the sunburst center label is visible; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstColorizerBase">
      <summary>
        <para>The base class for the Sunburst control&#39;s colorizers.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstColorizerBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SunburstColorizerBase"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraTreeMap.SunburstColorizerBase.ColorizerChanged">
      <summary>
        <para>Occurs after a colorizer&#39;s property changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstColorizerBase.TypeNameSerializable">
      <summary>
        <para>Returns the string value that helps the DevExpress Serializer serialize the specific sunburst colorizer type.</para>
      </summary>
      <value>The specific sunburst colorizer type name.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstDataAdapterBase">
      <summary>
        <para>The base class for all data adapters that provide items for the <see cref="T:DevExpress.XtraTreeMap.SunburstControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstDataAdapterBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SunburstDataAdapterBase"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstDataAdapterBase.Assign(DevExpress.XtraTreeMap.SunburstElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.SunburstDataAdapterBase"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.SunburstDataAdapterBase"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.SunburstElement"/> descendant) whose settings are assigned to the current object. 
If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="E:DevExpress.XtraTreeMap.SunburstDataAdapterBase.DataAdapterChanged">
      <summary>
        <para>Occurs after a data adapter&#39;s property changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstDataAdapterBase.TypeNameSerializable">
      <summary>
        <para>Returns the string value that helps the DevExpress Serializer serialize the specific sunburst data adapter type.</para>
      </summary>
      <value>The specific sunburst data adapter type name.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstElement">
      <summary>
        <para>The base class for the Sunburst control&#39;s elements.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstElement.Assign(DevExpress.XtraTreeMap.SunburstElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.SunburstElement"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.SunburstElement"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.SunburstElement"/> descendant) whose settings are assigned to the current object. 
If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstFlatDataAdapter">
      <summary>
        <para>The data adapter that provides flat data to a sunburst.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstFlatDataAdapter.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SunburstFlatDataAdapter"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstFlatDataAdapter.Assign(DevExpress.XtraTreeMap.SunburstElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.SunburstFlatDataAdapter"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.SunburstFlatDataAdapter"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.SunburstElement"/> descendant) whose settings are assigned to the current object. 
If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstFlatDataAdapter.DataMember">
      <summary>
        <para>Specifies the data source object&#39;s name that provides data for the data adapter.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstFlatDataAdapter.DataSource">
      <summary>
        <para>Gets or sets the object that is the data adapter&#39;s data source.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> that is the data source.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstFlatDataAdapter.GroupDataMembers">
      <summary>
        <para>Gets or sets the names of data source members used to group sunburst items generated from the data source.</para>
      </summary>
      <value>A collection of <see cref="T:System.String"/> values that specify the data member&#39;s names.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstFlatDataAdapter.GroupDataMembersSerializable">
      <summary>
        <para>Gets the string value used to support serialization of the group data members.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstFlatDataAdapter.LabelDataMember">
      <summary>
        <para>Gets or sets the name of the data field whose values are used to automatically generate <see cref="P:DevExpress.XtraTreeMap.SunburstItem.Label"/> values.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstFlatDataAdapter.ValueDataMember">
      <summary>
        <para>Gets or sets the name of the data field whose values are used to generate <see cref="P:DevExpress.XtraTreeMap.SunburstItem.Value"/> values.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstGradientColorizer">
      <summary>
        <para>The colorizers that colors sunburst items using color gradients.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstGradientColorizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SunburstGradientColorizer"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstGradientColorizer.Assign(DevExpress.XtraTreeMap.SunburstElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.SunburstGradientColorizer"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.SunburstGradientColorizer"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.SunburstElement"/> descendant) whose settings are assigned to the current object. 
If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstGradientColorizer.GradientColor">
      <summary>
        <para>Gets or sets the color that should be blended with the sunburst item color.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstGradientColorizer.Max">
      <summary>
        <para>The color intensity of the sunburst top level items.</para>
      </summary>
      <value>The <see cref="T:System.Double"/> value in the [0,1] range.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstGradientColorizer.Min">
      <summary>
        <para>The color intensity of the sunburst lowest level items.</para>
      </summary>
      <value>The <see cref="T:System.Double"/> value in the [0,1] range.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstGradientColorizer.Mode">
      <summary>
        <para>Gets or sets the mode that defines how to distribute a gradient across items.</para>
      </summary>
      <value>The gradient distribution&#39;s mode.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstHierarchicalDataAdapter">
      <summary>
        <para>A data adapter that allows you to provide hierarchical data to the SunburstControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstHierarchicalDataAdapter.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SunburstHierarchicalDataAdapter"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstHierarchicalDataAdapter.Assign(DevExpress.XtraTreeMap.SunburstElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.SunburstHierarchicalDataAdapter"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.SunburstHierarchicalDataAdapter"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.SunburstElement"/> descendant) whose settings are assigned to the current object. 
If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstHierarchicalDataAdapter.DataSource">
      <summary>
        <para>Gets or sets the object that is the data adapter&#39;s data source.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> that is the data source.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstHierarchicalDataAdapter.Mappings">
      <summary>
        <para>Returns the collection of mappings that specify how to convert data source objects to sunburst items.</para>
      </summary>
      <value>The collection of <see cref="T:DevExpress.XtraTreeMap.SunburstHierarchicalDataMapping"/> objects.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstHierarchicalDataMapping">
      <summary>
        <para>Information about data object mapping to a sunburst item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstHierarchicalDataMapping.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SunburstHierarchicalDataMapping"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstHighlightMode">
      <summary>
        <para>Lists values that define how to highlight sunburst items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.SunburstHighlightMode.None">
      <summary>
        <para>No item is highlighted when you hover the item over by the mouse pointer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.SunburstHighlightMode.PathFromRoot">
      <summary>
        <para>The item and all its parent items are highlighted when you hover over the item with the mouse pointer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.SunburstHighlightMode.Single">
      <summary>
        <para>The item is highlighted when you hover over it with the mouse pointer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.SunburstHighlightMode.WholeGroup">
      <summary>
        <para>The item and its child items are highlighted when you hover over the item with the mouse pointer.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstHitInfo">
      <summary>
        <para>Contains information about the Sunburst element that is under the test point.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstHitInfo.CenterLabel">
      <summary>
        <para>Returns the sunburst center label that is under the mouse cursor.</para>
      </summary>
      <value>The center label that is under the mouse cursor.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstHitInfo.InCenterLabel">
      <summary>
        <para>Returns the value that indicates whether the cursor is over the Sunburst center label.</para>
      </summary>
      <value>true, if the cursor over the center label; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstHitInfo.InSunburstItem">
      <summary>
        <para>Returns the value that specifies whether the cursor is over a sunburst item.</para>
      </summary>
      <value>true, if the cursor is over a sunburst item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstHitInfo.SunburstItem">
      <summary>
        <para>Returns the sunburst item that is under the mouse cursor.</para>
      </summary>
      <value>An object of a class that implements the DevExpress.TreeMap.ISunburstItem interface.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstItem">
      <summary>
        <para>The <see cref="T:DevExpress.XtraTreeMap.SunburstControl"/>&#39;s item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SunburstItem"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstItem.#ctor(System.Double,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SunburstItem"/> class with the specified settings.</para>
      </summary>
      <param name="value">The item&#39;s value.</param>
      <param name="label">The item&#39;s text label.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItem.Children">
      <summary>
        <para>Returns the current item&#39;s collection of child items.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.XtraTreeMap.SunburstItem"/> objects.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItem.IsGroup">
      <summary>
        <para>Returns a value that specifies whether the Sunburst Item is a group item.</para>
      </summary>
      <value>true if an item is a group; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItem.Label">
      <summary>
        <para>Gets or sets the sunburst item&#39;s label.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that defines the sunburst item&#39;s label.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItem.Style">
      <summary>
        <para>Returns the sunburst item&#39;s style settings.</para>
      </summary>
      <value>The sunburst item appearance settings storage.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItem.Tag">
      <summary>
        <para>Gets or sets the object that contains data related to a sunburst item.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> that contains data about the sunburst item.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItem.TypeNameSerializable">
      <summary>
        <para>Returns the string value that helps the DevExpress Serializer serialize the specific sunburst item type.</para>
      </summary>
      <value>The specific sunburst item type name.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItem.Value">
      <summary>
        <para>Gets or sets the sunburst item&#39;s value.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstItemCollection">
      <summary>
        <para>The collection of <see cref="T:DevExpress.XtraTreeMap.SunburstItem"/> objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstItemCollection.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SunburstItemCollection"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstItemGroupInfo">
      <summary>
        <para>This class stores information about a sunburst item group.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstItemGroupInfo.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Double,System.Double)">
      <summary>
        <para>Initializes a <see cref="T:DevExpress.XtraTreeMap.SunburstItemGroupInfo"/> object with the specified settings.</para>
      </summary>
      <param name="groupLevel">The item&#39;s parent group nesting level.</param>
      <param name="maxGroupLevel">The maximum nesting level in the Sunburst control.</param>
      <param name="groupIndex">The index of the parent group in its parent group&#39;s children collection.</param>
      <param name="itemIndex">The item&#39;s index within the parent group&#39;s children collection.</param>
      <param name="itemCount">The number of the parent group&#39;s children.</param>
      <param name="minValue">The minimum value of the parent group&#39;s child item.</param>
      <param name="maxValue">The maximum value of the parent group&#39;s child item.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItemGroupInfo.GroupIndex">
      <summary>
        <para>Returns the index of the group in the descending sorted item collection of the group&#39;s parent.</para>
      </summary>
      <value>The index of the group in the descending sorted item collection of the group&#39;s parent.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItemGroupInfo.GroupLevel">
      <summary>
        <para>Returns the parent group&#39;s nesting level.</para>
      </summary>
      <value>The parent group&#39;s nesting level.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItemGroupInfo.ItemCount">
      <summary>
        <para>Returns the number of the parent group&#39;s children.</para>
      </summary>
      <value>The number of the parent group&#39;s children.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItemGroupInfo.ItemIndex">
      <summary>
        <para>Returns the index of an item in the group&#39;s children collection descending sorted.</para>
      </summary>
      <value>The index of an item in the group&#39;s children collection descending sorted.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItemGroupInfo.MaxGroupLevel">
      <summary>
        <para>Returns the maximum nesting level within the Sunburst control.</para>
      </summary>
      <value>The maximum nesting level within the Sunburst control.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItemGroupInfo.MaxValue">
      <summary>
        <para>Returns the maximum item value within the parent group.</para>
      </summary>
      <value>The maximum item value within the parent group.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItemGroupInfo.MinValue">
      <summary>
        <para>Returns the minimum item value within the parent group.</para>
      </summary>
      <value>The minimum item value within the parent group.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstItemStorage">
      <summary>
        <para>Stores a collection of sunburst items and provides them to the SunburstControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstItemStorage.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SunburstItemStorage"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItemStorage.Items">
      <summary>
        <para>Returns the collection of sunburst items that the storage contains.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.SunburstItemCollection"/> object that contains <see cref="T:DevExpress.XtraTreeMap.SunburstItem"/> objects.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstItemStyle">
      <summary>
        <para>The sunburst item appearance settings&#39; storage.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstItemStyle.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SunburstItemStyle"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstItemStyle.Assign(DevExpress.XtraTreeMap.TreeMapItemLeafStyle)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.SunburstItemStyle"/> object passed as the parameter.</para>
      </summary>
      <param name="style">A <see cref="T:DevExpress.XtraTreeMap.SunburstItemStyle"/> object whose settings are assigned to the current object. 
If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstItemStyle.TextGlowColor">
      <summary>
        <para>Gets or sets the glow color of the sunburst item&#39;s text.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> object that is the glow color of the text.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstLabel">
      <summary>
        <para>Stores the settings of sunburst item labels.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstLabel.Assign(DevExpress.XtraTreeMap.SunburstElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.SunburstLabel"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.SunburstLabel"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.SunburstElement"/> descendant) whose settings are assigned to the current object. 
If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstLabel.AutoLayout">
      <summary>
        <para>Indicates whether the adaptive layout is enabled for sunburst item labels.</para>
      </summary>
      <value>true, if the adaptive layout is enabled, otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstLabel.DisplayMode">
      <summary>
        <para>Specifies how to align a label within a sunburst item.</para>
      </summary>
      <value>The value that defines the label display mode.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstLabel.TextPattern">
      <summary>
        <para>Gets or sets the pattern that formats the Sunburst&#39;s label text.</para>
      </summary>
      <value>The format string that configures the Sunburst&#39;s label text.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstLabel.Visible">
      <summary>
        <para>Specifies the visibility of sunburst item labels.</para>
      </summary>
      <value>true, if sunburst item labels are visible; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstLabelDisplayMode">
      <summary>
        <para>Lists values that specify how to align labels within sunburst items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.SunburstLabelDisplayMode.Horizontal">
      <summary>
        <para>Labels are drawn horizontally.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.SunburstLabelDisplayMode.Radial">
      <summary>
        <para>Labels are drawn radially.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.SunburstLabelDisplayMode.Tangent">
      <summary>
        <para>Labels are drawn tangentially.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstPaletteColorizer">
      <summary>
        <para>The colorizer that colors sunburst items using a palette.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstPaletteColorizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.SunburstPaletteColorizer"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstPaletteColorizer.Assign(DevExpress.XtraTreeMap.SunburstElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.SunburstPaletteColorizer"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.SunburstPaletteColorizer"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.SunburstElement"/> descendant) whose settings are assigned to the current object. 
If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstPaletteColorizer.VaryColorInGroup">
      <summary>
        <para>Gets or sets the value that specifies whether to color each item in the same group.</para>
      </summary>
      <value>true, if items in the same group have different colors; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstPaletteColorizerBase">
      <summary>
        <para>The base class for all colorizers that paint the Sunburst control&#39;s items using predefined or custom palettes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.SunburstPaletteColorizerBase.Assign(DevExpress.XtraTreeMap.SunburstElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.SunburstPaletteColorizerBase"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.SunburstPaletteColorizerBase"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.SunburstElement"/> descendant) whose settings are assigned to the current object. 
If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.SunburstPaletteColorizerBase.Palette">
      <summary>
        <para>Gets or sets the palette the colorizer uses to paint items.</para>
      </summary>
      <value>The palette that the colorizer uses.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.SunburstSweepDirection">
      <summary>
        <para>Lists values that define the SunburstControl&#39;s sweep direction.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.SunburstSweepDirection.Clockwise">
      <summary>
        <para>The sunburst items are positioned clockwise.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.SunburstSweepDirection.Counterclockwise">
      <summary>
        <para>The sunburst items are positioned counterclockwise.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapAppearance">
      <summary>
        <para>Specifies the treemap appearance settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapAppearance.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapAppearance"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.TreeMapAppearance"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapAppearance.GroupStyle">
      <summary>
        <para>Returns the style of treemap groups.</para>
      </summary>
      <value>An object that provides settings for a treemap group&#39;s appearance.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapAppearance.HighlightedLeafStyle">
      <summary>
        <para>Returns a style used to specify the appearance of highlighted treemap leaves.</para>
      </summary>
      <value>A treemap item style.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapAppearance.LeafStyle">
      <summary>
        <para>Returns the style that applies to treemap leaves.</para>
      </summary>
      <value>The treemap item leaf style.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapAppearance.SelectedLeafStyle">
      <summary>
        <para>Returns the style that applies to selected treemap leaves.</para>
      </summary>
      <value>The treemap item leaf style.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapColorizerBase">
      <summary>
        <para>The base class for the TreeMap control&#39;s colorizers.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapColorizerBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapColorizerBase"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraTreeMap.TreeMapColorizerBase.ColorizerChanged">
      <summary>
        <para>Occurs after a colorizer&#39;s property changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapColorizerBase.TypeNameSerializable">
      <summary>
        <para>Returns the string value that helps the DevExpress Serializer serialize the specific tree map colorizer type.</para>
      </summary>
      <value>The specific tree map colorizer type name.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapColorizerBase.ValueProvider">
      <summary>
        <para>Gets or sets a provider of values the colorizer uses to determine colors for treemap items.</para>
      </summary>
      <value>An object of a class that implements the <see cref="T:DevExpress.XtraTreeMap.IColorizerValueProvider"/> interface.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapCustomItem">
      <summary>
        <para>A styleable tree map item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapCustomItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapCustomItem"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapCustomItem.GroupStyle">
      <summary>
        <para>Returns the style that will be applied to the item if it is a group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.TreeMapItemGroupStyle"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapCustomItem.LeafStyle">
      <summary>
        <para>Returns the style that will be applied to the item if it is a leaf.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.TreeMapItemLeafStyle"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapDataAdapterBase">
      <summary>
        <para>A base class for all tree map data adapters.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapDataAdapterBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapDataAdapterBase"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapDataAdapterBase.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapDataAdapterBase"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.TreeMapDataAdapterBase"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. 
If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="E:DevExpress.XtraTreeMap.TreeMapDataAdapterBase.DataAdapterChanged">
      <summary>
        <para>Occurs after a data adapter&#39;s property changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapDataAdapterBase.TypeNameSerializable">
      <summary>
        <para>Returns the string value that helps the DevExpress Serializer serialize the specific tree map data adapter type.</para>
      </summary>
      <value>The specific tree map data adapter type name.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapElement">
      <summary>
        <para>A base class for all tree map elements.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapElement.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> object whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapElementStyleBase">
      <summary>
        <para>The base class for all tree map element style classes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapElementStyleBase.MergeStyles(DevExpress.XtraTreeMap.TreeMapElementStyleBase,DevExpress.XtraTreeMap.TreeMapElementStyleBase)">
      <summary>
        <para>Merges the specified styles and returns it in the destination style.</para>
      </summary>
      <param name="destinationStyle">The destination style that returns the merged style.</param>
      <param name="style">The style whose parameters this method merges into the destination style.</param>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapElementStyleBase.ToString">
      <summary>
        <para>Returns the textual representation of the <see cref="T:DevExpress.XtraTreeMap.TreeMapElementStyleBase"/>.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value, which is the textual representation of the <see cref="T:DevExpress.XtraTreeMap.TreeMapElementStyleBase"/>.</returns>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapFlatDataAdapter">
      <summary>
        <para>The data adapter that provides flat data to a tree map.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapFlatDataAdapter.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapFlatDataAdapter"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapFlatDataAdapter.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapFlatDataAdapter"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.TreeMapFlatDataAdapter"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapFlatDataAdapter.DataMember">
      <summary>
        <para>Specifies the name of an object in a data source providing data for a data adapter.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapFlatDataAdapter.DataSource">
      <summary>
        <para>Gets or sets the object that is the data source of the data adapter.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> that is the data source.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapFlatDataAdapter.GroupDataMembers">
      <summary>
        <para>Gets or sets the names of data members of the data source used to group tree map items generated from the data source.</para>
      </summary>
      <value>A collection of <see cref="T:System.String"/> values, specifying the names of data members.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapFlatDataAdapter.GroupDataMembersSerializable">
      <summary>
        <para>Gets the string value used to support serialization of the group data members.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapFlatDataAdapter.LabelDataMember">
      <summary>
        <para>Gets or sets the name of the data field whose values are used to automatically generate <see cref="P:DevExpress.XtraTreeMap.TreeMapItem.Label"/> values.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapFlatDataAdapter.ValueDataMember">
      <summary>
        <para>Gets or sets the name of the data field whose values are used to automatically generate <see cref="P:DevExpress.XtraTreeMap.TreeMapItem.Value"/> values.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapGradientColorizer">
      <summary>
        <para>The colorizer that colors tree map items from the <see cref="P:DevExpress.XtraTreeMap.TreeMapGradientColorizer.StartColor"/> to the <see cref="P:DevExpress.XtraTreeMap.TreeMapGradientColorizer.EndColor"/> using a gradient.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapGradientColorizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapGradientColorizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapGradientColorizer.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapGradientColorizer"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.TreeMapGradientColorizer"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapGradientColorizer.EndColor">
      <summary>
        <para>Gets or sets the end color of the gradient used to color tree map items.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapGradientColorizer.StartColor">
      <summary>
        <para>Gets or sets the start color of the gradient used to color tree map items.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapGroupGradientColorizer">
      <summary>
        <para>The colorizer that colors the tree map items in colors blended from group colors and gradient colors in a proportion based on the tree map item value.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapGroupGradientColorizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapGroupGradientColorizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapGroupGradientColorizer.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapGroupGradientColorizer"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.TreeMapGroupGradientColorizer"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapGroupGradientColorizer.GradientColor">
      <summary>
        <para>Gets or sets the color mixed with the group color for a tree map item.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapGroupGradientColorizer.Max">
      <summary>
        <para>Gets or sets the value indicating the maximum portion of the group color in the color mixed for a tree map item.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value between 0 and 1.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapGroupGradientColorizer.Min">
      <summary>
        <para>Gets or sets the value indicating the minimum portion of the group color in the color mixed for a tree map item.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value between 0 and 1.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapHierarchicalDataAdapter">
      <summary>
        <para>A data adapter that allows you to provide hierarchical data to the Tree Map.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapHierarchicalDataAdapter.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapHierarchicalDataAdapter"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapHierarchicalDataAdapter.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapHierarchicalDataAdapter"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.TreeMapHierarchicalDataAdapter"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapHierarchicalDataAdapter.DataSource">
      <summary>
        <para>Gets or sets the object that is the data source of the data adapter.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> that is the data source.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapHierarchicalDataAdapter.Mappings">
      <summary>
        <para>Returns the collection of data object mappings to tree map items.</para>
      </summary>
      <value>A list of <see cref="T:DevExpress.XtraTreeMap.TreeMapHierarchicalDataMapping"/> objects.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapHierarchicalDataMapping">
      <summary>
        <para>Information about data object mapping to a tree map item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapHierarchicalDataMapping.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapHierarchicalDataMapping"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapHitInfo">
      <summary>
        <para>Contains information about what is located at a specific point within the Tree Map.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapHitInfo.InTreeMapItem">
      <summary>
        <para>Returns a value indicating whether or not the cursor is over the tree map item.</para>
      </summary>
      <value>true if the cursor over an item; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapHitInfo.TreeMapItem">
      <summary>
        <para>Returns the tree map item over which the cursor is.</para>
      </summary>
      <value>The tree map item over which the cursor is.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapItem">
      <summary>
        <para>An item of a tree map.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapItem"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapItem.#ctor(System.Double,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapItem"/> class with the specified label and value.</para>
      </summary>
      <param name="value">A <see cref="T:System.Double"/> value, representing the value of the tree map item.</param>
      <param name="label">A <see cref="T:System.String"/> value, representing the label of the tree map item.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItem.Children">
      <summary>
        <para>Returns the collection of child tree map items of the current item.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.XtraTreeMap.TreeMapItem"/> objects.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItem.IsGroup">
      <summary>
        <para>Returns the value that indicates whether the item is a group.</para>
      </summary>
      <value>true if the item is a group; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItem.Label">
      <summary>
        <para>Gets or sets the tree map item label.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the treemap item&#39;s label.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItem.Tag">
      <summary>
        <para>Gets or sets the object containing data related to a tree map item.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> that contains data about the treemap item.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItem.TypeNameSerializable">
      <summary>
        <para>Returns the string value that helps the DevExpress Serializer serialize the specific tree map item type.</para>
      </summary>
      <value>The specific tree map item type name.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItem.Value">
      <summary>
        <para>Gets or sets the tree map item value.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapItemCollection">
      <summary>
        <para>A collection of <see cref="T:DevExpress.XtraTreeMap.TreeMapItem"/> objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapItemCollection.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapItemCollection"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapItemGroupInfo">
      <summary>
        <para>This class stores information about a tree map item group.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapItemGroupInfo.#ctor(System.Int32,System.Int32,System.Int32,System.Double,System.Double)">
      <summary>
        <para>Initializes a <see cref="T:DevExpress.XtraTreeMap.TreeMapItemGroupInfo"/> object with the specified settings.</para>
      </summary>
      <param name="groupLevel">The item&#39;s parent group nesting level.</param>
      <param name="groupIndex">The index of the parent group in its parent group&#39;s children collection.</param>
      <param name="itemIndex">The item&#39;s index within the parent group&#39;s children collection.</param>
      <param name="minValue">The minimum value of the parent group&#39;s child item.</param>
      <param name="maxValue">The maximum value of the parent group&#39;s child item.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemGroupInfo.GroupIndex">
      <summary>
        <para>Returns the index of the group in the descending sorted item collection of the group&#39;s parent.</para>
      </summary>
      <value>A <see cref="T:System.Int32"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemGroupInfo.GroupLevel">
      <summary>
        <para>Returns an item&#39;s nesting level.</para>
      </summary>
      <value>A <see cref="T:System.Int32"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemGroupInfo.ItemIndex">
      <summary>
        <para>Returns the index of an item in the source collection of the group.</para>
      </summary>
      <value>A <see cref="T:System.Int32"/> value specifying the index of an item.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemGroupInfo.MaxValue">
      <summary>
        <para>Returns the maximum value of the items in this group.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemGroupInfo.MinValue">
      <summary>
        <para>Returns the minimum value of the items in this group.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapItemGroupStyle">
      <summary>
        <para>The style of a tree map group.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapItemGroupStyle.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapItemGroupStyle"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapItemGroupStyle.Assign(DevExpress.XtraTreeMap.TreeMapItemLeafStyle)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapItemGroupStyle"/> object passed as the parameter.</para>
      </summary>
      <param name="style">An <see cref="T:DevExpress.XtraTreeMap.TreeMapItemGroupStyle"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemGroupStyle.HeaderPadding">
      <summary>
        <para>Gets or sets the padding of the tree map group&#39;s header</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemGroupStyle.SubGroupBorderColor">
      <summary>
        <para>Gets or sets the border color of sub groups of the styled group.</para>
      </summary>
      <value>The border color of sub groups.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemGroupStyle.SubGroupFill">
      <summary>
        <para>Gets or sets the subgroup fill color.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemGroupStyle.SubGroupFont">
      <summary>
        <para>Gets or sets the header font of sub groups of the styled group.</para>
      </summary>
      <value>The font of sub group headers.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemGroupStyle.SubGroupHeaderPadding">
      <summary>
        <para>Gets or sets the header padding of sub groups of the styled group.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemGroupStyle.SubGroupTextColor">
      <summary>
        <para>Gets or sets the subgroup text color.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapItemLeafStyle">
      <summary>
        <para>The style of a tree map leaf.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapItemLeafStyle.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapItemLeafStyle"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapItemLeafStyle.Assign(DevExpress.XtraTreeMap.TreeMapItemLeafStyle)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapItemLeafStyle"/> object passed as the parameter.</para>
      </summary>
      <param name="style">An <see cref="T:DevExpress.XtraTreeMap.TreeMapItemLeafStyle"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemLeafStyle.BorderVisible">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemLeafStyle.FillStyle">
      <summary>
        <para>Gets or sets the fill style for treemap leaves.</para>
      </summary>
      <value>The style used to fill treemap leaves.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemLeafStyle.LabelAlignment">
      <summary>
        <para>Gets or sets the alignment for treemap item labels.</para>
      </summary>
      <value>The alignment of treemap labels.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemLeafStyle.LabelVerticalAlignment">
      <summary>
        <para>Gets or sets the treemap leaf label&#39;s vertical alignment.</para>
      </summary>
      <value>The label&#39;s vertical alignment.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemLeafStyle.Padding">
      <summary>
        <para>Gets or sets the treemap leaf padding.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemLeafStyle.TextGlowColor">
      <summary>
        <para>Gets or sets the glow color of a text for tree map leaf items.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> object that is the glow color of a text.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapItemStorage">
      <summary>
        <para>Stores a collection of tree map items and provides them to a tree map control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapItemStorage.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapItemStorage"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapItemStorage.Items">
      <summary>
        <para>Returns the collection of tree map items contained in this storage.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.TreeMapItemCollection"/> object containing <see cref="T:DevExpress.XtraTreeMap.TreeMapItem"/> objects.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapLayoutAlgorithmBase">
      <summary>
        <para>A base class for all TreeMap layout algorithms.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapLayoutAlgorithmBase.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapLayoutAlgorithmBase"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.TreeMapLayoutAlgorithmBase"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapLayoutAlgorithmBase.Direction">
      <summary>
        <para>Gets or sets the layout fill direction used by the algorithm.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Charts.LayoutDirection"/> enumeration value.</value>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapLayoutAlgorithmBase.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current <see cref="T:DevExpress.XtraTreeMap.TreeMapLayoutAlgorithmBase"/> instance.</para>
      </summary>
      <param name="obj">The object to be compared with the current object.</param>
      <returns>true if the specified object is equal to the current <see cref="T:DevExpress.XtraTreeMap.TreeMapLayoutAlgorithmBase"/> instance; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapLayoutAlgorithmBase.GetHashCode">
      <summary>
        <para>Gets the hash code (a number) that corresponds to the value of the current <see cref="T:DevExpress.XtraTreeMap.TreeMapLayoutAlgorithmBase"/> object.</para>
      </summary>
      <returns>An integer value representing the hash code for the current object.</returns>
    </member>
    <member name="E:DevExpress.XtraTreeMap.TreeMapLayoutAlgorithmBase.LayoutAlgorithmChanged">
      <summary>
        <para>Occurs after a layout algorithm&#39;s property changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapLayoutAlgorithmBase.TypeNameSerializable">
      <summary>
        <para>Gets the string value used to support serialization of the layout algorithm type.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapLayoutDirection">
      <summary>
        <para>Lists the possible tree map fill layout directions.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.TreeMapLayoutDirection.BottomLeftToTopRight">
      <summary>
        <para>Items are arranged from the bottom-left angle to the top-right corner.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.TreeMapLayoutDirection.BottomRightToTopLeft">
      <summary>
        <para>Items are arranged from the bottom-right angle to the top-left corner.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.TreeMapLayoutDirection.TopLeftToBottomRight">
      <summary>
        <para>Items are arranged from the top-left angle to the bottom-right corner.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.TreeMapLayoutDirection.TopRightToBottomLeft">
      <summary>
        <para>Items are arranged from the top-right angle to the bottom-left corner.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapPaletteColorizer">
      <summary>
        <para>The colorizer that colors tree map items using a palette.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapPaletteColorizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapPaletteColorizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapPaletteColorizer.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapPaletteColorizer"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.TreeMapPaletteColorizer"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapPaletteColorizer.ColorizeGroups">
      <summary>
        <para>Gets or sets a value indicating whether or not tree map items should be colorized by groups.</para>
      </summary>
      <value>true if items in one group should be colorized using one color; otherwise false.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapPaletteColorizerBase">
      <summary>
        <para>A base class for all colorizers supporting a palette.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapPaletteColorizerBase.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapPaletteColorizerBase"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.TreeMapPaletteColorizerBase"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. 
If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapPaletteColorizerBase.Palette">
      <summary>
        <para>Gets or sets the palette the colorizer uses to paint items.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.Palette"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapRangeColorizer">
      <summary>
        <para>A colorizer that allows you to provide colors based on which range the <see cref="P:DevExpress.XtraTreeMap.TreeMapItem.Value"/> belongs to.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapRangeColorizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapRangeColorizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapRangeColorizer.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapRangeColorizer"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.TreeMapRangeColorizer"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapRangeColorizer.GroupColor">
      <summary>
        <para>Gets or sets the color used to color groups.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.Color"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapRangeColorizer.RangeStops">
      <summary>
        <para>Gets range stops for the range colorizer.</para>
      </summary>
      <value>A collection of <see cref="T:System.Double"/> values specifying range stops.</value>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapRangeColorizer.RangeStopsSerializable">
      <summary>
        <para>Gets the string value used to support serialization of the range stops.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutAlgorithm">
      <summary>
        <para>A class representing the Slice and Dice layout algorithm.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutAlgorithm.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutAlgorithm"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutAlgorithm.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutAlgorithm"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutAlgorithm"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutAlgorithm.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current <see cref="T:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutAlgorithm"/> instance.</para>
      </summary>
      <param name="obj">The object to be compared with the current object.</param>
      <returns>true, if the specified object is equal to the current <see cref="T:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutAlgorithm"/> instance; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutAlgorithm.GetHashCode">
      <summary>
        <para>Gets the hash code (a number) that corresponds to the value of the current <see cref="T:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutAlgorithm"/> object.</para>
      </summary>
      <returns>An integer value representing the hash code for the current object.</returns>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutAlgorithm.LayoutMode">
      <summary>
        <para>Gets or sets the layout mode used to arrange items using this algorithm.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutMode"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutMode">
      <summary>
        <para>Lists the modes of the <see cref="T:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutAlgorithm"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutMode.Auto">
      <summary>
        <para>The layout direction is selected depending on the layout&#39;s width/height ratio.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutMode.Horizontal">
      <summary>
        <para>All tree map items are arranged horizontally.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraTreeMap.TreeMapSliceAndDiceLayoutMode.Vertical">
      <summary>
        <para>All tree map items are arranged vertically.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapSquarifiedLayoutAlgorithm">
      <summary>
        <para>A class representing the Squarified layout algorithm.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapSquarifiedLayoutAlgorithm.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapSquarifiedLayoutAlgorithm"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraTreeMap.TreeMapStripedLayoutAlgorithm">
      <summary>
        <para>A class representing the Strip layout algorithm.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapStripedLayoutAlgorithm.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeMap.TreeMapStripedLayoutAlgorithm"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapStripedLayoutAlgorithm.Assign(DevExpress.XtraTreeMap.TreeMapElement)">
      <summary>
        <para>Copies all settings from the <see cref="T:DevExpress.XtraTreeMap.TreeMapStripedLayoutAlgorithm"/> object passed as the parameter.</para>
      </summary>
      <param name="obj">An <see cref="T:DevExpress.XtraTreeMap.TreeMapStripedLayoutAlgorithm"/> object (which is the <see cref="T:DevExpress.XtraTreeMap.TreeMapElement"/> descendant) whose settings are assigned to the current object. If null (Nothing in Visual Basic), then a <see cref="T:System.ArgumentNullException"/> will be thrown.</param>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapStripedLayoutAlgorithm.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current <see cref="T:DevExpress.XtraTreeMap.TreeMapStripedLayoutAlgorithm"/> instance.</para>
      </summary>
      <param name="obj">The object to be compared with the current object.</param>
      <returns>true, if the specified object is equal to the current <see cref="T:DevExpress.XtraTreeMap.TreeMapStripedLayoutAlgorithm"/> instance; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.XtraTreeMap.TreeMapStripedLayoutAlgorithm.GetHashCode">
      <summary>
        <para>Gets the hash code (a number) that corresponds to the value of the current <see cref="T:DevExpress.XtraTreeMap.TreeMapStripedLayoutAlgorithm"/> object.</para>
      </summary>
      <returns>An integer value representing the hash code for the current object.</returns>
    </member>
    <member name="P:DevExpress.XtraTreeMap.TreeMapStripedLayoutAlgorithm.LastStripeMinThickness">
      <summary>
        <para>Gets or sets the minimum thickness of the last strip.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value in percents.</value>
    </member>
  </members>
</doc>