﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.7.0" Ref="1" ControlType="MrSales.MrSReports.R_Sales.DetailedSales_ThermCustom, MrSales, Version=5.3.1.0, Culture=neutral, PublicKeyToken=null" Name="DetailedSales_ThermCustom" SnapGridSize="25" ReportUnit="TenthsOfAMillimeter" Margins="150, 150, 376, 203" PaperKind="A4" PageWidth="2100" PageHeight="2970" Version="17.2" RequestParameters="false" RightToLeft="Yes" RightToLeftLayout="Yes" EventsInfo="DetailedSales_ThermCustom,BeforePrint,DetailedSales_7_BeforePrint" FilterString="[sales_main.smain_ID] = ?SmainID" DataSource="#Ref-0" Dpi="254" Font="Segoe UI, 9.75pt">
  <FormattingRuleSheet>
    <Item1 Ref="2" Name="formattingRule1">
      <Formatting Ref="3" BorderStyle="Inset" StringFormat="Near;Near;0;None;Character;Default" />
    </Item1>
  </FormattingRuleSheet>
  <Parameters>
    <Item1 Ref="5" Visible="false" Name="mobile1" />
    <Item2 Ref="6" Visible="false" Name="mobile2" />
    <Item3 Ref="7" Visible="false" Name="mobile3" />
    <Item4 Ref="8" Visible="false" Name="country" />
    <Item5 Ref="9" Visible="false" Name="city" />
    <Item6 Ref="10" Visible="false" Name="address" />
    <Item7 Ref="11" Visible="false" Name="email" />
    <Item8 Ref="12" Visible="false" Name="website" />
    <Item9 Ref="14" Description="رقم الفاتورة" ValueInfo="0" Name="SmainID" Type="#Ref-13" />
    <Item10 Ref="15" Visible="false" Name="CompanyName" />
    <Item11 Ref="16" Visible="false" Name="CustomerBalance" />
    <Item12 Ref="17" Visible="false" Description="الملاحظات" Name="Invoicenotes" />
  </Parameters>
  <Bands>
    <Item1 Ref="18" ControlType="DetailBand" Name="Detail" HeightF="98" TextAlignment="TopLeft" Dpi="254" Padding="0,0,0,0,254">
      <Controls>
        <Item1 Ref="19" ControlType="XRTable" Name="xrTable2" TextAlignment="MiddleLeft" SizeF="1796.18909,96.52" LocationFloat="0.*********, 1.375885" Dpi="254" Font="Segoe UI, 7.5pt" BorderColor="DarkGray" Borders="Bottom" BorderWidth="0.7">
          <Rows>
            <Item1 Ref="20" ControlType="XRTableRow" Name="xrTableRow2" Weight="11.5" Dpi="254">
              <Cells>
                <Item1 Ref="21" ControlType="XRTableCell" Name="xrTableCell12" Weight="0.044070873424307204" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt">
                  <Summary Ref="22" Running="Report" Func="RecordNumber" />
                  <DataBindings>
                    <Item1 Ref="23" PropertyName="Text" DataMember="sitem_ID" />
                  </DataBindings>
                  <StylePriority Ref="24" UseFont="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="25" ControlType="XRTableCell" Name="xrTableCell6" Weight="0.13228216891107555" Dpi="254" StyleName="DetailData3" Font="Segoe UI, 8pt">
                  <DataBindings>
                    <Item1 Ref="26" PropertyName="Text" DataMember="st_items.stitems_Code" />
                  </DataBindings>
                  <StylePriority Ref="27" UseFont="false" />
                </Item2>
                <Item3 Ref="28" ControlType="XRTableCell" Name="xrTableCell8" Weight="0.22270403325975072" Dpi="254" StyleName="DetailData3" Font="Segoe UI, 8pt">
                  <DataBindings>
                    <Item1 Ref="29" PropertyName="Text" DataMember="st_items.stitems_Name" />
                  </DataBindings>
                  <StylePriority Ref="30" UseFont="false" />
                </Item3>
                <Item4 Ref="31" ControlType="XRTableCell" Name="xrTableCell14" Weight="0.0739061617408088" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt">
                  <DataBindings>
                    <Item1 Ref="32" PropertyName="Text" DataMember="st_items.st_items_units.st_units_name.Unit" />
                  </DataBindings>
                  <StylePriority Ref="33" UseFont="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="34" ControlType="XRTableCell" Name="xrTableCell11" Weight="0.072791614774006363" TextFormatString="{0:#.00}" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt">
                  <DataBindings>
                    <Item1 Ref="35" PropertyName="Text" DataMember="sitem_Quantity" />
                  </DataBindings>
                  <StylePriority Ref="36" UseFont="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="37" ControlType="XRTableCell" Name="xrTableCell22" Weight="0.0769086645563099" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt">
                  <DataBindings>
                    <Item1 Ref="38" FormatString="{0:#.00}" PropertyName="Text" DataMember="sitem_Price" />
                  </DataBindings>
                  <StylePriority Ref="39" UseFont="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="40" ControlType="XRTableCell" Name="xrTableCell19" Weight="0.065713210432808686" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt">
                  <DataBindings>
                    <Item1 Ref="41" FormatString="{0:#.00}" PropertyName="Text" DataMember="tax" />
                  </DataBindings>
                  <StylePriority Ref="42" UseFont="false" UseTextAlignment="false" />
                </Item7>
                <Item8 Ref="43" ControlType="XRTableCell" Name="xrTableCell18" Weight="0.068332702977775273" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt">
                  <DataBindings>
                    <Item1 Ref="44" FormatString="{0:#.00}" PropertyName="Text" DataMember="sitem_Disc" />
                  </DataBindings>
                  <StylePriority Ref="45" UseFont="false" UseTextAlignment="false" />
                </Item8>
                <Item9 Ref="46" ControlType="XRTableCell" Name="xrTableCell15" Weight="0.066846317336163724" TextFormatString="{0:#.00}" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt">
                  <DataBindings>
                    <Item1 Ref="47" PropertyName="Text" DataMember="sitem_Bounce" />
                  </DataBindings>
                  <StylePriority Ref="48" UseFont="false" UseTextAlignment="false" />
                </Item9>
                <Item10 Ref="49" ControlType="XRTableCell" Name="tableCell2" Weight="0.10171398990962061" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt">
                  <DataBindings>
                    <Item1 Ref="50" PropertyName="Text" DataMember="BatchNum" />
                  </DataBindings>
                  <StylePriority Ref="51" UseFont="false" UseTextAlignment="false" />
                </Item10>
                <Item11 Ref="52" ControlType="XRTableCell" Name="xrTableCell21" Weight="0.10171398990962061" TextFormatString="{0:dd/MM/yyyy}" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt">
                  <DataBindings>
                    <Item1 Ref="53" PropertyName="Text" DataMember="ExpiredDate" />
                  </DataBindings>
                  <StylePriority Ref="54" UseFont="false" UseTextAlignment="false" />
                </Item11>
                <Item12 Ref="55" ControlType="XRTableCell" Name="xrTableCell10" Weight="0.099512834374685222" TextAlignment="MiddleCenter" Dpi="254" StyleName="DetailData3" Font="Segoe UI, 8pt">
                  <DataBindings>
                    <Item1 Ref="56" FormatString="{0:#.00}" PropertyName="Text" DataMember="sitem_NetPrice" />
                  </DataBindings>
                  <StylePriority Ref="57" UseFont="false" UseTextAlignment="false" />
                </Item12>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="58" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item1>
    <Item2 Ref="59" ControlType="TopMarginBand" Name="TopMargin" HeightF="375.999969" TextAlignment="TopLeft" Dpi="254" Padding="0,0,0,0,254">
      <Controls>
        <Item1 Ref="60" ControlType="XRPanel" Name="xrPanel2" SizeF="1797.20837,279.4001" LocationFloat="2.791577, 96.599884" Dpi="254" BackColor="255,240,240,240">
          <Controls>
            <Item1 Ref="61" ControlType="XRLabel" Name="label2" Multiline="true" SizeF="307.0464,50.37668" LocationFloat="1424.23071, 211.772522" Dpi="254" Font="Segoe UI, 9pt" ForeColor="DimGray" Padding="5,5,0,0,254">
              <DataBindings>
                <Item1 Ref="62" PropertyName="Text" DataMember="sales_main.ad_branches.ad_company_data1.comp_TaxID" />
              </DataBindings>
              <StylePriority Ref="63" UseFont="false" UseForeColor="false" />
            </Item1>
            <Item2 Ref="64" ControlType="XRLabel" Name="label1" Multiline="true" Text="ب.ض." SizeF="103.844482,50.37668" LocationFloat="1307.94873, 211.772522" Dpi="254" Font="Segoe UI, 9pt" ForeColor="DimGray" Padding="5,5,0,0,254">
              <StylePriority Ref="65" UseFont="false" UseForeColor="false" />
            </Item2>
            <Item3 Ref="66" ControlType="XRLabel" Name="xrLabel4" Multiline="true" SizeF="1298.1521,64.26671" LocationFloat="433.916656, 34" Dpi="254" Font="Segoe UI, 10pt, style=Bold" ForeColor="DimGray" Padding="5,5,0,0,254" BorderColor="Silver" Borders="Bottom" BorderWidth="2">
              <DataBindings>
                <Item1 Ref="68" Parameter="#Ref-15" PropertyName="Text" DataMember="CompanyName" />
              </DataBindings>
              <StylePriority Ref="69" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" />
            </Item3>
            <Item4 Ref="70" ControlType="XRLabel" Name="xrLabel5" Multiline="true" SizeF="345.631531,50.3766861" LocationFloat="876.7233, 111.019142" Dpi="254" Font="Segoe UI, 8pt" ForeColor="DimGray" Padding="5,5,0,0,254">
              <DataBindings>
                <Item1 Ref="71" Parameter="#Ref-9" PropertyName="Text" DataMember="city" />
              </DataBindings>
              <StylePriority Ref="72" UseFont="false" UseForeColor="false" />
            </Item4>
            <Item5 Ref="73" ControlType="XRLabel" Name="xrLabel8" Multiline="true" SizeF="345.631531,50.37668" LocationFloat="876.7233, 211.772522" Dpi="254" Font="Segoe UI, 9pt" ForeColor="DimGray" Padding="5,5,0,0,254">
              <DataBindings>
                <Item1 Ref="74" Parameter="#Ref-10" PropertyName="Text" DataMember="address" />
              </DataBindings>
              <StylePriority Ref="75" UseFont="false" UseForeColor="false" />
            </Item5>
            <Item6 Ref="76" ControlType="XRLabel" Name="xrLabel11" Multiline="true" SizeF="345.631531,50.37668" LocationFloat="876.7233, 161.395844" Dpi="254" Font="Segoe UI, 9pt" ForeColor="DimGray" Padding="5,5,0,0,254">
              <DataBindings>
                <Item1 Ref="77" Parameter="#Ref-8" PropertyName="Text" DataMember="country" />
              </DataBindings>
              <StylePriority Ref="78" UseFont="false" UseForeColor="false" />
            </Item6>
            <Item7 Ref="79" ControlType="XRPictureBox" Name="xrPictureBox4" Image="iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAOxAAADsQBlSsOGwAAAHxJREFUOE+ljEEOgCAMBHkbD5anqRy4akpcU9pCGyWZAIWZ1Fq7/tADOedPqMCxnyHMQNlKKML/DgF69CKQcVYBYhbhMu5mgJARKWM2DRCIWDLhBjxUoJ61D4AlcVQAA757vAEOHqIMAVkHabWkKAkFgBTB83W9uBAXU7oBQ5HIOsSz+goAAAAASUVORK5CYII=" SizeF="42.3280029,42.32804" LocationFloat="1308.74072, 119.067787" Dpi="254" />
            <Item8 Ref="80" ControlType="XRLabel" Name="xrLabel9" Multiline="true" SizeF="381,50.3766861" LocationFloat="1351.06873, 111.019142" Dpi="254" Font="Segoe UI, 9pt" ForeColor="DimGray" Padding="5,5,0,0,254">
              <DataBindings>
                <Item1 Ref="81" Parameter="#Ref-11" PropertyName="Text" DataMember="email" />
              </DataBindings>
              <StylePriority Ref="82" UseFont="false" UseForeColor="false" />
            </Item8>
            <Item9 Ref="83" ControlType="XRLabel" Name="xrLabel10" Multiline="true" SizeF="381,50.37668" LocationFloat="1351.06873, 161.395844" Dpi="254" Font="Segoe UI, 9pt" ForeColor="DimGray" Padding="5,5,0,0,254">
              <DataBindings>
                <Item1 Ref="84" Parameter="#Ref-12" PropertyName="Text" DataMember="website" />
              </DataBindings>
              <StylePriority Ref="85" UseFont="false" UseForeColor="false" />
            </Item9>
            <Item10 Ref="86" ControlType="XRPictureBox" Name="xrPictureBox3" Image="iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAJlJREFUOE/djkEKwyAQRQdyMxci3rN3aEjBE7h3kTu02bid9g+Rikyi7TLCg8SZ95COTs55+nDbmfbrsVPkEAKDnyK17L0XhiOtvCwPYSiiya/nJnQjZ3I3MiIX1EiRrbU832dVrMEOdkuEUkpSNcaoggZ2nXMMFy9Y8dEG8F/TzmKMeMFKzBJRl3DfmdHVAi114GD2DfxHpjfEL6VwHw4G3gAAAABJRU5ErkJggg==" SizeF="42.333313,42.33333" LocationFloat="834.39, 119.0625" Dpi="254" />
            <Item11 Ref="87" ControlType="XRLabel" Name="xrLabel7" Multiline="true" SizeF="254,50.37668" LocationFloat="474.556641, 211.772522" Dpi="254" Font="Segoe UI, 9pt" ForeColor="DimGray" Padding="5,5,0,0,254">
              <DataBindings>
                <Item1 Ref="88" Parameter="#Ref-7" PropertyName="Text" DataMember="mobile3" />
              </DataBindings>
              <StylePriority Ref="89" UseFont="false" UseForeColor="false" />
            </Item11>
            <Item12 Ref="90" ControlType="XRLabel" Name="xrLabel6" Multiline="true" SizeF="254,50.37668" LocationFloat="474.556641, 161.395844" Dpi="254" Font="Segoe UI, 9pt" ForeColor="DimGray" Padding="5,5,0,0,254">
              <DataBindings>
                <Item1 Ref="91" Parameter="#Ref-6" PropertyName="Text" DataMember="mobile2" />
              </DataBindings>
              <StylePriority Ref="92" UseFont="false" UseForeColor="false" />
            </Item12>
            <Item13 Ref="93" ControlType="XRLabel" Name="xrLabel28" Multiline="true" SizeF="254,50.3766861" LocationFloat="474.556641, 111.019142" Dpi="254" Font="Segoe UI, 9pt" ForeColor="DimGray" Padding="5,5,0,0,254">
              <DataBindings>
                <Item1 Ref="94" Parameter="#Ref-5" PropertyName="Text" DataMember="mobile1" />
              </DataBindings>
              <StylePriority Ref="95" UseFont="false" UseForeColor="false" />
            </Item13>
            <Item14 Ref="96" ControlType="XRPictureBox" Name="xrPictureBox2" Image="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" Sizing="StretchImage" SizeF="355.599854,220.000015" LocationFloat="25.4000168, 33.9999847" Dpi="254" BorderColor="Silver" Borders="All" BorderWidth="3">
              <StylePriority Ref="97" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" />
            </Item14>
            <Item15 Ref="98" ControlType="XRPictureBox" Name="xrPictureBox1" Image="iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAOwwAADsMBx2+oZAAAAG1JREFUOE9jgIFv3779h2GoEHHgy5cvBkBN70EanZyc4IYQgd+D9IJsft/W1gbWTAoG6QHpBTsdJPDh/UcwRmbjwyB1IL14DQCx0TGyHO0NwIeJMgDGRqdh7FEXUNEFlCVl5MxEIoZkJsoAAwMAoS/FEKwh0MwAAAAASUVORK5CYII=" SizeF="42.3392334,42.33924" LocationFloat="432.2174, 119.067787" Dpi="254" />
          </Controls>
          <StylePriority Ref="99" UseBackColor="false" UseBorders="false" />
        </Item1>
      </Controls>
    </Item2>
    <Item3 Ref="100" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="203" TextAlignment="TopLeft" Dpi="254" Padding="0,0,0,0,254">
      <Controls>
        <Item1 Ref="101" ControlType="XRPageInfo" Name="xrPageInfo2" TextFormatString="صفحة {0} من{1}" TextAlignment="MiddleLeft" SizeF="325.4375,58.42" LocationFloat="0, 25.3999348" Dpi="254" StyleName="PageInfo" Padding="5,5,0,0,254">
          <StylePriority Ref="102" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="103" ControlType="XRLine" Name="xrLine1" LineWidth="3" SizeF="1124.47913,5.291666" LocationFloat="0, 12.1707687" Dpi="254" ForeColor="DimGray" BackColor="DimGray">
          <StylePriority Ref="104" UseForeColor="false" UseBackColor="false" />
        </Item2>
        <Item3 Ref="105" ControlType="XRLine" Name="xrLine2" LineWidth="3" SizeF="642.1969,5.291666" LocationFloat="1148.29163, 12.1707687" Dpi="254" ForeColor="255,19,133,214" BackColor="255,19,133,214">
          <StylePriority Ref="106" UseForeColor="false" UseBackColor="false" />
        </Item3>
        <Item4 Ref="107" ControlType="XRLabel" Name="xrLabel20" Text="Powered by vatoce" LockedInUserDesigner="true" TextAlignment="MiddleRight" SizeF="1362.07532,50.8000031" LocationFloat="420.574829, 25.3999348" Dpi="254" Font="Segoe UI, 9pt" Padding="5,5,0,0,254">
          <StylePriority Ref="108" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="109" ControlType="XRLabel" Name="xrLabel3" Text="vatoce.com" LockedInUserDesigner="true" TextAlignment="MiddleRight" SizeF="1268.0166,50.7999954" LocationFloat="515.2687, 71.11985" Dpi="254" Font="Segoe UI, 10pt" ForeColor="255,19,133,214" BackColor="Transparent" Padding="5,5,0,0,254">
          <StylePriority Ref="110" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item5>
      </Controls>
    </Item3>
    <Item4 Ref="111" ControlType="ReportHeaderBand" Name="reportHeaderBand1" HeightF="285.115021" Dpi="254">
      <Controls>
        <Item1 Ref="112" ControlType="XRLabel" Name="label4" TextAlignment="TopLeft" SizeF="639.4374,58.4200134" LocationFloat="252.478134, 218.977753" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,64,64,64" Padding="5,5,0,0,254">
          <DataBindings>
            <Item1 Ref="113" PropertyName="Text" DataMember="sales_main.people_data.Tax" />
          </DataBindings>
          <StylePriority Ref="114" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="115" ControlType="XRLabel" Name="label3" Text="ب. ضـ :" SizeF="220.360428,58.42" LocationFloat="17.8538837, 218.977432" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,30,30,30" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="Right">
          <StylePriority Ref="116" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" />
        </Item2>
        <Item3 Ref="117" ControlType="XRBarCode" Name="xrBarCode1" Module="5.08" AutoModule="true" Text="1254666" SizeF="548.166138,111.328323" LocationFloat="939.669067, 163.423325" Dpi="254" Padding="26,26,0,0,254">
          <Symbology Ref="118" Name="Code128" />
          <DataBindings>
            <Item1 Ref="119" PropertyName="Text" DataMember="sales_item.sales_main.smain_ID" />
          </DataBindings>
        </Item3>
        <Item4 Ref="120" ControlType="XRLabel" Name="xrLabel45" TextAlignment="TopLeft" SizeF="176.471527,58.42" LocationFloat="251.686508, 102.137741" Dpi="254" Font="Segoe UI, 11pt" ForeColor="255,64,64,64" BackColor="WhiteSmoke" Padding="5,5,0,0,254">
          <DataBindings>
            <Item1 Ref="121" PropertyName="Text" DataMember="sales_main.cust_ID" />
          </DataBindings>
          <StylePriority Ref="122" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="123" ControlType="XRLabel" Name="xrLabel39" Text="رقم الفاتورة :" SizeF="250.3003,58.41999" LocationFloat="938.877441, 102.137581" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,30,30,30" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="Right">
          <StylePriority Ref="124" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" />
        </Item5>
        <Item6 Ref="125" ControlType="XRLabel" Name="xrLabel40" SizeF="297.074219,58.4199677" LocationFloat="1189.96936, 102.137741" Dpi="254" Font="Segoe UI, 10pt" ForeColor="255,64,64,64" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="None">
          <DataBindings>
            <Item1 Ref="126" FormatString="{0}" PropertyName="Text" DataMember="smain_ID" />
          </DataBindings>
          <StylePriority Ref="127" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" />
        </Item6>
        <Item7 Ref="128" ControlType="XRLabel" Name="xrLabel27" TextAlignment="TopLeft" SizeF="298.160645,50.4824677" LocationFloat="1501.83936, 149.974213" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,64,64,64" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="None">
          <DataBindings>
            <Item1 Ref="129" FormatString="{0:yyyy-MM-dd}" PropertyName="Text" DataMember="sales_main.smain_Date" />
          </DataBindings>
          <StylePriority Ref="130" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="131" ControlType="XRLabel" Name="xrLabel26" Text="التاريخ :" TextAlignment="TopLeft" SizeF="297.8551,47.83663" LocationFloat="1502.145, 102.137581" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,30,30,30" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="None">
          <StylePriority Ref="132" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="133" ControlType="XRLabel" Name="xrLabel18" Text="طباعة :" TextAlignment="TopLeft" SizeF="112.370728,45.1909027" LocationFloat="1503.08752, 229.56076" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,30,30,30" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="Right">
          <StylePriority Ref="134" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="135" ControlType="XRLabel" Name="xrLabel22" TextAlignment="TopLeft" SizeF="316.009277,58.4199524" LocationFloat="575.90625, 160.557632" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,64,64,64" Padding="5,5,0,0,254">
          <DataBindings>
            <Item1 Ref="136" PropertyName="Text" DataMember="sales_main.people_data.pepole_Address" />
          </DataBindings>
          <StylePriority Ref="137" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="138" ControlType="XRLabel" Name="xrLabel17" Text="التفاصيل :" SizeF="220.360428,58.42" LocationFloat="17.8538837, 160.557632" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,30,30,30" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="Right">
          <StylePriority Ref="139" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" />
        </Item11>
        <Item12 Ref="140" ControlType="XRLabel" Name="xrLabel16" TextAlignment="TopLeft" SizeF="322.6366,58.4200134" LocationFloat="252.478134, 160.557785" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,64,64,64" Padding="5,5,0,0,254">
          <DataBindings>
            <Item1 Ref="141" PropertyName="Text" DataMember="sales_main.people_data.pepole_Mobile" />
          </DataBindings>
          <StylePriority Ref="142" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="143" ControlType="XRLabel" Name="xrLabel15" TextAlignment="TopLeft" SizeF="463.757263,58.42" LocationFloat="428.1584, 102.137665" Dpi="254" Font="Segoe UI, 11pt" ForeColor="255,64,64,64" Padding="5,5,0,0,254">
          <DataBindings>
            <Item1 Ref="144" PropertyName="Text" DataMember="sales_main.people_data.pepole_Name" />
          </DataBindings>
          <StylePriority Ref="145" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="146" ControlType="XRLabel" Name="xrLabel14" Text="اسم العميل :" SizeF="220.360428,58.4200058" LocationFloat="17.8538837, 102.137581" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,30,30,30" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="Right">
          <StylePriority Ref="147" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" />
        </Item14>
        <Item15 Ref="148" ControlType="XRShape" Name="xrShape5" Angle="90" Stretch="true" FillColor="255,19,133,214" SizeF="28.300354,88.90003" LocationFloat="571.5, 0" Dpi="254" ForeColor="255,19,133,214" BackColor="Transparent" BorderColor="255,19,133,214">
          <Shape Ref="149" ShapeName="Polygon" />
          <StylePriority Ref="150" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item15>
        <Item16 Ref="151" ControlType="XRPageInfo" Name="xrPageInfo3" PageInfo="DateTime" TextFormatString="{0:yyyy-MM-dd}" TextAlignment="TopLeft" SizeF="182.889038,47.8367157" LocationFloat="1617.111, 229.56076" Dpi="254" StyleName="PageInfo" Font="Segoe UI, 9pt" ForeColor="255,64,64,64" Padding="5,5,0,0,254">
          <StylePriority Ref="152" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="153" ControlType="XRLabel" Name="xrLabel21" Text="فاتورة مبيعات" TextAlignment="MiddleCenter" SizeF="575.1149,88.90003" LocationFloat="0, 0" Dpi="254" Font="Segoe UI, 14pt" ForeColor="White" BackColor="255,19,133,214" Padding="5,5,0,0,254">
          <StylePriority Ref="154" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="155" ControlType="XRShape" Name="xrShape3" Angle="90" Stretch="true" FillColor="White" SizeF="28.300354,88.90003" LocationFloat="586.74, 0.008397421" Dpi="254" ForeColor="White" BackColor="Transparent" BorderColor="White">
          <Shape Ref="156" ShapeName="Polygon" />
          <StylePriority Ref="157" UseForeColor="false" UseBackColor="false" UseBorderColor="false" />
        </Item18>
        <Item19 Ref="158" ControlType="XRLabel" Name="xrLabel19" TextAlignment="MiddleCenter" SizeF="1210.12354,88.9" LocationFloat="589.876465, 0.00839742" Dpi="254" Font="Segoe UI, 14.25pt" ForeColor="White" BackColor="255,240,240,240" Padding="5,5,0,0,254">
          <StylePriority Ref="159" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item19>
      </Controls>
    </Item4>
    <Item5 Ref="160" ControlType="GroupHeaderBand" Name="groupHeaderBand1" GroupUnion="WithFirstDetail" HeightF="107.520813" Dpi="254">
      <Controls>
        <Item1 Ref="161" ControlType="XRTable" Name="xrTable1" SizeF="1793.98511,102.129181" LocationFloat="1.58388269, 0" Dpi="254" Font="Segoe UI, 7.5pt">
          <Rows>
            <Item1 Ref="162" ControlType="XRTableRow" Name="xrTableRow1" Weight="1" Dpi="254">
              <Cells>
                <Item1 Ref="163" ControlType="XRTableCell" Name="xrTableCell1" Weight="0.018571251536477769" Text="م" TextAlignment="MiddleCenter" Dpi="254" StyleName="DetailCaption3" Font="Segoe UI, 9.75pt" ForeColor="WhiteSmoke" BackColor="255,48,65,75">
                  <StylePriority Ref="164" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="165" ControlType="XRTableCell" Name="xrTableCell16" Weight="0.056912264837707525" Text="كود" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt" ForeColor="WhiteSmoke" BackColor="255,48,65,75">
                  <StylePriority Ref="166" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="167" ControlType="XRTableCell" Name="xrTableCell2" Weight="0.095814773782893492" Text="صنف" TextAlignment="MiddleCenter" Dpi="254" StyleName="DetailCaption3" Font="Segoe UI, 8pt" ForeColor="WhiteSmoke" BackColor="255,19,133,214">
                  <StylePriority Ref="168" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="169" ControlType="XRTableCell" Name="xrTableCell3" Weight="0.031796919873767454" Text="الوحدة" TextAlignment="MiddleCenter" Dpi="254" StyleName="DetailCaption3" Font="Segoe UI, 8pt" ForeColor="WhiteSmoke" BackColor="255,48,65,75">
                  <StylePriority Ref="170" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="171" ControlType="XRTableCell" Name="xrTableCell5" Weight="0.03131740454046885" Text="كمية" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt" ForeColor="WhiteSmoke" BackColor="255,48,65,75">
                  <StylePriority Ref="172" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="173" ControlType="XRTableCell" Name="xrTableCell4" Weight="0.033088712009074234" Text="سعر" TextAlignment="MiddleCenter" Dpi="254" StyleName="DetailCaption3" Font="Segoe UI, 8pt" ForeColor="WhiteSmoke" BackColor="255,48,65,75">
                  <StylePriority Ref="174" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="175" ControlType="XRTableCell" Name="xrTableCell9" Weight="0.028272034771571086" Text="الضريبة" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt" ForeColor="WhiteSmoke" BackColor="255,48,65,75">
                  <StylePriority Ref="176" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item7>
                <Item8 Ref="177" ControlType="XRTableCell" Name="xrTableCell13" Weight="0.029009593279248565" Text="خصم" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt" ForeColor="WhiteSmoke" BackColor="255,48,65,75">
                  <StylePriority Ref="178" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item8>
                <Item9 Ref="179" ControlType="XRTableCell" Name="xrTableCell7" Weight="0.02914897449752115" Text="بونص" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt" ForeColor="WhiteSmoke" BackColor="255,48,65,75">
                  <StylePriority Ref="180" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item9>
                <Item10 Ref="181" ControlType="XRTableCell" Name="tableCell1" Weight="0.043760755262913224" Text="الباتش" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt" ForeColor="WhiteSmoke" BackColor="255,48,65,75">
                  <StylePriority Ref="182" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item10>
                <Item11 Ref="183" ControlType="XRTableCell" Name="xrTableCell20" Weight="0.043760755262913224" Text="تاريخ الصلاحية" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt" ForeColor="WhiteSmoke" BackColor="255,48,65,75">
                  <StylePriority Ref="184" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item11>
                <Item12 Ref="185" ControlType="XRTableCell" Name="xrTableCell17" Weight="0.04163332711148874" Text="اجمالي" TextAlignment="MiddleCenter" Dpi="254" Font="Segoe UI, 8pt" ForeColor="WhiteSmoke" BackColor="255,48,65,75">
                  <StylePriority Ref="186" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item12>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="187" UseFont="false" />
        </Item1>
      </Controls>
    </Item5>
    <Item6 Ref="188" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="488.437561" Dpi="254">
      <Controls>
        <Item1 Ref="189" ControlType="XRLabel" Name="Notes" Multiline="true" TextTrimming="Word" TextAlignment="TopLeft" SizeF="1789.32922,58.4200134" LocationFloat="6.23976755, 424.920929" Dpi="254" Font="Segoe UI, 8pt" Padding="5,5,0,0,254" BorderColor="DimGray">
          <DataBindings>
            <Item1 Ref="190" Parameter="#Ref-17" PropertyName="Text" DataMember="Invoicenotes" />
          </DataBindings>
          <StylePriority Ref="191" UseFont="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="192" ControlType="XRLabel" Name="xrLabel44" SizeF="555.8368,58.4200439" LocationFloat="271.603821, 366.5009" Dpi="254" Font="Segoe UI, 12pt" Padding="5,5,0,0,254">
          <DataBindings>
            <Item1 Ref="193" PropertyName="Text" DataMember="sales_main.emp_data3.empdata_Name" />
          </DataBindings>
          <StylePriority Ref="194" UseFont="false" />
        </Item2>
        <Item3 Ref="195" ControlType="XRLabel" Name="xrLabel43" Text="كاشير :" SizeF="253.999985,58.4200134" LocationFloat="17.6037388, 366.5009" Dpi="254" Font="Segoe UI, 12pt" Padding="5,5,0,0,254">
          <StylePriority Ref="196" UseFont="false" />
        </Item3>
        <Item4 Ref="197" ControlType="XRPanel" Name="xrPanel3" SizeF="966.62146,146.855057" LocationFloat="7.54144859, 219.645828" Dpi="254" BackColor="WhiteSmoke" BorderColor="DarkGray" Borders="All">
          <Controls>
            <Item1 Ref="198" ControlType="XRLabel" Name="xrLabel46" TextAlignment="MiddleCenter" SizeF="893.433655,77.22995" LocationFloat="56.68388, 69.62511" Dpi="254" Font="Segoe UI, 8pt" ForeColor="255,64,64,64" BackColor="Transparent" Padding="5,5,0,0,254" BorderColor="Transparent" Borders="All">
              <StylePriority Ref="199" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
            <Item2 Ref="200" ControlType="XRLabel" Name="xrLabel24" TextAlignment="MiddleCenter" SizeF="298.951965,59.0000343" LocationFloat="649.4875, 10.6249971" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,64,64,64" BackColor="Transparent" Padding="5,5,0,0,254" BorderColor="Transparent" Borders="All">
              <DataBindings>
                <Item1 Ref="201" PropertyName="Text" DataMember="sales_main.ad_branches1.ad_company_data1.comp_Currency" />
              </DataBindings>
              <StylePriority Ref="202" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item2>
            <Item3 Ref="203" ControlType="XRLabel" Name="xrLabel23" Text="الرصيد الحالي" TextAlignment="MiddleCenter" SizeF="266.081635,59.0000343" LocationFloat="56.1504822, 10.6249971" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,30,30,30" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="Right">
              <StylePriority Ref="204" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item3>
            <Item4 Ref="205" ControlType="XRLabel" Name="xrLabel41" TextAlignment="MiddleCenter" SizeF="300.6297,59.0000343" LocationFloat="348.306915, 10.6249971" Dpi="254" Font="Segoe UI, 9pt, style=Bold" ForeColor="255,64,64,64" BackColor="Transparent" Padding="5,5,0,0,254" BorderColor="Transparent" Borders="All">
              <DataBindings>
                <Item1 Ref="206" Parameter="#Ref-16" FormatString="{0:#.00}" PropertyName="Text" DataMember="CustomerBalance" />
              </DataBindings>
              <StylePriority Ref="207" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item4>
          </Controls>
          <StylePriority Ref="208" UseBackColor="false" UseBorderColor="false" UseBorders="false" />
        </Item4>
        <Item5 Ref="209" ControlType="XRLabel" Name="xrLabel33" Text="الضريبة :" TextAlignment="MiddleLeft" SizeF="266.081665,76.2" LocationFloat="1181.523, 177.799866" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,30,30,30" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="None">
          <StylePriority Ref="210" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="211" ControlType="XRLabel" Name="xrLabel34" TextAlignment="MiddleRight" SizeF="317.705444,76.20001" LocationFloat="1474.41577, 177.799789" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,64,64,64" Padding="5,5,0,0,254">
          <DataBindings>
            <Item1 Ref="212" FormatString="{0:#.00}" PropertyName="Text" DataMember="sales_main.smain_TaxValue" />
          </DataBindings>
          <StylePriority Ref="213" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="214" ControlType="XRLabel" Name="xrLabel31" Text="الخصم :" TextAlignment="MiddleLeft" SizeF="266.081665,76.2000046" LocationFloat="1181.523, 101.599983" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,30,30,30" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="None">
          <StylePriority Ref="215" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="216" ControlType="XRLabel" Name="xrLabel32" TextAlignment="MiddleRight" SizeF="317.705322,76.1999741" LocationFloat="1474.41577, 101.599983" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,64,64,64" Padding="5,5,0,0,254">
          <DataBindings>
            <Item1 Ref="217" FormatString="{0:#.00}" PropertyName="Text" DataMember="sales_main.smain_DiscValue" />
          </DataBindings>
          <StylePriority Ref="218" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="219" ControlType="XRLabel" Name="xrLabel30" TextAlignment="MiddleRight" SizeF="317.705322,76.2" LocationFloat="1474.41528, 25.3999348" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,64,64,64" Padding="5,5,0,0,254">
          <DataBindings>
            <Item1 Ref="220" FormatString="{0:#.00}" PropertyName="Text" DataMember="sales_main.smain_TotalValue" />
          </DataBindings>
          <StylePriority Ref="221" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="222" ControlType="XRLabel" Name="xrLabel29" Text="الاجمالي :" TextAlignment="MiddleLeft" SizeF="266.081665,76.2000046" LocationFloat="1181.523, 25.4000168" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,30,30,30" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="None">
          <StylePriority Ref="223" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="224" ControlType="XRLabel" Name="xrLabel1" Text="توقيع" SizeF="254,58.4200134" LocationFloat="1099.02417, 366.5009" Dpi="254" Font="Segoe UI, 12pt" Padding="5,5,0,0,254">
          <StylePriority Ref="225" UseFont="false" />
        </Item11>
        <Item12 Ref="226" ControlType="XRLabel" Name="xrLabel13" CanGrow="false" TextAlignment="MiddleCenter" WordWrap="false" SizeF="317.500366,71.750946" LocationFloat="1473.2, 269.240021" Dpi="254" Font="Segoe UI, 12pt, style=Bold" ForeColor="WhiteSmoke" Padding="5,5,0,0,254">
          <Summary Ref="227" Func="Count" />
          <DataBindings>
            <Item1 Ref="228" FormatString="{0:#.00}" PropertyName="Text" DataMember="sales_main.smain_NetValue" />
          </DataBindings>
          <StylePriority Ref="229" UseFont="false" UseForeColor="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="230" ControlType="XRLabel" Name="xrLabel12" Text="المبلغ المطلوب" SizeF="265.869873,64.76193" LocationFloat="1148.715, 271.78" Dpi="254" ForeColor="WhiteSmoke" Padding="5,5,0,0,254">
          <StylePriority Ref="231" UseForeColor="false" />
        </Item13>
        <Item14 Ref="232" ControlType="XRLabel" Name="xrLabel2" TextAlignment="MiddleCenter" SizeF="697.76,88.9000549" LocationFloat="1098.23254, 259.079956" Dpi="254" Font="Segoe UI, 14.25pt" ForeColor="White" BackColor="255,19,133,214" Padding="5,5,0,0,254">
          <StylePriority Ref="233" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="234" ControlType="XRPanel" Name="xrPanel1" SizeF="965.8295,191.416687" LocationFloat="8.333309, 9.999955" Dpi="254" BackColor="WhiteSmoke" BorderColor="DarkGray" Borders="All">
          <Controls>
            <Item1 Ref="235" ControlType="XRLabel" Name="xrLabel42" TextAlignment="MiddleCenter" SizeF="300.0495,76.19997" LocationFloat="652.443054, 101.599823" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,64,64,64" BackColor="Transparent" Padding="5,5,0,0,254" BorderColor="Transparent" Borders="All">
              <DataBindings>
                <Item1 Ref="236" PropertyName="Text" DataMember="sales_main.ad_branches1.ad_company_data1.comp_Currency" />
              </DataBindings>
              <StylePriority Ref="237" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
            <Item2 Ref="238" ControlType="XRLabel" Name="xrLabel25" TextAlignment="MiddleCenter" SizeF="300.629639,76.2000046" LocationFloat="652.6546, 25.4000168" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,64,64,64" BackColor="Transparent" Padding="5,5,0,0,254" BorderColor="Transparent" Borders="All">
              <DataBindings>
                <Item1 Ref="239" PropertyName="Text" DataMember="sales_main.ad_branches1.ad_company_data1.comp_Currency" />
              </DataBindings>
              <StylePriority Ref="240" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item2>
            <Item3 Ref="241" ControlType="XRLabel" Name="xrLabel35" Text="المدفوع" TextAlignment="MiddleLeft" SizeF="266.0816,76.2000046" LocationFloat="58.5252533, 25.4000168" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,30,30,30" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="None">
              <StylePriority Ref="242" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item3>
            <Item4 Ref="243" ControlType="XRLabel" Name="xrLabel38" Text="الباقي :" TextAlignment="MiddleLeft" SizeF="266.0816,76.2" LocationFloat="58.5252533, 101.599823" Dpi="254" Font="Segoe UI, 9pt" ForeColor="255,30,30,30" Padding="5,5,0,0,254" BorderColor="DimGray" Borders="None">
              <StylePriority Ref="244" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item4>
            <Item5 Ref="245" ControlType="XRLabel" Name="xrLabel37" TextAlignment="MiddleLeft" SizeF="300.757965,76.20003" LocationFloat="351.473541, 101.599823" Dpi="254" Font="Segoe UI, 11pt, style=Bold" ForeColor="255,64,64,64" Padding="5,5,0,0,254" Borders="None">
              <DataBindings>
                <Item1 Ref="246" FormatString="{0:#.00}" PropertyName="Text" DataMember="sales_main.smain_ReminValue" />
              </DataBindings>
              <StylePriority Ref="247" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item5>
            <Item6 Ref="248" ControlType="XRLabel" Name="xrLabel36" TextAlignment="MiddleLeft" SizeF="301.180878,76.19988" LocationFloat="351.473541, 25.4000168" Dpi="254" Font="Segoe UI, 11pt, style=Bold" ForeColor="255,64,64,64" Padding="5,5,0,0,254" Borders="None">
              <DataBindings>
                <Item1 Ref="249" FormatString="{0:#.00}" PropertyName="Text" DataMember="sales_main.smain_PaidValue" />
              </DataBindings>
              <StylePriority Ref="250" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item6>
          </Controls>
          <StylePriority Ref="251" UseBackColor="false" UseBorderColor="false" UseBorders="false" />
        </Item15>
      </Controls>
    </Item6>
  </Bands>
  <StyleSheet>
    <Item1 Ref="252" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="253" Name="DetailCaption3" BorderStyle="Inset" Padding="15,15,0,0,254" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="254" Name="DetailData3" BorderStyle="Inset" Padding="15,15,0,0,254" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="255" Name="DetailData3_Odd" BorderStyle="Inset" Padding="15,15,0,0,254" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="256" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="257" Name="PageInfo" BorderStyle="Inset" Padding="5,5,0,0,254" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ObjectStorage>
    <Item1 ObjectType="DevExpress.XtraReports.Serialization.ObjectStorageInfo, DevExpress.XtraReports.v17.2" Ref="13" Content="System.Int32" Type="System.Type" />
    <Item2 Ref="0" ObjectType="MrSales.MrSModels.TablesTranslations.TablesTranslations.CustomNames.CustomNames,MrSales" Name="objectDataSource1" Base64="PE9iamVjdERhdGFTb3VyY2U+PE5hbWU+b2JqZWN0RGF0YVNvdXJjZTE8L05hbWU+PERhdGFTb3VyY2UgVHlwZT0iTXJTYWxlcy5NclNNb2RlbHMuc2FsZXNfaXRlbSwgTXJTYWxlcywgVmVyc2lvbj01LjMuMS4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPW51bGwiIC8+PC9PYmplY3REYXRhU291cmNlPg==" />
  </ObjectStorage>
</XtraReportsLayoutSerializer>