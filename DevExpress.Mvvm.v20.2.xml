<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.Mvvm.v20.2</name>
  </assembly>
  <members>
    <member name="N:DevExpress.Mvvm">
      <summary>
        <para>Contains controls for building MVVM-aware applications.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.BindableBase">
      <summary>
        <para>Provides support for the INotifyPropertyChanged interface and capabilities for easy implementation of bindable properties with the GetProperty and SetProperty methods.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.BindableBase.GetPropertyName``1(System.Linq.Expressions.Expression{System.Func{``0}})">
      <summary>
        <para>Returns the name of a property identified by a lambda expression.</para>
      </summary>
      <param name="expression">A lambda expression selecting the property.</param>
      <typeparam name="T"></typeparam>
      <returns>The name of the property accessed by expression.</returns>
    </member>
    <member name="E:DevExpress.Mvvm.BindableBase.PropertyChanged">
      <summary>
        <para>Occurs when a property value changes.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Mvvm.DataAnnotations">
      <summary>
        <para>Contains classes that support data annotations and DevExpress Fluent API functionality.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute">
      <summary>
        <para>Provides settings for generating bindable properties by the POCO mechanism.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute.#ctor(System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute"/> class with the specified settings.</para>
      </summary>
      <param name="isBindable">true, to make the property bindable; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute.IsBindable">
      <summary>
        <para>Gets whether the property is bindable.</para>
      </summary>
      <value>true, if the property is bindable; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute.OnPropertyChangedMethodName">
      <summary>
        <para>Gets or sets the name of the function that is invoked after the property has been changed.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the name of the function.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute.OnPropertyChangingMethodName">
      <summary>
        <para>Gets or sets the name of the function that is invoked when the property is being changed.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the name of the function.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.CommandAttribute">
      <summary>
        <para>Provides settings for generating commands by the POCO mechanism.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.CommandAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandAttribute.#ctor(System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.CommandAttribute"/> class with specified settings.</para>
      </summary>
      <param name="isCommand">true, to generate a command from the method; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.CommandAttribute.CanExecuteMethodName">
      <summary>
        <para>Gets or sets the name of the function that defines whether the command can be executed.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the function name.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.CommandAttribute.IsCommand">
      <summary>
        <para>Gets whether to generate a command from the method.</para>
      </summary>
      <value>true, to generate a command from the method; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.CommandAttribute.Name">
      <summary>
        <para>Gets or sets the name of the generated command.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the name of the generated command.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.CommandAttribute.UseCommandManager">
      <summary>
        <para>Gets or sets whether to use a function that manages the command execution.</para>
      </summary>
      <value>true, to use the command manager; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilder`1">
      <summary>
        <para>Provides the command generating functionality.</para>
      </summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2">
      <summary>
        <para>Provides base command generating functionality.</para>
      </summary>
      <typeparam name="T"></typeparam>
      <typeparam name="TBuilder"></typeparam>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.AutoGenerated">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.Description(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="description"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.DisplayName(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="name"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.DisplayShortName(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="shortName"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.DoNotScaffold">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.DoNotScaffoldDetailCollection">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.EndCommand">
      <summary>
        <para>Moves the method call chain from the command metadata builder to the metadata builder.</para>
      </summary>
      <returns>The metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.ImageName(System.String)">
      <summary>
        <para>Configures the image name.</para>
      </summary>
      <param name="imageName">An image name.</param>
      <returns>The metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.ImageUri(System.String)">
      <summary>
        <para>Configures the image Uri.</para>
      </summary>
      <param name="imageUri">An image Uri.</param>
      <returns>The metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.ImageUriLarge(System.String)">
      <summary>
        <para>Configures the large image Uri.</para>
      </summary>
      <param name="uri">An image Uri.</param>
      <returns>The metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.ImageUriSmall(System.String)">
      <summary>
        <para>Configures the small image Uri.</para>
      </summary>
      <param name="uri">An image Uri.</param>
      <returns>The metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.LocatedAt(System.Int32,DevExpress.Mvvm.DataAnnotations.PropertyLocation)">
      <summary>
        <para></para>
      </summary>
      <param name="position"></param>
      <param name="propertyLocation"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.NotAutoGenerated">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.Parameter``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary>
        <para>Specifies the ViewModel property.</para>
      </summary>
      <param name="propertyExpression">A lambda expression that returns a ViewModel property.</param>
      <typeparam name="TParameter"></typeparam>
      <returns>The metadata builder instance.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.CommandMethodMetadataBuilder`1">
      <summary>
        <para>Provides the command generating functionality.</para>
      </summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode">
      <summary>
        <para>Lists the values used to specify the formatting applied to date-time values.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode.Date">
      <summary>
        <para>Displays a date.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode.DateTime">
      <summary>
        <para>Displays a date and time.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode.Time">
      <summary>
        <para>Displays a time.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.DateTimeMaskAttribute">
      <summary>
        <para>Provides date-time mask attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.DateTimeMaskAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.DateTimeMaskAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.DateTimeMaskAttribute.AutomaticallyAdvanceCaret">
      <summary>
        <para>Specifies whether to enable the caret automatic movement feature.</para>
      </summary>
      <value>true, to enable the caret automatic movement feature; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.DateTimeMaskAttribute.Culture">
      <summary>
        <para>Specifies the name of the culture whose settings are used by the mask.</para>
      </summary>
      <value>A culture&#39;s name.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.DXImageAttribute">
      <summary>
        <para>Provides the ability to associate an image file with a data item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.DXImageAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.DXImageAttribute"/> class with the specified owner.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.DXImageAttribute.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.DXImageAttribute"/> class with the specified image name.</para>
      </summary>
      <param name="imageName">The image name.</param>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.DXImageAttribute.ImageName">
      <summary>
        <para>Gets the image name.</para>
      </summary>
      <value>The image name.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.DXImageAttribute.LargeImageUri">
      <summary>
        <para>Gets or sets the absolute Uri that defines the location of an image file.</para>
      </summary>
      <value>A string that is a Uri to the image file.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.DXImageAttribute.SmallImageUri">
      <summary>
        <para>Gets or sets the Uri that defines the relative location of an image file.</para>
      </summary>
      <value>A string that is a Uri to the image file.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.HiddenAttribute">
      <summary>
        <para>Configures the property to be hidden.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.HiddenAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.HiddenAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.HiddenAttribute.#ctor(System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="hidden"></param>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.HiddenAttribute.Hidden">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.IMetadataLocator">
      <summary>
        <para>Used to implement metadata locators.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.IMetadataLocator.GetMetadataTypes(System.Type)">
      <summary>
        <para>Returns a list of types of metadata classes for the specified type.</para>
      </summary>
      <param name="type">A type for which to get a list of types of metadata classes.</param>
      <returns>A list of types of metadata classes for the specified type.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.IMetadataProvider`1">
      <summary>
        <para>Provides methods to build metadata.</para>
      </summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.IMetadataProvider`1.BuildMetadata(DevExpress.Mvvm.DataAnnotations.MetadataBuilder{`0})">
      <summary>
        <para>Builds data source configurations.</para>
      </summary>
      <param name="builder">A metadata builder instance.</param>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.InstanceInitializerAttribute">
      <summary>
        <para>Provides attributes for the instance initializer builder.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.InstanceInitializerAttribute.#ctor(System.Type)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.InstanceInitializerAttribute"/> class.</para>
      </summary>
      <param name="type">A <see cref="T:System.Type"/> object that specifies the instance type.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.InstanceInitializerAttribute.#ctor(System.Type,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.InstanceInitializerAttribute"/> class.</para>
      </summary>
      <param name="type">A <see cref="T:System.Type"/> object that specifies the instance type.</param>
      <param name="name">A <see cref="T:System.String"/> value that specifies the instance name.</param>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.LayoutBuilderBase`2">
      <summary>
        <para>Provides the base functionality for building layouts.</para>
      </summary>
      <typeparam name="T"></typeparam>
      <typeparam name="TBuilder"></typeparam>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.MaskAttributeBase">
      <summary>
        <para>Provides base mask attributes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.MaskAttributeBase.Mask">
      <summary>
        <para>Specifies a mask expression.</para>
      </summary>
      <value>A string representing a mask expression.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.MaskAttributeBase.UseAsDisplayFormat">
      <summary>
        <para>Specifies whether display values are still formatted using the mask when the editor is not focused. This is a dependency property.</para>
      </summary>
      <value>true if the mask settings are used to format display values when the editor is not focused; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1">
      <summary>
        <para>The base metadata builder class.</para>
      </summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.Command(System.Linq.Expressions.Expression{System.Func{`0,System.Windows.Input.ICommand}})">
      <summary>
        <para>Returns a command metadata builder for a command property.</para>
      </summary>
      <param name="propertyExpression">A lambda expression which specifies a command property.</param>
      <returns>The command metadata builder.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.CommandFromMethod(System.Linq.Expressions.Expression{System.Action{`0}})">
      <summary>
        <para>Returns a command method metadata builder for a method from which the command property is to be generated.</para>
      </summary>
      <param name="methodExpression">A lambda expression which specifies a command method.</param>
      <returns>The command method metadata builder.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.CommandFromMethod(System.Linq.Expressions.Expression{System.Func{`0,System.Threading.Tasks.Task}})">
      <summary>
        <para></para>
      </summary>
      <param name="asyncMethodExpression"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.DataFormLayout">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.DisplayName(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="name"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.Group(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="groupName"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.Property``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary>
        <para>Initializes a property metadata builder.</para>
      </summary>
      <param name="propertyExpression">A lambda expression that returns a ViewModel property.</param>
      <typeparam name="TProperty"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.Property``1(System.String)">
      <summary>
        <para>Initializes a property metadata builder.</para>
      </summary>
      <param name="propertyName">The name of a ViewModel property.</param>
      <typeparam name="TProperty"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.TableLayout">
      <summary>
        <para>Initializes a table group container builder.</para>
      </summary>
      <returns>The table group container builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.ToolBarLayout">
      <summary>
        <para>Initializes a toolbar layout builder instance.</para>
      </summary>
      <returns>The toolbar layout builder instance.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.MetadataLocator">
      <summary>
        <para>Allows you to register metadata classes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataLocator.AddMetadata(System.Type)">
      <summary>
        <para>Registers a metadata class.</para>
      </summary>
      <param name="metadataType">The metadata class.</param>
      <returns>The metadata locator instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataLocator.AddMetadata(System.Type,System.Type)">
      <summary>
        <para>Registers a metadata class.</para>
      </summary>
      <param name="type">The data class.</param>
      <param name="metadataType">The metadata class.</param>
      <returns>The metadata locator instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataLocator.AddMetadata``1">
      <summary>
        <para>Registers a metadata class.</para>
      </summary>
      <typeparam name="TMetadata"></typeparam>
      <returns>The metadata locator instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataLocator.AddMetadata``2">
      <summary>
        <para>Registers a metadata class.</para>
      </summary>
      <typeparam name="T"></typeparam>
      <typeparam name="TMetadata"></typeparam>
      <returns>The metadata locator instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataLocator.Create">
      <summary>
        <para>Creates a metadata locator instance.</para>
      </summary>
      <returns>The metadata locator instance.</returns>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.MetadataLocator.Default">
      <summary>
        <para>The metadata to be registered.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.NumericMaskAttribute">
      <summary>
        <para>Provides numeric mask attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericMaskAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.NumericMaskAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.NumericMaskAttribute.Culture">
      <summary>
        <para>Specifies the name of the culture whose settings are used by the mask.</para>
      </summary>
      <value>A culture&#39;s name.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions">
      <summary>
        <para>Contains methods specific to numeric unsigned data types.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.UInt16}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.UInt32}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.UInt64}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.UInt16})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.UInt32})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.UInt64})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.UInt16}},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.UInt32}},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.UInt64}},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.UInt16},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.UInt32},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.UInt64},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.UInt16}},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.UInt32}},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.UInt64}},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.UInt16},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.UInt32},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.UInt64},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.POCOViewModelAttribute">
      <summary>
        <para>Provides settings for generating POCO View models.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.POCOViewModelAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.POCOViewModelAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.POCOViewModelAttribute.ImplementIDataErrorInfo">
      <summary>
        <para>Specifies whether to implement the IDataErrorInfo interface.The IDataErrorInfo interface provides the functionality to offer custom error information that a user interface can bind to.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.POCOViewModelAttribute.ImplementINotifyPropertyChanging">
      <summary>
        <para>Specifies whether to implement the INotifyPropertyChanging interface.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.POCOViewModelAttribute.InvokeOnPropertyChangedMethodBeforeRaisingINPC">
      <summary>
        <para>Specifies the order of invoking methods and events. If enabled, the OnPropertyChanging\OnPropertyChanged methods are called before invoking the corresponding PropertyChanging\PropertyChanged events. Otherwise, events are invoked before calling corresponding methods.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.PredefinedMasks">
      <summary>
        <para>Contains predefined mask expressions.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime.FullDateLongTime">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime.FullDateShortTime">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime.GeneralDateLongTime">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime.GeneralDateShortTime">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime.LongDate">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime.LongTime">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime.MonthDay">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime.RFC1123">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime.ShortDate">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime.ShortTime">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime.SortableDateTime">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime.UniversalSortableDateTime">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.DateTime.YearMonth">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.Numeric">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.Numeric.Currency">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.Numeric.Decimal">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.Numeric.FixedPoint">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.Numeric.Number">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.Numeric.Percent">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PredefinedMasks.Numeric.PercentFractional">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.PropertyLocation">
      <summary>
        <para>Lists values that specify whether the property is positioned before or after properties without the specified location.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PropertyLocation.AfterPropertiesWithoutSpecifiedLocation">
      <summary>
        <para>The property is positioned after properties without the specified location.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PropertyLocation.BeforePropertiesWithoutSpecifiedLocation">
      <summary>
        <para>The property is positioned before properties without the specified location.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder`2">
      <summary>
        <para>Provides data source configuration functionality.</para>
      </summary>
      <typeparam name="T"></typeparam>
      <typeparam name="TProperty"></typeparam>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderBase`3">
      <summary>
        <para>The base class for the data source configuration functionality.</para>
      </summary>
      <typeparam name="T"></typeparam>
      <typeparam name="TProperty"></typeparam>
      <typeparam name="TBuilder"></typeparam>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions">
      <summary>
        <para>Provides additional data source configuration functionality.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CreditCardDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String},System.Func{System.String})">
      <summary>
        <para>Configures the property editor to display credit card data values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <param name="errorMessageAccessor">A function that returns an error message.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Byte})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Decimal})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Double})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Int16})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Int32})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Int64})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Decimal}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Double}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Int16}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Int32}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Int64}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Single}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Single})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.DateTimeDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.DateTime},DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode)">
      <summary>
        <para>Configures the property editor to display date-time values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <param name="displayMode">The required <see cref="T:DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode"/> value.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.DateTimeDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.DateTime}},DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode)">
      <summary>
        <para>Configures the property editor to display date-time values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <param name="displayMode">The required <see cref="T:DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode"/> value.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.DateTimeMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.DateTime},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.EmailAddressDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String},System.Func{System.String})">
      <summary>
        <para>Configures the property editor to display email address values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <param name="errorMessageAccessor">A function that returns an error message.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Byte},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Int16},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Int32},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Int64},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Byte}},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Int16}},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Int32}},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.EnumDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Int64}},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="enumType"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.ImageUrlDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String})">
      <summary>
        <para>Configures the property editor to display image url values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.InRange``2(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,``1},``1,``1,System.Func{``1,System.String})">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="minimum"></param>
      <param name="maximum"></param>
      <param name="errorMessageAccessor"></param>
      <typeparam name="T"></typeparam>
      <typeparam name="TProperty"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.InRange``2(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,``1},``1,``1,System.Func{System.String})">
      <summary>
        <para>Specifies property value boundaries.</para>
      </summary>
      <param name="builder">A property metadata builder instance.</param>
      <param name="minimum">The initial boundary.</param>
      <param name="maximum">The final boundary.</param>
      <param name="errorMessageAccessor">A function that returns an error message.</param>
      <typeparam name="T"></typeparam>
      <typeparam name="TProperty"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.InRange``2(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{``1}},System.Nullable{``1},System.Nullable{``1},System.Func{``1,System.String})">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="minimum"></param>
      <param name="maximum"></param>
      <param name="errorMessageAccessor"></param>
      <typeparam name="T"></typeparam>
      <typeparam name="TProperty"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.InRange``2(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{``1}},System.Nullable{``1},System.Nullable{``1},System.Func{System.String})">
      <summary>
        <para>Specifies property value boundaries.</para>
      </summary>
      <param name="builder">A property metadata builder instance.</param>
      <param name="minimum">The initial boundary.</param>
      <param name="maximum">The final boundary.</param>
      <param name="errorMessageAccessor">A function that returns an error message.</param>
      <typeparam name="T"></typeparam>
      <typeparam name="TProperty"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.MultilineTextDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String})">
      <summary>
        <para>Configures the property editor to display multiline data values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NewItemInitializer``2(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Collections.IDictionary},System.Func{System.ComponentModel.ITypeDescriptorContext,System.Collections.IDictionary,System.Collections.Generic.KeyValuePair{System.Object,``1}},System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="createDelegate"></param>
      <param name="name"></param>
      <typeparam name="T"></typeparam>
      <typeparam name="TNewItem"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NewItemInitializer``3(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Collections.Generic.ICollection{``1}},System.Func{``2},System.String)">
      <summary>
        <para>Creates a new item initializer.</para>
      </summary>
      <param name="builder">A property metadata builder instance.</param>
      <param name="createDelegate">A function that creates a delegate of the specified type.</param>
      <param name="name">An item name.</param>
      <typeparam name="T"></typeparam>
      <typeparam name="TItem"></typeparam>
      <typeparam name="TValue"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NewItemInitializer``4(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Collections.Generic.IDictionary{``1,``2}},System.Func{System.ComponentModel.ITypeDescriptorContext,System.Collections.Generic.IDictionary{``1,``2},System.Collections.Generic.KeyValuePair{``1,``3}},System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="createDelegate"></param>
      <param name="name"></param>
      <typeparam name="T"></typeparam>
      <typeparam name="TKey"></typeparam>
      <typeparam name="TValue"></typeparam>
      <typeparam name="TNewItem"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Byte},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Decimal},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Double},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Int16},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Int32},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Int64},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Decimal}},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Double}},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Int16}},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Int32}},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Int64}},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Single}},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.NumericMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Single},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.PasswordDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String})">
      <summary>
        <para>Configures the property editor to display password values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.PhoneNumberDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String},System.Func{System.String})">
      <summary>
        <para>Configures the property editor to display phone number values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <param name="errorMessageAccessor">A function that returns an error message.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.RegExMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.RegularMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.ScaffoldDetailCollection``2(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Collections.Generic.IEnumerable{``1}})">
      <summary>
        <para>Scaffolds a detail collection.</para>
      </summary>
      <param name="builder">A property metadata builder instance.</param>
      <typeparam name="T"></typeparam>
      <typeparam name="TProperty"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.SimpleMask``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String},System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="builder"></param>
      <param name="mask"></param>
      <param name="useMaskAsDisplayFormat"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.UrlDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String},System.Func{System.String})">
      <summary>
        <para>Configures the property editor to display URL values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <param name="errorMessageAccessor">A function that returns an error message.</param>
      <typeparam name="T"></typeparam>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.RegExMaskAttribute">
      <summary>
        <para>Provides RegEx mask attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.RegExMaskAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.RegExMaskAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.RegExMaskAttribute.ShowPlaceHolders">
      <summary>
        <para>Gets or sets whether placeholders are displayed in a masked editor.</para>
      </summary>
      <value>true to display placeholders in a masked editor; otherwise false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.RegExMaskAttributeBase">
      <summary>
        <para>Provides base RegEx mask attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.RegExMaskAttributeBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.RegExMaskAttributeBase"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.RegExMaskAttributeBase.IgnoreBlank">
      <summary>
        <para>Specifies whether the editor can lose focus when a value has not been completely filled.</para>
      </summary>
      <value>true, if the editor can lose focus when a value has not been completely filled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.RegExMaskAttributeBase.PlaceHolder">
      <summary>
        <para>Gets or sets the character used as the placeholder in a masked editor.</para>
      </summary>
      <value>The character used as the placeholder.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.RegularMaskAttribute">
      <summary>
        <para>Provides regular mask attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.RegularMaskAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.RegularMaskAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.RegularMaskAttribute.SaveLiteral">
      <summary>
        <para>Gets or sets whether constantly displayed mask characters (literals) are included in an editor&#39;s value.</para>
      </summary>
      <value>true if the constantly displayed mask characters are included in an editor&#39;s value; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute">
      <summary>
        <para>Provides settings for generating service properties by the POCO mechanism.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute.#ctor(DevExpress.Mvvm.ServiceSearchMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute"/> class with specified settings.</para>
      </summary>
      <param name="searchMode">A <see cref="T:DevExpress.Mvvm.ServiceSearchMode"/> enumeration value.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute.#ctor(System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute"/> class with specified settings.</para>
      </summary>
      <param name="isServiceProperty">true, to allow service generation; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute.IsServiceProperty">
      <summary>
        <para>Gets whether service generation is allowed.</para>
      </summary>
      <value>true, if service generation is allowed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute.Key">
      <summary>
        <para>Gets or sets the service key name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the service key name.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute.SearchMode">
      <summary>
        <para>Gets or sets whether a search for a service must be carried out within the current service container, or within the current and parent service containers.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Mvvm.ServiceSearchMode"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.SimpleMaskAttribute">
      <summary>
        <para>Provides simple mask attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.SimpleMaskAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.SimpleMaskAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.SimpleMaskAttribute.SaveLiteral">
      <summary>
        <para>Gets or sets whether constantly displayed mask characters (literals) are included in an editor&#39;s value.</para>
      </summary>
      <value>true if the constantly displayed mask characters are included in an editor&#39;s value; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.TableGroupContainerLayoutBuilder`1">
      <summary>
        <para>Provides methods for building table group containers.</para>
      </summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.TableGroupContainerLayoutBuilder`1.EndGroupContainer">
      <summary>
        <para>Moves the method call chain one level higher.</para>
      </summary>
      <returns>The table group container builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.TableGroupContainerLayoutBuilder`1.Group(System.String)">
      <summary>
        <para>Initializes a group builder.</para>
      </summary>
      <param name="groupName">A group name.</param>
      <returns>The table group layout builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.TableGroupContainerLayoutBuilder`1.GroupContainer(System.String)">
      <summary>
        <para>Initializes a group container builder.</para>
      </summary>
      <param name="groupName">A group container name.</param>
      <returns>The table group container layout builder instance.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.TableGroupLayoutBuilder`1">
      <summary>
        <para>Provides methods for building table groups.</para>
      </summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.TableGroupLayoutBuilder`1.ContainsProperty``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary>
        <para>Configures the specified ViewModel property to be displayed within the group.</para>
      </summary>
      <param name="propertyExpression">A lambda expression that returns a ViewModel property.</param>
      <typeparam name="TProperty"></typeparam>
      <returns>The table group layout builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.TableGroupLayoutBuilder`1.EndGroup">
      <summary>
        <para>Moves the method call chain from the group builder to the table group container builder.</para>
      </summary>
      <returns>The table group container builder instance.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DateTimeRange">
      <summary>
        <para>Time interval which provides extended operations such as Union and Intersect.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.#ctor(DevExpress.Mvvm.DateTimeRange)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DateTimeRange"/> class with specified settings.</para>
      </summary>
      <param name="source"></param>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.#ctor(System.DateTime,System.DateTime)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DateTimeRange"/> class with the specified settings.</para>
      </summary>
      <param name="start">A <see cref="T:System.DateTime"/> object that represents the time range start.</param>
      <param name="end">A <see cref="T:System.DateTime"/> object that represents the time range end.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.#ctor(System.DateTime,System.TimeSpan)">
      <summary>
        <para></para>
      </summary>
      <param name="start"></param>
      <param name="duration"></param>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.Contains(DevExpress.Mvvm.DateTimeRange)">
      <summary>
        <para></para>
      </summary>
      <param name="x"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.Contains(System.DateTime,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="date"></param>
      <param name="includeRangeEnd"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Mvvm.DateTimeRange.Duration">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.DateTimeRange.Empty">
      <summary>
        <para>Returns an empty datetime range.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Mvvm.DateTimeRange"/> value representing an empty datetime range.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DateTimeRange.End">
      <summary>
        <para>Gets the end date and time of the datetime range.</para>
      </summary>
      <value>A <see cref="T:System.DateTime"/> value representing the end of the datetime range.</value>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.Equals(DevExpress.Mvvm.DateTimeRange)">
      <summary>
        <para></para>
      </summary>
      <param name="other"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.Equals(System.Object)">
      <summary>
        <para>Defines whether the given object is equal to the current <see cref="T:DevExpress.Mvvm.DateTimeRange"/> instance.</para>
      </summary>
      <param name="obj">The object to compare with the current instance.</param>
      <returns>true, if the given object is equal to the current <see cref="T:DevExpress.Mvvm.DateTimeRange"/> instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.GetHashCode">
      <summary>
        <para>Gets the hash code (a number) that corresponds to the value of the current TimeInterval object.</para>
      </summary>
      <returns>An integer value representing the hash code for the current object.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.Intersect(DevExpress.Mvvm.DateTimeRange,DevExpress.Mvvm.DateTimeRange,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="x"></param>
      <param name="y"></param>
      <param name="includeBounds"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.Intersect(DevExpress.Mvvm.DateTimeRange,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="x"></param>
      <param name="includeBounds"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.IntersectsWith(DevExpress.Mvvm.DateTimeRange,DevExpress.Mvvm.DateTimeRange,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="x"></param>
      <param name="y"></param>
      <param name="includeBounds"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.IntersectsWith(DevExpress.Mvvm.DateTimeRange,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="x"></param>
      <param name="includeBounds"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Mvvm.DateTimeRange.IsEmpty">
      <summary>
        <para>Determines whether the current datetime range is empty.</para>
      </summary>
      <value>true, if the range is empty; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DateTimeRange.IsValid">
      <summary>
        <para>Gets whether the current datetime range is valid.</para>
      </summary>
      <value>true, if the range is valid; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DateTimeRange.Start">
      <summary>
        <para>Gets the start date and time of the datetime range.</para>
      </summary>
      <value>A <see cref="T:System.DateTime"/> value representing the start of the datetime range.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DateTimeRange.Today">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.ToString">
      <summary>
        <para>Returns the textual representation of the datetime range.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value which is the textual representation of the datetime range.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.ToString(System.IFormatProvider)">
      <summary>
        <para>Provides a culture-specific string representation of the time range.</para>
      </summary>
      <param name="provider">An object, implementing the <see cref="T:System.IFormatProvider"/> interface, which specifies the datetime formatting template.</param>
      <returns>A string value representing the <see cref="T:DevExpress.Mvvm.DateTimeRange"/> object.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.ToString(System.String,System.IFormatProvider)">
      <summary>
        <para></para>
      </summary>
      <param name="format"></param>
      <param name="provider"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.TryParse(System.String,System.Globalization.CultureInfo,DevExpress.Mvvm.DateTimeRange@)">
      <summary>
        <para></para>
      </summary>
      <param name="input"></param>
      <param name="culture"></param>
      <param name="result"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.Union(DevExpress.Mvvm.DateTimeRange)">
      <summary>
        <para>Returns a datetime range which is composed of the current time range and the given datetime range.</para>
      </summary>
      <param name="x">A <see cref="T:DevExpress.Mvvm.DateTimeRange"/> object that is the time range to be united.</param>
      <returns>A <see cref="T:DevExpress.Mvvm.DateTimeRange"/> object representing the union of the two datetime ranges.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.Union(DevExpress.Mvvm.DateTimeRange,DevExpress.Mvvm.DateTimeRange)">
      <summary>
        <para>Returns a datetime range which is composed of the two given time ranges.</para>
      </summary>
      <param name="x">A <see cref="T:DevExpress.Mvvm.DateTimeRange"/> object which represents the first time range to be united.</param>
      <param name="y">A <see cref="T:DevExpress.Mvvm.DateTimeRange"/> object which represents the second time range to be united.</param>
      <returns>A <see cref="T:DevExpress.Mvvm.DateTimeRange"/> object representing the union of the two time ranges.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DelegateCommand">
      <summary>
        <para>A command that calls your parameterless delegates when Execute and CanExecute logic is invoked on the command.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand.#ctor(System.Action)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DelegateCommand"/> that can always be executed.</para>
      </summary>
      <param name="executeMethod">The execution logic.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand.#ctor(System.Action,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DelegateCommand"/> that can always be executed.</para>
      </summary>
      <param name="executeMethod">The execution logic.</param>
      <param name="useCommandManager">Specifies whether the command is automatically updated by the command manager.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand.#ctor(System.Action,System.Func{System.Boolean},System.Nullable{System.Boolean})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DelegateCommand"/> that can always be executed.</para>
      </summary>
      <param name="executeMethod">The execution logic.</param>
      <param name="canExecuteMethod">The execution status logic.</param>
      <param name="useCommandManager">Specifies whether the command is automatically updated by the command manager.</param>
    </member>
    <member name="T:DevExpress.Mvvm.DelegateCommand`1">
      <summary>
        <para>A command that calls your parameterized delegates when Execute and CanExecute logic is invoked on the command.</para>
      </summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand`1.#ctor(System.Action{`0})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DelegateCommand`1"/> that can always be executed.</para>
      </summary>
      <param name="executeMethod">The execution logic.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand`1.#ctor(System.Action{`0},System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DelegateCommand`1"/> that can always be executed.</para>
      </summary>
      <param name="executeMethod">The execution logic.</param>
      <param name="useCommandManager">Specifies whether the command is automatically updated by the command manager.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand`1.#ctor(System.Action{`0},System.Func{`0,System.Boolean},System.Nullable{System.Boolean})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DelegateCommand`1"/> that can always be executed.</para>
      </summary>
      <param name="executeMethod">The execution logic.</param>
      <param name="canExecuteMethod">The execution status logic.</param>
      <param name="useCommandManager">Specifies whether the command is automatically updated by the command manager.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand`1.Execute(`0)">
      <summary>
        <para>Defines the method to be called when the command is invoked.</para>
      </summary>
      <param name="parameter">Data used by the command. If the command does not require data to be passed, this object can be set to a null reference.</param>
    </member>
    <member name="T:DevExpress.Mvvm.DialogButtonAlignment">
      <summary>
        <para>Lists the DialogButton&#39;s horizontal alignment values.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DialogButtonAlignment.Center">
      <summary>
        <para>The DialogButton is centered within the dialog&#39;s content area.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DialogButtonAlignment.Left">
      <summary>
        <para>The DialogButton is aligned to the left of the dialog&#39;s content area.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DialogButtonAlignment.Right">
      <summary>
        <para>The DialogButton is aligned to the right of the dialog&#39;s content area.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DocumentManagerServiceExtensions">
      <summary>
        <para>Provides extension methods for <see cref="T:DevExpress.Mvvm.IDocumentManagerService"/> implementation to create and control a service&#39;s documents.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.CreateDocument(DevExpress.Mvvm.IDocumentManagerService,System.Object)">
      <summary>
        <para>Static extension method that creates and returns a IDocument descendant with the specified View Model.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="viewModel">An object specifying the document&#39;s View Models.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.CreateDocument(DevExpress.Mvvm.IDocumentManagerService,System.String,System.Object)">
      <summary>
        <para>Static extension method that creates and returns a IDocument descendant with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="documentType">A System.String value that specifies the type of the view to be created by the <see cref="T:DevExpress.Mvvm.IDocumentManagerService"/>.</param>
      <param name="viewModel">An object specifying the document&#39;s View Models.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.CreateDocument(DevExpress.Mvvm.IDocumentManagerService,System.String,System.Object,System.Object)">
      <summary>
        <para>Static extension method that creates and returns a IDocument descendant with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="documentType">A System.String value that specifies the type of the view to be created by the <see cref="T:DevExpress.Mvvm.IDocumentManagerService"/>.</param>
      <param name="parameter">A parameter passed to the view model via the <see cref="P:DevExpress.Mvvm.ISupportParameter.Parameter"/> property.</param>
      <param name="parentViewModel">An object that is passed to the view model of the created view via the <see cref="P:DevExpress.Mvvm.ISupportParentViewModel.ParentViewModel"/> property.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.CreateDocument(DevExpress.Mvvm.IDocumentManagerService,System.String,System.Object,System.Object,System.Boolean)">
      <summary>
        <para>Static extension method that creates and returns a IDocument descendant with the specified parameters.</para>
      </summary>
      <param name="service"></param>
      <param name="documentType"></param>
      <param name="parameter"></param>
      <param name="parentViewModel"></param>
      <param name="useParameterAsViewModel"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.CreateDocumentIfNotExistsAndShow(DevExpress.Mvvm.IDocumentManagerService,DevExpress.Mvvm.IDocument@,System.String,System.Object,System.Object,System.Object)">
      <summary>
        <para>Static extension method that creates and displays a new document if a document with the specified parameters does not exist. Otherwise, the document with the specified parameters will be shown.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="documentStorage">An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface that represents the document storage.</param>
      <param name="documentType">A System.String value that specifies the type of the view to be created by the <see cref="T:DevExpress.Mvvm.IDocumentManagerService"/>.</param>
      <param name="parameter">A parameter passed to the view model via the <see cref="P:DevExpress.Mvvm.ISupportParameter.Parameter"/> property.</param>
      <param name="parentViewModel">An object that is passed to the view model of the created view via the <see cref="P:DevExpress.Mvvm.ISupportParentViewModel.ParentViewModel"/> property.</param>
      <param name="title">A System.String value specifying the document title.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.FindDocument(DevExpress.Mvvm.IDocumentManagerService,System.Object)">
      <summary>
        <para>Static extension method that finds and returns a document with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="viewModel">An object specifying the document&#39;s View Models.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.FindDocument(DevExpress.Mvvm.IDocumentManagerService,System.Object,System.Object)">
      <summary>
        <para>Static extension method that finds and returns a document with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="parameter">A parameter passed to the view model via the <see cref="P:DevExpress.Mvvm.ISupportParameter.Parameter"/> property.</param>
      <param name="parentViewModel">An object that is passed to the view model of the view via the <see cref="P:DevExpress.Mvvm.ISupportParentViewModel.ParentViewModel"/> property.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.FindDocumentById(DevExpress.Mvvm.IDocumentManagerService,System.Object)">
      <summary>
        <para>Static extension method that retrieves the document by the specified identifier.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="id">An System.Object object that represents the document&#39;s <see cref="P:DevExpress.Mvvm.IDocument.Id"/>.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.FindDocumentByIdOrCreate(DevExpress.Mvvm.IDocumentManagerService,System.Object,System.Func{DevExpress.Mvvm.IDocumentManagerService,DevExpress.Mvvm.IDocument})">
      <summary>
        <para>Static extension method that retrieves the document by the specified identifier. If a document with such an identifier does not exist, it will be created.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="id">An System.Object object that represents the document&#39;s <see cref="P:DevExpress.Mvvm.IDocument.Id"/>.</param>
      <param name="createDocumentCallback">A System.Func object specifying the Callback function of the document creation.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.GetDocumentsByParentViewModel(DevExpress.Mvvm.IDocumentManagerService,System.Object)">
      <summary>
        <para>Static extension method that returns a collection of documents whose Parent View Model (<see cref="T:DevExpress.Mvvm.ISupportParentViewModel"/>) is equal to the specified View Model.</para>
      </summary>
      <param name="service">A System.Type value specifying the type of the service.</param>
      <param name="parentViewModel">An object that is passed to the view model of the created view via the <see cref="P:DevExpress.Mvvm.ISupportParentViewModel.ParentViewModel"/> property.</param>
      <returns>A collection of objects implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DXSplashScreenViewModel">
      <summary>
        <para>A view model that specifies data and options for the SplashScreenManager.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DXSplashScreenViewModel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DXSplashScreenViewModel"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Copyright">
      <summary>
        <para>Specifies the copyright notice.</para>
      </summary>
      <value>The copyright notice text.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.DesignTimeData">
      <summary>
        <para>For internal use. The default view model instance.</para>
      </summary>
      <value>A default view model instance.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.IsIndeterminate">
      <summary>
        <para>Specifies whether the to apply the marquee style to the progress bar displayed by the splash screen.</para>
      </summary>
      <value>true, to apply the marquee style to the progress bar; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Logo">
      <summary>
        <para>Specifies the logo image.</para>
      </summary>
      <value>A logo image in the System.Uri format.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Progress">
      <summary>
        <para>Specifies the current position of the progress bar displayed by the splash screen.</para>
      </summary>
      <value>A System.Double value from 0d to 100d.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Status">
      <summary>
        <para>Specifies the status text.</para>
      </summary>
      <value>The status text.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Subtitle">
      <summary>
        <para>Specifies the subtitle text.</para>
      </summary>
      <value>A string value that is the subtitle.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Tag">
      <summary>
        <para>Specifies the additional data associated with the view model instance.</para>
      </summary>
      <value>An object that contains the additional data is associated with the view model instance.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Title">
      <summary>
        <para>Specifies the title text.</para>
      </summary>
      <value>The title text.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DXWindowState">
      <summary>
        <para>Lists values that specify the window state (minimized, maximized, restored).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DXWindowState.Maximized">
      <summary>
        <para>The window is maximized.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DXWindowState.Minimized">
      <summary>
        <para>The window is minimized.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DXWindowState.Normal">
      <summary>
        <para>The window is restored.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Mvvm.Gantt">
      <summary>
        <para>Contains the predefined <see cref="T:DevExpress.Xpf.Gantt.GanttControl"/> data classes.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.Gantt.GanttPredecessorLink">
      <summary>
        <para>A task dependency data object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Gantt.GanttPredecessorLink.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Gantt.GanttPredecessorLink"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttPredecessorLink.Lag">
      <summary>
        <para>Specifies the task dependency time lag.</para>
      </summary>
      <value>A task dependency time lag.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttPredecessorLink.LinkType">
      <summary>
        <para>Specifies the task relationship type (FinishToStart, FinishToFinish, etc.)</para>
      </summary>
      <value>A task relationship type.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttPredecessorLink.PredecessorTaskId">
      <summary>
        <para>Specifies the predecessor task&#39;s id.</para>
      </summary>
      <value>A predecessor task&#39;s id.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttPredecessorLink.Tag">
      <summary>
        <para>Specifies the connector&#39;s additional info.</para>
      </summary>
      <value>A connector&#39;s additional info.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Gantt.GanttResource">
      <summary>
        <para>A Gantt resource data object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Gantt.GanttResource.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Gantt.GanttResource"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttResource.Color">
      <summary>
        <para>Gets or sets the resource&#39;s color.</para>
      </summary>
      <value>The resource&#39;s color.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttResource.Id">
      <summary>
        <para>Gets or sets the resource&#39;s ID.</para>
      </summary>
      <value>The resource&#39;s ID.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttResource.Name">
      <summary>
        <para>Gets or sets the resource&#39;s name.</para>
      </summary>
      <value>The resource&#39;s name.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Gantt.GanttResourceLink">
      <summary>
        <para>A resource dependency data object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Gantt.GanttResourceLink.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Gantt.GanttResourceLink"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttResourceLink.AllocationPercentage">
      <summary>
        <para>Gets or sets a percentage of time a resource spends on a task.</para>
      </summary>
      <value>A percentage of time a resource spends on a task.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttResourceLink.ResourceId">
      <summary>
        <para>Gets or sets a resource ID.</para>
      </summary>
      <value>A resource ID.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Gantt.GanttTask">
      <summary>
        <para>A Gantt task data object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Gantt.GanttTask.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Gantt.GanttTask"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttTask.BaselineFinishDate">
      <summary>
        <para>Specifies the task&#39;s baseline finish date.</para>
      </summary>
      <value>A task&#39;s baseline finish date.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttTask.BaselineStartDate">
      <summary>
        <para>Specifies the task&#39;s baseline start date.</para>
      </summary>
      <value>A task&#39;s baseline start date.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttTask.FinishDate">
      <summary>
        <para>Specifies the task&#39;s finish date.</para>
      </summary>
      <value>A task&#39;s finish date.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttTask.Id">
      <summary>
        <para>Specifies the task&#39;s id.</para>
      </summary>
      <value>A task&#39;s id.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttTask.Name">
      <summary>
        <para>Specifies the task&#39;s name and caption.</para>
      </summary>
      <value>A task&#39;s name and caption.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttTask.ParentId">
      <summary>
        <para>Specifies the task&#39;s parent id.</para>
      </summary>
      <value>A task&#39;s parent id.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttTask.PredecessorLinks">
      <summary>
        <para>Provides access to a collection of task&#39;s predecessors</para>
      </summary>
      <value>A collection of links to a task&#39;s predecessors.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttTask.Progress">
      <summary>
        <para>Specifies task&#39;s progress.</para>
      </summary>
      <value>A task&#39;s progress.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttTask.ResourceLinks">
      <summary>
        <para>Provides access to a collection of links to resources.</para>
      </summary>
      <value>A collection of links to resources.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttTask.StartDate">
      <summary>
        <para>Specifies the task&#39;s start date.</para>
      </summary>
      <value>A task&#39;s start date.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttTask.Tag">
      <summary>
        <para>Specifies the task&#39;s additional info.</para>
      </summary>
      <value>A task&#39;s additional info.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Gantt.PredecessorLinkType">
      <summary>
        <para>Lists the values used to specify the Gantt task relationships.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Gantt.PredecessorLinkType.FinishToFinish">
      <summary>
        <para>The successor task may not finish before the predecessor task is finished.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Gantt.PredecessorLinkType.FinishToStart">
      <summary>
        <para>The successor task may not start before the predecessor task is finished.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Gantt.PredecessorLinkType.StartToFinish">
      <summary>
        <para>The successor task may not finish before the predecessor task is started.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Gantt.PredecessorLinkType.StartToStart">
      <summary>
        <para>The successor task may not start before the predecessor task is started.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.IApplicationJumpListService">
      <summary>
        <para>Provides methods to add jump tasks to the application&#39;s Jump List.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IApplicationJumpListService.AddToRecentCategory(System.String)">
      <summary>
        <para>Adds the specified jump path to the Recent category of the Jump List.</para>
      </summary>
      <param name="jumpPath">The JumpPath to add to the Jump List.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IApplicationJumpListService.Apply">
      <summary>
        <para>Registers and adds the newly added jump tasks to its Jump List.</para>
      </summary>
      <returns>Returns the collection of rejected items.</returns>
    </member>
    <member name="P:DevExpress.Mvvm.IApplicationJumpListService.Items">
      <summary>
        <para>Gets the collection of <see cref="T:DevExpress.Mvvm.UI.ApplicationJumpTask"/> objects that are displayed in the Jump List.</para>
      </summary>
      <value>The collection of <see cref="T:DevExpress.Mvvm.UI.ApplicationJumpTask"/> objects displayed in the Jump List.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IApplicationJumpListService.ShowFrequentCategory">
      <summary>
        <para>Gets or sets a value that indicates whether frequently used items are displayed in the Jump List.</para>
      </summary>
      <value>true, if frequently used items are displayed in the Jump List; otherwise, false.The default is false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IApplicationJumpListService.ShowRecentCategory">
      <summary>
        <para>Gets or sets a value that indicates whether recently used items are displayed in the Jump List.</para>
      </summary>
      <value>true, if recently used items are displayed in the Jump List; otherwise, false.The default is false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ICurrentWindowService">
      <summary>
        <para>Provides methods to work with a window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ICurrentWindowService.Activate">
      <summary>
        <para>Activates the window associated with the <see cref="T:DevExpress.Mvvm.ICurrentWindowService"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ICurrentWindowService.Close">
      <summary>
        <para>Closes the window associated with the <see cref="T:DevExpress.Mvvm.ICurrentWindowService"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ICurrentWindowService.Hide">
      <summary>
        <para>Hides the window associated with the <see cref="T:DevExpress.Mvvm.ICurrentWindowService"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ICurrentWindowService.Show">
      <summary>
        <para>Shows the window associated with the <see cref="T:DevExpress.Mvvm.ICurrentWindowService"/>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ICurrentWindowService.WindowState">
      <summary>
        <para>Gets or sets the specified state for the window associated with the <see cref="T:DevExpress.Mvvm.ICurrentWindowService"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Mvvm.DXWindowState"/> enumeration value</value>
    </member>
    <member name="T:DevExpress.Mvvm.IDataErrorInfoHelper">
      <summary>
        <para>Provides extension methods to get an error based on defined DataAnnotation attributes or Fluent API</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IDataErrorInfoHelper.GetErrorText(System.Object,System.String)">
      <summary>
        <para>Returns the error text for the specified property.</para>
      </summary>
      <param name="owner">A System.Object object, which is the owner of the specified property.</param>
      <param name="propertyName">A System.String value that specifies the name of the property.</param>
      <returns>A System.String value that describes the error of the specified property.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.IDataErrorInfoHelper.HasErrors(System.ComponentModel.IDataErrorInfo,System.Boolean,System.Int32,System.Func{System.ComponentModel.PropertyDescriptor,System.Boolean})">
      <summary>
        <para></para>
      </summary>
      <param name="owner"></param>
      <param name="ignoreOwnerError"></param>
      <param name="deep"></param>
      <param name="propertyFilter"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.IDataErrorInfoHelper.HasErrors``1(``0,System.Int32,System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
      <summary>
        <para></para>
      </summary>
      <param name="owner"></param>
      <param name="deep"></param>
      <param name="excludedProperties"></param>
      <typeparam name="TOwner"></typeparam>
      <returns></returns>
    </member>
    <member name="T:DevExpress.Mvvm.IDialogService">
      <summary>
        <para>Provides methods to show a dialog window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IDialogService.ShowDialog(System.Collections.Generic.IEnumerable{DevExpress.Mvvm.UICommand},System.String,System.String,System.Object,System.Object,System.Object)">
      <summary>
        <para>Shows a dialog window with specified parameters.</para>
      </summary>
      <param name="dialogCommands">A list of <see cref="T:DevExpress.Mvvm.UICommand"/> objects that are used to generate dialog buttons.</param>
      <param name="title">A dialog window caption.</param>
      <param name="documentType">A <see cref="T:System.String"/> value that specifies the name of a document type to be shown in the dialog window.</param>
      <param name="viewModel">An object that is the view model to be passed to the data context of the dialog view.</param>
      <param name="parameter">A parameter for passing data to the passed view model.</param>
      <param name="parentViewModel">The parent View model for building a parent-child view model relationship.</param>
      <returns>An object of the <see cref="T:DevExpress.Mvvm.UICommand"/> type that is the command corresponding to the button the end-user clicked.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.IDispatcherService">
      <summary>
        <para>Provides the method to perform actions in a ViewModel using the Dispatcher.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IDispatcherService.BeginInvoke(System.Action)">
      <summary>
        <para>Executes the specified delegate asynchronously.</para>
      </summary>
      <param name="action">The delegate to execute, which takes no arguments and does not return a value.</param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.IDispatcherService.Invoke(System.Action)">
      <summary>
        <para></para>
      </summary>
      <param name="action"></param>
    </member>
    <member name="T:DevExpress.Mvvm.IDocument">
      <summary>
        <para>Provides methods to work with a document created with the IDocumentManagerService.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IDocument.Close(System.Boolean)">
      <summary>
        <para>Closes the document.</para>
      </summary>
      <param name="force">true, to disable the confirmation logic; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Mvvm.IDocument.Content">
      <summary>
        <para>Specifies the document content.</para>
      </summary>
      <value>The document content.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IDocument.DestroyOnClose">
      <summary>
        <para>Specifies whether to release the memory reserved for the document after it is closed.</para>
      </summary>
      <value>true, to release the memory reserved for the document after it is closed; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IDocument.Hide">
      <summary>
        <para>Hides the document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IDocument.Id">
      <summary>
        <para>Specifies the document ID.</para>
      </summary>
      <value>The document ID.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IDocument.Show">
      <summary>
        <para>Shows the document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IDocument.Title">
      <summary>
        <para>Specifies the document title.</para>
      </summary>
      <value>The document title.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IDocumentContent">
      <summary>
        <para>Provides a way to obtain the document&#39;s properties at the document&#39;s ViewModel level.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IDocumentContent.DocumentOwner">
      <summary>
        <para>Gets or sets the service which owns the current document.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Mvvm.IDocumentOwner"/> implementation that represents the service to which the current document belongs.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IDocumentContent.OnClose(System.ComponentModel.CancelEventArgs)">
      <summary>
        <para>Invoked before a document is closed (hidden), and allows you to prevent this action.</para>
      </summary>
      <param name="e">Provides data for the event handler that can be used to prevent specific operations on a document.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IDocumentContent.OnDestroy">
      <summary>
        <para>Invoked after a document has been closed (hidden).</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IDocumentContent.Title">
      <summary>
        <para>Gets a value specifying the document title.</para>
      </summary>
      <value>The text displayed in the document title.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IDocumentManagerService">
      <summary>
        <para>Provides methods to create documents and operate with them.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IDocumentManagerService.ActiveDocument">
      <summary>
        <para>Specifies the active document.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</value>
    </member>
    <member name="E:DevExpress.Mvvm.IDocumentManagerService.ActiveDocumentChanged">
      <summary>
        <para>Invoked each time the active document is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IDocumentManagerService.CreateDocument(System.String,System.Object,System.Object,System.Object)">
      <summary>
        <para>Creates a document.</para>
      </summary>
      <param name="documentType">A <see cref="T:System.String"/> value that specifies the type of the view to be created by the <see cref="T:DevExpress.Mvvm.IDocumentManagerService"/>.</param>
      <param name="viewModel">An object that is assigned to the DataContext property of the created view.</param>
      <param name="parameter">A parameter to be passed to the view model.</param>
      <param name="parentViewModel">An object that is passed to the view model of the created view.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface that is the created document.</returns>
    </member>
    <member name="P:DevExpress.Mvvm.IDocumentManagerService.Documents">
      <summary>
        <para>Specifies a set of created documents.</para>
      </summary>
      <value>A set of created documents.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IDocumentOwner">
      <summary>
        <para>Provides a way to obtain the document owner (service) at the document level.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IDocumentOwner.Close(DevExpress.Mvvm.IDocumentContent,System.Boolean)">
      <summary>
        <para>Closes the specified document.</para>
      </summary>
      <param name="documentContent">An <see cref="T:DevExpress.Mvvm.IDocument"/> implementation to be closed.</param>
      <param name="force">true, to disable the confirmation logic; otherwise, false.</param>
    </member>
    <member name="T:DevExpress.Mvvm.IDocumentViewModel">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IDocumentViewModel.Close">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Mvvm.IDocumentViewModel.Title">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.IFileDialogServiceBase">
      <summary>
        <para>Provides methods and properties to configure the standard dialog box.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.AddExtension">
      <summary>
        <para>Gets or sets a value indicating whether the dialog box automatically adds an extension to the file name if the user omits it.</para>
      </summary>
      <value>true, if the dialog box adds an extension to the file name; otherwise, false. The default is true.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.AutoUpgradeEnabled">
      <summary>
        <para>Gets or sets a value indicating whether the dialog box automatically upgrades appearance and behavior.</para>
      </summary>
      <value>true, if the dialog box automatically upgrades appearance and behavior; otherwise, false. The default is true.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.CheckFileExists">
      <summary>
        <para>Gets or sets a value indicating whether the dialog box displays a warning when the user specifies a file name that does not exist.</para>
      </summary>
      <value>true, if the dialog box displays a warning when the user specifies a file name that does not exist; otherwise, false. The default is false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.CheckPathExists">
      <summary>
        <para>Gets or sets a value indicating whether the dialog box displays a warning when the user specifies a path that does not exist.</para>
      </summary>
      <value>true, if the dialog box displays a warning when the user specifies a path that does not exist; otherwise, false. The default is true.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.DereferenceLinks">
      <summary>
        <para>Gets or sets a value indicating whether the dialog box returns the location of the file referenced by the shortcut or it returns the location of the shortcut.</para>
      </summary>
      <value>true, if the dialog box returns the location of the file referenced by the shortcut; otherwise, false. The default is true.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.Filter">
      <summary>
        <para>Gets or sets a filter string which specifies options that are available in the &quot;Files of type&quot; box in the dialog box.</para>
      </summary>
      <value>A System.String value containing the file filtering options available in the dialog box. The default is an empty string.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.FilterIndex">
      <summary>
        <para>Gets or sets the index of the filtering option currently selected in the dialog box.</para>
      </summary>
      <value>A System.Int32 value containing the index of the filtering option currently selected in the file dialog box. The default value is 1.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.InitialDirectory">
      <summary>
        <para>Gets or sets the initial folder shown by the dialog box.</para>
      </summary>
      <value>A System.String value specifying the initial folder shown by the dialog box. The default is an empty string.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IFileDialogServiceBase.Reset">
      <summary>
        <para>Resets all properties to the default values.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.RestoreDirectory">
      <summary>
        <para>Gets or sets whether the dialog should remember the previous directory and restore it when you show this dialog again.</para>
      </summary>
      <value>true, if the dialog box restores the current directory to the previously selected directory if the initial directory was changed while searching for files; otherwise, false. The default is false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.ShowHelp">
      <summary>
        <para>Gets or sets a value indicating whether the Help button is displayed in the dialog box.</para>
      </summary>
      <value>true, if the Help button is shown in the dialog box; otherwise, false. The default is true.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.SupportMultiDottedExtensions">
      <summary>
        <para>Gets or sets a value whether the dialog box displays and saves files that have multiple file name extensions.</para>
      </summary>
      <value>true, if the dialog box supports multiple file name extensions; otherwise, false. The default is false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.Title">
      <summary>
        <para>Gets or sets the title displayed by the dialog box.</para>
      </summary>
      <value>A System.String value that specifies the title displayed by the dialog box.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.ValidateNames">
      <summary>
        <para>Gets or sets a value indicating whether the dialog box accepts only valid Win32 file names.</para>
      </summary>
      <value>true, if the dialog box accepts only valid Win32 file names; otherwise, false. The default is true.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IFileInfo">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IFileInfo.AppendText">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.IFileInfo.CopyTo(System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="destinationFileName"></param>
      <param name="overwrite"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.IFileInfo.Create">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.IFileInfo.CreateText">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Mvvm.IFileInfo.Length">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Mvvm.IFileInfo.Open(System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>
        <para></para>
      </summary>
      <param name="mode"></param>
      <param name="access"></param>
      <param name="share"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.IFileInfo.OpenRead">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.IFileInfo.OpenText">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.IFileInfo.OpenWrite">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="T:DevExpress.Mvvm.IFileSystemInfo">
      <summary>
        <para>Provides members common to <see cref="T:DevExpress.Mvvm.IFolderInfo"/> and <see cref="T:DevExpress.Mvvm.IFileInfo"/></para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IFileSystemInfo.Attributes">
      <summary>
        <para>Gets or sets file attributes.</para>
      </summary>
      <value>The file attributes.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IFileSystemInfo.Delete">
      <summary>
        <para>Deletes a file or folder.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IFileSystemInfo.DirectoryName">
      <summary>
        <para>Gets a string representing the directory&#39;s full path.</para>
      </summary>
      <value>A string that is the directory&#39;s full path.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileSystemInfo.Exists">
      <summary>
        <para>Determines whether the specified file exists.</para>
      </summary>
      <value>true, if the file exists; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IFileSystemInfo.MoveTo(System.String)">
      <summary>
        <para>Moves a specified file to a new location and allows you to specify a new file name.</para>
      </summary>
      <param name="destinationFileName">The new path and name for the file.</param>
    </member>
    <member name="P:DevExpress.Mvvm.IFileSystemInfo.Name">
      <summary>
        <para>Gets a file or a folder name.</para>
      </summary>
      <value>A file or a folder name.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IFolderBrowserDialogService">
      <summary>
        <para>Provides methods to browse, create, and select folders in the File System by using the standard folder browser dialog.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IFolderBrowserDialogService.ResultPath">
      <summary>
        <para>Gets or sets the path of the selected directory.</para>
      </summary>
      <value>A Systems.Stringvalue specifying the path of the selected directory. The default is an empty string.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IFolderBrowserDialogService.ShowDialog">
      <summary>
        <para>Shows the dialog box.</para>
      </summary>
      <returns>true, if the user clicks OK in the dialog box; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Mvvm.IFolderBrowserDialogService.StartPath">
      <summary>
        <para>Gets or sets the path of the initially selected directory.</para>
      </summary>
      <value>A Systems.Stringvalue specifying the path of the initially selected directory. The default is an empty string.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IFolderInfo">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IFolderInfo.Path">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.ILayoutSerializationService">
      <summary>
        <para>Provides methods to save/restore the layout of serializable DevExpress WPF Controls.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ILayoutSerializationService.Deserialize(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="state"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ILayoutSerializationService.Serialize">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="T:DevExpress.Mvvm.IMessageBoxService">
      <summary>
        <para>Provides methods to show dialog boxes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IMessageBoxService.Show(System.String,System.String,DevExpress.Mvvm.MessageButton,DevExpress.Mvvm.MessageIcon,DevExpress.Mvvm.MessageResult)">
      <summary>
        <para>Shows a message box with specified parameters.</para>
      </summary>
      <param name="messageBoxText">A text shown within the message box.</param>
      <param name="caption">A caption of the message box.</param>
      <param name="button">An object of the DevExpress.Mvvm.MessageButton type that is the set of buttons shown within the message box.</param>
      <param name="icon">An object of the DevExpress.Mvvm.MessageIcon type that is the icon shown within the message box.</param>
      <param name="defaultResult">An object of the DevExpress.Mvvm.MessageResult type that specifies which message box button is default. A default button is highlighted when a message box is shown.</param>
      <returns>An object of the DevExpress.Mvvm.MessageResult type that is the button the end-user clicked.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.IMessenger">
      <summary>
        <para>Provides methods to send messages and register message handlers.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IMessenger.Register``1(System.Object,System.Object,System.Boolean,System.Action{``0})">
      <summary>
        <para>Registers a handler of a specific message type.</para>
      </summary>
      <param name="recipient">An object that will receive messages.</param>
      <param name="token">An object (marker) that can be used to identify a specific message. null if you want to process messages without tokens.</param>
      <param name="receiveInheritedMessages">true to receive messages of the TMessage type and all derived types; false to receive messages of the TMessage type only.</param>
      <param name="action">An action that will be invoked when the specified message occurs.</param>
      <typeparam name="TMessage"></typeparam>
    </member>
    <member name="M:DevExpress.Mvvm.IMessenger.Send``1(``0,System.Type,System.Object)">
      <summary>
        <para>Sends the specified message.</para>
      </summary>
      <param name="message">The message of the TMessage type to be sent.</param>
      <param name="messageTargetType">Only recipients of the messageTargetType or inherited type will receive the current message. Set this parameter to null to send a message without an addressee type.</param>
      <param name="token">An object (marker) that can be used to identify a specific message. null if you want to send regular messages (without tokens).When sending a message, a token can be assigned to the message via the current token parameter. Only message recipients that registered the same token via the Register method are invoked when this message occurs.</param>
      <typeparam name="TMessage"></typeparam>
    </member>
    <member name="M:DevExpress.Mvvm.IMessenger.Unregister(System.Object)">
      <summary>
        <para>Unsubscribes the specified object from receiving any messages.</para>
      </summary>
      <param name="recipient">An object to be unsubscribed from receiving any messages.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IMessenger.Unregister``1(System.Object,System.Object,System.Action{``0})">
      <summary>
        <para>Unsubscribes the specified object&#39;s action from being invoked when a specific message occurs.</para>
      </summary>
      <param name="recipient">An object containing an action to be unsubscribed from receiving the messages of TMessage type.</param>
      <param name="token">An object (marker) that was used to identify a specific message when subscribing to it via the Register method. null to unsubscribe from the message regardless of the token assigned to it.</param>
      <param name="action">An action to be unsubscribed from receiving messages.</param>
      <typeparam name="TMessage"></typeparam>
    </member>
    <member name="T:DevExpress.Mvvm.INavigationService">
      <summary>
        <para>Provides members to navigate between Views.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.INavigationService.CanGoBack">
      <summary>
        <para>Specifies whether it is possible to perform a navigation to the previous view.</para>
      </summary>
      <value>true, if a navigation to the previous view is allowed; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.Mvvm.INavigationService.CanGoBackChanged">
      <summary>
        <para>Fires after the <see cref="P:DevExpress.Mvvm.INavigationService.CanGoBack"/> property value has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.INavigationService.CanGoForward">
      <summary>
        <para>Specifies whether it is possible to perform a navigation to the next view.</para>
      </summary>
      <value>true, if a navigation to the next view is allowed; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.Mvvm.INavigationService.CanGoForwardChanged">
      <summary>
        <para>Fires after the <see cref="P:DevExpress.Mvvm.INavigationService.CanGoForward"/> property value has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.INavigationService.CanNavigate">
      <summary>
        <para>Gets a value indicating whether a <see cref="T:DevExpress.Xpf.WindowsUI.NavigationFrame"/> associated with the current service can navigate.</para>
      </summary>
      <value>true, a <see cref="T:DevExpress.Xpf.WindowsUI.NavigationFrame"/> associated with the current service can navigate; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.ClearCache">
      <summary>
        <para>Clears the navigation cache.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.ClearNavigationHistory">
      <summary>
        <para>Clears the navigation history.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.INavigationService.Current">
      <summary>
        <para>Returns the current view model.</para>
      </summary>
      <value>A current view model.</value>
    </member>
    <member name="E:DevExpress.Mvvm.INavigationService.CurrentChanged">
      <summary>
        <para>Fires after the <see cref="P:DevExpress.Mvvm.INavigationService.Current"/> property value has been changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.GoBack">
      <summary>
        <para>Performs a navigation to the previous view, if allowed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.GoBack(System.Object)">
      <summary>
        <para>Navigates back to the previously selected screen (view).</para>
      </summary>
      <param name="param">An object that specifies the navigation parameter for the current navigation.</param>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.GoForward">
      <summary>
        <para>Performs a navigation to the next view, if allowed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.GoForward(System.Object)">
      <summary>
        <para>Navigates forward to the next screen (view).</para>
      </summary>
      <param name="param">An object that specifies the navigation parameter for the current navigation.</param>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.Navigate(System.String,System.Object,System.Object,System.Object,System.Boolean)">
      <summary>
        <para>Performs navigation to the target View.</para>
      </summary>
      <param name="viewType">A System.String value that specifies the name of the target View type.</param>
      <param name="viewModel">An object specifying the ViewModel of the target View.</param>
      <param name="param">A parameter for passing data to the target ViewModel.</param>
      <param name="parentViewModel">The parent ViewModel for building a parent-child view model relationship.</param>
      <param name="saveToJournal">true, to log the navigation; otherwise, false.</param>
    </member>
    <member name="T:DevExpress.Mvvm.INotificationService">
      <summary>
        <para>Provides methods to display notifications in Windows 8 style.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.INotificationService.CreateCustomNotification(System.Object)">
      <summary>
        <para>Creates and returns a custom notification with the specified View Model.</para>
      </summary>
      <param name="viewModel">An object specifying the notification&#39;s View Model.</param>
      <returns>An DevExpress.Mvvm.INotification descendant with the specified View Model.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.INotificationService.CreatePredefinedNotification(System.String,System.String,System.String,System.Windows.Media.ImageSource)">
      <summary>
        <para>Creates and returns a predefined notification with the specified header, and body text and image.</para>
      </summary>
      <param name="text1">The System.string value specifying the notification header.</param>
      <param name="text2">The System.String value specifying the notification&#39;s body text1.</param>
      <param name="text3">The System.String value specifying the notification&#39;s body text2.</param>
      <param name="image">An ImageSource object that represents the notification image.</param>
      <returns>An DevExpress.Mvvm.INotification descendant with the with the specified header, and body text and image.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.IOpenDialogServiceBase">
      <summary>
        <para>Provides members common to <see cref="T:DevExpress.Mvvm.IOpenFileDialogService"/> and <see cref="T:DevExpress.Mvvm.IOpenFolderDialogService"/></para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IOpenDialogServiceBase.Multiselect">
      <summary>
        <para>Gets or sets whether a dialog box allows users to select multiple files.</para>
      </summary>
      <value>true if a dialog box allows users to select multiple files; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IOpenDialogServiceBase.ShowDialog(System.Action{System.ComponentModel.CancelEventArgs},System.String)">
      <summary>
        <para>Shows the dialog box.</para>
      </summary>
      <param name="fileOK">A System.Action object that allows you to cancel file selection.</param>
      <param name="directoryName">A System.String object that specifies the initial directory.</param>
      <returns>true, if the user clicks OK in the dialog box; otherwise, false.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.IOpenFileDialogService">
      <summary>
        <para>Provides methods to browse and open files in the File System by using the standard dialog box.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IOpenFileDialogService.File">
      <summary>
        <para>Gets an object specifying the file selected in the dialog box.</para>
      </summary>
      <value>A FileInfo implementation that specifies the file selected in the dialog box.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IOpenFileDialogService.Files">
      <summary>
        <para>Gets a collection specifying all the files selected in the dialog box.</para>
      </summary>
      <value>A collection that specifies all the files selected in the dialog box.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IOpenFolderDialogService">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IOpenFolderDialogService.Folder">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.IOpenFolderDialogService.Folders">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.ISaveFileDialogService">
      <summary>
        <para>Provides methods to save the data of a ViewModel to a file by using the standard dialog box.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISaveFileDialogService.DefaultExt">
      <summary>
        <para>Gets or sets the default file extension.</para>
      </summary>
      <value>A System.String value specifying the default file extension. The default value is an empty string.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ISaveFileDialogService.DefaultFileName">
      <summary>
        <para>Gets or sets the default file name.</para>
      </summary>
      <value>A System.String value specifying the default file name. The default value is an empty string.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ISaveFileDialogService.File">
      <summary>
        <para>Gets an object specifying the file selected in the dialog box.</para>
      </summary>
      <value>A FileInfo implementation that specifies the file selected in the dialog box.</value>
    </member>
    <member name="M:DevExpress.Mvvm.ISaveFileDialogService.ShowDialog(System.Action{System.ComponentModel.CancelEventArgs},System.String,System.String)">
      <summary>
        <para>Shows the dialog box.</para>
      </summary>
      <param name="fileOK">A System.Action object that allows you to cancel file selection.</param>
      <param name="directoryName">A System.String object that specifies the initial directory.</param>
      <param name="fileName">A System.String object that specifies the file name.</param>
      <returns>true, if the user clicks OK in the dialog box; otherwise, false.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.IServiceContainer">
      <summary>
        <para>Provides methods to retrieve services and register them in a service container.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.Clear">
      <summary>
        <para>Clears services stored in the current service container.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.GetService(System.Type,System.String,DevExpress.Mvvm.ServiceSearchMode,System.Boolean@)">
      <summary>
        <para></para>
      </summary>
      <param name="type"></param>
      <param name="key"></param>
      <param name="searchMode"></param>
      <param name="serviceHasKey"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.GetService``1(DevExpress.Mvvm.ServiceSearchMode)">
      <summary>
        <para>Performs a search for a service implementing the specified service interface.</para>
      </summary>
      <param name="searchMode">A <see cref="T:DevExpress.Mvvm.ServiceSearchMode"/> enumeration value.</param>
      <typeparam name="T"></typeparam>
      <returns>An object implementing the specified service interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.GetService``1(System.String,DevExpress.Mvvm.ServiceSearchMode)">
      <summary>
        <para>Performs a search for a service implementing the specified service interface with a specified key.</para>
      </summary>
      <param name="key">A service identifier.</param>
      <param name="searchMode">A <see cref="T:DevExpress.Mvvm.ServiceSearchMode"/> enumeration value.</param>
      <typeparam name="T"></typeparam>
      <returns>An object implementing the specified service interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.GetService``1(System.String,DevExpress.Mvvm.ServiceSearchMode,System.Boolean@)">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="key"></param>
      <param name="searchMode"></param>
      <param name="serviceHasKey"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.GetServices(System.Type,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="type"></param>
      <param name="localOnly"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.RegisterService(System.Object,System.Boolean)">
      <summary>
        <para>Registers the specified service.</para>
      </summary>
      <param name="service">The service to register.</param>
      <param name="yieldToParent">true, to allow access to the passed service from the parent service container; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.RegisterService(System.String,System.Object,System.Boolean)">
      <summary>
        <para>Registers the specified service.</para>
      </summary>
      <param name="key">The service identifier.</param>
      <param name="service">The service to register.</param>
      <param name="yieldToParent">true to allow access to the passed service from the parent service container; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.UnregisterService(System.Object)">
      <summary>
        <para>Unregisters the specified service.</para>
      </summary>
      <param name="service">The service to unregister.</param>
    </member>
    <member name="T:DevExpress.Mvvm.ISplashScreenManagerService">
      <summary>
        <para>Provides data and methods used by the SplashScreenManagerService.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ISplashScreenManagerService.Close">
      <summary>
        <para>Hides the splash screen.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ISplashScreenManagerService.Show(System.String,System.Int32)">
      <summary>
        <para>Displays the splash screen.</para>
      </summary>
      <param name="documentType">The string value that identifies the document type.</param>
      <param name="timeout">A time interval for which the splash screen initialization process is prioritized over the main application, in milliseconds.</param>
    </member>
    <member name="P:DevExpress.Mvvm.ISplashScreenManagerService.State">
      <summary>
        <para>Specifies the current state of the splash screen.</para>
      </summary>
      <value>A DevExpress.Mvvm.SplashScreenState enumeration value.</value>
    </member>
    <member name="E:DevExpress.Mvvm.ISplashScreenManagerService.StateChanged">
      <summary>
        <para>Occurs when the State property value is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISplashScreenManagerService.ViewModel">
      <summary>
        <para>Provides access to the view model that stores the splash screen data and options.</para>
      </summary>
      <value>A DevExpress.Mvvm.DXSplashScreenViewModel object.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ISplashScreenService">
      <summary>
        <para>Provides methods to display splash screens.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ISplashScreenService.HideSplashScreen">
      <summary>
        <para>Hides the splash screen.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISplashScreenService.IsSplashScreenActive">
      <summary>
        <para>Gets whether the splash screen is currently shown.</para>
      </summary>
      <value>true, if the splash screen is currently shown; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.ISplashScreenService.SetSplashScreenProgress(System.Double,System.Double)">
      <summary>
        <para>Specifies the current progress of the splash screen.</para>
      </summary>
      <param name="progress">The current progress value.</param>
      <param name="maxProgress">The maximum progress value.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ISplashScreenService.SetSplashScreenState(System.Object)">
      <summary>
        <para>Specifies a data object to be passed to the splash screen service.</para>
      </summary>
      <param name="state">A data object to be passed to the splash screen service.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ISplashScreenService.ShowSplashScreen(System.String)">
      <summary>
        <para>Shows the splash screen.</para>
      </summary>
      <param name="documentType">A <see cref="T:System.String"/> value that specifies the name of a document type to be shown in the splash screen.</param>
    </member>
    <member name="T:DevExpress.Mvvm.ISupportNavigation">
      <summary>
        <para>Provides members that occur when navigating to an object and when navigating away from it.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ISupportNavigation.OnNavigatedFrom">
      <summary>
        <para>This method is called when a navigation from the current view model is performed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ISupportNavigation.OnNavigatedTo">
      <summary>
        <para>This method is called when navigation to the current view model is performed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ISupportParameter">
      <summary>
        <para>Provides a member that allows data to be passed from a Main to a Detail ViewModel, when these ViewModels are loosely coupled.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISupportParameter.Parameter">
      <summary>
        <para>Specifies a parameter for passing data between view models.</para>
      </summary>
      <value>A parameter to be passed.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ISupportParentViewModel">
      <summary>
        <para>Provides a member that allows a Main ViewModel to be passed to a Detail ViewModel. In addition, this allows the Main ViewModel&#39;s services to be used within the Detail ViewModel.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISupportParentViewModel.ParentViewModel">
      <summary>
        <para>Specifies the parent View model for building a parent-child view model relationship.</para>
      </summary>
      <value>A parent view model.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ISupportServices">
      <summary>
        <para>Provides a member to access an object containing services.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISupportServices.ServiceContainer">
      <summary>
        <para>Returns an object implementing the <see cref="T:DevExpress.Mvvm.IServiceContainer"/> interface which is used to access services.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Mvvm.IServiceContainer"/> interface.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ISupportWizardBackCommand">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISupportWizardBackCommand.CanGoBack">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Mvvm.ISupportWizardBackCommand.OnGoBack(System.ComponentModel.CancelEventArgs)">
      <summary>
        <para></para>
      </summary>
      <param name="e"></param>
    </member>
    <member name="T:DevExpress.Mvvm.ISupportWizardCancelCommand">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISupportWizardCancelCommand.CanCancel">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Mvvm.ISupportWizardCancelCommand.OnCancel(System.ComponentModel.CancelEventArgs)">
      <summary>
        <para></para>
      </summary>
      <param name="e"></param>
    </member>
    <member name="T:DevExpress.Mvvm.ISupportWizardFinishCommand">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISupportWizardFinishCommand.CanFinish">
      <summary>
        <para>Indicates whether or not the Finish button is enabled on a wizard page.</para>
      </summary>
      <value>true, if the Finish button is enabled; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.ISupportWizardFinishCommand.OnFinish(System.ComponentModel.CancelEventArgs)">
      <summary>
        <para></para>
      </summary>
      <param name="e"></param>
    </member>
    <member name="T:DevExpress.Mvvm.ISupportWizardNextCommand">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISupportWizardNextCommand.CanGoForward">
      <summary>
        <para>Indicates whether or not the Next button is enabled on a wizard page.</para>
      </summary>
      <value>true, if the Next button is enabled; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.ISupportWizardNextCommand.OnGoForward(System.ComponentModel.CancelEventArgs)">
      <summary>
        <para></para>
      </summary>
      <param name="e"></param>
    </member>
    <member name="T:DevExpress.Mvvm.ITaskbarButtonService">
      <summary>
        <para>Provides methods to display customizable application taskbar buttons.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.Description">
      <summary>
        <para>Gets or sets the text to display for the task-bar thumbnail tool-tip.</para>
      </summary>
      <value>A System.String value specifying the text to display for the thumbnail tool-tip.The default is an empty string.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.OverlayIcon">
      <summary>
        <para>Gets or sets the icon that is displayed over the taskbar button.</para>
      </summary>
      <value>The icon that is displayed over the taskbar button.The default is null.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.ProgressState">
      <summary>
        <para>Gets or sets the taskbar button&#39;s progress state.</para>
      </summary>
      <value>A System.Windows.Shell.TaskbarItemProgressState enumerator value.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.ProgressValue">
      <summary>
        <para>Gets or sets the taskbar button&#39;s progress. This is a dependency property.</para>
      </summary>
      <value>A System.Double value specifing the taskbar button&#39;s progress</value>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.ThumbButtonInfos">
      <summary>
        <para>Gets or sets the TaskbarThumbButtonInfo collection.</para>
      </summary>
      <value>The TaskbarThumbButtonInfo collection that represents the taskbar thumbnail&#39;s Thumb buttons.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.ThumbnailClipMargin">
      <summary>
        <para>Gets or sets the thumbnail clipping margins</para>
      </summary>
      <value>A System.Windows.Thickness value.The default is 0.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.ThumbnailClipMarginCallback">
      <summary>
        <para>Gets or sets the ThumbnailClipMargin property callback.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Mvvm.ITaskbarButtonService.UpdateThumbnailClipMargin">
      <summary>
        <para>Updates the thumbnail clipping margin.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.IViewInjectionManager">
      <summary>
        <para>Provides methods to inject and manipulate under ViewModels (and their Views)</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.GetService(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.Inject(System.String,System.Object,System.Func{System.Object},System.String,System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="key"></param>
      <param name="viewModelFactory"></param>
      <param name="viewName"></param>
      <param name="viewType"></param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.Navigate(System.String,System.Object)">
      <summary>
        <para>Navigates to the specified ViewModel (and its View) inside the corresponding region.</para>
      </summary>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the key of the ViewModel (and its View).</param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.RaiseNavigatedAwayEvent(System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.RaiseNavigatedEvent(System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.RaiseViewModelClosingEvent(DevExpress.Mvvm.ViewModelClosingEventArgs)">
      <summary>
        <para></para>
      </summary>
      <param name="e"></param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.RegisterNavigatedAwayEventHandler(System.Object,System.Action)">
      <summary>
        <para>Registers the NavigateAway event handler for the specified ViewModel.</para>
      </summary>
      <param name="viewModel">An object representing the ViewModel.</param>
      <param name="eventHandler">A System.Action object encapsulating the event handler.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.RegisterNavigatedEventHandler(System.Object,System.Action)">
      <summary>
        <para>Registers the Navigated event handler for the specified ViewModel.</para>
      </summary>
      <param name="viewModel">An object representing the ViewModel.</param>
      <param name="eventHandler">A System.Action object encapsulating the event handler.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.RegisterService(DevExpress.Mvvm.IViewInjectionService)">
      <summary>
        <para>Register the service. For internal use.</para>
      </summary>
      <param name="service"></param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.RegisterViewModelClosingEventHandler(System.Object,System.Action{DevExpress.Mvvm.ViewModelClosingEventArgs})">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
      <param name="eventHandler"></param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.Remove(System.String,System.Object)">
      <summary>
        <para>Removes the ViewModel (and its View) from the specified region.</para>
      </summary>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the identifier (key) of the View and its ViewModel.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.UnregisterNavigatedAwayEventHandler(System.Object,System.Action)">
      <summary>
        <para>Unregisters the NavigateAway event handler for the specified ViewModel.</para>
      </summary>
      <param name="viewModel">An object representing the ViewModel.</param>
      <param name="eventHandler">A System.Action object encapsulating the event handler.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.UnregisterNavigatedEventHandler(System.Object,System.Action)">
      <summary>
        <para>Unregisters the Navigated event handler for the specified ViewModel.</para>
      </summary>
      <param name="viewModel">An object representing the ViewModel.</param>
      <param name="eventHandler">A System.Action object encapsulating the event handler.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.UnregisterService(DevExpress.Mvvm.IViewInjectionService)">
      <summary>
        <para>Unregister the service. For internal use.</para>
      </summary>
      <param name="service"></param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.UnregisterViewModelClosingEventHandler(System.Object,System.Action{DevExpress.Mvvm.ViewModelClosingEventArgs})">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
      <param name="eventHandler"></param>
    </member>
    <member name="T:DevExpress.Mvvm.IViewInjectionService">
      <summary>
        <para>Provides methods and properties to inject VewModels (and their Views) and operate with them.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionService.GetKey(System.Object)">
      <summary>
        <para>Returns the key of an injected ViewModel (and its View).</para>
      </summary>
      <param name="viewModel">An object representing the ViewModel.</param>
      <returns>An object that specifies the key of the injected ViewModel (and its View).</returns>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionService.Inject(System.Object,System.Object,System.String,System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="key"></param>
      <param name="viewModel"></param>
      <param name="viewName"></param>
      <param name="viewType"></param>
    </member>
    <member name="P:DevExpress.Mvvm.IViewInjectionService.RegionName">
      <summary>
        <para>Gets or sets the region name.</para>
      </summary>
      <value>A System.String that specifies the region name.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionService.Remove(System.Object)">
      <summary>
        <para>Removes the specified ViewModel (and its View) from the collection of the injected items.</para>
      </summary>
      <param name="viewModel">An object representing the ViewModel.</param>
      <returns>true if the item is successfully removed; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Mvvm.IViewInjectionService.SelectedViewModel">
      <summary>
        <para>Gets or sets the selected ViewModel.</para>
      </summary>
      <value>An object representing the currently selected ViewModel.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IViewInjectionService.ViewModels">
      <summary>
        <para>Gets the collection of injected View Models (and their Views).</para>
      </summary>
      <value>A collection of injected View Models (and their Views).</value>
    </member>
    <member name="T:DevExpress.Mvvm.IWindowService">
      <summary>
        <para>Provides methods to display a view as a window and control it from the ViewModel.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IWindowService.Activate">
      <summary>
        <para>Brings the service&#39;s window to the front and activates it.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IWindowService.Close">
      <summary>
        <para>Closes the service window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IWindowService.Hide">
      <summary>
        <para>Hides the service&#39;s window.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IWindowService.IsWindowAlive">
      <summary>
        <para>Gets whether the service&#39;s window is created and alive.</para>
      </summary>
      <value>true, if the service&#39;s window is created and alive; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IWindowService.Restore">
      <summary>
        <para>Restores the service&#39;s window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IWindowService.Show(System.String,System.Object,System.Object,System.Object)">
      <summary>
        <para>Shows a window with specified View and ViewModel.</para>
      </summary>
      <param name="documentType">A String value that specifies the type of the view to be created by the WindowService.</param>
      <param name="viewModel">An object that represents the ViewModel.</param>
      <param name="parameter">An object that represents the paramenter passed to the ViewModel</param>
      <param name="parentViewModel">An object that represents the parent ViewModel.</param>
    </member>
    <member name="P:DevExpress.Mvvm.IWindowService.Title">
      <summary>
        <para>Gets or sets the service window&#39;s title.</para>
      </summary>
      <value>The service window&#39;s title.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IWindowService.WindowState">
      <summary>
        <para>Gets or sets a window state (restored, minimized, maximized).</para>
      </summary>
      <value>A window state.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IWizardService">
      <summary>
        <para>Provides methods to use the <see cref="T:DevExpress.Xpf.Controls.Wizard"/> control in compliance with the MVVM pattern.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IWizardService.Current">
      <summary>
        <para>Returns the currently displayed object.</para>
      </summary>
      <value>A System.Object value representing the currently displayed object.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IWizardService.GoBack(System.Object)">
      <summary>
        <para>Navigates to the previous wizard page.</para>
      </summary>
      <param name="param">An object that specifies the navigation parameter for the current navigation.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IWizardService.GoForward(System.Object)">
      <summary>
        <para>Navigates to the next wizard page.</para>
      </summary>
      <param name="param">An object that specifies the navigation parameter for the current navigation.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IWizardService.Navigate(System.String,System.Object,System.Object,System.Object)">
      <summary>
        <para>Performs navigation to the target View.</para>
      </summary>
      <param name="viewType">A System.String value that specifies the name of the target View type.</param>
      <param name="viewModel">An object specifying the ViewModel of the target View.</param>
      <param name="param">A parameter for passing data to the target ViewModel.</param>
      <param name="parentViewModel"></param>
    </member>
    <member name="T:DevExpress.Mvvm.Messenger">
      <summary>
        <para>Allows you to send messages and register handlers that will process these messages.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.#ctor">
      <summary>
        <para>Initializes a <see cref="T:DevExpress.Mvvm.Messenger"/> instance.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.#ctor(System.Boolean,DevExpress.Mvvm.ActionReferenceType)">
      <summary>
        <para>Initializes a <see cref="T:DevExpress.Mvvm.Messenger"/> instance.</para>
      </summary>
      <param name="isMultiThreadSafe">true, if the messenger can be used from multiple threads; otherwise, false.</param>
      <param name="actionReferenceType">An ActionReferenceType enumeration value.</param>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.#ctor(System.Boolean,DevExpress.Mvvm.Native.IActionInvokerFactory)">
      <summary>
        <para>Initializes a <see cref="T:DevExpress.Mvvm.Messenger"/> instance.</para>
      </summary>
      <param name="isMultiThreadSafe">true, if the messenger can be used from multiple threads; otherwise, false.</param>
      <param name="actionInvokerFactory">An object implementing the IActionInvokerFactory interface.</param>
    </member>
    <member name="T:DevExpress.Mvvm.Messenger.ActionInvokerCollection">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.ActionInvokerCollection.#ctor">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.ActionInvokerCollection.CleanUp">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.ActionInvokerCollection.Register(System.Object,System.Boolean,System.Type,DevExpress.Mvvm.Native.IActionInvoker)">
      <summary>
        <para></para>
      </summary>
      <param name="token"></param>
      <param name="receiveInheritedMessagesToo"></param>
      <param name="messageType"></param>
      <param name="actionInvoker"></param>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.ActionInvokerCollection.Send(System.Object,System.Type,System.Object,System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="message"></param>
      <param name="messageTargetType"></param>
      <param name="token"></param>
      <param name="messageType"></param>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.ActionInvokerCollection.Unregister(System.Object,System.Object,System.Delegate,System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="recipient"></param>
      <param name="token"></param>
      <param name="action"></param>
      <param name="messageType"></param>
    </member>
    <member name="T:DevExpress.Mvvm.Messenger.ActionInvokerTokenPair">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.ActionInvokerTokenPair.#ctor(DevExpress.Mvvm.Native.IActionInvoker,System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="actionInvoker"></param>
      <param name="token"></param>
    </member>
    <member name="F:DevExpress.Mvvm.Messenger.ActionInvokerTokenPair.ActionInvoker">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Mvvm.Messenger.ActionInvokerTokenPair.Token">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.Cleanup">
      <summary>
        <para>Removes all references to the actions that belong to non-existing recipients.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Messenger.Default">
      <summary>
        <para>Specifies the default messenger.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Mvvm.IMessenger"/> interface.</value>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.Register``1(System.Object,System.Object,System.Boolean,System.Action{``0})">
      <summary>
        <para>Registers a handler of a specific message type.</para>
      </summary>
      <param name="recipient">An object that will receive messages.</param>
      <param name="token">An object (marker) that can be used to identify a specific message. null if you want to process messages without tokens.</param>
      <param name="receiveInheritedMessages">true to receive messages of the TMessage type and all derived types; false to receive messages of the TMessage type only.</param>
      <param name="action">An action that will be invoked when the specified message occurs.</param>
      <typeparam name="TMessage"></typeparam>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.RequestCleanup">
      <summary>
        <para>Executes the <see cref="M:DevExpress.Mvvm.Messenger.Cleanup"/> method asynchronously when the current application is idle.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.Send``1(``0,System.Type,System.Object)">
      <summary>
        <para>Sends the specified message.</para>
      </summary>
      <param name="message">The message to send.</param>
      <param name="messageTargetType">The message target type.</param>
      <param name="token">An object that separates messages.</param>
      <typeparam name="TMessage"></typeparam>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.Unregister(System.Object)">
      <summary>
        <para>Unregisters the specified object from all registered message handlers.</para>
      </summary>
      <param name="recipient">An object to unregister.</param>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.Unregister``1(System.Object,System.Object,System.Action{``0})">
      <summary>
        <para>Unsubscribes the specified object&#39;s action from being invoked when a specific message occurs.</para>
      </summary>
      <param name="recipient">An object containing an action to be unsubscribed from receiving the messages of TMessage type.</param>
      <param name="token">An object (marker) that was used to identify a specific message when subscribing to it via the Register method. null to unsubscribe from the message regardless of the token assigned to it.</param>
      <param name="action">An action to be unsubscribed from receiving messages.</param>
      <typeparam name="TMessage"></typeparam>
    </member>
    <member name="N:DevExpress.Mvvm.ModuleInjection">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.IModule">
      <summary>
        <para>A structure that binds a ViewModel to its View.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.IModule.Key">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.IModule.ViewModelFactory">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.IModule.ViewModelName">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.IModule.ViewName">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.IModule.ViewType">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.IModuleManager">
      <summary>
        <para>Provides methods to control modules and regions.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManager.Clear(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManager.Inject(System.String,System.String,System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="key"></param>
      <param name="parameter"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManager.IsInjected(System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="key"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManager.Navigate(System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="key"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManager.Remove(System.String,System.String,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="key"></param>
      <param name="raiseViewModelRemovingEvent"></param>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.IModuleManagerBase">
      <summary>
        <para>Provides methods to control modules and regions.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManagerBase.GetEvents(System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManagerBase.GetEvents(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManagerBase.GetModule(System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="key"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManagerBase.GetRegion(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManagerBase.GetRegions(System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManagerBase.Register(System.String,DevExpress.Mvvm.ModuleInjection.IModule)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="module"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManagerBase.Restore(System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="logicalState"></param>
      <param name="visualState"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManagerBase.Save(System.String,System.String@,System.String@)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="logicalState"></param>
      <param name="visualState"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleManagerBase.Unregister(System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="key"></param>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.IModuleWindowManager">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleWindowManager.Activate(System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="key"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleWindowManager.Clear(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleWindowManager.Close(System.String,System.String,System.Nullable{System.Windows.MessageBoxResult},System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="key"></param>
      <param name="dialogResult"></param>
      <param name="raiseViewModelRemovingEvent"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleWindowManager.IsShown(System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="key"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IModuleWindowManager.Show(System.String,System.String,System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="key"></param>
      <param name="parameter"></param>
      <returns></returns>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.IRegion">
      <summary>
        <para>Provides control of injected ViewModels.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IRegion.GetKey(System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IRegion.GetLogicalSerializationMode(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="key"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IRegion.GetViewModel(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="key"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IRegion.GetVisualSerializationMode(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="key"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.IRegion.LogicalSerializationMode">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.IRegion.RegionName">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IRegion.ResetVisualState">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.IRegion.SelectedKey">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.IRegion.SelectedViewModel">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IRegion.SetLogicalSerializationMode(System.String,System.Nullable{DevExpress.Mvvm.ModuleInjection.LogicalSerializationMode})">
      <summary>
        <para></para>
      </summary>
      <param name="key"></param>
      <param name="mode"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IRegion.SetVisualSerializationMode(System.String,System.Nullable{DevExpress.Mvvm.ModuleInjection.VisualSerializationMode})">
      <summary>
        <para></para>
      </summary>
      <param name="key"></param>
      <param name="mode"></param>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.IRegion.ViewModels">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.IRegion.VisualSerializationMode">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.IRegionEventManager">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="E:DevExpress.Mvvm.ModuleInjection.IRegionEventManager.Navigation">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="E:DevExpress.Mvvm.ModuleInjection.IRegionEventManager.ViewModelCreated">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="E:DevExpress.Mvvm.ModuleInjection.IRegionEventManager.ViewModelRemoved">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="E:DevExpress.Mvvm.ModuleInjection.IRegionEventManager.ViewModelRemoving">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.IViewModelEventManager">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="E:DevExpress.Mvvm.ModuleInjection.IViewModelEventManager.Navigated">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="E:DevExpress.Mvvm.ModuleInjection.IViewModelEventManager.NavigatedAway">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="E:DevExpress.Mvvm.ModuleInjection.IViewModelEventManager.ViewModelRemoved">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="E:DevExpress.Mvvm.ModuleInjection.IViewModelEventManager.ViewModelRemoving">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.IVisualStateService">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.IVisualStateService.DefaultState">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IVisualStateService.GetCurrentState">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IVisualStateService.GetSavedState">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IVisualStateService.RestoreState(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="state"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.IVisualStateService.SaveState(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="state"></param>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.LogicalSerializationMode">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.ModuleInjection.LogicalSerializationMode.Disabled">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.ModuleInjection.LogicalSerializationMode.Enabled">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.Module">
      <summary>
        <para>A structure that binds a ViewModel to its View.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.Module.#ctor(System.String,System.Func{System.Object})">
      <summary>
        <para></para>
      </summary>
      <param name="key"></param>
      <param name="viewModelFactory"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.Module.#ctor(System.String,System.Func{System.Object},System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="key"></param>
      <param name="viewModelFactory"></param>
      <param name="viewName"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.Module.#ctor(System.String,System.Func{System.Object},System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="key"></param>
      <param name="viewModelFactory"></param>
      <param name="viewType"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.Module.#ctor(System.String,System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="key"></param>
      <param name="viewModelName"></param>
      <param name="viewName"></param>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.Module.Key">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.Module.ViewModelFactory">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.Module.ViewModelName">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.Module.ViewName">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.Module.ViewType">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.ModuleManager">
      <summary>
        <para>Provides methods to control modules and regions.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ModuleManager.#ctor(DevExpress.Mvvm.IViewModelLocator,DevExpress.Mvvm.UI.IViewLocator,DevExpress.Mvvm.IStateSerializer,System.Boolean,System.Boolean,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.ModuleInjection.ModuleManager"/> class with specified settings.</para>
      </summary>
      <param name="viewModelLocator"></param>
      <param name="viewLocator"></param>
      <param name="viewModelStateSerializer"></param>
      <param name="allowSaveRestoreLayout"></param>
      <param name="isTestingMode"></param>
      <param name="keepViewModelsAlive"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ModuleManager.#ctor(System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.ModuleInjection.ModuleManager"/> class with specified settings.</para>
      </summary>
      <param name="keepViewModelsAlive"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ModuleManager.#ctor(System.Boolean,System.Boolean,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.ModuleInjection.ModuleManager"/> class with specified settings.</para>
      </summary>
      <param name="allowSaveRestoreLayout"></param>
      <param name="isTestingMode"></param>
      <param name="keepViewModelsAlive"></param>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.ModuleManager.DefaultImplementation">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.ModuleManager.DefaultManager">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.ModuleManager.DefaultWindowManager">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.ModuleManagerExtensions">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ModuleManagerExtensions.GetRegion(DevExpress.Mvvm.ModuleInjection.IModuleManagerBase,System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="manager"></param>
      <param name="viewModel"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ModuleManagerExtensions.InjectOrNavigate(DevExpress.Mvvm.ModuleInjection.IModuleManager,System.String,System.String,System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="manager"></param>
      <param name="regionName"></param>
      <param name="key"></param>
      <param name="parameter"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ModuleManagerExtensions.RegisterOrInjectOrNavigate(DevExpress.Mvvm.ModuleInjection.IModuleManager,System.String,DevExpress.Mvvm.ModuleInjection.IModule,System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="manager"></param>
      <param name="regionName"></param>
      <param name="module"></param>
      <param name="parameter"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ModuleManagerExtensions.RegisterOrShowOrActivate(DevExpress.Mvvm.ModuleInjection.IModuleWindowManager,System.String,DevExpress.Mvvm.ModuleInjection.IModule,System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="manager"></param>
      <param name="regionName"></param>
      <param name="module"></param>
      <param name="parameter"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ModuleManagerExtensions.Save(DevExpress.Mvvm.ModuleInjection.IModuleManagerBase,System.String@,System.String@)">
      <summary>
        <para></para>
      </summary>
      <param name="manager"></param>
      <param name="logicalState"></param>
      <param name="visualState"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ModuleManagerExtensions.ShowOrActivate(DevExpress.Mvvm.ModuleInjection.IModuleWindowManager,System.String,System.String,System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="manager"></param>
      <param name="regionName"></param>
      <param name="key"></param>
      <param name="parameter"></param>
      <returns></returns>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.NavigationEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.NavigationEventArgs.#ctor(System.String,System.Object,System.Object,System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="oldVM"></param>
      <param name="newVM"></param>
      <param name="oldVMKey"></param>
      <param name="newVMKey"></param>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.NavigationEventArgs.NewViewModel">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.NavigationEventArgs.NewViewModelKey">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.NavigationEventArgs.OldViewModel">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.NavigationEventArgs.OldViewModelKey">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.NavigationEventArgs.RegionName">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.ViewModelRemovedEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ViewModelRemovedEventArgs.#ctor(System.String,System.Object,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="viewModel"></param>
      <param name="viewModelKey"></param>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.ViewModelRemovedEventArgs.RegionName">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.ViewModelRemovedEventArgs.ViewModel">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.ViewModelRemovedEventArgs.ViewModelKey">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.ViewModelRemovingEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ViewModelRemovingEventArgs.#ctor(System.String,System.Object,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="viewModel"></param>
      <param name="viewModelKey"></param>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.ViewModelRemovingEventArgs.RegionName">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.ViewModelRemovingEventArgs.ViewModel">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.ViewModelRemovingEventArgs.ViewModelKey">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.VisualSerializationMode">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.ModuleInjection.VisualSerializationMode.Disabled">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.ModuleInjection.VisualSerializationMode.PerKey">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.ModuleInjection.VisualSerializationMode.PerViewType">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.VisualStateServiceExtensions">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.VisualStateServiceExtensions.IsDefaultState(DevExpress.Mvvm.ModuleInjection.IVisualStateService)">
      <summary>
        <para></para>
      </summary>
      <param name="service"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.VisualStateServiceExtensions.IsStateChanged(DevExpress.Mvvm.ModuleInjection.IVisualStateService)">
      <summary>
        <para></para>
      </summary>
      <param name="service"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.VisualStateServiceExtensions.RestoreDefaultState(DevExpress.Mvvm.ModuleInjection.IVisualStateService)">
      <summary>
        <para></para>
      </summary>
      <param name="service"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.VisualStateServiceExtensions.RestoreState(DevExpress.Mvvm.ModuleInjection.IVisualStateService)">
      <summary>
        <para></para>
      </summary>
      <param name="service"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.VisualStateServiceExtensions.SaveState(DevExpress.Mvvm.ModuleInjection.IVisualStateService)">
      <summary>
        <para></para>
      </summary>
      <param name="service"></param>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.WindowInjectionResult">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.WindowInjectionResult.#ctor(System.String,System.Object,System.Object,System.Nullable{System.Windows.MessageBoxResult})">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="viewModel"></param>
      <param name="viewModelKey"></param>
      <param name="result"></param>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.WindowInjectionResult.RegionName">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.WindowInjectionResult.Result">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.WindowInjectionResult.ViewModel">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.ModuleInjection.WindowInjectionResult.ViewModelKey">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Mvvm.NavigationViewModelBase">
      <summary>
        <para>A View Model for Views between which navigation can be organized if they are placed within a <see cref="T:DevExpress.Xpf.WindowsUI.NavigationFrame"/>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ServiceContainer">
      <summary>
        <para>An object that contains services.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.#ctor(System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.ServiceContainer"/> class with the specified owner.</para>
      </summary>
      <param name="owner">An object that will be the owner of the created service container.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.Clear">
      <summary>
        <para>Clears services stored in the current service container.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ServiceContainer.Default">
      <summary>
        <para>Gets the default service contaner.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Mvvm.IServiceContainer"/> implementation.</value>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.GetService``1(DevExpress.Mvvm.ServiceSearchMode)">
      <summary>
        <para>Performs a search for a service implementing the specified service interface.</para>
      </summary>
      <param name="searchMode">A <see cref="T:DevExpress.Mvvm.ServiceSearchMode"/> enumeration value.</param>
      <typeparam name="T"></typeparam>
      <returns>An object implementing the specified service interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.GetService``1(System.String,DevExpress.Mvvm.ServiceSearchMode)">
      <summary>
        <para>Performs a search for a service implementing the specified service interface with a specified key.</para>
      </summary>
      <param name="key">A service identifier.</param>
      <param name="searchMode">A <see cref="T:DevExpress.Mvvm.ServiceSearchMode"/> enumeration value.</param>
      <typeparam name="T"></typeparam>
      <returns>An object implementing the specified service interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.RegisterService(System.Object,System.Boolean)">
      <summary>
        <para>Registers the specified service.</para>
      </summary>
      <param name="service">The service to register.</param>
      <param name="yieldToParent">true, to allow access to the passed service from the parent service container; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.RegisterService(System.String,System.Object,System.Boolean)">
      <summary>
        <para>Registers the specified service.</para>
      </summary>
      <param name="key">The service identifier.</param>
      <param name="service">The service to register.</param>
      <param name="yieldToParent">true, to allow access to the passed service from the parent service container; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.UnregisterService(System.Object)">
      <summary>
        <para>Unregisters the specified service.</para>
      </summary>
      <param name="service">The service to unregister.</param>
    </member>
    <member name="T:DevExpress.Mvvm.ServiceSearchMode">
      <summary>
        <para>Lists values that specify whether a search for a service must be carried out within the current service container, or within the current and parent service containers.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.ServiceSearchMode.LocalOnly">
      <summary>
        <para>A search for a service is carried out within the current service container.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.ServiceSearchMode.PreferLocal">
      <summary>
        <para>A search for a service is carried out within both the current and parent service containers. A service found in the current container has a higher priority.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.ServiceSearchMode.PreferParents">
      <summary>
        <para>A search for a service is carried out within both the current and parent service containers. A service found in parent containers has a higher priority.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.SplashScreenState">
      <summary>
        <para>Lists values that specify the current state of the splash screen window.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.SplashScreenState.Closed">
      <summary>
        <para>The splash screen is closed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.SplashScreenState.Closing">
      <summary>
        <para>The splash screen is about to be closed (the Closing event fired).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.SplashScreenState.Showing">
      <summary>
        <para>The splash screen is about to be shown (the Show method is called).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.SplashScreenState.Shown">
      <summary>
        <para>The splash screen is shown (the Loaded event fired).</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.TimeSpanRange">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.#ctor(System.TimeSpan,System.TimeSpan)">
      <summary>
        <para></para>
      </summary>
      <param name="start"></param>
      <param name="end"></param>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.Contains(DevExpress.Mvvm.TimeSpanRange)">
      <summary>
        <para></para>
      </summary>
      <param name="x"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.Contains(System.TimeSpan,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="time"></param>
      <param name="includeRangeEnd"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Mvvm.TimeSpanRange.Day">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.TimeSpanRange.Duration">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.TimeSpanRange.End">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.Equals(DevExpress.Mvvm.TimeSpanRange)">
      <summary>
        <para></para>
      </summary>
      <param name="other"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.Equals(System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="obj"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.GetHashCode">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.Intersect(DevExpress.Mvvm.TimeSpanRange,DevExpress.Mvvm.TimeSpanRange,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="x"></param>
      <param name="y"></param>
      <param name="includeBounds"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.Intersect(DevExpress.Mvvm.TimeSpanRange,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="x"></param>
      <param name="includeBounds"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.IntersectsWith(DevExpress.Mvvm.TimeSpanRange,DevExpress.Mvvm.TimeSpanRange,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="x"></param>
      <param name="y"></param>
      <param name="includeBounds"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.IntersectsWith(DevExpress.Mvvm.TimeSpanRange,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="x"></param>
      <param name="includeBounds"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Mvvm.TimeSpanRange.IsDay">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.TimeSpanRange.IsValid">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.TimeSpanRange.IsZero">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.Parse(System.String,System.Globalization.CultureInfo)">
      <summary>
        <para></para>
      </summary>
      <param name="input"></param>
      <param name="culture"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Mvvm.TimeSpanRange.Start">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.ToString">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.ToString(System.IFormatProvider)">
      <summary>
        <para></para>
      </summary>
      <param name="provider"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.ToString(System.String,System.IFormatProvider)">
      <summary>
        <para></para>
      </summary>
      <param name="format"></param>
      <param name="provider"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.TryParse(System.String,System.Globalization.CultureInfo,DevExpress.Mvvm.TimeSpanRange@)">
      <summary>
        <para></para>
      </summary>
      <param name="input"></param>
      <param name="culture"></param>
      <param name="result"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.Union(DevExpress.Mvvm.TimeSpanRange)">
      <summary>
        <para></para>
      </summary>
      <param name="x"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.TimeSpanRange.Union(DevExpress.Mvvm.TimeSpanRange,DevExpress.Mvvm.TimeSpanRange)">
      <summary>
        <para></para>
      </summary>
      <param name="x"></param>
      <param name="y"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Mvvm.TimeSpanRange.Zero">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="N:DevExpress.Mvvm.UI">
      <summary>
        <para>Contains controls for building a UI using the MVVM design pattern.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.UI.IViewLocator">
      <summary>
        <para>Provides a method to retrieve a View by its type name.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.UI.IViewLocator.GetViewTypeName(System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="type"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.UI.IViewLocator.ResolveView(System.String)">
      <summary>
        <para>Returns a View based on its short type name.</para>
      </summary>
      <param name="name">The short type name of the View to be returned.</param>
      <returns>A View of the specified type.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.UI.IViewLocator.ResolveViewType(System.String)">
      <summary>
        <para>Returns a view type based on its short type name.</para>
      </summary>
      <param name="name">The short type name of the view which type is to be returned.</param>
      <returns>The type of the view.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.UICommand">
      <summary>
        <para>A ViewModel that is used to generate a dialog button.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.UICommand.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.UICommand"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.UICommand.#ctor(System.Object,System.Object,System.Windows.Input.ICommand,System.Boolean,System.Boolean,System.Object,System.Boolean,System.Windows.Controls.Dock,DevExpress.Mvvm.DialogButtonAlignment)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.UICommand"/> class with the custom settings.</para>
      </summary>
      <param name="id">A dialog button identifier. This value is assigned to the <see cref="P:DevExpress.Mvvm.UICommand.Id"/> property.</param>
      <param name="caption">A text displayed within a dialog button. This value is assigned to the <see cref="P:DevExpress.Mvvm.UICommand.Caption"/> property.</param>
      <param name="command">A command to invoke when the dialog button is clicked. This value is assigned to the <see cref="P:DevExpress.Mvvm.UICommand.Command"/> property.</param>
      <param name="isDefault">true, if the button is the default button; otherwise false. This value is assigned to the <see cref="P:DevExpress.Mvvm.UICommand.IsDefault"/> property.</param>
      <param name="isCancel">true, if the button is the cancel button; otherwise false. This value is assigned to the <see cref="P:DevExpress.Mvvm.UICommand.IsDefault"/> property.</param>
      <param name="tag">An object associated with the dialog button. This value is assigned to the <see cref="P:DevExpress.Mvvm.UICommand.Tag"/> property.</param>
      <param name="allowCloseWindow">true, if the button closes the current window; otherwise, false. This value is assigned to the <see cref="P:DevExpress.Mvvm.UICommand.AllowCloseWindow"/> property.</param>
      <param name="placement">The dialog button&#39;s placement. This value is assigned to the <see cref="P:DevExpress.Mvvm.UICommand.Placement"/> property.</param>
      <param name="alignment">The dialog button&#39;s alignment. This value is assigned to the <see cref="P:DevExpress.Mvvm.UICommand.ActualAlignment"/> property.</param>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.ActualAlignment">
      <summary>
        <para>For internal use only.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.Alignment">
      <summary>
        <para>Gets or sets the dialog button&#39;s horizontal alignment.</para>
      </summary>
      <value>Specifies the dialog button alignment.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.AllowCloseWindow">
      <summary>
        <para>Gets or sets a value that indicates whether the dialog button is the close button.</para>
      </summary>
      <value>true, if the button closes the current window; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.Caption">
      <summary>
        <para>Gets or sets the text displayed within the dialog button.</para>
      </summary>
      <value>A string which specifies the text displayed within the dialog button.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.Command">
      <summary>
        <para>Gets or sets the command to invoke when the dialog button is clicked.</para>
      </summary>
      <value>A command to invoke when the dialog button is clicked.</value>
    </member>
    <member name="M:DevExpress.Mvvm.UICommand.GenerateFromMessageBoxButton(System.Windows.MessageBoxButton,DevExpress.Mvvm.IMessageBoxButtonLocalizer,System.Nullable{System.Windows.MessageBoxResult},System.Nullable{System.Windows.MessageBoxResult})">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="dialogButtons"></param>
      <param name="buttonLocalizer"></param>
      <param name="defaultButton"></param>
      <param name="cancelButton"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.UICommand.GenerateFromMessageBoxButton(System.Windows.MessageBoxButton,DevExpress.Mvvm.IMessageButtonLocalizer,System.Nullable{System.Windows.MessageBoxResult},System.Nullable{System.Windows.MessageBoxResult})">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="dialogButtons"></param>
      <param name="buttonLocalizer"></param>
      <param name="defaultButton"></param>
      <param name="cancelButton"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.UICommand.GenerateFromMessageButton(DevExpress.Mvvm.MessageButton,DevExpress.Mvvm.IMessageButtonLocalizer,System.Nullable{DevExpress.Mvvm.MessageResult},System.Nullable{DevExpress.Mvvm.MessageResult})">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="dialogButtons"></param>
      <param name="buttonLocalizer"></param>
      <param name="defaultButton"></param>
      <param name="cancelButton"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.Id">
      <summary>
        <para>Gets or sets the identifier of the dialog button.</para>
      </summary>
      <value>An object that specifies the identifier the dialog button.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.IsCancel">
      <summary>
        <para>Gets or sets a value that indicates whether the dialog button is the cancel button.</para>
      </summary>
      <value>true, if the dialog button is the cancel button; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.IsDefault">
      <summary>
        <para>Gets or sets a value that indicates whether the dialog button is the default button.</para>
      </summary>
      <value>true, if the dialog button is the default button; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.Placement">
      <summary>
        <para>Gets or sets the dialog button alignment.</para>
      </summary>
      <value>Specifies the dialog button alignment.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.Tag">
      <summary>
        <para>Gets or sets the data associated with the dialog button.</para>
      </summary>
      <value>An object that contains information associated with the dialog button.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ViewInjectionManager">
      <summary>
        <para>Allows you to perform injecting and manipulation under ViewModels (and their Views) in any section of the application&#39;s code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.#ctor(DevExpress.Mvvm.ViewInjectionMode)">
      <summary>
        <para></para>
      </summary>
      <param name="mode"></param>
    </member>
    <member name="P:DevExpress.Mvvm.ViewInjectionManager.Default">
      <summary>
        <para>Gets the default view injection manager.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Mvvm.IViewInjectionManager"/> implementation.</value>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.GetService(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.Inject(System.String,System.Object,System.Func{System.Object},System.String,System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="regionName"></param>
      <param name="key"></param>
      <param name="viewModelFactory"></param>
      <param name="viewName"></param>
      <param name="viewType"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.Navigate(System.String,System.Object)">
      <summary>
        <para>Navigates to the specified ViewModel (and its View) inside the corresponding region.</para>
      </summary>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the key of the ViewModel (and its View).</param>
    </member>
    <member name="P:DevExpress.Mvvm.ViewInjectionManager.PersistentManager">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.RaiseNavigatedAwayEvent(System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.RaiseNavigatedEvent(System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.RaiseViewModelClosingEvent(DevExpress.Mvvm.ViewModelClosingEventArgs)">
      <summary>
        <para></para>
      </summary>
      <param name="e"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.RegisterNavigatedAwayEventHandler(System.Object,System.Action)">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
      <param name="eventHandler"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.RegisterNavigatedEventHandler(System.Object,System.Action)">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
      <param name="eventHandler"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.RegisterService(DevExpress.Mvvm.IViewInjectionService)">
      <summary>
        <para>Register the service. For internal use.</para>
      </summary>
      <param name="service"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.RegisterViewModelClosingEventHandler(System.Object,System.Action{DevExpress.Mvvm.ViewModelClosingEventArgs})">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
      <param name="eventHandler"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.Remove(System.String,System.Object)">
      <summary>
        <para>Removes the ViewModel (and its View) from the specified region.</para>
      </summary>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the identifier (key) of the View and its ViewModel.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.UnregisterNavigatedAwayEventHandler(System.Object,System.Action)">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
      <param name="eventHandler"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.UnregisterNavigatedEventHandler(System.Object,System.Action)">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
      <param name="eventHandler"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.UnregisterService(DevExpress.Mvvm.IViewInjectionService)">
      <summary>
        <para>Unregister the service. For internal use.</para>
      </summary>
      <param name="service"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.UnregisterViewModelClosingEventHandler(System.Object,System.Action{DevExpress.Mvvm.ViewModelClosingEventArgs})">
      <summary>
        <para></para>
      </summary>
      <param name="viewModel"></param>
      <param name="eventHandler"></param>
    </member>
    <member name="T:DevExpress.Mvvm.ViewInjectionManagerExtensions">
      <summary>
        <para>Provides extension methods for the <see cref="T:DevExpress.Mvvm.ViewInjectionManager"/> to integrate and control ViewModels (with their Views).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManagerExtensions.Inject(DevExpress.Mvvm.IViewInjectionManager,System.String,System.Object,System.Func{System.Object})">
      <summary>
        <para>Injects a ViewModel (and its View) with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the identifier (key) of the View and its ViewModel.</param>
      <param name="viewModelFactory">A object encapsulating the method that returns the ViewModel.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManagerExtensions.Inject(DevExpress.Mvvm.IViewInjectionManager,System.String,System.Object,System.Func{System.Object},System.String)">
      <summary>
        <para>Static extension method that injects a ViewModel (and its View) with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the identifier (key) of the View and its ViewModel.</param>
      <param name="viewModelFactory">A object encapsulating the method that returns the ViewModel.</param>
      <param name="viewName">A System.String value specifying the view that will be created using the <see cref="T:DevExpress.Mvvm.UI.ViewLocator"/>.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManagerExtensions.Inject(DevExpress.Mvvm.IViewInjectionManager,System.String,System.Object,System.Func{System.Object},System.Type)">
      <summary>
        <para>Static extension method that injects a ViewModel (and its View) with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the identifier (key) of the View and its ViewModel.</param>
      <param name="viewModelFactory">A object encapsulating the method that returns the ViewModel.</param>
      <param name="viewType">A System.String value specifying the view that will be created using the <see cref="T:DevExpress.Mvvm.UI.ViewLocator"/>.</param>
    </member>
    <member name="T:DevExpress.Mvvm.ViewInjectionServiceExtensions">
      <summary>
        <para>Provides extension methods for the <see cref="T:DevExpress.Mvvm.UI.ViewInjectionService"/> to integrate and control ViewModels (with their Views).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionServiceExtensions.GetViewModel(DevExpress.Mvvm.IViewInjectionService,System.Object)">
      <summary>
        <para>Static extension method that finds and returns a ViewModel with the specified key.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="key">An object representing the identifier (key) of the View and its ViewModel.</param>
      <returns>An object representing the ViewModel.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionServiceExtensions.Inject(DevExpress.Mvvm.IViewInjectionService,System.Object,System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="service"></param>
      <param name="key"></param>
      <param name="viewModel"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionServiceExtensions.Inject(DevExpress.Mvvm.IViewInjectionService,System.Object,System.Object,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="service"></param>
      <param name="key"></param>
      <param name="viewModel"></param>
      <param name="viewName"></param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionServiceExtensions.Inject(DevExpress.Mvvm.IViewInjectionService,System.Object,System.Object,System.Type)">
      <summary>
        <para></para>
      </summary>
      <param name="service"></param>
      <param name="key"></param>
      <param name="viewModel"></param>
      <param name="viewType"></param>
    </member>
    <member name="T:DevExpress.Mvvm.ViewModelBase">
      <summary>
        <para>The base class for ViewModels.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ViewModelBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.ViewModelBase"/> class.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ViewModelBase.CreateCommandHelper`1">
      <summary>
        <para></para>
      </summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:DevExpress.Mvvm.ViewModelBase.CreateCommandHelper`1.CreateAsyncCommand(System.Object,System.Reflection.MethodInfo,System.Reflection.MethodInfo,System.Nullable{System.Boolean},System.Boolean,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="owner"></param>
      <param name="method"></param>
      <param name="canExecuteMethod"></param>
      <param name="useCommandManager"></param>
      <param name="hasParameter"></param>
      <param name="allowMultipleExecution"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Mvvm.ViewModelBase.CreateCommandHelper`1.CreateCommand(System.Object,System.Reflection.MethodInfo,System.Reflection.MethodInfo,System.Nullable{System.Boolean},System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="owner"></param>
      <param name="method"></param>
      <param name="canExecuteMethod"></param>
      <param name="useCommandManager"></param>
      <param name="hasParameter"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Mvvm.ViewModelBase.IsInDesignMode">
      <summary>
        <para>Gets whether design-time mode is active.</para>
      </summary>
      <value>true, if design-time mode is active; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ViewModelClosingEventArgs">
      <summary>
        <para>Provides data for the ViewModelClosingEvent event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ViewModelClosingEventArgs.#ctor(System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.ViewModelClosingEventArgs"/> class.</para>
      </summary>
      <param name="viewModel">An object specifying the View Model that is about to be closed.</param>
    </member>
    <member name="P:DevExpress.Mvvm.ViewModelClosingEventArgs.ViewModel">
      <summary>
        <para>Gets or sets the View Model.</para>
      </summary>
      <value>An object specifying the View Model.</value>
    </member>
  </members>
</doc>