<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.Xpf.PropertyGrid.v20.2</name>
  </assembly>
  <members>
    <member name="N:DevExpress.Xpf.PropertyGrid">
      <summary>
        <para>Contains classes that implement the functionality of the PropertyGridControl. To use these classes in XAML code, add the xmlns:dxprg=&quot;&quot; namespace reference.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.AllowExpandingMode">
      <summary>
        <para>Lists the values used to specify which properties can be expanded.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.AllowExpandingMode.Default">
      <summary>
        <para>Expands properties according to their type converters.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.AllowExpandingMode.Force">
      <summary>
        <para>Expands all properties.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.AllowExpandingMode.ForceIfNoTypeConverter">
      <summary>
        <para>Expands all properties that don&#39;t have a type converter.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.AllowExpandingMode.Never">
      <summary>
        <para>Doesn&#39;t expand any property.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.ApplyingMode">
      <summary>
        <para>Lists values that specify when the property definition is applied.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.ApplyingMode.Always">
      <summary>
        <para>The definition is always shown.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.ApplyingMode.WhenGrouping">
      <summary>
        <para>The definition is shown if grouping is applied.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.ApplyingMode.WhenNoGrouping">
      <summary>
        <para>The definition is shown if grouping is not applied.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.BarItemNames">
      <summary>
        <para>Stores the names of the default property menu items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.BarItemNames.Refresh">
      <summary>
        <para>Identifies the Refresh property menu item.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.BarItemNames.Reset">
      <summary>
        <para>Identifies the Reset property menu item.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.BrushEdit">
      <summary>
        <para>Represents a brush editor.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.BrushEdit.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.PropertyGrid.BrushEdit"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.BrushEditSettings">
      <summary>
        <para>Contains settings specific to a brush editor.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.BrushEditSettings.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.PropertyGrid.BrushEditSettings"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.CategoriesShowMode">
      <summary>
        <para>Lists the values used to specify the categories display mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CategoriesShowMode.Hidden">
      <summary>
        <para>Shows data fields from all categories without grouping.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CategoriesShowMode.Tabbed">
      <summary>
        <para>Shows each category in a separate tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CategoriesShowMode.Visible">
      <summary>
        <para>Shows the expandable/collapsible category rows by default.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.CategoryDefinition">
      <summary>
        <para>Represents a category definition.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.CategoryDefinition.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.PropertyGrid.CategoryDefinition"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CategoryDefinition.ColorizeGlyph">
      <summary>
        <para>Gets or sets whether the category header glyph is colorized.</para>
      </summary>
      <value>true, to colorize the glyph; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CategoryDefinition.ColorizeGlyphProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.CategoryDefinition.ColorizeGlyph"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CategoryDefinition.Glyph">
      <summary>
        <para>Gets or sets an image displayed within a category header. This is a dependency property.</para>
      </summary>
      <value>An ImageSource object that specifies the image displayed within a category header.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CategoryDefinition.GlyphProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.CategoryDefinition.Glyph"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.CellEditorPresenter">
      <summary>
        <para>Represents an object that is used to configure and display cell editors within property grid cells.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.PropertyGrid.CellEditorPresenter"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.ActualRowData">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.BeginInit">
      <summary>
        <para>Starts the runtime initialization.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.EndInit">
      <summary>
        <para>Ends the runtime initialization.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.IsSelected">
      <summary>
        <para>Specifies whether the current cell editor is focused. This is a dependency property.</para>
      </summary>
      <value>true, if the cell editor is focused; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.IsSelectedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.IsSelected"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.OwnerView">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.Path">
      <summary>
        <para>Gets or sets the path to the data source field that will be associated with the cell editor. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the name of a data field.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.PathMode">
      <summary>
        <para>Specifies whether the <see cref="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.Path"/> property represents an absolute path or a path relative to the parent <see cref="T:DevExpress.Xpf.PropertyGrid.PropertyDefinition"/>. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.CellEditorPresenterPathMode"/> enumeration value that specifies whether the <see cref="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.Path"/> property represents a relative path or an absolute path. The default is <see cref="F:DevExpress.Xpf.PropertyGrid.CellEditorPresenterPathMode.Relative"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.PathModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.PathMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.PathProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.Path"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.RowControl">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.RowData">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.CellEditorPresenterPathMode">
      <summary>
        <para>Lists values that specify whether the <see cref="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.Path"/> property value represents a relative path or an absolute path.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CellEditorPresenterPathMode.Absolute">
      <summary>
        <para>The <see cref="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.Path"/> points to a property of the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectedObject"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CellEditorPresenterPathMode.Relative">
      <summary>
        <para>The <see cref="P:DevExpress.Xpf.PropertyGrid.CellEditorPresenter.Path"/> points to a property of an object associated with the parent <see cref="T:DevExpress.Xpf.PropertyGrid.PropertyDefinition"/>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.CollectionDefinition">
      <summary>
        <para>Represents a collection definition.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.CollectionDefinition.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.PropertyGrid.CollectionDefinition"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.AllowAddItems">
      <summary>
        <para>Gets or sets whether end-users can add new items to collection. This is a dependency property.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CollectionDefinition.AllowAddItemsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.AllowAddItems"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.AllowNewItemInitializer">
      <summary>
        <para>Gets or sets whether to enable the new item initializer for the collection. This is a dependency property.</para>
      </summary>
      <value>true, to enable the new item initializer; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CollectionDefinition.AllowNewItemInitializerProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.AllowNewItemInitializer"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.AllowRemoveItems">
      <summary>
        <para>Gets or sets whether end-users can remove collection items. This is a dependency property.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CollectionDefinition.AllowRemoveItemsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.AllowRemoveItems"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.AllowRemoveItemsWithoutNewItemInitializer">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CollectionDefinition.AllowRemoveItemsWithoutNewItemInitializerProperty">
      <summary>
        <para></para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.CollectionDefinition.GetHideCollectionButton(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.HideCollectionButton"/> attached property for the specified object.</para>
      </summary>
      <param name="element">An object whose <see cref="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.HideCollectionButton"/> attached property&#39;s value is to be returned.</param>
      <returns>The value of the <see cref="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.HideCollectionButton"/> attached property for the specified object.</returns>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CollectionDefinition.HideCollectionButtonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.HideCollectionButton"/> attached property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.NewItemInitializer">
      <summary>
        <para>Gets or sets a new item initializer. This is a dependency property.</para>
      </summary>
      <value>An object implementing the DevExpress.Mvvm.Native.IInstanceInitializer interface.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CollectionDefinition.NewItemInitializerProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.NewItemInitializer"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.CollectionDefinition.SetHideCollectionButton(System.Windows.DependencyObject,System.Nullable{System.Boolean})">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.HideCollectionButton"/> attached property for the specified object.</para>
      </summary>
      <param name="element">An object whose <see cref="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.HideCollectionButton"/> attached property is to be set.</param>
      <param name="value">The new value of the <see cref="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.HideCollectionButton"/> attached property for the specified object.</param>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.UseCollectionEditor">
      <summary>
        <para>Specifies whether to enable collection editing within the collection. This is a dependency property.</para>
      </summary>
      <value>true, to enable collection editing; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.CollectionDefinition.UseCollectionEditorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.CollectionDefinition.UseCollectionEditor"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.CustomExpandEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CustomExpand"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CustomExpandEventArgs.IsExpanded">
      <summary>
        <para>Gets or sets whether the item is expanded.</para>
      </summary>
      <value>true, if the item is expanded; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CustomExpandEventArgs.IsInitializing">
      <summary>
        <para>Gets whether the item is being initialized.</para>
      </summary>
      <value>A Boolean value that specifies whether the item is being initialized.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.CustomExpandEventArgs.Row">
      <summary>
        <para>Gets information about an expanding row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.RowInfo"/> object that contains information about the row that is being expanded.</value>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.DescriptionLocation">
      <summary>
        <para>Lists values that specify the location of the property description.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.DescriptionLocation.None">
      <summary>
        <para>Disable showing property descriptions.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.DescriptionLocation.Panel">
      <summary>
        <para>Show property descriptions within the description panel.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.DescriptionLocation.ToolTip">
      <summary>
        <para>Show property descriptions within the tooltip.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.DescriptionLocation.ToolTipAndPanel">
      <summary>
        <para>Show property descriptions within the description panel and the tooltip.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.HeaderHighlightingMode">
      <summary>
        <para>Lists values that specify which parts of the selected property should be highlighted.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.HeaderHighlightingMode.HeaderAndContent">
      <summary>
        <para>Both the header cell and the value cell are highlighted.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.HeaderHighlightingMode.None">
      <summary>
        <para>Neither the header cell, nor the value cell is highlighted.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.HeaderHighlightingMode.OnlyContent">
      <summary>
        <para>Only the value cell is highlighted.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.HeaderHighlightingMode.OnlyHeader">
      <summary>
        <para>Only the header cell is highlighted.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.HeaderShowMode">
      <summary>
        <para>Lists the values used to specify the display mode of <see cref="T:DevExpress.Xpf.PropertyGrid.PropertyDefinition"/> and <see cref="T:DevExpress.Xpf.PropertyGrid.CollectionDefinition"/> headers.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.HeaderShowMode.Hidden">
      <summary>
        <para>Hides the header of a property or collection.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.HeaderShowMode.Left">
      <summary>
        <para>Displays the header of a property or collection to the left of the element&#39;s value.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.HeaderShowMode.OnlyExpander">
      <summary>
        <para>Hides the text in the header of an expandable property or collection.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.HeaderShowMode.OnlyHeader">
      <summary>
        <para>Hides the value of a property or collection.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.HeaderShowMode.Top">
      <summary>
        <para>Displays the header of a property or collection at the top of the element&#39;s value.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.InstanceInitializer">
      <summary>
        <para>Represents item initializers.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.InstanceInitializer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.PropertyGrid.InstanceInitializer"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.InstanceInitializer.CreateInstance(DevExpress.Mvvm.Native.TypeInfo)">
      <summary>
        <para>Creates an instance of the specified type.</para>
      </summary>
      <param name="type">The instance type.</param>
      <returns>The created instance.</returns>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.InstanceInitializer.Types">
      <summary>
        <para>Gets or sets a list of available instance types.</para>
      </summary>
      <value>A list of available instance types.</value>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.PopupBrushEdit">
      <summary>
        <para>Represents a brush editor displayed within a dropdown window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PopupBrushEdit.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.PropertyGrid.PopupBrushEdit"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.PopupBrushEditSettings">
      <summary>
        <para>Contains settings specific to a popup brush editor.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PopupBrushEditSettings.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.PropertyGrid.PopupBrushEditSettings"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.PropertyDefinition">
      <summary>
        <para>Represents a property definition.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyDefinition.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.PropertyGrid.PropertyDefinition"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.AllowExpanding">
      <summary>
        <para>Gets or sets whether the end-user is allowed to expand the property to get access to its nested properties. This is a dependency property.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Xpf.PropertyGrid.AllowExpandingMode"/> enumeration value.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.AllowExpandingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.AllowExpanding"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.AllowInstanceInitializer">
      <summary>
        <para>Gets or sets whether to enable the instance initializer for the property definition. This is a dependency property.</para>
      </summary>
      <value>true, to enable the instance initializer; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.AllowInstanceInitializerProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.AllowInstanceInitializer"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.CellTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of data cells. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the presentation of data cells.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.CellTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.CellTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.CellTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a cell template based on custom logic. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.CellTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.CellTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.Command">
      <summary>
        <para>Gets or sets the command assigned to the property. This is a dependency property.</para>
      </summary>
      <value>An object that defines a command implementing the <see cref="T:System.Windows.Input.ICommand"/> interface.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.CommandParameter">
      <summary>
        <para>Gets or sets the parameter to pass to the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.Command"/>. This is a dependency property.</para>
      </summary>
      <value>A parameter to pass to the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.Command"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.CommandParameterProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.CommandParameter"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.CommandProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.Command"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.EditorHorizontalAlignment">
      <summary>
        <para>Gets or sets the horizontal alignment of the editor nested in the current property&#39;s cell. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.HorizontalAlignment"/> enumeration value that specifies the horizontal alignment of an in-place editor.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.EditorHorizontalAlignmentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.EditorHorizontalAlignment"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.EditSettings">
      <summary>
        <para>Gets or sets an object that specifies the cell editor for the current property definition. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Editors.Settings.BaseEditSettings"/> descendant that specifies the cell editor.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.EditSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.EditSettings"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.InsertDefinitionsFrom">
      <summary>
        <para>Gets or sets the property definition whose child definitions should be added to the child collection. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.PropertyDefinition"/> object.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.InsertDefinitionsFromProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.InsertDefinitionsFrom"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.MenuButtonTemplate">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.MenuButtonTemplateProperty">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.MenuCustomizations">
      <summary>
        <para>Allows you to customize a current property menu by adding new menu items or removing existing ones. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/>&lt;<see cref="T:DevExpress.Xpf.Bars.IControllerAction"/>,&gt; object.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.PostOnEditValueChanged">
      <summary>
        <para>Gets or sets the way a value specified by a user should be posted. This is a dependency property.</para>
      </summary>
      <value>true, to post changes immediately; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.PostOnEditValueChangedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.PostOnEditValueChanged"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.Scope">
      <summary>
        <para>Gets or sets the path to the property&#39;s parent. This is a dependency property.</para>
      </summary>
      <value>The path to the property&#39;s parent.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.ScopeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.Scope"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.ShowEditorButtons">
      <summary>
        <para>Gets or sets whether to display the buttons of the current editor nested in a property when the editor is not active. This is a dependency property.</para>
      </summary>
      <value>true, to display the editor buttons when the editor is not active; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.ShowEditorButtonsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.ShowEditorButtons"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.ShowMenuButton">
      <summary>
        <para>Gets or sets whether to display the menu button within the property definition. This is a dependency property.</para>
      </summary>
      <value>true, to display the menu button; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.ShowMenuButtonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.ShowMenuButton"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.Type">
      <summary>
        <para>Gets or sets the type of the property that is associated with current property definition. This is a dependency property.</para>
      </summary>
      <value>The property type.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.TypeMatchMode">
      <summary>
        <para>Defines the rules of type matching between the properties in the data source and the PropertyGrid&#39;s property definitions.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.TypeMatchMode"/> enumeration value.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.TypeMatchModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.TypeMatchMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.TypeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.Type"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.UseTypeConverterToStringConversion">
      <summary>
        <para>Gets or sets whether to convert values to a string with a TypeConverter. This is a dependency property.</para>
      </summary>
      <value>true, to convert values to a string with a TypeConverter; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.UseTypeConverterToStringConversionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinition.UseTypeConverterToStringConversion"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.PropertyGrid.PropertyDefinition.ValidateCell">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinition.ValidateCellEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyDefinition.ValidateCell"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase">
      <summary>
        <para>Serves as a base for classes that represent property definitions.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ApplyingMode">
      <summary>
        <para>Gets or sets whether a condition for applying the property definition exists. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.ApplyingMode"/> enumeration value.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ApplyingModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ApplyingMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.BeginInit">
      <summary>
        <para>Starts the runtime initialization.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ChildrenSortMode">
      <summary>
        <para>Gets or sets the sort mode for the nested properties. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.PropertyGridSortMode"/> enumeration value.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ChildrenSortModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ChildrenSortMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ContentTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of the property content. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the presentation of the property content.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ContentTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ContentTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ContentTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a property content template based on custom logic. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ContentTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ContentTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.Description">
      <summary>
        <para>Gets or sets the property description. This is a dependency property.</para>
      </summary>
      <value>An object representing the property description.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.DescriptionContainerStyle">
      <summary>
        <para>Gets or sets the style applied to the property description. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object that specifies the style applied to the property description.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.DescriptionContainerStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.DescriptionContainerStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.DescriptionContainerStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a property description style based on custom logic. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that chooses a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.DescriptionContainerStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.DescriptionContainerStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.DescriptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.Description"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.DescriptionTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of a property description. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the presentation of a property description.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.DescriptionTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.DescriptionTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.DescriptionTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a property description template based on custom logic. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.DescriptionTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.DescriptionTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.EndInit">
      <summary>
        <para>Ends the runtime initialization.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ExpandButtonVisibility">
      <summary>
        <para>Gets or sets whether the expand button is visible within a property grid row that supports expanding. This is a dependency property.</para>
      </summary>
      <value>A Visibility enumeration value that is the visibility of the expand button.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ExpandButtonVisibilityProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ExpandButtonVisibility"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.GetMergedDescription(DevExpress.Xpf.PropertyGrid.Internal.DataViewBase,DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase,DevExpress.Xpf.PropertyGrid.RowHandle)">
      <summary>
        <para></para>
      </summary>
      <param name="dataView"></param>
      <param name="standard"></param>
      <param name="handle"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.GetMergedHeader(DevExpress.Xpf.PropertyGrid.Internal.DataViewBase,DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase,DevExpress.Xpf.PropertyGrid.RowHandle)">
      <summary>
        <para></para>
      </summary>
      <param name="dataView"></param>
      <param name="standard"></param>
      <param name="handle"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.Header">
      <summary>
        <para>Gets or sets the property header&#39;s caption. This is a dependency property.</para>
      </summary>
      <value>An object that represents the header&#39;s caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.HeaderProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.Header"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.HeaderShowMode">
      <summary>
        <para>Gets or sets the property display mode. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.HeaderShowMode"/> enumeration value that specifies the property display mode.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.HeaderShowModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.HeaderShowMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.HeaderTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of property headers. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the presentation of property headers.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.HeaderTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.HeaderTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.HeaderTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a property header template based on custom logic. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.HeaderTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.HeaderTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.HighlightingMode">
      <summary>
        <para>Gets or sets the currently selected property&#39;s highlighting mode. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.HeaderHighlightingMode"/> enumeration that specifies the property highlighting mode.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.HighlightingModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.HighlightingMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.InstanceInitializer">
      <summary>
        <para>Gets or sets the instance initializer. This is a dependency property.</para>
      </summary>
      <value>An object implementing the DevExpress.Mvvm.Native.IInstanceInitializer interface.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.InstanceInitializerProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.InstanceInitializer"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.IsReadOnly">
      <summary>
        <para>Gets or sets whether the property&#39;s value can be changed by end-users. This is a dependency property.</para>
      </summary>
      <value>true, if an end-user cannot modify the property&#39;s value; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.IsReadOnlyProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.IsReadOnly"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.OnHideCollectionButtonChanged(System.Nullable{System.Boolean},System.Nullable{System.Boolean})">
      <summary>
        <para></para>
      </summary>
      <param name="eOldValue"></param>
      <param name="eNewValue"></param>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.Padding">
      <summary>
        <para>Gets or sets the thickness of the padding space between the item&#39;s borders and it&#39;s contents. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> value that specifies the amount of space between the item&#39;s borders and its contents.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PaddingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.Padding"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.Path">
      <summary>
        <para>Gets or sets the path to the database field associated with current property definition. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the name of a data field.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PathProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.Path"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitions">
      <summary>
        <para>Provides access to the child collection of properties. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.PropertyDefinitionCollection"/> object.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitionsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitions"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitionsSource">
      <summary>
        <para>Gets or sets the source from which the grid generates the property definitions. This is a dependency property.</para>
      </summary>
      <value>The source from which the grid generates the property definitions.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitionsSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitionsSource"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitionStyle">
      <summary>
        <para>Gets or sets the style applied to the property. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object that represents the style applied to the property.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitionStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitionStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitionTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of the property. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the presentation of the property.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitionTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitionTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitionTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a property definition template based on custom logic. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitionTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.PropertyDefinitionTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.RowTemplate">
      <summary>
        <para>Gets or sets a template that defines the presentation of rows displayed within the property grid. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.ControlTemplate"/> object that represents the template which defines the presentation of rows displayed within the property grid.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.RowTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.RowTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ShowChildren">
      <summary>
        <para>Gets or sets whether to show child properties. This is a dependency property.</para>
      </summary>
      <value>true, to show child properties; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ShowChildrenProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase.ShowChildren"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.PropertyDefinitionCollection">
      <summary>
        <para>This class supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyDefinitionCollection.#ctor(DevExpress.Xpf.PropertyGrid.PropertyDefinitionBase)">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <param name="owner"></param>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.PropertyGridControl">
      <summary>
        <para>The property grid control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.PropertyGrid.PropertyGridControl"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ActualDescriptionContainerStyle">
      <summary>
        <para>Gets the actual property description style. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object that specifies the actual style applied to property descriptions.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ActualDescriptionContainerStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ActualDescriptionContainerStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ActualDescriptionContainerStyleSelector">
      <summary>
        <para>Gets the actual style selector that chooses a property description style based on custom logic. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that is the actual style selector.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ActualDescriptionContainerStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ActualDescriptionContainerStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ActualDescriptionTemplateSelector">
      <summary>
        <para>Gets the actual template selector that chooses a property description template based on custom logic. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ActualDescriptionTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ActualDescriptionTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.AllowCommitOnValidationAttributeError">
      <summary>
        <para>Gets or sets whether a cell&#39;s value can be posted to a data source if it has failed the validation specified by the Data Annotations attributes. This is a dependency property.</para>
      </summary>
      <value>true, to allow posting of invalid cell values to a data source; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.AllowCommitOnValidationAttributeErrorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.AllowCommitOnValidationAttributeError"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.AllowExpanding">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.AllowExpandingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.AllowExpanding"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.AllowInstanceInitializer">
      <summary>
        <para>Gets or sets whether to enable the instance initializer for the property grid. This is a dependency property.</para>
      </summary>
      <value>true, to enable the instance initializer; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.AllowInstanceInitializerProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.AllowInstanceInitializer"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.AllowListItemInitializer">
      <summary>
        <para>Gets or sets whether to enable the list item initializer for the property grid. This is a dependency property.</para>
      </summary>
      <value>true, to enable the list item initializer; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.AllowListItemInitializerProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.AllowListItemInitializer"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.BeginInit">
      <summary>
        <para>Starts the runtime initialization.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CellValueChanged">
      <summary>
        <para>Occurs after a cell&#39;s value has been changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CellValueChangedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CellValueChanged"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CellValueChanging">
      <summary>
        <para>Fires when the updated cell value is about to be posted. Set the e.Cancel property to true to cancel the posting operation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CellValueChangingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CellValueChanging"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CloseEditor">
      <summary>
        <para>Saves the data and closes the editor nested in a focused cell.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.Collapse(System.String)">
      <summary>
        <para>Collapses the specified property or category.</para>
      </summary>
      <param name="propertyPath">A <see cref="T:System.String"/> which specifies the property or category path.</param>
    </member>
    <member name="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CustomDisplayText">
      <summary>
        <para>Enables you to display a custom text within the property grid rows.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CustomDisplayTextEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CustomDisplayText"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CustomExpand">
      <summary>
        <para>Enables you to control how properties and categories expand.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CustomExpandEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.CustomExpand"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.DescriptionContainerStyle">
      <summary>
        <para>Gets or sets the style applied to property descriptions within the grid. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object that specifies the style applied to property descriptions.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.DescriptionContainerStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.DescriptionContainerStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.DescriptionContainerStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a property description style based on custom logic. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that chooses a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.DescriptionContainerStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.DescriptionContainerStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.DescriptionTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of property descriptions. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the presentation of property descriptions.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.DescriptionTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.DescriptionTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.DescriptionTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a property description template based on custom logic. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.DescriptionTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.DescriptionTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.EndInit">
      <summary>
        <para>Ends the runtime initialization.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.Expand(System.String)">
      <summary>
        <para>Expands the specified property or category.</para>
      </summary>
      <param name="propertyPath">A <see cref="T:System.String"/> which specifies the property or category path.</param>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ExpandButtonsVisibility">
      <summary>
        <para>Gets or sets whether the expand button is visible within property grid rows that support expanding. This is a dependency property.</para>
      </summary>
      <value>A Visibility enumeration value.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ExpandButtonsVisibilityProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ExpandButtonsVisibility"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ExpandCategoriesWhenSelectedObjectChanged">
      <summary>
        <para>Gets or sets whether to automatically expand all categories when changing the selected object. This is a dependency property.</para>
      </summary>
      <value>true, to expand all categories when changing the selected object; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ExpandCategoriesWhenSelectedObjectChangedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ExpandCategoriesWhenSelectedObjectChanged"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.FilterCriteria">
      <summary>
        <para>Gets or sets the grid&#39;s filter criteria. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents filter criteria.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.FilterCriteriaProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.FilterCriteria"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.FilterMode">
      <summary>
        <para>Gets or sets the columns by which the grid data is filtered. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.PropertyGridFilterMode"/> enumeration value that specifies the columns by which grid data filtered.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.FilterModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.FilterMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.GetAvailableColumns">
      <summary>
        <para>This property supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.GetParentPath(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="fullPath"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.GetRowValueByRowPath(System.String)">
      <summary>
        <para>Returns the value of the specified property.</para>
      </summary>
      <param name="propertyPath">A <see cref="T:System.String"/> which specifies the property path.</param>
      <returns>The object that is the specified property&#39;s value.</returns>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HeaderColumnMaxWidth">
      <summary>
        <para>Gets or sets the header column&#39;s maximum width. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that specifies the header column&#39;s maximum width, in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HeaderColumnMaxWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HeaderColumnMaxWidth"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HeaderColumnMinWidth">
      <summary>
        <para>Gets or sets the header column&#39;s minimum width. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that specifies the header column&#39;s minimum width, in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HeaderColumnMinWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HeaderColumnMinWidth"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HeaderColumnWidth">
      <summary>
        <para>Gets or sets the header column&#39;s width. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.GridLength"/> value that specifies the header column&#39;s width.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HeaderColumnWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HeaderColumnWidth"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HiddenEditor">
      <summary>
        <para>Occurs after an active cell&#39;s editor has been closed or hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HiddenEditorEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HiddenEditor"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HideEditor">
      <summary>
        <para>Closes the editor nested in a focused cell and cancels all changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HighlightingMode">
      <summary>
        <para>Gets or sets the property highlighting mode for the entire property grid. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.HeaderHighlightingMode"/> enumeration that specifies the property highlighting mode.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HighlightingModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HighlightingMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HighlightNonDefaultValues">
      <summary>
        <para>Gets or sets whether to highlight non-default property values. This is a dependency property.</para>
      </summary>
      <value>true, to highlight non-default property values; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HighlightNonDefaultValuesProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.HighlightNonDefaultValues"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.InvalidateData">
      <summary>
        <para>Schedules the asynchronous data update. The data update is performed when the <see cref="M:System.Windows.UIElement.UpdateLayout"/> method is called.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.InvalidateProperty(System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="propertyPath"></param>
    </member>
    <member name="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.InvalidCellException">
      <summary>
        <para>Fires when a cell fails validation, or when it cannot be saved to a data source.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.InvalidCellExceptionEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.InvalidCellException"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.MenuCustomizations">
      <summary>
        <para>Allows you to customize a set of property menu items for the entire property grid by adding new menu items or removing existing ones. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/>&lt;<see cref="T:DevExpress.Xpf.Bars.IControllerAction"/>,&gt; object.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.OnApplyTemplate">
      <summary>
        <para>This property supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PostEditor">
      <summary>
        <para>Saves the active editor&#39;s value to a data source without closing the editor.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitions">
      <summary>
        <para>Provides access to the collection of properties. This is a dependency property.</para>
      </summary>
      <value>The collection of properties.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitionsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitions"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitionsSource">
      <summary>
        <para>Gets or sets the source from which the grid generates property definitions. This is a dependency property.</para>
      </summary>
      <value>The source from which the grid generates property definitions.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitionsSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitionsSource"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitionStyle">
      <summary>
        <para>Gets or sets the style applied to property definitions. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object that represents the style applied to property definitions.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitionStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitionStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitionTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of properties. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the presentation of properties.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitionTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitionTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitionTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a property definition template based on custom logic. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitionTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.PropertyDefinitionTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ReadOnly">
      <summary>
        <para>Gets or sets whether the end-user can modify the values of properties displayed by the <see cref="T:DevExpress.Xpf.PropertyGrid.PropertyGridControl"/>. This is a dependency property.</para>
      </summary>
      <value>true, to allow the end-user to edit property values; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ReadOnlyProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ReadOnly"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.RowPadding">
      <summary>
        <para>Gets or sets the padding for all property definitions in a property grid. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> value that specifies the amount of space between the item&#39;s borders and its contents.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.RowPaddingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.RowPadding"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ScrollIntoView(System.String)">
      <summary>
        <para>Makes the specified property visible onscreen.</para>
      </summary>
      <param name="path">A <see cref="T:System.String"/> that is a path to the required property.</param>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectedObject">
      <summary>
        <para>Gets or sets the object whose properties are currently displayed within the property grid. This is a dependency property.</para>
      </summary>
      <value>An object that represents the data source from which the grid retrieves its data.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectedObjectProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectedObject"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectedObjects">
      <summary>
        <para>Gets or sets the collection of objects whose properties are currently displayed within the property grid. This is a dependency property.</para>
      </summary>
      <value>The collection of objects that represents the data source from which the grid retrieves its data.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectedObjectsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectedObjects"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectedPropertyPath">
      <summary>
        <para>Gets or sets the path of the currently selected property.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the name of the data field the property is bound to.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectedPropertyPathProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectedPropertyPath"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectedPropertyValue">
      <summary>
        <para>Gets the value of the currently selected property.</para>
      </summary>
      <value>The object that is the value of the currently selected property.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectedPropertyValueProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectedPropertyValue"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectionChanged">
      <summary>
        <para>Occurs after the property grid&#39;s selection has been changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectionChangedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SelectionChanged"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SetRowValueByRowPath(System.String,System.Object)">
      <summary>
        <para>Sets the value of the specified property.</para>
      </summary>
      <param name="propertyPath">A <see cref="T:System.String"/> which specifies the property path.</param>
      <param name="value">The object to be set as the specified property&#39;s value.</param>
      <returns>A <see cref="T:System.Exception"/>.</returns>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowCategories">
      <summary>
        <para>Gets or sets the categories show mode. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.CategoriesShowMode"/> enumeration value that specifies the way the categories are displayed.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowCategoriesProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowCategories"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowDescriptionIn">
      <summary>
        <para>Gets or sets where to show property descriptions. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.DescriptionLocation"/> enumeration value.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowDescriptionInProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowDescriptionIn"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowEditor(System.Boolean)">
      <summary>
        <para>Activates the editor nested in a focused cell.</para>
      </summary>
      <param name="selectAll">true to select the editor&#39;s content; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowEditorButtons">
      <summary>
        <para>Gets or sets whether to display the buttons of inactive editors embedded into grid cells. This is a dependency property.</para>
      </summary>
      <value>true, to display the editor buttons when the editor is not active; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowEditorButtonsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowEditorButtons"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowGridLines">
      <summary>
        <para>Gets or sets a value indicating whether the grid lines should be visible in the property grid. This is a dependency property.</para>
      </summary>
      <value>true, to show the grid lines in the property grid; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowGridLinesProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowGridLines"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowingEditor">
      <summary>
        <para>Enables you to prevent an end-user from activating editors of individual properties.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowingEditorEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowingEditor"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowMenu">
      <summary>
        <para>Gets or sets when the Property Menu is shown. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.ShowMenuMode"/> enumeration value that specifies when the property menu is shown.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowMenuButtonInRows">
      <summary>
        <para>Gets or sets whether to display the menu button. This is a dependency property.</para>
      </summary>
      <value>true, to display the menu button; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowMenuButtonInRowsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowMenuButtonInRows"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowMenuProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowMenu"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShownEditor">
      <summary>
        <para>Occurs after the focused cell&#39;s editor has been displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShownEditorEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShownEditor"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowProperties">
      <summary>
        <para>Gets or sets whether rows should be created automatically for all fields in the underlying data source. This is a dependency property.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Xpf.PropertyGrid.ShowPropertiesMode"/> enumeration value that specifies the way rows are generated.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowPropertiesProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowProperties"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowSearchBox">
      <summary>
        <para>Gets or sets whether to display the search box. This is a dependency property.</para>
      </summary>
      <value>true, to display the search box; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowSearchBoxProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowSearchBox"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowToolPanel">
      <summary>
        <para>Gets or sets whether to display the tool panel. This is a dependency property.</para>
      </summary>
      <value>true, to display the tool panel; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowToolPanelProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ShowToolPanel"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.Sort">
      <summary>
        <para>Enables you to sort data using custom rules.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SortEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.Sort"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SortMode">
      <summary>
        <para>Gets or sets how the property grid&#39;s data is sorted when sorting is applied. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.PropertyGridSortMode"/> enumeration value that specifies the sort mode.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SortModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SortMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.TrimDisplayText">
      <summary>
        <para>Gets or sets whether to display the fully qualified name of the class within the value cell of the collection&#39;s object definition. This is a dependency property.</para>
      </summary>
      <value>true, to display only the name of the class; otherwise, false. The default value is true.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.TrimDisplayTextProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.TrimDisplayText"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.PropertyGridControl.UpdateData">
      <summary>
        <para>Performs the scheduled data update synchronously.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.UseCollectionEditor">
      <summary>
        <para>Specifies whether to enable collection editing within the grid. This is a dependency property.</para>
      </summary>
      <value>true, to enable collection editing; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.UseCollectionEditorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.UseCollectionEditor"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.UseOptimizedEditors">
      <summary>
        <para>Gets or sets whether to enable optimized editors. This is a dependency property.</para>
      </summary>
      <value>true, to enable optimized editors; otherwise, false. The default value is true.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.UseOptimizedEditorsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.UseOptimizedEditors"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.UserFilterCriteria">
      <summary>
        <para>Gets or sets the property grid&#39;s user filter criteria. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents filter criteria.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.UserFilterCriteriaProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.UserFilterCriteria"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ValidateCell">
      <summary>
        <para>Enables you to specify whether the focused cell&#39;s data is valid, and whether the node can lose focus.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ValidateCellEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ValidateCell"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ValueColumnMaxWidth">
      <summary>
        <para>Gets or sets the value column&#39;s maximum width. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that specifies the value column&#39;s maximum width, in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ValueColumnMaxWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ValueColumnMaxWidth"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ValueColumnMinWidth">
      <summary>
        <para>Gets or sets the value column&#39;s minimum width. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that specifies the value column&#39;s minimum width, in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ValueColumnMinWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ValueColumnMinWidth"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ValueColumnWidth">
      <summary>
        <para>Gets or sets the value column&#39;s width. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.GridLength"/> value that specifies the value column&#39;s width.</value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ValueColumnWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ValueColumnWidth"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.PropertyGridFilterMode">
      <summary>
        <para>Lists values that specify how properties are filtered.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridFilterMode.All">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridFilterMode.ByDescription">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridFilterMode.ByHeader">
      <summary>
        <para>Filters only by header content.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridFilterMode.ByHeaderAndValue">
      <summary>
        <para>Filters both by header content and value.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridFilterMode.ByValue">
      <summary>
        <para>Filters only by value.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.PropertyGridSortMode">
      <summary>
        <para>Lists values that specify how grid data is sorted.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridSortMode.Ascending">
      <summary>
        <para>Sorts grid data by property headers in ascending order.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridSortMode.Custom">
      <summary>
        <para>Sorts grid data using a custom sorting rule, implemented within the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.Sort"/> event handler.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridSortMode.Definitions">
      <summary>
        <para>Sorts grid data in definition order. Properties having no definitions are located below properties that have definitions.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridSortMode.Descending">
      <summary>
        <para>Sorts grid data by definition headers in descending order.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridSortMode.NoSort">
      <summary>
        <para>Doesn&#39;t sort grid data.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.PropertyGridSortMode.Unspecified">
      <summary>
        <para>Uses the value of the <see cref="P:DevExpress.Xpf.PropertyGrid.PropertyGridControl.SortMode"/> property.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.RowInfo">
      <summary>
        <para>Contains information about a data row.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.RowInfo.Children">
      <summary>
        <para>Gets a collection of objects that store information about corresponding child rows.</para>
      </summary>
      <value>A collection of RowInfo objects that contain information about the current row&#39;s children.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.RowInfo.Description">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.RowInfo.DescriptionColumn">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.RowInfo.DisplayName">
      <summary>
        <para>Gets the display name shown by the row.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.RowInfo.DisplayText">
      <summary>
        <para>Gets the display text shown by the row.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.RowInfo.EditableObject">
      <summary>
        <para>Gets the row value object.</para>
      </summary>
      <value>The row value object.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.RowInfo.FullPath">
      <summary>
        <para>Gets the full path to the data field displayed by the row. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the path to the data field.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.RowInfo.HasPropertyDefinition">
      <summary>
        <para>Gets whether the row has any appropriate property definitions.</para>
      </summary>
      <value>true, if the row corresponds to any property definition; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.RowInfo.Header">
      <summary>
        <para>Gets the actual row header.</para>
      </summary>
      <value>The row header.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.RowInfo.IsAttached">
      <summary>
        <para>Gets whether the row represents an attached property.</para>
      </summary>
      <value>true if the row represents an attached property; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.RowInfo.IsCategory">
      <summary>
        <para>Gets whether the row is a category description.</para>
      </summary>
      <value>true, if the row is a category description; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.RowInfo.IsReadOnly">
      <summary>
        <para>Gets whether the row is read-only.</para>
      </summary>
      <value>true if the row is read-only; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.RowInfo.IsVisible">
      <summary>
        <para>Gets whether the row is visible.</para>
      </summary>
      <value>true, if the row is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.RowInfo.Path">
      <summary>
        <para>Gets the path to the database field displayed by the row. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the name of a data field.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.RowInfo.Type">
      <summary>
        <para>Gets the type of data displayed by the row.</para>
      </summary>
      <value>The <see cref="T:System.Type"/> of data displayed by the row.</value>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.ShowMenuMode">
      <summary>
        <para>Lists values that specify when a Property Grid displays the Property Menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.ShowMenuMode.Always">
      <summary>
        <para>The property menu is shown by clicking the menu button or right-clicking the property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.ShowMenuMode.OnMenuButtonClick">
      <summary>
        <para>The property menu is shown only by clicking the menu button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.ShowMenuMode.OnRightClick">
      <summary>
        <para>The property menu is shown only by right-clicking the property.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.ShowPropertiesMode">
      <summary>
        <para>Lists values that specify which properties are shown.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.ShowPropertiesMode.All">
      <summary>
        <para>Display all properties in the underlying data source.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.ShowPropertiesMode.WithPropertyDefinitions">
      <summary>
        <para>Display only properties that are defined in XAML.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.TypeMatchMode">
      <summary>
        <para>Lists the values that specify the rules of type matching between the properties in the data source and the PropertyGrid&#39;s property definitions.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.TypeMatchMode.Direct">
      <summary>
        <para>Property definition of type T is linked to a data source&#39;s property of type T.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.PropertyGrid.TypeMatchMode.Extended">
      <summary>
        <para>Property definition of type T is linked to a data source&#39;s property that can be assigned to T.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.PropertyGrid.ValidateCellEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.PropertyGrid.PropertyGridControl.ValidateCell"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.PropertyGrid.ValidateCellEventArgs.#ctor(DevExpress.Xpf.PropertyGrid.RowHandle,DevExpress.Xpf.PropertyGrid.Internal.RowDataGenerator,System.Object,System.Object)">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <param name="handle"></param>
      <param name="generator"></param>
      <param name="oldValue"></param>
      <param name="newValue"></param>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.ValidateCellEventArgs.ErrorContent">
      <summary>
        <para>Gets or sets an object that describes the validation error.</para>
      </summary>
      <value>An object that represents the validation error&#39;s content.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.ValidateCellEventArgs.ErrorType">
      <summary>
        <para>Gets the error type.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.DXErrorProvider.ErrorType"/> enumeration value that specifies the error type.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.ValidateCellEventArgs.IsValid">
      <summary>
        <para>Gets or sets a value specifying whether the value is valid.</para>
      </summary>
      <value>true, if the value is valid; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.ValidateCellEventArgs.NewValue">
      <summary>
        <para>Gets a new value of the cell to be validated.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> that is the newly entered data.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.ValidateCellEventArgs.OldValue">
      <summary>
        <para>Gets the previous value of the cell to be validated.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> that is the cell&#39;s previous value.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.ValidateCellEventArgs.Row">
      <summary>
        <para>Gets information about a row that contains a validated cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.PropertyGrid.RowInfo"/> object that stores information about the row whose cell is validated.</value>
    </member>
    <member name="P:DevExpress.Xpf.PropertyGrid.ValidateCellEventArgs.ValidationException">
      <summary>
        <para>Gets or sets the error message that is displayed if the cell has failed validation.</para>
      </summary>
      <value>A <see cref="T:System.Exception"/> object that contains the error message.</value>
    </member>
  </members>
</doc>