<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="MrSales.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="JetEntityFrameworkProvider" type="JetEntityFrameworkProvider.JetProviderServices, JetEntityFrameworkProvider" />
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
      
    <provider invariantName="MySql.Data.MySqlClient" type="MySql.Data.MySqlClient.MySqlProviderServices, MySql.Data.Entity.EF6, Version=*******, Culture=neutral, PublicKeyToken=c5687fc88969c44d"></provider></providers>
  </entityFramework>
  <system.data>
    <DbProviderFactories>
      <remove invariant="JetEntityFrameworkProvider" />
      <add invariant="JetEntityFrameworkProvider" name="Jet Entity Framework Provider" description="Jet Entity Framework Provider" type="JetEntityFrameworkProvider.JetProviderFactory, JetEntityFrameworkProvider" />
      
      
    <remove invariant="MySql.Data.MySqlClient" /><add name="MySQL Data Provider" invariant="MySql.Data.MySqlClient" description=".Net Framework Data Provider for MySQL" type="MySql.Data.MySqlClient.MySqlClientFactory, MySql.Data, Version=*******, Culture=neutral, PublicKeyToken=c5687fc88969c44d" /></DbProviderFactories>
  </system.data>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="MySql.Data" publicKeyToken="c5687fc88969c44d" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.4.0" newVersion="4.1.4.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <connectionStrings>
    <add name="mrsalesdbEntities" connectionString="SdBHB8ZsqRmnAE1VatHMbn5RoqaF71P3ZE08EN6Ct08iF3TlTMeZu9cgmtACYEVNf3JKKPq08peJA4TyKYKn1UafYc7rVD/g0KvokOUvW3Xt2BimiD7WKdb/uxf4YTEr0+pysy+PJPLC5fOHzQNzQx+3rm6OOxywH3MR+Xfu11dbt/xhBLwUPKw8uAGyI5rzBJhfNrSldSfjChLf7rdWhAesK8zvF3T6Ys5FL1v2YeYYZXqsdQqBGNUBOoM/CQt9vN4RMR9A4GzLL0mjw26LrlDQ5fzNDhazKxkiFnvGRnMYKxwMw6PYzk9GANDorqWdOcjG2VX7UcL4+vH6YVznTvgjvLYwqn8RN4m2CSpeZmlpZxzfsb4KJ5BVM0JY81SFtrD+rdSOxtAV0xpsJmjpQx2X1gygD3R8vbpfSpEK2qqdrfW8wGQPMg=="
      providerName="System.Data.EntityClient" />
  </connectionStrings>
  <userSettings>
    <MrSales.Properties.Settings>
      <setting name="Dash1_lftTiles_Size" serializeAs="String">
        <value>100</value>
      </setting>
      <setting name="SuccTone" serializeAs="String">
        <value />
      </setting>
      <setting name="ErrTone" serializeAs="String">
        <value />
      </setting>
      <setting name="NotiTone" serializeAs="String">
        <value />
      </setting>
      <setting name="SupportTone" serializeAs="String">
        <value />
      </setting>
      <setting name="WarrnTone" serializeAs="String">
        <value />
      </setting>
      <setting name="SuccToneTOG" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="ErrToneTOG" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="NotiToneTOG" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="SuppToneTOG" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="WarrToneTOG" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="Barcode_repeatedNo" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="sendSMSallTime" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="UpgradeRequired" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="emp_PopupFilterMode" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="people_PopupFilterMode" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="items_PopupFilterMode" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="DoneCustom" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="ErrCustom" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="NotiCustom" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="SupportCustom" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="WarrCustom" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="OverrideReportPrinterSetting" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="ForceFontsForReports" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="showItemUsageAfterSavingInvoices" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="SalesInvoicesSplitterPosition" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="PurchasesInvoicesSplitterPosition" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="SystemUpdatesAlert" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="PAG_CountPerPage_index" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="ShowCompanyCopyRightsOnReports" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="CanReActivate" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="FingerPrintDevice_Selected" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="FingerPrintDevice_MachineNumber" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="FingerPrintDevice_IP" serializeAs="String">
        <value>*************</value>
      </setting>
      <setting name="FingerPrintDevice_Port" serializeAs="String">
        <value>4370</value>
      </setting>
      <setting name="ElectronicScale_enable" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="ElectronicScale_perfex" serializeAs="String">
        <value>XYZX</value>
      </setting>
      <setting name="ElectronicScale_perfixInstead" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="ElectronicScale_Perfixlength" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="ElectronicScale_ItemCodeStartAt" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="ElectronicScale_ItemLength" serializeAs="String">
        <value>6</value>
      </setting>
      <setting name="ElectronicScale_autoSelectUnit" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="ElectronicScale_LastPartIsWeight" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="ElectronicScale_PriceWeight_largeStartAt" serializeAs="String">
        <value>7</value>
      </setting>
      <setting name="ElectronicScale_PriceWeight_largeLength" serializeAs="String">
        <value>3</value>
      </setting>
      <setting name="ElectronicScale_PriceWeight_smallStartAt" serializeAs="String">
        <value>10</value>
      </setting>
      <setting name="ElectronicScale_PriceWeight_smallLength" serializeAs="String">
        <value>3</value>
      </setting>
      <setting name="show_data_syns_signup" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="SalesInvoicesPrintedCopies" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="SyncsData" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="api_listening_enabled" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="api_port" serializeAs="String">
        <value>5000</value>
      </setting>
    </MrSales.Properties.Settings>
  </userSettings>
</configuration>