<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.XtraSpellChecker.v20.2</name>
  </assembly>
  <members>
    <member name="N:DevExpress.XtraSpellChecker">
      <summary>
        <para>Contains classes which are used to implement the main functionality of the XtraSpellChecker.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpellChecker.CheckAsYouTypeOptions">
      <summary>
        <para>Represents an object that contains various settings for the <see cref="F:DevExpress.XtraSpellChecker.SpellCheckMode.AsYouType"/> operation mode of the spell checker.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.CheckAsYouTypeOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpellChecker.CheckAsYouTypeOptions"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.CheckAsYouTypeOptions.CheckControlsInParentContainer">
      <summary>
        <para>Gets or sets whether the spell checker is activated on a user entering the control in the parent container.</para>
      </summary>
      <value>true if the spell check is started automatically when any control within a container gets focus; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.CheckAsYouTypeOptions.Color">
      <summary>
        <para>Gets or sets the color used to underline the misspelled word.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> object.</value>
    </member>
    <member name="E:DevExpress.XtraSpellChecker.CheckAsYouTypeOptions.OptionChanged">
      <summary>
        <para>Fires when any property of the <see cref="T:DevExpress.XtraSpellChecker.CheckAsYouTypeOptions"/> object is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.CheckAsYouTypeOptions.ShowSpellCheckForm">
      <summary>
        <para>Gets or sets whether the spelling form can be invoked in a <see cref="F:DevExpress.XtraSpellChecker.SpellCheckMode.AsYouType"/> mode.</para>
      </summary>
      <value>true if a spelling form can be displayed when the spell checker is in <see cref="F:DevExpress.XtraSpellChecker.SpellCheckMode.AsYouType"/> mode; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.CheckAsYouTypeOptions.SuggestionCount">
      <summary>
        <para>Gets or sets the number of suggested words displayed in the context menu.</para>
      </summary>
      <value>An integer, representing the number of suggestions for display.</value>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.CheckAsYouTypeOptions.UnderlineStyle">
      <summary>
        <para>Gets or sets the underline style for misspelled words.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraSpellChecker.UnderlineStyle"/> enumeration member.</value>
    </member>
    <member name="N:DevExpress.XtraSpellChecker.Localization">
      <summary>
        <para>Contains classes and enumerations that are intended to localize the User Interface of DevExpress WinForms Spell Checker.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpellChecker.Localization.SpellCheckerLocalizer">
      <summary>
        <para>A base class that provides necessary functionality for custom localizers of the WinForms Spell Checker.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.Localization.SpellCheckerLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpellChecker.Localization.SpellCheckerLocalizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.Localization.SpellCheckerLocalizer.Active">
      <summary>
        <para>Gets or sets a localizer object providing localization of the user interface at runtime.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> descendant, used to localize the user interface at runtime.</value>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.Localization.SpellCheckerLocalizer.CreateDefaultLocalizer">
      <summary>
        <para>Returns a localizer object, which provides resources based on the thread&#39;s language and regional settings (culture).</para>
      </summary>
      <returns>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object representing resources based on the thread&#39;s culture.</returns>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.Localization.SpellCheckerLocalizer.CreateResXLocalizer">
      <summary>
        <para>Returns a localizer object, which provides resources based on the thread&#39;s language and regional settings (culture).</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object, which provides resources based on the thread&#39;s culture.</returns>
    </member>
    <member name="T:DevExpress.XtraSpellChecker.Localization.SpellCheckerResLocalizer">
      <summary>
        <para>A default localizer to translate resources for WinForms Spell Checker.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.Localization.SpellCheckerResLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpellChecker.Localization.SpellCheckerResLocalizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.Localization.SpellCheckerResLocalizer.GetLocalizedString(DevExpress.XtraSpellChecker.Localization.SpellCheckerStringId)">
      <summary>
        <para>Gets the string, localized by the current <see cref="T:DevExpress.XtraSpellChecker.Localization.SpellCheckerResLocalizer"/>, for the specified user interface element.</para>
      </summary>
      <param name="id">A DevExpress.XtraSpellChecker.Localization.SpellCheckerStringId enumeration value specifying the UI element whose caption (text) is to be localized.</param>
      <returns>A <see cref="T:System.String"/> representing the text to be displayed within the specified UI element.</returns>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.Localization.SpellCheckerResLocalizer.Language">
      <summary>
        <para>Returns the name of the language currently used by this localizer object.</para>
      </summary>
      <value>A <see cref="T:System.String"/> specifying the language used to localize the user interface.</value>
    </member>
    <member name="T:DevExpress.XtraSpellChecker.OptionsSpelling">
      <summary>
        <para>Contains options that affect text processing.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.OptionsSpelling.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpellChecker.OptionsSpelling"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpellChecker.PopupMenuShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraSpellChecker.SpellChecker.PopupMenuShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.PopupMenuShowingEventArgs.#ctor(DevExpress.Utils.Menu.DXPopupMenu,System.Drawing.Point)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpellChecker.PopupMenuShowingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="menu">A <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> object that represents the context menu to be invoked.</param>
      <param name="location">A <see cref="T:System.Drawing.Point"/> representing the point at which a menu is shown.</param>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.PopupMenuShowingEventArgs.Location">
      <summary>
        <para>Gets the point at which the popup menu is shown.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> representing the point at which the menu is shown.</value>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.PopupMenuShowingEventArgs.Menu">
      <summary>
        <para>Gets the control&#39;s context menu.</para>
      </summary>
      <value>An object that represents the context menu.</value>
    </member>
    <member name="T:DevExpress.XtraSpellChecker.PopupMenuShowingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraSpellChecker.SpellChecker.PopupMenuShowing"/> event.</para>
      </summary>
      <param name="sender">An object that triggers the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.PopupMenuShowing"/> event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.PopupMenuShowingEventArgs"/> object that provides data for the <see cref="E:DevExpress.XtraSpellChecker.SpellChecker.PopupMenuShowing"/> event.</param>
    </member>
    <member name="T:DevExpress.XtraSpellChecker.SharedDictionaryStorage">
      <summary>
        <para>The component that holds dictionaries available for different instances of the <see cref="T:DevExpress.XtraSpellChecker.SpellChecker"/> component.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SharedDictionaryStorage.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpellChecker.SharedDictionaryStorage"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SharedDictionaryStorage.#ctor(System.ComponentModel.IContainer)">
      <summary>
        <para>Initializes a new <see cref="T:DevExpress.XtraSpellChecker.SharedDictionaryStorage"/> class instance with the specified container.</para>
      </summary>
      <param name="container">A <see cref="T:System.ComponentModel.IContainer"/> object that provides functionality for containers.</param>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.SharedDictionaryStorage.Dictionaries">
      <summary>
        <para>Provides access to shared dictionaries that are stored in a separate component to be available throughout the application.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpellChecker.DictionaryCollection"/> object, representing a collection of shared dictionaries.</value>
    </member>
    <member name="T:DevExpress.XtraSpellChecker.SpellChecker">
      <summary>
        <para>The component that allows you to add Microsoft Office style spell checking capabilities into your application.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpellChecker.SpellChecker"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.#ctor(System.ComponentModel.IContainer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpellChecker.SpellChecker"/> class with the specified container.</para>
      </summary>
      <param name="container">An object implementing the <see cref="T:System.ComponentModel.IContainer"/> interface.</param>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.About">
      <summary>
        <para>Invokes the About dialog box.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.CalcError(System.Drawing.Point)">
      <summary>
        <para>Performs a spell check of the word, which occupies the rectangular area to which the specified point belongs.</para>
      </summary>
      <param name="p">A <see cref="T:System.Drawing.Point"/> that aims the spell checker to test a word located at that point.</param>
      <returns>A DevExpress.XtraSpellChecker.Rules.SpellCheckErrorBase class instance that contains information on the checked word and found suggestions.</returns>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.CanCheck(System.Windows.Forms.Control)">
      <summary>
        <para>Checks whether the control is registered to be used with the XtraSpellChecker.</para>
      </summary>
      <param name="control">A <see cref="T:System.Windows.Forms.Control"/> object representing a control to be checked for spelling mistakes.</param>
      <returns>true if the control is already registered to be used with the XtraSpellChecker; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.Check(DevExpress.XtraSpellChecker.Native.ISpellCheckTextControlController)">
      <summary>
        <para>Checks the spelling of the text available through the controller interface.</para>
      </summary>
      <param name="controller">An object implementing the ISpellCheckTextController interface.</param>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.Check(System.Windows.Forms.Control)">
      <summary>
        <para>Checks the specified control for spelling mistakes.</para>
      </summary>
      <param name="editControl">A <see cref="T:System.Windows.Forms.Control"/> object representing a control to be checked for spelling mistakes.</param>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.SpellChecker.CheckAsYouTypeOptions">
      <summary>
        <para>Provides access to the options used to visually indicate misspelled words and customize the context menu, invoked by right-clicking the word.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpellChecker.CheckAsYouTypeOptions"/> object.</value>
    </member>
    <member name="E:DevExpress.XtraSpellChecker.SpellChecker.CheckCompleteFormShowing">
      <summary>
        <para>Occurs when the spell check is complete.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.CheckContainer">
      <summary>
        <para>Checks the controls in the container of current control for spelling mistakes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.CheckContainer(System.Windows.Forms.Control)">
      <summary>
        <para>Checks the controls within the container for spelling mistakes.</para>
      </summary>
      <param name="container">A <see cref="T:System.Windows.Forms.Control"/> object representing a control container to be checked for spelling mistakes.</param>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.SpellChecker.FormsManager">
      <summary>
        <para>Provides access to the object that manages the spelling forms for user interaction.</para>
      </summary>
      <value>A SpellingFormsManager object.</value>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.GetCanCheckText(System.Windows.Forms.Control)">
      <summary>
        <para>Returns a value that determines whether the control can be processed by a spell checker.</para>
      </summary>
      <param name="control">A control for which the spell checking option is determined.</param>
      <returns>true if the control can be passed for spelling check; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.GetCommandsByError(DevExpress.XtraSpellChecker.Rules.SpellCheckErrorBase)">
      <summary>
        <para>Creates a list of commands, available to the end-user, which depend on the type of misspelling and current spellchecker operation mode.</para>
      </summary>
      <param name="error">A DevExpress.XtraSpellChecker.Rules.SpellCheckErrorBase object, representing a situation when a misspelled word is found.</param>
      <returns>A generic list of <see cref="T:DevExpress.XtraSpellChecker.SpellCheckerCommand"/> objects.</returns>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.GetIgnoreList(System.Windows.Forms.Control)">
      <summary>
        <para>Returns the list of words ignored during spell check.</para>
      </summary>
      <param name="control">A <see cref="T:System.Windows.Forms.Control"/> object.</param>
      <returns>An object implementing the <see cref="T:DevExpress.XtraSpellChecker.IIgnoreList"/> interface.</returns>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.GetShowSpellCheckMenu(System.Windows.Forms.Control)">
      <summary>
        <para>Returns a value that determines whether the command that invokes the spell checker should be added to the control&#39;s drop-down menu.</para>
      </summary>
      <param name="control">A control which option is determined.</param>
      <returns>true if the spell checker item is present in a context menu; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.GetSpellCheckerOptions(System.Windows.Forms.Control)">
      <summary>
        <para>Gets the options of spelling check specified for the control.</para>
      </summary>
      <param name="control">A control for which the spell checking options are specified.</param>
      <returns>An <see cref="T:DevExpress.XtraSpellChecker.OptionsSpelling"/> object containing the spell checking options.</returns>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.SpellChecker.LookAndFeel">
      <summary>
        <para>Provides access to the settings which control the SpellChecker look and feel appearance.</para>
      </summary>
      <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the SpellChecker control&#39;s look and feel.</value>
    </member>
    <member name="E:DevExpress.XtraSpellChecker.SpellChecker.OptionsFormShowing">
      <summary>
        <para>Occurs when the form used to specify the <see cref="T:DevExpress.XtraSpellChecker.OptionsSpelling"/> is about to be shown.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.SpellChecker.OptionsSpelling">
      <summary>
        <para>Provides access to the spelling options set for the spell checker instance.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraSpellChecker.OptionsSpelling"/> object containing the spell checking options.</value>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.SpellChecker.ParentContainer">
      <summary>
        <para>Gets or sets the control, representing the parent container for the XtraSpellChecker instance.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Control"/> object, which is the parent container for the controls to be checked.</value>
    </member>
    <member name="E:DevExpress.XtraSpellChecker.SpellChecker.PopupMenuShowing">
      <summary>
        <para>Occurs before a context menu is shown.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpellChecker.SpellChecker.PrepareContextMenu">
      <summary>
        <para>Occurs before a context menu is created.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.SetCanCheckText(System.Windows.Forms.Control,System.Boolean)">
      <summary>
        <para>Returns a value that specifies whether the control can be processed by a spell checker.</para>
      </summary>
      <param name="control">A control for which the spell checking option is specified.</param>
      <param name="canCheckText">true if the control can be passed for spelling check; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.SetShowSpellCheckMenu(System.Windows.Forms.Control,System.Boolean)">
      <summary>
        <para>Specifies a value that determines whether the command that invokes the spell checker should be added to the control&#39;s drop-down menu.</para>
      </summary>
      <param name="control">A control for which the option is determined.</param>
      <param name="showSpellCheckMenu">true if the spell checker item is present in a context menu; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraSpellChecker.SpellChecker.SetSpellCheckerOptions(System.Windows.Forms.Control,DevExpress.XtraSpellChecker.OptionsSpelling)">
      <summary>
        <para>Sets the options of spelling check specified for the control.</para>
      </summary>
      <param name="control">An <see cref="T:DevExpress.XtraSpellChecker.OptionsSpelling"/> object containing the spell checking options.</param>
      <param name="options">A control for which the spell checking options are specified.</param>
    </member>
    <member name="P:DevExpress.XtraSpellChecker.SpellChecker.SpellCheckMode">
      <summary>
        <para>Gets or sets the XtraSpellChecker&#39;s operation mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpellChecker.SpellCheckMode"/> enumeration member.</value>
    </member>
    <member name="E:DevExpress.XtraSpellChecker.SpellChecker.SpellingFormShowing">
      <summary>
        <para>Occurs when the spelling form is about to be displayed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpellChecker.UnderlineStyle">
      <summary>
        <para>Lists line types that can be used to mark the misspelled words.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpellChecker.UnderlineStyle.Line">
      <summary>
        <para>Draws a line below the mispelled word.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpellChecker.UnderlineStyle.WavyLine">
      <summary>
        <para>Draws a wavy line below the misspelled word.</para>
      </summary>
    </member>
  </members>
</doc>