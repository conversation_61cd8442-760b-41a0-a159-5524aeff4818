<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.XtraSpreadsheet.v20.2</name>
  </assembly>
  <members>
    <member name="N:DevExpress.XtraSpreadsheet">
      <summary>
        <para>Contains the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> class that provides the main functionality of the XtraSpreadsheet Suite.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawCellBackgroundEventArgs">
      <summary>
        <para>Provides data for the SpreadsheetControl.CustomDrawCellBackground event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawCellBackgroundEventArgs.BackColor">
      <summary>
        <para>Gets or sets the background color of the painted cell.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/>  structure that specifies the cell&#39;s background color.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawCellBackgroundEventHandler">
      <summary>
        <para>A method that will handle the CustomDrawCellBackground event of the SpreadsheetControl.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the SpreadsheetControl which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraSpreadsheet.CustomDrawCellBackgroundEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawCellEventArgs">
      <summary>
        <para>Provides data for the SpreadsheetControl.CustomDrawCell event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawCellEventArgs.Font">
      <summary>
        <para>Gets the font attributes of the painted cell.</para>
      </summary>
      <value>A System.Drawing.Font object specifying the font options (font name, size, color and etc.).</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawCellEventArgs.ForeColor">
      <summary>
        <para>Gets or sets the color of the text within the painted cell.</para>
      </summary>
      <value>A System.Drawing.Color object specifying the foreground color.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawCellEventArgs.Text">
      <summary>
        <para>Gets or sets the painted cell&#39;s text.</para>
      </summary>
      <value>A System.String object specifying the text of the painted cell.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawCellEventArgsBase">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawCell"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawCellEventArgsBase.Bounds">
      <summary>
        <para>Returns the bounding rectangle of the drawing area.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> value which specifies the object&#39;s bounding rectangle.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawCellEventArgsBase.Cell">
      <summary>
        <para>Gets the worksheet cell being painted.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Spreadsheet.Cell"/> object that is the worksheet cell.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawCellEventArgsBase.FillBounds">
      <summary>
        <para>Returns the bounding rectangle of the drawing area for painting the cell background.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> value which specifies the object&#39;s bounding rectangle.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawCellEventHandler">
      <summary>
        <para>A method that will handle the CustomDrawCell event of the SpreadsheetControl.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the SpreadsheetControl which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraSpreadsheet.CustomDrawCellEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawColumnHeaderBackgroundEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawColumnHeaderBackground"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawColumnHeaderBackgroundEventArgs.ColumnIndex">
      <summary>
        <para>Returns the column index of the column header being painted.</para>
      </summary>
      <value>An integer that is the zero-based worksheet column index.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawColumnHeaderBackgroundEventHandler">
      <summary>
        <para>A method that will handle the CustomDrawCellColumnHeaderBackground event of the SpreadsheetControl.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the SpreadsheetControl which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraSpreadsheet.CustomDrawColumnHeaderBackgroundEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawColumnHeaderEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawColumnHeader"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawColumnHeaderEventArgs.ColumnIndex">
      <summary>
        <para>Returns the column index of the column header being painted.</para>
      </summary>
      <value>An integer that is the zero-based worksheet column index.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawColumnHeaderEventHandler">
      <summary>
        <para>A method that will handle the CustomDrawColumnHeader event of the SpreadsheetControl.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the SpreadsheetControl which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraSpreadsheet.CustomDrawColumnHeaderEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawCommentIndicatorEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawCommentIndicator"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawCommentIndicatorEventArgs.Cell">
      <summary>
        <para>Gets a cell that contains a comment indicator being pained.</para>
      </summary>
      <value>A cell in a worksheet.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawCommentIndicatorEventArgs.CellBounds">
      <summary>
        <para>Returns the cell&#39;s boundaries, excluding cell borders.</para>
      </summary>
      <value>A rectangle that specifies the cell&#39;s size and location.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawCommentIndicatorEventArgs.ForeColor">
      <summary>
        <para>Gets or sets the color of the default comment indicator.</para>
      </summary>
      <value>Specifies a comment indicator&#39;s color.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawCommentIndicatorEventArgs.Size">
      <summary>
        <para>Gets or sets the size of the default comment indicator in units of measurement that are currently in effect.</para>
      </summary>
      <value>A comment indicator&#39;s size.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawCommentIndicatorEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawCommentIndicator"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the SpreadsheetControl that raised the event.</param>
      <param name="e">An object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawFrozenPaneBorderEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawFrozenPaneBorder"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawFrozenPaneBorderEventArgs.ForeColor">
      <summary>
        <para>Gets the color of the frozen pane border line.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/>  structure that specifies the color of the frozen pane border line.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawFrozenPaneBorderEventArgs.Point1">
      <summary>
        <para>Gets the first endpoint of the frozen pane border line.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> structure that specifies the point where the line drawing starts.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawFrozenPaneBorderEventArgs.Point2">
      <summary>
        <para>Gets the second endpoint of the frozen pane border line.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> structure that specifies the point where the line drawing finishes.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawFrozenPaneBorderEventArgs.Type">
      <summary>
        <para>Gets whether the frozen pane is vertical or horizontal.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.FrozenPaneBorderType"/> enumeration member.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawFrozenPaneBorderEventArgs.Width">
      <summary>
        <para>Gets the width of the frozen pane border line.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value that specifies the width of the frozen pane border line.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawFrozenPaneBorderEventHandler">
      <summary>
        <para>A method that will handle the CustomDrawFrozenPaneBorder event of the SpreadsheetControl.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the SpreadsheetControl that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraSpreadsheet.CustomDrawFrozenPaneBorderEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawHeaderEventArgsBase">
      <summary>
        <para>Represents the base class for classes that provide data for the <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawColumnHeaderBackground"/>, <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawColumnHeader"/>, <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawRowHeaderBackground"/> and <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawRowHeader"/> events of the SpreadsheetControl.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawHeaderEventArgsBase.Appearance">
      <summary>
        <para>Provides access to the properties that control the appearance of a worksheet header.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the header.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawHeaderEventArgsBase.BackColor">
      <summary>
        <para>Gets the background color of the header.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> object that identifies the background color of the header.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawHeaderEventArgsBase.Bounds">
      <summary>
        <para>Gets the header&#39;s bound rectangle.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> object specifying the header boundaries.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawHeaderEventArgsBase.Control">
      <summary>
        <para>Provides access to the SpreadsheetControl that raised the event.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawHeaderEventArgsBase.Font">
      <summary>
        <para>Gets the font used to paint the header caption.</para>
      </summary>
      <value>A System.Drawing.Font object specifying font options (font name, size, color and etc.).</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawHeaderEventArgsBase.ForeColor">
      <summary>
        <para>Gets the color used to paint the header caption text.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> object specifying the text color.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawHeaderEventArgsBase.IsHovered">
      <summary>
        <para>Gets whether a mouse is currently over the worksheet header.</para>
      </summary>
      <value>true, if a row or column header is hovered over with a mouse; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawHeaderEventArgsBase.IsSelected">
      <summary>
        <para>Gets whether the current column contains selected cell(s).</para>
      </summary>
      <value>true, if the column contains selected cell(s); otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawHeaderEventArgsBase.Text">
      <summary>
        <para>Gets the text of the header caption.</para>
      </summary>
      <value>A string that is the header caption.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawObjectEventsArgs">
      <summary>
        <para>Represents the base class for all classes that provide data for the custom draw events.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawObjectEventsArgs.Cache">
      <summary>
        <para>Gets an object that serves as the storage for pens, fonts and brushes.</para>
      </summary>
      <value>Provides methods used to paint on drawing surfaces in GDI+.</value>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.CustomDrawObjectEventsArgs.DrawDefault">
      <summary>
        <para>Renders the element using the default drawing mechanism.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawObjectEventsArgs.Graphics">
      <summary>
        <para>Gets an object used for painting.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Graphics"/> object which provides a means for painting.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawObjectEventsArgs.Handled">
      <summary>
        <para>Gets or sets whether an event is handled. If true, the default actions are not required.</para>
      </summary>
      <value>true, if default painting isn&#39;t required; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawRowHeaderBackgroundEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawRowHeaderBackground"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawRowHeaderBackgroundEventArgs.RowIndex">
      <summary>
        <para>Returns the row index of the column header being painted.</para>
      </summary>
      <value>An integer that is the zero-based worksheet row index.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawRowHeaderBackgroundEventHandler">
      <summary>
        <para>A method that will handle the CustomDrawRowHeaderBackground event of the SpreadsheetControl.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the SpreadsheetControl which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraSpreadsheet.CustomDrawRowHeaderBackgroundEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawRowHeaderEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawRowHeader"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.CustomDrawRowHeaderEventArgs.RowIndex">
      <summary>
        <para>Returns the column index of the row header being painted.</para>
      </summary>
      <value>An integer that is the zero-based worksheet row index.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.CustomDrawRowHeaderEventHandler">
      <summary>
        <para>A method that will handle the CustomDrawRowHeader event of the SpreadsheetControl.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the SpreadsheetControl which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraSpreadsheet.CustomDrawRowHeaderEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.FrozenPaneBorderType">
      <summary>
        <para>Lists values used to specify the frozen pane type.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.FrozenPaneBorderType.Horizontal">
      <summary>
        <para>The horizontal frozen pane (frozen rows).</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.FrozenPaneBorderType.Vertical">
      <summary>
        <para>The vertical frozen pane (frozen columns).</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.NameBoxItemDisplayMode">
      <summary>
        <para>Lists values used to specify what items should be displayed in the Name Box&#39;s drop-down list.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.NameBoxItemDisplayMode.Default">
      <summary>
        <para>Specifies that all items should be displayed in the Name Box.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.NameBoxItemDisplayMode.ExcludeDefinedNames">
      <summary>
        <para>Specifies that defined names should not be displayed in the Name Box.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.NameBoxItemDisplayMode.ExcludeTableNames">
      <summary>
        <para>Specifies that table names should not be displayed in the Name Box.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.PopupMenuShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.PopupMenuShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.PopupMenuShowingEventArgs.#ctor(DevExpress.XtraSpreadsheet.Menu.SpreadsheetPopupMenu,DevExpress.XtraSpreadsheet.SpreadsheetMenuType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpreadsheet.PopupMenuShowingEventArgs"/> class with the specified parameters.</para>
      </summary>
      <param name="menu">The SpreadsheetPopupMenu object which represents the context menu to be invoked.</param>
      <param name="menuType">One of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetMenuType"/> enumeration values specifying the type of the context menu.</param>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.PopupMenuShowingEventArgs.Menu">
      <summary>
        <para>Gets or sets the SpreadsheetControl&#39;s popup menu for which the event was raised.</para>
      </summary>
      <value>The SpreadsheetPopupMenu object which represents the current context menu.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.PopupMenuShowingEventArgs.MenuType">
      <summary>
        <para>Returns the type of the current SpreadsheetControl&#39;s popup menu.</para>
      </summary>
      <value>One of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetMenuType"/> enumeration values.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.PopupMenuShowingEventHandler">
      <summary>
        <para>A method that will handle the PopupMenuShowing event of the SpreadsheetControl.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the SpreadsheetControl which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraSpreadsheet.PopupMenuShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="N:DevExpress.XtraSpreadsheet.Services">
      <summary>
        <para>Contains interfaces that define services implemented in the SpreadsheetControl.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.Services.ObjectDataSourceValidationService">
      <summary>
        <para>Default service that validates the <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/> data sources before using them in the document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.Services.ObjectDataSourceValidationService.#ctor(DevExpress.XtraSpreadsheet.SpreadsheetControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpreadsheet.Services.ObjectDataSourceValidationService"/> class with the default settings.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> instance.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.Services.ObjectDataSourceValidationService.Validate(System.Collections.Generic.IEnumerable{DevExpress.DataAccess.ObjectBinding.ObjectDataSource})">
      <summary>
        <para>Provides default validation of the specified <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/> data objects before data retrieval.</para>
      </summary>
      <param name="dataSources">A <see cref="T:System.Collections.Generic.IEnumerable`1"/>&lt;<see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>,&gt; collection of <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/> instances to validate.</param>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetControl">
      <summary>
        <para>Emulates the Microsoft&#174; Excel&#174; appearance and allows you to create, load, edit, save and print spreadsheet documents.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.About">
      <summary>
        <para>Invokes the About dialog box.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.AcceptsEscape">
      <summary>
        <para>Gets or sets a value indicating whether pressing the ESC key is processed by the SpreadsheetControl.</para>
      </summary>
      <value>true if the input ESC key is processed by the SpreadsheetControl, otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.AcceptsReturn">
      <summary>
        <para>Gets or sets a value indicating whether pressing the RETURN key is processed by the SpreadsheetControl.</para>
      </summary>
      <value>true if the input RETURN key is processed by the SpreadsheetControl, otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.AcceptsTab">
      <summary>
        <para>Gets or sets a value indicating whether pressing the TAB key is processed by the SpreadsheetControl instead of moving the focus to the next control in the tab order.</para>
      </summary>
      <value>true if the input TAB key is processed by the SpreadsheetControl, otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.ActiveCell">
      <summary>
        <para>Returns an active cell in the active worksheet.</para>
      </summary>
      <value>Specifies an active cell.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.ActiveChartSheet">
      <summary>
        <para>Gets the chart sheet that is currently displayed in the SpreadsheetControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Spreadsheet.ChartSheet"/> object that specifies the active chart sheet.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.ActiveSheet">
      <summary>
        <para>Gets the sheet that is currently displayed in the SpreadsheetControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Spreadsheet.Sheet"/> descendant that specifies the active worksheet or chart sheet.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ActiveSheetChanged">
      <summary>
        <para>Occurs after an active worksheet has been changed via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ActiveSheetChanging">
      <summary>
        <para>Occurs when an active worksheet is about to be changed via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.ActiveView">
      <summary>
        <para>Provides access to a SpreadsheetView object that contains layout information. This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>A DevExpress.Spreadsheet.SpreadsheetView object.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.ActiveViewZoom">
      <summary>
        <para>Gets or sets the zoom level for the sheet currently displayed in the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>.</para>
      </summary>
      <value>An integer value specifying the zoom percentage.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.ActiveWorksheet">
      <summary>
        <para>Gets the worksheet that is currently displayed in the SpreadsheetControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Spreadsheet.Worksheet"/> object that specifies the active worksheet.</value>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback)">
      <summary>
        <para>Adds the specified service to the service container.</para>
      </summary>
      <param name="serviceType">The type of service to add.</param>
      <param name="callback">A callback object that is used to create the service. This allows a service to be declared as available, but delays the creation of the object until the service is requested.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback,System.Boolean)">
      <summary>
        <para>Adds the specified service to the service container.</para>
      </summary>
      <param name="serviceType">The type of service to add.</param>
      <param name="callback">A callback object that is used to create the service. This allows a service to be declared as available, but delays the creation of the object until the service is requested.</param>
      <param name="promote">true, to promote this request to any parent service containers; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.AddService(System.Type,System.Object)">
      <summary>
        <para>Adds the specified service to the service container.</para>
      </summary>
      <param name="serviceType">The type of service to add.</param>
      <param name="serviceInstance">An instance of the service type to add. This object must implement or inherit from the type indicated by the serviceType parameter.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.AddService(System.Type,System.Object,System.Boolean)">
      <summary>
        <para>Adds the specified service to the service container.</para>
      </summary>
      <param name="serviceType">The type of service to add.</param>
      <param name="serviceInstance">An instance of the service type to add. This object must implement or inherit from the type indicated by the serviceType parameter.</param>
      <param name="promote">true, to promote this request to any parent service containers; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.AddToolbarToMenuManager(DevExpress.XtraSpreadsheet.SpreadsheetToolbarType)">
      <summary>
        <para>Adds the specified ribbon tabs/bar groups to the existing ribbon or toolbar menu.</para>
      </summary>
      <param name="toolbarType">The <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType"/> enumeration members that specify ribbon tabs/toolbars to add to the control.</param>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.AfterDropRange">
      <summary>
        <para>Occurs after a user dropped the selected cell range in a new location.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.AfterFillRange">
      <summary>
        <para>Occurs after a cell range was automatically filled with data based on values in the source range.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.AllowDrop">
      <summary>
        <para>Overrides the System.Windows.Forms.Control.AllowDrop property.</para>
      </summary>
      <value>true, if drag-and-drop operations are allowed in the SpreadsheetControl; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.AssignShortcutKeyToCommand(System.Windows.Forms.Keys,System.Windows.Forms.Keys,DevExpress.XtraSpreadsheet.Commands.SpreadsheetCommandId)">
      <summary>
        <para>Assigns a shortcut key to a command.</para>
      </summary>
      <param name="key">A System.Windows.Forms.Keys enumeration member specifying a key to assign.</param>
      <param name="modifier">A System.Windows.Forms.Keys enumeration member specifying a modifier key.</param>
      <param name="commandId">A SpreadsheetCommandId enumeration member specifying a command.</param>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.BackColor">
      <summary>
        <para>This property is not supported by the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.BackgroundImage">
      <summary>
        <para>This property has no effect on the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.BackgroundImageLayout">
      <summary>
        <para>This property has no effect on the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.BeforeDispose">
      <summary>
        <para>Occurs before the SpreadsheetControl control is released from memory.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.BeforeDragRange">
      <summary>
        <para>Occurs when a user starts to drag the selected cell range.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.BeforeDropRange">
      <summary>
        <para>Occurs when a user is about to drop the selected cell range in a new location.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.BeforeExport">
      <summary>
        <para>Occurs before the document is saved (exported to a certain format).</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.BeforeFillRange">
      <summary>
        <para>Occurs when a user drags the fill handle to populate cells with data based on values in the source range.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.BeforeImport">
      <summary>
        <para>Occurs before a document is loaded (imported from an external source).</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.BeforePrintSheet">
      <summary>
        <para>Provides the capability to prevent printing of the required worksheets in a workbook.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.BeginUpdate">
      <summary>
        <para>Locks the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> to prevent its visual updates until the SpreadsheetControl.EndUpdate method is called.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.BorderStyle">
      <summary>
        <para>Gets or sets the SpreadsheetControl&#39;s border style.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Controls.BorderStyles"/> enumeration value specifying the border style for the SpreadsheetControl.</value>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.CancelUpdate">
      <summary>
        <para>This method supports the internal infrastructure and is not intended to be called directly from your code. Use the SpreadsheetControl.EndUpdate method instead.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CellBeginEdit">
      <summary>
        <para>Occurs before a cell editor is opened.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CellCancelEdit">
      <summary>
        <para>Occurs before the cell editor is closed and the entered value is rolled back.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CellEndEdit">
      <summary>
        <para>Occurs before a cell editor is closed and the entered value is committed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CellValueChanged">
      <summary>
        <para>Occurs after the cell content was changed via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> UI.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.CircleInvalidData">
      <summary>
        <para>Displays circles around invalid data in a worksheet to which data validation is applied.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ClearValidationCircles">
      <summary>
        <para>Hides circles around invalid data in a worksheet to which data validation is applied.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.Clipboard">
      <summary>
        <para>Provides access to the object used for working with the system Clipboard.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Spreadsheet.IClipboardManager"/> interface.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ClipboardDataObtained">
      <summary>
        <para>Occurs after data on the clipboard is obtained and recognized, but before data is pasted.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ClipboardDataPasted">
      <summary>
        <para>Occurs after data was pasted from the clipboard onto a worksheet.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ClipboardDataPasting">
      <summary>
        <para>Occurs before data is pasted into destination cells.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.CloseCellEditor(DevExpress.XtraSpreadsheet.CellEditorEnterValueMode)">
      <summary>
        <para>Closes the cell editor.</para>
      </summary>
      <param name="mode">A <see cref="T:DevExpress.XtraSpreadsheet.CellEditorEnterValueMode"/> enumeration member.</param>
      <returns>true, if the cell editor is closed successfully; otherwise, false.</returns>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ColumnsInserted">
      <summary>
        <para>Occurs after new columns have been added to a worksheet via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ColumnsInserting">
      <summary>
        <para>Occurs when new columns are about to be inserted into a worksheet.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ColumnsRemoved">
      <summary>
        <para>Occurs after columns have been deleted from a worksheet via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ColumnsRemoving">
      <summary>
        <para>Occurs when columns are about to be deleted from a worksheet.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ColumnWidthChanged">
      <summary>
        <para>Occurs after the column width was changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CommentInserted">
      <summary>
        <para>Occurs when a comment is inserted.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CommentInserting">
      <summary>
        <para>Occurs before inserting a comment.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CommentRemoved">
      <summary>
        <para>Occurs when a comment is deleted.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CommentRemoving">
      <summary>
        <para>Occurs before deleting a comment.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ContentChanged">
      <summary>
        <para>Occurs when the document content is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CopiedRangePasted">
      <summary>
        <para>Occurs after the range content has been pasted into target cells.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CopiedRangePasting">
      <summary>
        <para>Occurs before the range content is pasted into target cells.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.CreateBars">
      <summary>
        <para>Creates a <see cref="T:DevExpress.XtraBars.BarManager"/> that contains all SpreadsheetControl-specific bars.</para>
      </summary>
      <returns>A component that manages the created toolbars.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.CreateBars(DevExpress.XtraSpreadsheet.SpreadsheetToolbarType)">
      <summary>
        <para>Adds a <see cref="T:DevExpress.XtraBars.BarManager"/> with the specified bars to the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>.</para>
      </summary>
      <param name="toolbarType">The <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType"/> enumeration members that specify toolbars to add to the control.</param>
      <returns>A component that manages the created toolbars.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.CreateCommand(DevExpress.XtraSpreadsheet.Commands.SpreadsheetCommandId)">
      <summary>
        <para>Creates a command by its identifier.</para>
      </summary>
      <param name="commandId">A DevExpress.XtraSpreadsheet.Commands.SpreadsheetCommandId structure that identifies the command.</param>
      <returns>A DevExpress.XtraSpreadsheet.Commands.SpreadsheetCommand object that is the command used in a Spreadsheet UI.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.CreateNewDocument">
      <summary>
        <para>Creates and loads a new empty workbook.</para>
      </summary>
      <returns>true, if the document is created successfully; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.CreateRibbon">
      <summary>
        <para>Creates a <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> with all SpreadsheetControl-specific ribbon tabs.</para>
      </summary>
      <returns>The created ribbon.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.CreateRibbon(DevExpress.XtraSpreadsheet.SpreadsheetToolbarType)">
      <summary>
        <para>Adds a <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> with the specified tabs to the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>.</para>
      </summary>
      <param name="toolbarType">The <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType"/> enumeration members that specify ribbon tabs to add to the control.</param>
      <returns>The created ribbon.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.CreateRibbonStatusBar(DevExpress.XtraBars.Ribbon.RibbonControl)">
      <summary>
        <para>Creates a <see cref="T:DevExpress.XtraBars.Ribbon.RibbonStatusBar"/> with all Spreadsheet-related items and binds it to the specified <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/>.</para>
      </summary>
      <param name="ribbon">Specifies a Ribbon control to which the status bar should be bound.</param>
      <returns>The created status bar.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.CreateRibbonStatusBar(DevExpress.XtraBars.Ribbon.RibbonControl,DevExpress.XtraSpreadsheet.SpreadsheetStatusBarItems)">
      <summary>
        <para>Creates a <see cref="T:DevExpress.XtraBars.Ribbon.RibbonStatusBar"/> with the specified items and binds it to the <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/>.</para>
      </summary>
      <param name="ribbon">Specifies a Ribbon control to which the status bar should be bound.</param>
      <param name="statusBarItems">Specifies items to display on the status bar.</param>
      <returns>The created status bar.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.CreateStatusBarManager">
      <summary>
        <para>Adds a status bar with all Spreadsheet-related items to the Spreadsheet control with a Bar UI.</para>
      </summary>
      <returns>A component that manages the created status bar.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.CreateStatusBarManager(DevExpress.XtraSpreadsheet.SpreadsheetStatusBarItems)">
      <summary>
        <para>Adds a status bar with the specified items to the Spreadsheet control with a Bar UI.</para>
      </summary>
      <param name="statusBarItems">Specifies items to display on the status bar.</param>
      <returns>A component that manages the created status bar.</returns>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomAssemblyLoading">
      <summary>
        <para>Occurs before a custom assembly is loaded for use as the Entity Framework data source during mail merge and allows cancelling loading.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomCellEdit">
      <summary>
        <para>Allows you to assign a custom in-place editor to a cell.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawCell">
      <summary>
        <para>Allows you to paint cell content.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawCellBackground">
      <summary>
        <para>Performs custom painting of the cell background.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawColumnHeader">
      <summary>
        <para>Enables the column header to be painted manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawColumnHeaderBackground">
      <summary>
        <para>Enables the column header background to be painted manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawCommentIndicator">
      <summary>
        <para>Allows you to customize comment indicators.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawFrozenPaneBorder">
      <summary>
        <para>Enables frozen pane borders to be painted manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawRowHeader">
      <summary>
        <para>Enables the row header to be painted manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomDrawRowHeaderBackground">
      <summary>
        <para>Enables the row header background to be painted manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.DataBindingWarning">
      <summary>
        <para>Occurs before a data binding warning dialog is shown in the SpreadsheetControl.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.DefinedNameConflictResolving">
      <summary>
        <para>Occurs when a formula or sheet being moved or copied contains a defined name which already exists on the destination worksheet or workbook.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.DefinedNameDeleting">
      <summary>
        <para>Occurs before a Defined Name is deleted.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.DefinedNameEditing">
      <summary>
        <para>Occurs before a Defined Name is being edited.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.DefinedNameValidating">
      <summary>
        <para>Occurs when a new  Defined Name is created before it is added to a collection.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.Document">
      <summary>
        <para>Provides access to a workbook loaded in the control.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Spreadsheet.IWorkbook"/> interface.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.DocumentClosing">
      <summary>
        <para>Occurs when a document that has not yet been saved is about to be closed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.DocumentLoaded">
      <summary>
        <para>Occurs after a document is loaded into the Spreadsheet control.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.DocumentPropertiesChanged">
      <summary>
        <para>Occurs after one of the <see cref="T:DevExpress.Spreadsheet.DocumentProperties"/> is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.DocumentSaved">
      <summary>
        <para>Occurs after a document has been saved.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.DpiX">
      <summary>
        <para>Gets the current dpi value for the X-coordinate.</para>
      </summary>
      <value>A Single dpi value.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.DpiY">
      <summary>
        <para>Gets the current dpi value for the Y-coordinate.</para>
      </summary>
      <value>A Single dpi value.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.EmptyDocumentCreated">
      <summary>
        <para>Occurs when a new document is created in the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.EncryptedFileIntegrityCheckFailed">
      <summary>
        <para>Occurs when the encrypted file does not pass data integrity verification.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.EncryptedFilePasswordCheckFailed">
      <summary>
        <para>Occurs when the encryption password is empty or invalid.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.EncryptedFilePasswordRequest">
      <summary>
        <para>Occurs when the <see cref="P:DevExpress.XtraSpreadsheet.Import.WorkbookImportOptions.Password"/> property is not set or contains the wrong password.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.EndUpdate">
      <summary>
        <para>Unlocks the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> object after a call to the SpreadsheetControl.BeginUpdate method and causes an immediate visual update.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ExportToHtml(System.IO.Stream,DevExpress.Spreadsheet.CellRange)">
      <summary>
        <para>Exports the specified range to the specified stream in HTML format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created HTML file should be sent.</param>
      <param name="range">A <see cref="T:DevExpress.Spreadsheet.CellRange"/> object to be exported to HTML.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ExportToHtml(System.IO.Stream,DevExpress.Spreadsheet.Worksheet)">
      <summary>
        <para>Exports the specified worksheet to the specified stream in HTML format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created HTML file should be sent.</param>
      <param name="sheet">A <see cref="T:DevExpress.Spreadsheet.Worksheet"/> object to be exported to HTML.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ExportToHtml(System.IO.Stream,DevExpress.XtraSpreadsheet.Export.HtmlDocumentExporterOptions)">
      <summary>
        <para>Exports the document&#39;s data to the specified stream in HTML format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created HTML file should be sent.</param>
      <param name="options">A <see cref="T:DevExpress.XtraSpreadsheet.Export.HtmlDocumentExporterOptions"/> instance containing required export options.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ExportToHtml(System.IO.Stream,System.Int32)">
      <summary>
        <para>Exports the specified worksheet to the specified stream in HTML format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created HTML file should be sent.</param>
      <param name="sheetIndex">An integer value that is the index of the worksheet to be exported to HTML.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ExportToHtml(System.String,DevExpress.Spreadsheet.CellRange)">
      <summary>
        <para>Exports the specified range to the specified file in HTML format.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> value which contains the full path (including the file name and extension) specifying where the HTML file will be created.</param>
      <param name="range">A <see cref="T:DevExpress.Spreadsheet.CellRange"/> object to be exported to HTML.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ExportToHtml(System.String,DevExpress.Spreadsheet.Worksheet)">
      <summary>
        <para>Exports the specified worksheet to the specified file in HTML format.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> value which contains the full path (including the file name and extension) specifying where the HTML file will be created.</param>
      <param name="sheet">A <see cref="T:DevExpress.Spreadsheet.Worksheet"/> object to be exported to HTML.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ExportToHtml(System.String,DevExpress.XtraSpreadsheet.Export.HtmlDocumentExporterOptions)">
      <summary>
        <para>Exports the document&#39;s data to the specified file in HTML format using the specified options.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> value which contains the full path (including the file name and extension) specifying where the HTML file will be created.</param>
      <param name="options">A <see cref="T:DevExpress.XtraSpreadsheet.Export.HtmlDocumentExporterOptions"/> instance containing required export options.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ExportToHtml(System.String,System.Int32)">
      <summary>
        <para>Exports the specified worksheet to the specified file in HTML format.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> value which contains the full path (including the file name and extension) specifying where the HTML file will be created.</param>
      <param name="sheetIndex">An integer value that is the index of the worksheet to be exported to HTML.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ExportToPdf(System.IO.Stream)">
      <summary>
        <para>Exports the content of the SpreadsheetControl to the specified stream in PDF format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ExportToPdf(System.IO.Stream,DevExpress.XtraPrinting.PdfExportOptions)">
      <summary>
        <para>Exports the content of the SpreadsheetControl to the specified stream in PDF format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="pdfExportOptions">A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the PDF export options.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ExportToPdf(System.String)">
      <summary>
        <para>Exports the content of the SpreadsheetControl to the specified file in PDF format.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> which specifies the name (including the full path) of the PDF file to which the data is exported.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ExportToPdf(System.String,DevExpress.XtraPrinting.PdfExportOptions)">
      <summary>
        <para>Exports the content of the SpreadsheetControl to the specified file in PDF format using the specified options.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> which specifies the name (including the full path) of the PDF file to which the data is exported.</param>
      <param name="pdfExportOptions">A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the PDF export options.</param>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.Font">
      <summary>
        <para>This property is not supported by the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.ForeColor">
      <summary>
        <para>This property is not supported by the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.GetCellBounds(System.Int32,System.Int32)">
      <summary>
        <para>Obtains the coordinates and size of a cell specified by the row and column indices.</para>
      </summary>
      <param name="rowIndex">An integer that is the zero-based index of the row that contains the required cell.</param>
      <param name="columnIndex">An integer that is the zero-based index of the column that contains the required cell.</param>
      <returns>A <see cref="T:System.Drawing.Rectangle"/> object that is the rectangle with the specified location and size in pixels.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.GetCellFromPoint(System.Drawing.PointF)">
      <summary>
        <para>Gets a worksheet cell located at the specified point.</para>
      </summary>
      <param name="clientPoint">A <see cref="T:System.Drawing.PointF"/> structure specifying the location for which the position is retrieved. The point coordinates are measured in documents units.</param>
      <returns>A <see cref="T:DevExpress.Spreadsheet.Cell"/> object.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.GetSelectedRanges">
      <summary>
        <para>Returns cell ranges currently selected in the active worksheet.</para>
      </summary>
      <returns>A list of the <see cref="T:DevExpress.Spreadsheet.CellRange"/> objects.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.GetSelectedShapes">
      <summary>
        <para>Returns shapes currently selected in the active worksheet.</para>
      </summary>
      <returns>A list of the <see cref="T:DevExpress.Spreadsheet.Shape"/> objects.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.GetService(System.Type)">
      <summary>
        <para>Gets the service object of the specified type.</para>
      </summary>
      <param name="serviceType">An object that specifies the type of service object to get.</param>
      <returns>A service object of the specified type, or a null reference (Nothing in Visual Basic) if there is no service object of this type.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.GetService``1">
      <summary>
        <para>Gets the specified service.</para>
      </summary>
      <typeparam name="T"></typeparam>
      <returns>A service object of the specified type or null for reference types and zero for numeric value types if a service is not available.</returns>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.HyperlinkClick">
      <summary>
        <para>Occurs when an end-user activates the hyperlink before navigating to the referenced location.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.InitializeDocument">
      <summary>
        <para>Occurs before a document is loaded. Handle this event to set the initial document settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.InvalidFormatException">
      <summary>
        <para>Fires when the supplied data could not be recognized as data in the assumed format for import.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.IsCellEditorActive">
      <summary>
        <para>Gets whether the cell editor is currently active.</para>
      </summary>
      <value>true, if the cell editor is opened; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.IsPrintingAvailable">
      <summary>
        <para>Indicates whether the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> can be printed or exported.</para>
      </summary>
      <value>true, if the control can be printed and exported; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.IsPrintPreviewAvailable">
      <summary>
        <para>Gets a value indicating whether a preview of the SpreadsheetControl&#39;s document is allowed.</para>
      </summary>
      <value>true, if the print preview is available; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.IsUpdateLocked">
      <summary>
        <para>Returns a value indicating whether the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> is locked for update.</para>
      </summary>
      <value>true, if the control is locked; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.LayoutUnit">
      <summary>
        <para>Gets or sets a unit of measure used for a document layout.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Office.DocumentLayoutUnit"/> enumeration value.</value>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.LoadDocument">
      <summary>
        <para>Invokes the Open dialog, creates a specific importer and loads the file.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.LoadDocument(System.Byte[])">
      <summary>
        <para>Load the document from a <see cref="T:System.Byte"/>[] array.</para>
      </summary>
      <param name="buffer">A System.Byte[] object that is an array of bytes containing document data.</param>
      <returns>true, if the document is loaded succesfully; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.LoadDocument(System.Byte[],DevExpress.Spreadsheet.DocumentFormat)">
      <summary>
        <para>Loads a document from a byte array.</para>
      </summary>
      <param name="buffer">A <see cref="T:System.Byte"/>[] object that is an array of bytes containing document data in the specified format.</param>
      <param name="format">A <see cref="T:DevExpress.Spreadsheet.DocumentFormat"/> enumeration member specifying the format of the document to be loaded.</param>
      <returns>true, if the document is successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.LoadDocument(System.IO.Stream)">
      <summary>
        <para>Loads the document from a stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object that is the stream used to load a document.</param>
      <returns>true, if the document is loaded succesfully; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.LoadDocument(System.IO.Stream,DevExpress.Spreadsheet.DocumentFormat)">
      <summary>
        <para>Loads a document from a stream, specifying the document format.</para>
      </summary>
      <param name="stream">The stream from which to load a document.</param>
      <param name="format">One of the <see cref="T:DevExpress.Spreadsheet.DocumentFormat"/> members.</param>
      <returns>true, if a document is loaded successfully; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.LoadDocument(System.String)">
      <summary>
        <para>Loads a document from a file.</para>
      </summary>
      <param name="fileName">A string specifying the file to load (including the full path).</param>
      <returns>true, if a document is loaded successfully; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.LoadDocument(System.String,DevExpress.Spreadsheet.DocumentFormat)">
      <summary>
        <para>Loads a document from a file, specifying the document format.</para>
      </summary>
      <param name="fileName">A string specifying the file to load (including the full path).</param>
      <param name="format">One of the <see cref="T:DevExpress.Spreadsheet.DocumentFormat"/> members.</param>
      <returns>true, if a document is loaded successfully; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.LoadDocument(System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Invokes the Open file dialog as a child of the specified parent window.</para>
      </summary>
      <param name="parent">The <see cref="T:System.Windows.Forms.IWin32Window"/> that is the parent window.</param>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.LookAndFeel">
      <summary>
        <para>Provides access to the control&#39;s look and feel settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the control&#39;s look and feel.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.MenuManager">
      <summary>
        <para>Gets or sets the menu manager which controls the look and feel of context menus.</para>
      </summary>
      <value>An object that implements the DevExpress.Utils.Menu.IDXMenuManager interface.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.Modified">
      <summary>
        <para>Gets or sets a value that indicates that the document content was modified since it was last saved.</para>
      </summary>
      <value>true, if the control&#39;s content was modified since it was last saved; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ModifiedChanged">
      <summary>
        <para>Occurs when the value of the <see cref="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.Modified"/> property is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.OpenCellEditor(DevExpress.XtraSpreadsheet.CellEditorMode)">
      <summary>
        <para>Invokes a cell editor and switches to the spreadsheet edit mode, allowing you to edit cell content.</para>
      </summary>
      <param name="mode">A <see cref="T:DevExpress.XtraSpreadsheet.CellEditorMode"/> that specifies the editor and the mode in which it is activated.</param>
      <returns>true, if the cell editor is active; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.Options">
      <summary>
        <para>Provides access to the variety of options which can be specified for the document loaded into the SpreadsheetControl and the control itself.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions"/> object.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.PanesFrozen">
      <summary>
        <para>Occurs after a worksheet area has been frozen via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.PanesUnfrozen">
      <summary>
        <para>Occurs after a frozen worksheet area has been unlocked via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.PopupMenuShowing">
      <summary>
        <para>Occurs when a popup menu is about to be displayed for the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s visual elements.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.Print">
      <summary>
        <para>Prints the document to the default printer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.Print(System.Drawing.Printing.PrinterSettings)">
      <summary>
        <para>Prints the document using the specified printer settings.</para>
      </summary>
      <param name="printerSettings">A <see cref="T:System.Drawing.Printing.PrinterSettings"/> object that contains printer settings.</param>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.PropertyChanged">
      <summary>
        <para>Occurs every time any of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> class properties has changed its value.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ProtectionWarning">
      <summary>
        <para>Occurs when attempting to edit a locked cell in a protected worksheet.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.RangeCopied">
      <summary>
        <para>Occurs after the range content has been copied.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.RangeCopying">
      <summary>
        <para>Occurs before a cell range is copied in a worksheet.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.ReadOnly">
      <summary>
        <para>Gets or sets whether document modifications are prohibited.</para>
      </summary>
      <value>true, if the document is in a read-only state; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ReadOnlyChanged">
      <summary>
        <para>Occurs when the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s read-only state is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.RemoveService(System.Type)">
      <summary>
        <para>Removes the service of the specified type from the service container.</para>
      </summary>
      <param name="serviceType">The type of service to remove.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.RemoveService(System.Type,System.Boolean)">
      <summary>
        <para>Removes the service of the specified type from the service container.</para>
      </summary>
      <param name="serviceType">The type of service to remove.</param>
      <param name="promote">true, to promote this request to any parent service containers; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.RemoveShortcutKey(System.Windows.Forms.Keys,System.Windows.Forms.Keys)">
      <summary>
        <para>Removes a command shortcut.</para>
      </summary>
      <param name="key">A System.Windows.Forms.Keys enumeration member specifying a shortcut key.</param>
      <param name="modifier">A System.Windows.Forms.Keys enumeration member specifying a modifier key.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ReplaceService``1(``0)">
      <summary>
        <para>Performs a service substitution.</para>
      </summary>
      <param name="newService">A service of the specified type that should be replaced.</param>
      <typeparam name="T">The type of the service to replace.</typeparam>
      <returns>The registered service of the specified type, or null (Nothing in Visual Basic) if the service does not exist.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ResetLayout">
      <summary>
        <para>Updates the layout of the document loaded into control and the control itself.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.RightToLeft">
      <summary>
        <para>This property is not in effect for the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.RowHeightChanged">
      <summary>
        <para>Occurs after the row height was changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.RowsInserted">
      <summary>
        <para>Occurs after new rows have been added to a worksheet via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.RowsInserting">
      <summary>
        <para>Occurs when new rows are about to be inserted into a worksheet.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.RowsRemoved">
      <summary>
        <para>Occurs after rows have been deleted from a worksheet via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.RowsRemoving">
      <summary>
        <para>Occurs when rows are about to be deleted from a worksheet.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SaveDocument">
      <summary>
        <para>Saves a document in its original format to its original location.</para>
      </summary>
      <returns>true, if a document has been successfully saved; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SaveDocument(DevExpress.Spreadsheet.DocumentFormat)">
      <summary>
        <para>Saves a document to an array of bytes in the specified format.</para>
      </summary>
      <param name="format">A <see cref="T:DevExpress.Spreadsheet.DocumentFormat"/> enumeration member specifying the format of the document to be saved.</param>
      <returns>A <see cref="T:System.Byte"/>[] object that is an array of bytes containing document data in the specified format.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SaveDocument(DevExpress.Spreadsheet.DocumentFormat,DevExpress.Spreadsheet.EncryptionSettings)">
      <summary>
        <para>Saves a document to an array of bytes in the specified format and with specified encryption settings.</para>
      </summary>
      <param name="format">The format of the exported document.</param>
      <param name="encryptionSettings">Document encryption settings.</param>
      <returns>An array of bytes containing document data in the specified format.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SaveDocument(System.IO.Stream,DevExpress.Spreadsheet.DocumentFormat)">
      <summary>
        <para>Saves the workbook to a stream, specifying the export format.</para>
      </summary>
      <param name="stream">The <see cref="T:System.IO.Stream"/> object to output the document to.</param>
      <param name="format">One of the <see cref="T:DevExpress.Spreadsheet.DocumentFormat"/> values that is the format of the exported document.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SaveDocument(System.IO.Stream,DevExpress.Spreadsheet.DocumentFormat,DevExpress.Spreadsheet.EncryptionSettings)">
      <summary>
        <para>Saves the workbook to a stream, specifying the export format and encryption options.</para>
      </summary>
      <param name="stream">The stream used to output the document.</param>
      <param name="format">The format of the exported document.</param>
      <param name="encryptionSettings">Document encryption settings.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SaveDocument(System.String)">
      <summary>
        <para>Saves the document to the specified file in the Excel or text format. The file format is identified by the file extension.</para>
      </summary>
      <param name="fileName">Specifies the file path to save the document to.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SaveDocument(System.String,DevExpress.Spreadsheet.DocumentFormat)">
      <summary>
        <para>Saves the control&#39;s document to a file, specifying the document format.</para>
      </summary>
      <param name="fileName">A string value specifying the path to a file in which to save the control&#39;s document.</param>
      <param name="format">One of the <see cref="T:DevExpress.Spreadsheet.DocumentFormat"/> enumeration values.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SaveDocument(System.String,DevExpress.Spreadsheet.DocumentFormat,DevExpress.Spreadsheet.EncryptionSettings)">
      <summary>
        <para>Saves the control&#39;s document to a file, specifying the document format and encryption settings.</para>
      </summary>
      <param name="fileName">The path to a file in which to save the document.</param>
      <param name="format">The format of exported document.</param>
      <param name="encryptionSettings">Document encryption settings.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SaveDocument(System.String,DevExpress.Spreadsheet.EncryptionSettings)">
      <summary>
        <para>Saves the document to a file with the specified encryption settings.</para>
      </summary>
      <param name="fileName">The path to a file in which to save the document.</param>
      <param name="encryptionSettings">Document encryption settings.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SaveDocument(System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Saves a document in its original format to its original location. If original format and location are not specified, invokes the Save As dialog that is shown modally as a child of the specified parent window.</para>
      </summary>
      <param name="parent">The <see cref="T:System.Windows.Forms.IWin32Window"/> that is the parent window.</param>
      <returns>true, if a document has been successfully saved; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SaveDocumentAs">
      <summary>
        <para>Invokes the Save As dialog and saves a document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SaveDocumentAs(System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Invokes the Save As dialog which is shown modally as a child of the specified parent window.</para>
      </summary>
      <param name="parent">The <see cref="T:System.Windows.Forms.IWin32Window"/> that is the parent window.</param>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ScrollPositionChanged">
      <summary>
        <para>Occurs when a worksheet is being scrolled in the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.SelectedCell">
      <summary>
        <para>Gets or sets an active cell in the active worksheet.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Spreadsheet.CellRange"/> object specifying an active cell in a worksheet that is currently active. If you assign a cell located in a worksheet that is not active, an exception is raised.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.SelectedComment">
      <summary>
        <para>Gets or sets a comment selected in the active worksheet.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Spreadsheet.Comment"/> object, or null (Nothing in VB) if a comment is not selected.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.SelectedPicture">
      <summary>
        <para>Gets or sets a picture selected in the active worksheet.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Spreadsheet.Picture"/> object. If you assign a picture located in a worksheet that is not active, an exception is raised.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.SelectedShape">
      <summary>
        <para>Gets or sets a shape selected in the active worksheet.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Spreadsheet.Shape"/> object. If you assign a shape located in a worksheet that is not active, an exception is raised.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.Selection">
      <summary>
        <para>Gets or sets a cell range selected in the active worksheet.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Spreadsheet.CellRange"/> object specifying the cell selection in a worksheet that is currently active. If you assign a cell range located in a worksheet that is not active, an exception is raised.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.SelectionChanged">
      <summary>
        <para>Fires when the selection changes in an active worksheet.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SetSelectedRanges(System.Collections.Generic.IList{DevExpress.Spreadsheet.CellRange})">
      <summary>
        <para>Sets cell ranges selected in the active worksheet.</para>
      </summary>
      <param name="ranges">A list of the <see cref="T:DevExpress.Spreadsheet.CellRange"/> objects.</param>
      <returns>true if cell ranges are selected successfully; otherwise false. If you pass null (Nothing in Visual Basic), or an empty list, or a list that contains at least one cell range located in a worksheet other than the current worksheet, the method returns false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SetSelectedRanges(System.Collections.Generic.IList{DevExpress.Spreadsheet.CellRange},System.Boolean)">
      <summary>
        <para>Selects cell ranges in the worksheet and specifies whether the selection expands to fit merged cells.</para>
      </summary>
      <param name="ranges">A list of the <see cref="T:DevExpress.Spreadsheet.CellRange"/> objects.</param>
      <param name="expandToMergedCellsSize">true, to expand the selection to include merged cells; otherwise, false.</param>
      <returns>true, if cell ranges are selected successfully; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.SetSelectedShapes(System.Collections.Generic.IList{DevExpress.Spreadsheet.Shape})">
      <summary>
        <para>Sets shapes selected in the active worksheet.</para>
      </summary>
      <param name="Shapes">A list of the <see cref="T:DevExpress.Spreadsheet.Shape"/> objects.</param>
      <returns>true if shapes are selected successfully; otherwise false. If you pass a list that contains at least one shape located in a worksheet that is not active, the method returns false.</returns>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ShapeInserted">
      <summary>
        <para>Occurs after a drawing object has been inserted into a worksheet.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ShapeRemoved">
      <summary>
        <para>Occurs after a drawing object has been removed from a worksheet.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ShapeRemoving">
      <summary>
        <para>Occurs before a drawing object is removed from a worksheet.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ShapesCopying">
      <summary>
        <para>Occurs before a drawing object is copied.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.SheetInserted">
      <summary>
        <para>Occurs after a new worksheet has been added into a workbook via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.SheetRemoved">
      <summary>
        <para>Occurs after a worksheet has been removed from a workbook via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.SheetRemoving">
      <summary>
        <para>Occurs when a worksheet is about to be removed from a workbook.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.SheetRenamed">
      <summary>
        <para>Occurs after a worksheet has been renamed via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.SheetRenaming">
      <summary>
        <para>Occurs when a worksheet is about to be renamed via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ShowPrintDialog">
      <summary>
        <para>Invokes the Print dialog to select a printer, specify the print options (number of copies, page range, and paper source) and print the document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ShowPrintPreview">
      <summary>
        <para>Invokes the Print Preview window with a Bar UI.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.ShowRibbonPrintPreview">
      <summary>
        <para>Invokes the Print Preview window with a Ribbon UI.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.ToolTipController">
      <summary>
        <para>Gets or sets the tooltip controller component that controls the appearance, position and the content of the hints displayed by the SpreadsheetControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.ToolTipController"/> component which controls the appearance and behavior of the hints displayed by the SpreadsheetControl.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.UnhandledException">
      <summary>
        <para>This event is raised when an exception, unhandled by the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>, occurs.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.Unit">
      <summary>
        <para>Gets or sets a unit of measure used in the workbook.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Office.DocumentUnit"/> enumeration member.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.UnitChanged">
      <summary>
        <para>Fires after a unit of measurement used in the workbook is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.UnitChanging">
      <summary>
        <para>Fires before a unit of measurement used within the workbook is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControl.UpdateCommandUI">
      <summary>
        <para>Refreshes the SpreadsheetControl command UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.UpdateUI">
      <summary>
        <para>Raised when changes occur which may affect the control&#39;s UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ValidateCustomSqlQuery">
      <summary>
        <para>Allows validation of the custom SQL query created using the Data Source Wizard.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.VisibleRange">
      <summary>
        <para>Gets the cell range that is currently visible in the SpreadsheetControl.</para>
      </summary>
      <value>Specifies the currently visible cell range.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.VisibleRangeChanged">
      <summary>
        <para>Occurs after the visible cell range was changed in the Spreadsheet control.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.VisibleUnfrozenRange">
      <summary>
        <para>Gets the visible cell range of the scrollable area when there are frozen rows and columns in the worksheet.</para>
      </summary>
      <value>Specifies the visible unfrozen range.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControl.WorksheetDisplayArea">
      <summary>
        <para>Provides access to an object enabling you to specify a worksheet area that is visible in the SpreadsheetControl and allows end-user input.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.WorksheetDisplayArea"/> object.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.ZoomChanged">
      <summary>
        <para>Occurs when the current zoom level of the active worksheet has been changed via the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s UI.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions">
      <summary>
        <para>Contains various options you can specify for the Spreadsheet control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions.#ctor(DevExpress.XtraSpreadsheet.Internal.InnerSpreadsheetDocumentServer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions"/> class with the specified settings.</para>
      </summary>
      <param name="documentServer"></param>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions.Behavior">
      <summary>
        <para>Provides access to an object that enables you to apply restrictions to different spreadsheet operations.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetBehaviorOptions"/> class instance containing restriction specifications.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions.DataSourceOptions">
      <summary>
        <para>Allows you to specify whether to prohibit the ObjectDataSource data retrieval, prompt the user or silently load the data.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions.DataSourceWizard">
      <summary>
        <para>Provides access to the customization options of the Data Source Wizard.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetDataSourceWizardOptions"/> object containing options which affect the layout and behavior of the Data Source Wizard and Query Builder.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions.FormulaAutoComplete">
      <summary>
        <para>Provides access to options that control the Formula AutoComplete behavior.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetFormulaAutoCompleteOptions"/> object containing options which affect the Formula AutoComplete functionality.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions.HorizontalScrollbar">
      <summary>
        <para>Provides access to the options specific to the horizontal scrollbar of the SpreadsheetControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetHorizontalScrollbarOptions"/> object used to specify options for the horizontal scrollbar.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions.PivotTableFieldList">
      <summary>
        <para>Provides access to the options used to specify the size and starting position of the PivotTable Field List pane at runtime.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetPivotTableFieldListOptions"/> object containing the PivotTable Field List options.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions.Print">
      <summary>
        <para>Provides access to options used to generate and preview a printed document in the SpreadsheetControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetPrintOptions"/> object containing options for previewing and printing a document in the SpreadsheetControl.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions.TabSelector">
      <summary>
        <para>Provides access to the options specific to the Sheet Tab Selector of the SpreadsheetControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetTabSelectorOptions"/> object used to specify options for the Sheet Tab Selector.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions.VerticalScrollbar">
      <summary>
        <para>Provides access to the options specific to the vertical scrollbar of the SpreadsheetControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetVerticalScrollbarOptions"/> object used to specify options for the vertical scrollbar.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetControlOptions.View">
      <summary>
        <para>Gets the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/>&#39;s view options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetViewOptions"/> object that specifies the display settings for the SpreadsheetControl.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetCustomCellEditEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraSpreadsheet.SpreadsheetControl.CustomCellEdit"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetCustomCellEditEventArgs.#ctor(DevExpress.XtraSpreadsheet.Internal.InnerSpreadsheetControl,DevExpress.XtraSpreadsheet.Model.Worksheet,System.Int32,System.Int32,DevExpress.Spreadsheet.ValueObject,DevExpress.XtraEditors.Repository.RepositoryItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetCustomCellEditEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="innerControl">An InnerSpreadsheetControl object.</param>
      <param name="sheet">A worksheet that contains the cell for which the event has been raised.</param>
      <param name="columnIndex">An integer specifying the index of the column that contains the cell for which the event has been raised.</param>
      <param name="rowIndex">An integer specifying the index of the row that contains the cell for which the event has been raised.</param>
      <param name="valueObject">A <see cref="T:DevExpress.Spreadsheet.ValueObject"/> value associated with the custom in-place editor assigned to the processed cell.</param>
      <param name="repositoryItem">A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant specifying the in-place editor assigned to the processed cell.</param>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetCustomCellEditEventArgs.RepositoryItem">
      <summary>
        <para>Gets or sets the in-place editor for the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant that specifies the in-place editor for the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetCustomCellEditEventArgs.ValueObject">
      <summary>
        <para>Gets a value associated with the <see cref="T:DevExpress.Spreadsheet.CustomCellInplaceEditor"/> object assigned to the cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Spreadsheet.ValueObject"/> object associated with the custom cell editor.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetCustomCellEditEventHandler">
      <summary>
        <para>A method that will handle the CustomCellEdit event of the SpreadsheetControl.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the SpreadsheetControl which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetCustomCellEditEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetDataSourceWizardOptions">
      <summary>
        <para>Contains options which affect the behavior of the Data Source Wizard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetDataSourceWizardOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetDataSourceWizardOptions"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetDataSourceWizardOptions.AlwaysSaveCredentials">
      <summary>
        <para>Gets or sets whether the user will be prompted to save credentials after creating a connection string.</para>
      </summary>
      <value>True, to save credentials in the connection string without prompting; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetDataSourceWizardOptions.CustomAssemblyBehavior">
      <summary>
        <para>Enables you to specify whether the Data Source Wizard can load a custom assembly containing the Entity Framework data context during mail merge.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetCustomAssemblyBehavior"/> enumeration value that specifies whether to load an assembly without prompting, to not load and disable browsing for the assembly file, or pass the request to a special service.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetDataSourceWizardOptions.DataSourceTypes">
      <summary>
        <para>Gets or sets data source types displayed on the &quot;Select the data source type&quot; page of the Data Source Wizard.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Office.Options.DataSourceTypes"/> enumeration member.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetDataSourceWizardOptions.DisableNewConnections">
      <summary>
        <para>Gets or sets whether an option for the user to create a new connection by specifying connection parameters is shown.</para>
      </summary>
      <value>True, to choose from existing connections only; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetDataSourceWizardOptions.EnableCustomSql">
      <summary>
        <para>Gets or sets whether the direct SQL text editing is enabled.</para>
      </summary>
      <value>True, to enable direct SQL text editing; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetDataSourceWizardOptions.QueryBuilderLight">
      <summary>
        <para>Specifies whether or not the Query Builder enables end-users to specify custom SQL queries, column aliases and expressions.</para>
      </summary>
      <value>true, to disallow end-users from specifying custom SQL queries, column aliases and expressions; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetDataSourceWizardOptions.ShowEFWizardBrowseButton">
      <summary>
        <para>Gets or sets a value indicating whether the Browse... button is displayed on the Data Source Wizard page, which allows an end-user to select the required Entity Framework data context.</para>
      </summary>
      <value>true, to show the Browse... button on the &quot;Choose a data context&quot; page; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetElementVisibility">
      <summary>
        <para>Lists values used to specify the visibility of a certain element of the SpreadsheetControl.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetElementVisibility.Default">
      <summary>
        <para>The element of the SpreadsheetControl is hidden or displayed as specified by the workbook display options.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetElementVisibility.Hidden">
      <summary>
        <para>The element of the SpreadsheetControl is always hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetElementVisibility.Visible">
      <summary>
        <para>The element of the SpreadsheetControl is always displayed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar">
      <summary>
        <para>Displays the active cell&#39;s address and content and is used to view, enter and edit formulas and cell data in the SpreadsheetControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.BeforeDispose">
      <summary>
        <para>Occurs before the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar"/> control is released from memory.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.BeforeEnter">
      <summary>
        <para>Occurs before the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar"/> is entered.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.CancelButtonClick">
      <summary>
        <para>Occurs when the Cancel button on the Formula Bar is clicked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.ExecuteResourceNavigatorAction(DevExpress.XtraEditors.NavigatorButtonType)">
      <summary>
        <para>Executes the command corresponding to one of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar"/>&#39;s buttons.</para>
      </summary>
      <param name="type">One of the <see cref="T:DevExpress.XtraEditors.NavigatorButtonType"/> enumeration values that is the type of button displayed in the Formula Bar.</param>
      <returns>true, if the command is executed; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.Expanded">
      <summary>
        <para>Gets or sets a value indicating whether the Formula Bar&#39;s editor is expanded to allow multiline editing.</para>
      </summary>
      <value>true, if the formula editor is expanded; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.InsertFunctionButtonClick">
      <summary>
        <para>Occurs when the Insert Function button on the Formula Bar is clicked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.IsResourceNavigatorActionEnabled(DevExpress.XtraEditors.NavigatorButtonType)">
      <summary>
        <para>Determines whether the specified button on the Formula Bar is enabled.</para>
      </summary>
      <param name="type">One of the <see cref="T:DevExpress.XtraEditors.NavigatorButtonType"/> enumeration values that is the type of button located on the Formula Bar.</param>
      <returns>true, if the button is enabled; otherwise, false.</returns>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.KeyDown">
      <summary>
        <para>Occurs when a key is pressed while the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar"/> has focus.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.OkButtonClick">
      <summary>
        <para>Occurs when the Enter button on the Formula Bar is clicked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.Rollback">
      <summary>
        <para>Occurs before the user input is canceled and the modified value is replaced with a value before modification.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.ShowButtons">
      <summary>
        <para>Gets or sets a value indicating whether the Cancel, Enter and Insert function buttons are displayed in the Formula Bar.</para>
      </summary>
      <value>true, to display the Formula Bar&#39;s buttons; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.ShowEditor">
      <summary>
        <para>Gets or sets a value indicating whether the formula editor is displayed in the Formula Bar.</para>
      </summary>
      <value>true, to display the Formula Bar&#39;s editor; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.ShowNameBox">
      <summary>
        <para>Gets or sets a value indicating whether the Name Box is displayed in the Formula Bar.</para>
      </summary>
      <value>true, to display the Name Box; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar.SpreadsheetControl">
      <summary>
        <para>Gets or sets the SpreadsheetControl to which the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBar"/> is bound.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> with which the Formula Bar interacts.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl">
      <summary>
        <para>Displays the content of the active cell and is used to view, enter and edit formulas and cell data in the SpreadsheetControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl"/> class with default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl.BeforeDispose">
      <summary>
        <para>Occurs before the SpreadsheetFormulaBarControl control is released from memory.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl.BeforeEnter">
      <summary>
        <para>Occurs before the SpreadsheetFormulaBarControl is entered.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl.CancelButtonClick">
      <summary>
        <para>Occurs when the Cancel button on the Formula Bar has been clicked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl.ExecuteResourceNavigatorAction(DevExpress.XtraEditors.NavigatorButtonType)">
      <summary>
        <para>Executes the command corresponding to one of the SpreadsheetFormularBarControl&#39;s buttons.</para>
      </summary>
      <param name="type">One of the <see cref="T:DevExpress.XtraEditors.NavigatorButtonType"/> enumeration values that is the type of button displayed in the Formula Bar.</param>
      <returns>true, if the command is executed; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl.Expanded">
      <summary>
        <para>Gets or sets whether the Formula Bar is expanded or collapsed.</para>
      </summary>
      <value>true, if the Formula Bar is expanded; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl.InsertFunctionButtonClick">
      <summary>
        <para>Occurs when the Insert Function button on the Formula Bar has been clicked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl.IsResourceNavigatorActionEnabled(DevExpress.XtraEditors.NavigatorButtonType)">
      <summary>
        <para>Determines whether the specified button on the Formula Bar is enabled.</para>
      </summary>
      <param name="type">One of the <see cref="T:DevExpress.XtraEditors.NavigatorButtonType"/> enumeration values that is the type of button displayed in the Formula Bar.</param>
      <returns>true, if the button is enabled; otherwise, false.</returns>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl.KeyDown">
      <summary>
        <para>Occurs when a key is pressed while the SpreadsheetFormulaBarControl has focus.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl.OkButtonClick">
      <summary>
        <para>Occurs when the Enter button on the Formula Bar has been clicked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl.Rollback">
      <summary>
        <para>Occurs before the user input is canceled and the modified value is replaced with a value before modification.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetFormulaBarControl.SpreadsheetControl">
      <summary>
        <para>Gets or sets the SpreadsheetControl to which the Formula Bar is bound.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> with which the Formula Bar interacts.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetHorizontalScrollbarOptions">
      <summary>
        <para>Contains options for the horizontal scrollbar of the SpreadsheetControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetHorizontalScrollbarOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetHorizontalScrollbarOptions"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetMenuType">
      <summary>
        <para>Lists values used to specify the type of the SpreadsheetControl&#39;s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetMenuType.AutoFilter">
      <summary>
        <para>Specifies a context menu which can be invoked by clicking the AutoFilter drop-down arrow.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetMenuType.Cell">
      <summary>
        <para>Specifies a context menu which can be invoked by right-clicking any cell in a worksheet.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetMenuType.Chart">
      <summary>
        <para>Specifies a context menu which can be invoked by right-clicking a chart in a worksheet.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetMenuType.ColumnHeading">
      <summary>
        <para>Specifies a context menu which can be invoked by right-clicking a column header.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetMenuType.DrawingObjects">
      <summary>
        <para>Specifies a context menu which can be invoked by right-clicking a drawing object when a worksheet contains several drawing objects, such as pictures or charts.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetMenuType.None">
      <summary>
        <para>Specifies no menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetMenuType.Picture">
      <summary>
        <para>Specifies a context menu which can be invoked by right-clicking a picture embedded in a worksheet.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetMenuType.PivotTable">
      <summary>
        <para>Specifies a context menu which can be invoked by right-clicking any cell in a pivot table.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetMenuType.PivotTableAutoFilter">
      <summary>
        <para>Specifies a context menu which can be invoked by clicking the AutoFilter drop-down arrow on the row or column label of a pivot table.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetMenuType.RowHeading">
      <summary>
        <para>Specifies a context menu which can be invoked by right-clicking a row header.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetMenuType.SelectAllButton">
      <summary>
        <para>Specifies a context menu which can be invoked by right-clicking the Select All button in the upper-left corner of a worksheet.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetMenuType.SheetTab">
      <summary>
        <para>Specifies a context menu which can be invoked by right-clicking a worksheet tab.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl">
      <summary>
        <para>Displays cell references and defined names specified in a worksheet.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl"/> class with default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl.BeforeDispose">
      <summary>
        <para>Occurs before the SpreadsheetNameBoxControl control is released from memory.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl.EditorTypeName">
      <summary>
        <para>Gets the class name of the editor.</para>
      </summary>
      <value>A System.String object identifying the class name.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl.ItemDisplayMode">
      <summary>
        <para>Gets or sets a value indicating what items should appear in the Name Box list.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.NameBoxItemDisplayMode"/> enumeration member specifying what items should be displayed in the drop-down list of the Name Box control.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl.Properties">
      <summary>
        <para>Gets an object that contains settings specific to the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl"/> control.</para>
      </summary>
      <value>A RepositoryItemNameBox object that contains settings for the current <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl"/>.</value>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl.ReadOnly">
      <summary>
        <para>Gets or sets a value indicating whether the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl"/>&#39;s value can be changed by end-users.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> enumeration value specifying the accessibility of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl"/> control.The default is DefaultBoolean.Default.</value>
    </member>
    <member name="E:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl.ReadOnlyChanged">
      <summary>
        <para>Occurs when the value of the <see cref="P:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl.ReadOnly"/> property has changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetNameBoxControl.SpreadsheetControl">
      <summary>
        <para>Gets or sets the SpreadsheetControl to which the Name Box is bound.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetControl"/> with which the Name Box interacts.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetScrollbarOptions">
      <summary>
        <para>Represents the base class for scrollbar options.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetScrollbarOptions.Visibility">
      <summary>
        <para>Gets or sets a value that indicates whether the SpreadsheetControl&#39;s scrollbar should be displayed or hidden.</para>
      </summary>
      <value>One of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetScrollbarVisibility"/> enumeration values.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetScrollbarVisibility">
      <summary>
        <para>Lists values used to specify the visibility of a scrollbar in the SpreadsheetControl.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetScrollbarVisibility.Auto">
      <summary>
        <para>The scrollbar is hidden or displayed depending on the workbook display options.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetScrollbarVisibility.Hidden">
      <summary>
        <para>The scrollbar is always hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetScrollbarVisibility.Visible">
      <summary>
        <para>The scrollbar is always displayed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetStatusBarItems">
      <summary>
        <para>Lists items that can be displayed on the Spreadsheet status bar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetStatusBarItems.All">
      <summary>
        <para>Displays all status bar items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetStatusBarItems.Average">
      <summary>
        <para>Displays the average of numerical values in the selected cells.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetStatusBarItems.Count">
      <summary>
        <para>Displays the number of selected cells that contain data.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetStatusBarItems.Max">
      <summary>
        <para>Displays the maximum numerical value in the selected cells.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetStatusBarItems.Min">
      <summary>
        <para>Displays the minimum numerical value in the selected cells.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetStatusBarItems.NumCount">
      <summary>
        <para>Displays the number of selected cells that contain numerical data.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetStatusBarItems.Sum">
      <summary>
        <para>Displays the sum of numerical values in the selected cells.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetStatusBarItems.ZoomPercentage">
      <summary>
        <para>Displays the current zoom level.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetStatusBarItems.ZoomSlider">
      <summary>
        <para>Displays the zoom slider that allows you zoom the current worksheet.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetTabSelectorOptions">
      <summary>
        <para>Contains options which can be specified for the Sheet Tab Selector of the SpreadsheetControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetTabSelectorOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetTabSelectorOptions"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraSpreadsheet.SpreadsheetTabSelectorOptions.Visibility">
      <summary>
        <para>Gets or sets a value that indicates whether the Sheet Tab Selector of the SpreadsheetControl should be displayed or hidden.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetElementVisibility"/> enumeration value that specifies the visibility of the Sheet Tab Selector.</value>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType">
      <summary>
        <para>Lists ribbon tabs/toolbars that can be added to the SpreadsheetControl at runtime.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.All">
      <summary>
        <para>Specifies all ribbon tabs/bars, except for the Mail Merge toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.ChartTools">
      <summary>
        <para>Specifies the Chart Tools contextual ribbon tab/bar group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.Data">
      <summary>
        <para>Specifies the Data ribbon tab/bar group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.DrawingTools">
      <summary>
        <para>Specifies the Drawing Tools contextual ribbon tab/toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.File">
      <summary>
        <para>Specifies the File ribbon tab/toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.Formulas">
      <summary>
        <para>Specifies the Formulas ribbon tab/bar group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.Home">
      <summary>
        <para>Specifies the Home ribbon tab/bar group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.Insert">
      <summary>
        <para>Specifies the Insert ribbon tab/bar group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.MailMerge">
      <summary>
        <para>Specifies the Mail Merge ribbon tab/bar group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.PageLayout">
      <summary>
        <para>Specifies the Page Layout ribbon tab/bar group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.PictureTools">
      <summary>
        <para>Specifies the Picture Tools contextual ribbon tab/toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.PivotTableTools">
      <summary>
        <para>Specifies the PivotTable Tools contextual ribbon tab/bar group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.Review">
      <summary>
        <para>Specifies the Review ribbon tab/bar group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.TableTools">
      <summary>
        <para>Specifies the Table Tools contextual ribbon tab/bar group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraSpreadsheet.SpreadsheetToolbarType.View">
      <summary>
        <para>Specifies the View ribbon tab/bar group.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.SpreadsheetVerticalScrollbarOptions">
      <summary>
        <para>Contains options for the vertical scrollbar of the SpreadsheetControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.SpreadsheetVerticalScrollbarOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpreadsheet.SpreadsheetVerticalScrollbarOptions"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraSpreadsheet.WorksheetDisplayArea">
      <summary>
        <para>An object that allows you to specify a worksheet&#39;s visible area.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.WorksheetDisplayArea.#ctor(DevExpress.XtraSpreadsheet.Internal.InnerSpreadsheetControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraSpreadsheet.WorksheetDisplayArea"/> class with the specified settings.</para>
      </summary>
      <param name="innerControl"></param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.WorksheetDisplayArea.GetColumnCount(System.Int32)">
      <summary>
        <para>Returns the number of visible columns in the specified worksheet.</para>
      </summary>
      <param name="worksheetIndex">An integer that is the zero-based index of the worksheet in the worksheet collection.</param>
      <returns>An integer that represents the number of columns currently visible in a worksheet.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.WorksheetDisplayArea.GetColumnCount(System.String)">
      <summary>
        <para>Returns the number of visible columns in the specified worksheet.</para>
      </summary>
      <param name="worksheetName">A string value specifying the worksheet name in the worksheet collection.</param>
      <returns>An integer that represents the number of columns currently visible in a worksheet.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.WorksheetDisplayArea.GetRowCount(System.Int32)">
      <summary>
        <para>Returns the number of visible rows in the specified worksheet.</para>
      </summary>
      <param name="worksheetIndex">An integer that is the zero-based index of the worksheet in the worksheet collection.</param>
      <returns>An integer that represents the number of rows currently visible in a worksheet.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.WorksheetDisplayArea.GetRowCount(System.String)">
      <summary>
        <para>Returns the number of visible rows in the specified worksheet.</para>
      </summary>
      <param name="worksheetName">A string value specifying the worksheet name in the worksheet collection.</param>
      <returns>An integer that represents the number of rows currently visible in a worksheet.</returns>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.WorksheetDisplayArea.Reset(System.Int32)">
      <summary>
        <para>Displays all columns and rows in the specified worksheet.</para>
      </summary>
      <param name="worksheetIndex">An integer that is the zero-based index of the worksheet in the worksheet collection.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.WorksheetDisplayArea.Reset(System.String)">
      <summary>
        <para>Displays all columns and rows in the specified worksheet.</para>
      </summary>
      <param name="worksheetName">A string value specifying the worksheet name in the worksheet collection.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.WorksheetDisplayArea.SetSize(System.Int32,System.Int32,System.Int32)">
      <summary>
        <para>Defines the number of visible columns and rows for the specified worksheet.</para>
      </summary>
      <param name="worksheetIndex">An integer that is the zero-based index of the worksheet whose visible area should be restricted.</param>
      <param name="maxColumnCount">An integer that represents the maximum number of columns to be displayed in a worksheet. The value must be a whole number between 1 and 16,384.</param>
      <param name="maxRowCount">An integer that represents the maximum number of rows to be displayed in a worksheet. The value must be a whole number between 1 and 1,048,576.</param>
    </member>
    <member name="M:DevExpress.XtraSpreadsheet.WorksheetDisplayArea.SetSize(System.String,System.Int32,System.Int32)">
      <summary>
        <para>Defines the number of visible columns and rows for the specified worksheet.</para>
      </summary>
      <param name="worksheetName">A string value specifying the name of the worksheet whose visible area should be restricted.</param>
      <param name="maxColumnCount">An integer that represents the maximum number of columns to be displayed in a worksheet. The value must be a whole number between 1 and 16,384.</param>
      <param name="maxRowCount">An integer that represents the maximum number of rows to be displayed in a worksheet. The value must be a whole number between 1 and 1,048,576.</param>
    </member>
  </members>
</doc>