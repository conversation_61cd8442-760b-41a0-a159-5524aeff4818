<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.Charts.Designer.v20.2</name>
  </assembly>
  <members>
    <member name="N:DevExpress.Charts.Designer">
      <summary>
        <para>Contains a class which is intended to invoke the ChartControl designer.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Charts.Designer.ChartDesigner">
      <summary>
        <para>The Chart Designer used to design a chart at runtime.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Charts.Designer.ChartDesigner.#ctor(DevExpress.Xpf.Charts.ChartControl)">
      <summary>
        <para>Creates a Chart Designer for the specified chart control.</para>
      </summary>
      <param name="chartControl">A <see cref="T:DevExpress.Xpf.Charts.ChartControl"/> object that is a chart control for which a chart designer is created.</param>
    </member>
    <member name="M:DevExpress.Charts.Designer.ChartDesigner.Close">
      <summary>
        <para>Closes the chart designer with the chart currently being edited.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Charts.Designer.ChartDesigner.Show(System.Windows.Window,DevExpress.Xpf.Core.Theme,DevExpress.Charts.Designer.DesignerWindowKind)">
      <summary>
        <para>Invokes the Chart Designer dialog with specified parameters.</para>
      </summary>
      <param name="owner">The <see cref="T:System.Windows.Window"/> object specifying the parent window for the Chart Designer dialog.</param>
      <param name="theme">The theme to be applied to the designer.</param>
      <param name="windowKind">The Chart Designer&#39;s kind.</param>
      <returns>true if an end-user modifies the chart within the Chart Designer and saves changes; otherwise false.</returns>
    </member>
    <member name="T:DevExpress.Charts.Designer.DesignerWindowKind">
      <summary>
        <para>Lists values that specify the Chart Designer kind.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Charts.Designer.DesignerWindowKind.Bar">
      <summary>
        <para>The Chart Designer with the Toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Charts.Designer.DesignerWindowKind.Ribbon">
      <summary>
        <para>The Chart Designer with the Ribbon.</para>
      </summary>
    </member>
  </members>
</doc>