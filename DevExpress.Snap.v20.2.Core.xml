<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.Snap.v20.2.Core</name>
  </assembly>
  <members>
    <member name="N:DevExpress.Snap">
      <summary>
        <para>Contains the <see cref="T:DevExpress.Snap.SnapControl"/> class that provides the main functionality of Snap.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.AfterDataSourceImportEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.SnapControl.AfterDataSourceImport"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.AfterDataSourceImportEventArgs.#ctor(System.String,System.Byte[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.AfterDataSourceImportEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="dataSourceName">A <see cref="T:System.String"/> value. This value is assigned to the <see cref="P:DevExpress.Snap.AfterDataSourceImportEventArgs.DataSourceName"/> property.</param>
      <param name="data">A <see cref="T:System.Byte"/> array. This value is assigned to the <see cref="P:DevExpress.Snap.AfterDataSourceImportEventArgs.Data"/> property.</param>
    </member>
    <member name="P:DevExpress.Snap.AfterDataSourceImportEventArgs.Data">
      <summary>
        <para>Returns the data stored in the connected data source.</para>
      </summary>
      <value>A <see cref="T:System.Byte"/> array.</value>
    </member>
    <member name="P:DevExpress.Snap.AfterDataSourceImportEventArgs.DataSourceName">
      <summary>
        <para>Returns the data source name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.Snap.AfterDataSourceImportEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.SnapControl.AfterDataSourceImport"/> event.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.AfterDataSourceImportEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="N:DevExpress.Snap.API.Native">
      <summary>
        <para>Provides the document section functionality to Snap.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.API.Native.SnapSection">
      <summary>
        <para>A section within a Snap document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.API.Native.SnapSection.BeginUpdateFooter">
      <summary>
        <para>Starts editing the document&#39;s footer for the current section.</para>
      </summary>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapSubDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.API.Native.SnapSection.BeginUpdateFooter(DevExpress.XtraRichEdit.API.Native.HeaderFooterType)">
      <summary>
        <para>Starts editing the document&#39;s footer for the current section.</para>
      </summary>
      <param name="type">A <see cref="T:DevExpress.XtraRichEdit.API.Native.HeaderFooterType"/> enumeration value.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapSubDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.API.Native.SnapSection.BeginUpdateHeader">
      <summary>
        <para>Ends editing the document&#39;s footer for the current section.</para>
      </summary>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapSubDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.API.Native.SnapSection.BeginUpdateHeader(DevExpress.XtraRichEdit.API.Native.HeaderFooterType)">
      <summary>
        <para>Ends editing the document&#39;s footer for the current section.</para>
      </summary>
      <param name="type">A <see cref="T:DevExpress.XtraRichEdit.API.Native.HeaderFooterType"/> enumeration value.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapSubDocument"/> interface.</returns>
    </member>
    <member name="T:DevExpress.Snap.API.Native.SnapSectionCollection">
      <summary>
        <para>A collection of <see cref="T:DevExpress.Snap.API.Native.SnapSection"/> objects.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.API.Native.SnapSectionCollection.Item(System.Int32)">
      <summary>
        <para>Provides access to the specified section of a Snap document.</para>
      </summary>
      <param name="index">A zero-based integer value, specifying the document section&#39;s position in the collection.</param>
      <value>An object implementing the <see cref="T:DevExpress.Snap.API.Native.SnapSection"/> interface.</value>
    </member>
    <member name="T:DevExpress.Snap.BeforeConversionEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.SnapControl.BeforeConversion"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.BeforeConversionEventArgs.#ctor(DevExpress.Snap.Core.API.SnapDocument)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.BeforeConversionEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="document">A <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> object that is the document being exported.</param>
    </member>
    <member name="P:DevExpress.Snap.BeforeConversionEventArgs.Document">
      <summary>
        <para>Provides access to a document being saved to format different from the native .SNX (.RTF, .DOC, .DOCX).</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> object that is the document being saved.</value>
    </member>
    <member name="T:DevExpress.Snap.BeforeConversionEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.SnapControl.BeforeConversion"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.Snap.SnapControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Snap.BeforeConversionEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.Snap.BeforeDataSourceExportEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.SnapControl.BeforeDataSourceExport"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.BeforeDataSourceExportEventArgs.#ctor(System.Object,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.BeforeDataSourceExportEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="dataSource">A <see cref="T:System.Object"/> value. This value is assigned to the <see cref="P:DevExpress.Snap.BeforeDataSourceExportEventArgs.DataSource"/> property.</param>
      <param name="dataSourceName">A <see cref="T:System.String"/> value. This value is assigned to the <see cref="P:DevExpress.Snap.BeforeDataSourceExportEventArgs.DataSourceName"/> property.</param>
    </member>
    <member name="P:DevExpress.Snap.BeforeDataSourceExportEventArgs.Data">
      <summary>
        <para>Returns the data stored in the connected data source.</para>
      </summary>
      <value>A <see cref="T:System.Byte"/> array.</value>
    </member>
    <member name="P:DevExpress.Snap.BeforeDataSourceExportEventArgs.DataSource">
      <summary>
        <para>Provides access to the connected data source.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> value, specifying the connected data source.</value>
    </member>
    <member name="P:DevExpress.Snap.BeforeDataSourceExportEventArgs.DataSourceName">
      <summary>
        <para>Returns the data source name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.Snap.BeforeDataSourceExportEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.SnapControl.BeforeDataSourceExport"/> event.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">A <see cref="T:DevExpress.Snap.BeforeDataSourceExportEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="N:DevExpress.Snap.Core">
      <summary>
        <para>Contains the <see cref="T:DevExpress.Snap.Core.ISnapControl"/> interface that implements the main functionality of Snap.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Snap.Core.API">
      <summary>
        <para>Contains the application programming interface classes that allow you to manage the data source options of your DevExpress Snap application.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.API.AfterInsertSnListColumnsEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.AfterInsertSnListColumns"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.AfterInsertSnListColumnsEventArgs.SnList">
      <summary>
        <para>Provides access to a <see cref="T:DevExpress.Snap.Core.API.SnapList"/> object for which the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.AfterInsertSnListColumns"/> event is raised.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapList"/> interface.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.AfterInsertSnListColumnsEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.AfterInsertSnListColumns"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.Core.API.AfterInsertSnListColumnsEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.AfterInsertSnListDetailEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.AfterInsertSnListDetail"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.AfterInsertSnListDetailEventArgs.Master">
      <summary>
        <para>Provides access to the master list to which the detail list is inserted.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapList"/> interface.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.AfterInsertSnListDetailEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.AfterInsertSnListDetail"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.Core.API.AfterInsertSnListColumnsEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.AfterInsertSnListEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.AfterInsertSnList"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.AfterInsertSnListEventArgs.Range">
      <summary>
        <para>Gets the range for the current Snap list.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocumentRange"/> value.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.AfterInsertSnListEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.AfterInsertSnList"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.Core.API.AfterInsertSnListEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.AfterInsertSnListRecordDataEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.AfterInsertSnListRecordData"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.AfterInsertSnListRecordDataEventArgs.Range">
      <summary>
        <para>Gets the range for the current Snap list.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocumentRange"/> value.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.AfterInsertSnListRecordDataEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.AfterInsertSnListRecordData"/> event.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.Core.API.AfterInsertSnListRecordDataEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.BeforeInsertSnListColumnsEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.BeforeInsertSnListColumns"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.BeforeInsertSnListColumnsEventArgs.DataFields">
      <summary>
        <para>Specifies the data added to the inserted list.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Snap.Core.API.DataFieldInfo"/> objects.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.BeforeInsertSnListColumnsEventArgs.Target">
      <summary>
        <para>Provides access to the target list into which an inserted list is placed.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapList"/> interface.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.BeforeInsertSnListColumnsEventArgs.TargetColumnIndex">
      <summary>
        <para>Specifies the index of a target column, to which an inserted list is placed.</para>
      </summary>
      <value>An integer value.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.BeforeInsertSnListColumnsEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.BeforeInsertSnListColumns"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.Core.API.BeforeInsertSnListColumnsEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.BeforeInsertSnListDetailEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.BeforeInsertSnListDetail"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.BeforeInsertSnListDetailEventArgs.DataFields">
      <summary>
        <para>Specifies the data added to the inserted list.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Snap.Core.API.DataFieldInfo"/> objects.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.BeforeInsertSnListDetailEventArgs.Master">
      <summary>
        <para>Provides access to the master list to which the detail list is inserted.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapList"/> interface.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.BeforeInsertSnListDetailEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.BeforeInsertSnListDetail"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.Core.API.BeforeInsertSnListDetailEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.BeforeInsertSnListEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.BeforeInsertSnList"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.BeforeInsertSnListEventArgs.DataFields">
      <summary>
        <para>Specifies the data added to the inserted list.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Snap.Core.API.DataFieldInfo"/> objects.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.BeforeInsertSnListEventArgs.Position">
      <summary>
        <para>Specifies the position in the document into which a Snap list is being inserted.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocumentPosition"/> value.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.BeforeInsertSnListEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.BeforeInsertSnList"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.Core.API.BeforeInsertSnListEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.BeforeInsertSnListRecordDataEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.BeforeInsertSnListRecordData"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.BeforeInsertSnListRecordDataEventArgs.DataFields">
      <summary>
        <para>Specifies the fields to add. You can customize the set and/or order of the fields.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Snap.Core.API.DataFieldInfo"/> objects.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.BeforeInsertSnListRecordDataEventArgs.Target">
      <summary>
        <para>Provides access to the list to which the fields are being added. Making changes to the list during this event is only safe when passing it with a null set of fields (see below).</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapList"/> value, specifying the target list.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.BeforeInsertSnListRecordDataEventArgs.TargetColumnIndex">
      <summary>
        <para>Indicates the column index to which the fields are added.</para>
      </summary>
      <value>An integer value, specifying the target column index.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.BeforeInsertSnListRecordDataEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.BeforeInsertSnListRecordData"/> event.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">A <see cref="T:DevExpress.Snap.Core.API.BeforeInsertSnListRecordDataEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.CalculatedField">
      <summary>
        <para>Provides the functionality for calculated fields.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.CalculatedField.#ctor(System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.CalculatedField"/> class with the specified name and data member.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> value, specifying the name of the calculated field.</param>
      <param name="dataMember">A <see cref="T:System.String"/> value, specifying the data member for the calculated field.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.CalculatedField.Clone">
      <summary>
        <para>Makes the exact copy of a <see cref="T:DevExpress.Snap.Core.API.CalculatedField"/> object.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.CalculatedField"/> object that is the copy of an object.</returns>
    </member>
    <member name="P:DevExpress.Snap.Core.API.CalculatedField.DataMember">
      <summary>
        <para>Provides access to the calculated field&#39;s data member.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying a calculated field&#39;s data member.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.CalculatedField.DataSource">
      <summary>
        <para>Provides access to the calculated field&#39;s data source.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> value, specifying the calculated field&#39;s data source.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.CalculatedField.DataSourceName">
      <summary>
        <para>Associates the <see cref="T:DevExpress.Snap.Core.API.CalculatedField"/> with a data source.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the data source name.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.CalculatedField.DisplayName">
      <summary>
        <para>Provides access to the display name of the calculated field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the display name of the calculated field.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.CalculatedField.Dispose">
      <summary>
        <para>Disposes of the <see cref="T:DevExpress.Snap.Core.API.CalculatedField"/> object.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.CalculatedField.Disposed">
      <summary>
        <para>Occurs after the current calculated field has been disposed of.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.CalculatedField.Expression">
      <summary>
        <para>Specifies the expression of the current calculated field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.CalculatedField.FieldType">
      <summary>
        <para>Specifies the type of the calculated field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.UI.FieldType"/> enumeration value, specifying the calculated field&#39;s value type. The default is FieldType.None.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.CalculatedField.Name">
      <summary>
        <para>Specifies the calculated field name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the calculated field name.</value>
    </member>
    <member name="E:DevExpress.Snap.Core.API.CalculatedField.PropertyChanged">
      <summary>
        <para>Occurs every time any of the <see cref="T:DevExpress.Snap.Core.API.CalculatedField"/> class properties has changed its value.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.API.CalculatedFieldCollection">
      <summary>
        <para>A collection of a document&#39;s calculated fields.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.CalculatedFieldCollection.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.CalculatedFieldCollection"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.CalculatedFieldCollection.AddRangeByValue(System.Collections.IList)">
      <summary>
        <para>Adds the specified collection of calculated fields to this collection.</para>
      </summary>
      <param name="calculatedFields">A list that represents the calculated fields to add to this collection.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.CalculatedFieldCollection.BeginUpdate">
      <summary>
        <para>Updates the <see cref="T:DevExpress.Snap.Core.API.CalculatedFieldCollection"/> collection.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.CalculatedFieldCollection.EndUpdate">
      <summary>
        <para>Ends the initialization of the <see cref="T:DevExpress.Snap.Core.API.CalculatedFieldCollection"/>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.API.DataFieldInfo">
      <summary>
        <para>Provides information about a data field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataFieldInfo.#ctor(System.Object,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.DataFieldInfo"/> class with the specified settings.</para>
      </summary>
      <param name="dataSource">A <see cref="T:System.Object"/> value.</param>
      <param name="dataMember">A <see cref="T:System.String"/> value.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataFieldInfo.#ctor(System.Object,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.DataFieldInfo"/> class with the specified settings.</para>
      </summary>
      <param name="dataSource">A <see cref="T:System.Object"/> value.</param>
      <param name="dataMember">A <see cref="T:System.String"/> value.</param>
      <param name="displayName">A <see cref="T:System.String"/> value.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataFieldInfo.#ctor(System.Object,System.String[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.DataFieldInfo"/> class with the specified settings.</para>
      </summary>
      <param name="dataSource">A <see cref="T:System.Object"/> value.</param>
      <param name="dataPaths">An array of <see cref="T:System.String"/> values.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataFieldInfo.#ctor(System.Object,System.String[],System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.DataFieldInfo"/> class with the specified settings.</para>
      </summary>
      <param name="dataSource">A <see cref="T:System.Object"/> value.</param>
      <param name="dataPaths">An array of <see cref="T:System.String"/> values.</param>
      <param name="displayName">A <see cref="T:System.String"/> value.</param>
    </member>
    <member name="P:DevExpress.Snap.Core.API.DataFieldInfo.DataPaths">
      <summary>
        <para>Specifies the path to the data field.</para>
      </summary>
      <value>An array of <see cref="T:System.String"/> values.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.DataFieldInfo.DataSource">
      <summary>
        <para>Specifies the bound data source.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.DataFieldInfo.DisplayName">
      <summary>
        <para>Specifies the display name of the <see cref="T:DevExpress.Snap.Core.API.DataFieldInfo"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.DataSourceChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.DataSourceChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceChangedEventArgs.#ctor(DevExpress.Snap.Core.API.DataSourceInfo,DevExpress.Snap.Core.API.DataSourceChangeType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.DataSourceChangedEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="changedDataSourceInfo">A <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> object.</param>
      <param name="dataSourceChangeType">A <see cref="T:DevExpress.Snap.Core.API.DataSourceChangeType"/> enumeration value.</param>
    </member>
    <member name="P:DevExpress.Snap.Core.API.DataSourceChangedEventArgs.ChangedDataSourceInfo">
      <summary>
        <para>Provides information about a data source that is being changed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> object.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.DataSourceChangedEventArgs.DataSourceChangeType">
      <summary>
        <para>Specifies the type of the data source that is being changed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.DataSourceChangeType"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.DataSourceChangedEventArgs.Name">
      <summary>
        <para>Specifies the name of the data source that is being changed.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.DataSourceChangedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.DataSourceChanged"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.Core.API.DataSourceChangedEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.DataSourceChangeType">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.API.DataSourceChangeType.CalculatedFields">
      <summary>
        <para>A collection of calculated fields.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.API.DataSourceChangeType.DataSource">
      <summary>
        <para>A data source.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.API.DataSourceInfo">
      <summary>
        <para>Contains information about a document&#39;s data source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfo.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfo.#ctor(System.String,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> class with the specified name and datasource.</para>
      </summary>
      <param name="dataSourceName">A <see cref="T:System.String"/> value, specifying the datasource name.</param>
      <param name="dataSource">A <see cref="T:System.Object"/> value, specifying the datasource.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfo.AddCalculatedFieldWithReplace(DevExpress.Snap.Core.API.CalculatedField)">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="calculatedField"></param>
    </member>
    <member name="P:DevExpress.Snap.Core.API.DataSourceInfo.CalculatedFields">
      <summary>
        <para>Provides access to the collection of calculated fields.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.CalculatedFieldCollection"/> object, containing the collection of calculated fields.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.DataSourceInfo.DataSource">
      <summary>
        <para>Specifies the report&#39;s data source.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.DataSourceInfo.DataSourceName">
      <summary>
        <para>Provides access to the report datasource name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the datasource name.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.DataSourceInfoCollection">
      <summary>
        <para>A collection of a document&#39;s data sources and calculated fields.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfoCollection.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.DataSourceInfoCollection"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfoCollection.Add(System.String,System.Object)">
      <summary>
        <para>Adds the specified object to the collection and returns the newly created object.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> value, specifying the name of the added object.</param>
      <param name="dataSource">A <see cref="T:System.Object"/> value, specifying the data source object.</param>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> object.</returns>
    </member>
    <member name="E:DevExpress.Snap.Core.API.DataSourceInfoCollection.CollectionChanged">
      <summary>
        <para>Occurs when the collection has been changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.DataSourceInfoCollection.DataSourceChanged">
      <summary>
        <para>Occurs when changing the datasource or calculated fields within the collection.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.DataSourceInfoCollection.DataSourceRemoved">
      <summary>
        <para>Occurs when removing the data source from the collection.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.DataSourceInfoCollection.DefaultDataSourceInfo">
      <summary>
        <para>Provides access to the default datasource.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> object, specifying the default settings.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfoCollection.Dispose">
      <summary>
        <para>Disposes of the <see cref="T:DevExpress.Snap.Core.API.DataSourceInfoCollection"/> object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfoCollection.GetCalculatedFields">
      <summary>
        <para>Returns the collection of calculated fields belonging to <see cref="T:DevExpress.Snap.Core.API.DataSourceInfoCollection"/>.</para>
      </summary>
      <returns>A collection containing the report&#39;s calculated fields.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfoCollection.GetCalculatedFieldsByDataSource(System.Object)">
      <summary>
        <para>Provides access to the collection of the calculated fields by the specified datasource object.</para>
      </summary>
      <param name="dataSource">A <see cref="T:System.Object"/> value, specifying the datasource.</param>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.CalculatedFieldCollection"/> object.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfoCollection.GetCalculatedFieldsByDataSourceName(System.String)">
      <summary>
        <para>Provides access to the collection of calculated fields by the specified datasource name.</para>
      </summary>
      <param name="dataSourceName">A <see cref="T:System.String"/> value, specifying the name of a datasource.</param>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.CalculatedFieldCollection"/> object.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfoCollection.GetDataSourceByName(System.String)">
      <summary>
        <para>Provides access to the report&#39;s datasource by its name.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> value, specifying the datasource&#39;s name.</param>
      <returns>A <see cref="T:System.Object"/> value, specifying the datasource.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfoCollection.GetDataSourceNameByDataSource(System.Object)">
      <summary>
        <para>Returns the name of the specified datasource.</para>
      </summary>
      <param name="dataSource">A <see cref="T:System.Object"/> value, specifying the datasource.</param>
      <returns>A <see cref="T:System.String"/> value, specifying the datasource name.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfoCollection.GetDataSources">
      <summary>
        <para>Obtains the available datasources from the <see cref="T:DevExpress.Snap.Core.API.DataSourceInfoCollection"/>.</para>
      </summary>
      <returns>A collection of <see cref="T:System.Object"/> values specifying the available datasources.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfoCollection.GetInfo(System.Object)">
      <summary>
        <para>Returns an object that contains information about a report&#39;s datasource.</para>
      </summary>
      <param name="dataSource">A <see cref="T:System.Object"/> object value specifying the datasource.</param>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> object or null (Nothing in Visual Basic) if not found.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfoCollection.IsDefaultDataSource(System.Object)">
      <summary>
        <para>Specifies whether or not the specified datasource is the default one.</para>
      </summary>
      <param name="dataSource">A <see cref="T:System.Object"/> value, specifying the datasource.</param>
      <returns>true if the specified object is the default datasource; otherwise false.</returns>
    </member>
    <member name="P:DevExpress.Snap.Core.API.DataSourceInfoCollection.Item(System.String)">
      <summary>
        <para>Returns the <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> element with the specified name.</para>
      </summary>
      <param name="dataSourceName">A <see cref="T:System.String"/> value, specifying the name of the report&#39;s parameter within the collection.</param>
      <value>A <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> descendant.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.DataSourceInfoCollection.Remove(System.String)">
      <summary>
        <para>Removes the specified data source from the collection.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> value, specifying the datasource name.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.GroupInterval">
      <summary>
        <para>Lists the available date-time group intervals.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.API.GroupInterval.Day">
      <summary>
        <para>A day.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.API.GroupInterval.Default">
      <summary>
        <para>The default interval.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.API.GroupInterval.Month">
      <summary>
        <para>A month.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.API.GroupInterval.Smart">
      <summary>
        <para>An interval relative with today&#39;s date: &quot;Next Week&quot;,&quot;Today&quot;,&quot;Tomorrow&quot;, etc.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.API.GroupInterval.Year">
      <summary>
        <para>A year.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.API.IDataSourceOwner">
      <summary>
        <para>If implemented by a class, contains properties related to a Snap application&#39;s datasource.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.IDataSourceOwner.CalculatedFields">
      <summary>
        <para>Provides access to a <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/>&#39;s collection of calculated fields.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.CalculatedFieldCollection"/> object.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.IDataSourceOwner.DataSource">
      <summary>
        <para>Specifies the datasource for the <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> collection.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> descendant, specifying the datasource.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.ISnapFieldOwner">
      <summary>
        <para>If implemented by a class, contains methods related to managing Snap fields in a document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.ISnapFieldOwner.ActiveEntity">
      <summary>
        <para>Specifies a Snap field for which the <see cref="P:DevExpress.Snap.Core.API.SnapEntity.Active"/> property is enabled.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapEntity"/> object.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.CreateSnBarCode(DevExpress.XtraRichEdit.API.Native.DocumentPosition)">
      <summary>
        <para>Adds a Bar Code to the specified position in a document.</para>
      </summary>
      <param name="position">A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> object.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapBarCode"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.CreateSnChart(DevExpress.XtraRichEdit.API.Native.DocumentPosition)">
      <summary>
        <para>Inserts a Chart into the specified position in a document.</para>
      </summary>
      <param name="position">A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> object that specifies where to place the chart in the document.</param>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.SnapChart"/> object that specifies the newly created chart.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.CreateSnCheckBox(DevExpress.XtraRichEdit.API.Native.DocumentPosition,System.String)">
      <summary>
        <para>Adds a Check Box to the specified position in a document.</para>
      </summary>
      <param name="position">A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> object.</param>
      <param name="dataFieldName">A <see cref="T:System.String"/> value, specifying the name of a data field bound to the created <see cref="T:DevExpress.Snap.Core.API.SnapCheckBox"/>.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapCheckBox"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.CreateSnHyperlink(DevExpress.XtraRichEdit.API.Native.DocumentPosition,System.String)">
      <summary>
        <para>Adds a Hyperlink to the specified position in a document.</para>
      </summary>
      <param name="position">A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> object.</param>
      <param name="dataFieldName">A <see cref="T:System.String"/> value, specifying the name of a data field bound to the created <see cref="T:DevExpress.Snap.Core.API.SnapHyperlink"/>.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapHyperlink"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.CreateSnImage(DevExpress.XtraRichEdit.API.Native.DocumentPosition,System.String)">
      <summary>
        <para>Adds a Picture to the specified position in a document.</para>
      </summary>
      <param name="position">A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> object.</param>
      <param name="dataFieldName">A <see cref="T:System.String"/> value, specifying the name of a data field bound to the created <see cref="T:DevExpress.Snap.Core.API.SnapImage"/>.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapImage"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.CreateSnList(DevExpress.XtraRichEdit.API.Native.DocumentPosition,System.String)">
      <summary>
        <para>Adds a Snap list to the specified position in a document.</para>
      </summary>
      <param name="position">A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> object.</param>
      <param name="name">A <see cref="T:System.String"/> value, specifying the name of a data field bound to the created <see cref="T:DevExpress.Snap.Core.API.SnapList"/>.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapList"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.CreateSnRowIndex(DevExpress.XtraRichEdit.API.Native.DocumentPosition)">
      <summary>
        <para>Adds a Row Index to the specified position in an document.</para>
      </summary>
      <param name="position">A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> object.</param>
      <returns>An object implementing the DevExpress.Snap.Core.API.SnapRowIndex interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.CreateSnSparkline(DevExpress.XtraRichEdit.API.Native.DocumentPosition,System.String)">
      <summary>
        <para>Adds a Sparkline to the specified position in a document.</para>
      </summary>
      <param name="position">A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> object.</param>
      <param name="dataFieldName">A <see cref="T:System.String"/> value, specifying the name of a data field bound to the created <see cref="T:DevExpress.Snap.Core.API.SnapSparkline"/>.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapSparkline"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.CreateSnText(DevExpress.XtraRichEdit.API.Native.DocumentPosition,System.String)">
      <summary>
        <para>Adds a text box to the specified position in a document.</para>
      </summary>
      <param name="position">A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> object.</param>
      <param name="dataFieldName">A <see cref="T:System.String"/> value, specifying the name of a data field bound to the created <see cref="T:DevExpress.Snap.Core.API.SnapText"/>.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapText"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.FindListByName(System.String)">
      <summary>
        <para>Searches for a Snap list by its name.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> value, specifying the Snap list name.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapList"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.ParseField(DevExpress.XtraRichEdit.API.Native.Field)">
      <summary>
        <para>Converts the fields of a <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> to Snap fields.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraRichEdit.API.Native.Field"/> object.</param>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.SnapEntity"/> descendant.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.ParseField(System.Int32)">
      <summary>
        <para>Converts the fields of a <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> to Snap fields.</para>
      </summary>
      <param name="index">An integer value, specifying the index of a <see cref="T:DevExpress.XtraRichEdit.API.Native.Field"/> in the document&#39;s <see cref="T:DevExpress.XtraRichEdit.API.Native.FieldCollection"/>.</param>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.SnapEntity"/> descendant.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.RemoveField(System.Int32)">
      <summary>
        <para>Removes a Snap field at the specified position.</para>
      </summary>
      <param name="index">A zero-based integer value, specifying the field&#39;s position in a Snap list.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ISnapFieldOwner.RemoveSnList(System.String)">
      <summary>
        <para>Removes the specified Snap list from a document.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> value, specifying the list name.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.Parameter">
      <summary>
        <para>Provides functionality to a report parameter.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.Parameter.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.Parameter"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.Parameter.Clone">
      <summary>
        <para>Creates a new object that is a copy of the current <see cref="T:DevExpress.Snap.Core.API.Parameter"/> instance.</para>
      </summary>
      <returns>A new <see cref="T:DevExpress.Snap.Core.API.Parameter"/> instance, which is a copy of the current instance.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.Parameter.Dispose">
      <summary>
        <para>Disposes of the <see cref="T:DevExpress.Snap.Core.API.Parameter"/> object.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.Parameter.Disposed">
      <summary>
        <para>Occurs after the current parameter has been disposed of.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.Parameter.Name">
      <summary>
        <para>Specifies the parameter&#39;s name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the parameter&#39;s name.</value>
    </member>
    <member name="E:DevExpress.Snap.Core.API.Parameter.PropertyChanged">
      <summary>
        <para>Occurs every time any of the <see cref="T:DevExpress.Snap.Core.API.Parameter"/> object properties has changed its value.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.Parameter.Type">
      <summary>
        <para>Specifies the type for the parameter.</para>
      </summary>
      <value>A <see cref="T:System.Type"/> object.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.Parameter.Value">
      <summary>
        <para>Specifies the parameter&#39;s value.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> value, specifying the parameter&#39;s value.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.ParameterCollection">
      <summary>
        <para>A collection of a document&#39;s parameters.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ParameterCollection.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.ParameterCollection"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ParameterCollection.AddRange(DevExpress.Snap.Core.API.Parameter[])">
      <summary>
        <para>Appends an array of parameters to the storage collection.</para>
      </summary>
      <param name="parameters">An array of <see cref="T:DevExpress.Snap.Core.API.Parameter"/> objects to append to the collection.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ParameterCollection.AddRangeByValue(System.Collections.IList)">
      <summary>
        <para>Adds new parameters to the collection with their values corresponding to the specified parameter range.</para>
      </summary>
      <param name="parameters">An object implementing the <see cref="T:System.Collections.IList"/> interface.</param>
    </member>
    <member name="P:DevExpress.Snap.Core.API.ParameterCollection.Item(System.String)">
      <summary>
        <para>Provides access to the parameter with the specified name.</para>
      </summary>
      <param name="parameterName">A <see cref="T:System.String"/> value, specifying the name of a parameter within the collection.</param>
      <value>A <see cref="T:DevExpress.Snap.Core.API.Parameter"/> object.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.PrepareSnListColumnsEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.PrepareSnListColumns"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.PrepareSnListColumnsEventArgs.Body">
      <summary>
        <para>Provides access to a document&#39;s body.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> interface.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.PrepareSnListColumnsEventArgs.Header">
      <summary>
        <para>Provides access to a document&#39;s header.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> interface.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.PrepareSnListColumnsEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.PrepareSnListColumns"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.Core.API.PrepareSnListColumnsEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.PrepareSnListDetailEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.PrepareSnListDetail"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.PrepareSnListDetailEventArgs.Template">
      <summary>
        <para>Provides access to a document template.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> interface.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.PrepareSnListDetailEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.PrepareSnListDetail"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.Core.API.PrepareSnListDetailEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.PrepareSnListEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.PrepareSnList"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.PrepareSnListEventArgs.Template">
      <summary>
        <para>Provides access to a document template.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> interface.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.PrepareSnListEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.Core.API.SnapDocument.PrepareSnList"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.Core.API.PrepareSnListEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapBarCode">
      <summary>
        <para>Provides functionality to manipulate bar codes in Snap documents.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapBarCode.Alignment">
      <summary>
        <para>Specifies the alignment of the bar code image within the control&#39;s bounds.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPrinting.TextAlignment"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapBarCode.AutoModule">
      <summary>
        <para>Specifies whether or not the <see cref="P:DevExpress.Snap.Core.API.SnapBarCode.Module"/> property value should be calculated automatically based upon the bar code size.</para>
      </summary>
      <value>true, if the bar code module is calculated automatically; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapBarCode.Data">
      <summary>
        <para>Provides data to the <see cref="T:DevExpress.Snap.Core.API.SnapBarCode"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value containing the bar code&#39;s data.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapBarCode.GetGenerator">
      <summary>
        <para>Returns the <see cref="T:DevExpress.Snap.Core.API.SnapBarCode"/>&#39;s generator that specifies the bar code type.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraPrinting.BarCode.BarCodeGeneratorBase"/> descendant specifying the bar code generator type.</returns>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapBarCode.Module">
      <summary>
        <para>Specifies the width of the narrowest bar or space in the bar code.</para>
      </summary>
      <value>An integer value, specifying the width of the narrowest bar or space.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapBarCode.Orientation">
      <summary>
        <para>Specifies the angle by which a bar code is rotated.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPrinting.BarCode.BarCodeOrientation"/> enumeration value.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapBarCode.SetGenerator(DevExpress.XtraPrinting.BarCode.BarCodeGeneratorBase)">
      <summary>
        <para>Assigns a generator that specifies the bar code type to the <see cref="T:DevExpress.Snap.Core.API.SnapBarCode"/>.</para>
      </summary>
      <param name="value">A <see cref="T:DevExpress.XtraPrinting.BarCode.BarCodeGeneratorBase"/> descendant, specifying the bar code generator type.</param>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapBarCode.ShowData">
      <summary>
        <para>Specifies whether or not to display the <see cref="T:DevExpress.Snap.Core.API.SnapBarCode"/>&#39;s incoming data as plain text together with the encoded bar code image.</para>
      </summary>
      <value>true to show the bar code text; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapBarCode.TextAlignment">
      <summary>
        <para>Specifies the alignment of the bar code&#39;s text.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPrinting.TextAlignment"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapChart">
      <summary>
        <para>A chart embedded in a Snap report.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.AnnotationRepository">
      <summary>
        <para>Provides centralized access to all annotations present in a chart.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.AnnotationRepository"/> object specifying the annotation collection.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.Annotations">
      <summary>
        <para>Provides access to the chart&#39;s collection of annotations.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.AnnotationCollection"/> object that specifies the chart&#39;s collection of annotations.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.AppearanceName">
      <summary>
        <para>Gets or sets the appearance name currently used to draw the chart&#39;s elements.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the appearance name.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.AppearanceRepository">
      <summary>
        <para>Provides access to the repository of a chart&#39;s appearance presets.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.AppearanceRepository"/> object.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.AutoLayout">
      <summary>
        <para>Gets or sets a value indicating whether the adaptive layout feature is enabled for chart elements.</para>
      </summary>
      <value>true, to apply the adaptive layout algorithm to the chart; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.BackColor">
      <summary>
        <para>Gets or sets the chart&#39;s background color.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value which specifies the chart&#39;s background color.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.BackImage">
      <summary>
        <para>Gets the chart&#39;s background image settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.BackgroundImage"/> object containing settings used to specify the chart&#39;s background image.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.DataMember">
      <summary>
        <para>Gets or sets a specific member in the data source which supplies data to the chart.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the data source member.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.DataSource">
      <summary>
        <para>Gets or sets the chart&#39;s data source.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> representing the chart&#39;s data source.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.DataSourceName">
      <summary>
        <para>Gets or sets the name of the chart&#39;s data source.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value specifying the chart&#39;s data source name.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.Diagram">
      <summary>
        <para>Returns the chart&#39;s diagram and provides access to its settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.Diagram"/> object that specifies the chart&#39;s diagram.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.FillStyle">
      <summary>
        <para>Gets the chart&#39;s background fill style.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.RectangleFillStyle"/> object that specifies the background fill style.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.Height">
      <summary>
        <para>Gets or sets the height of the chart.</para>
      </summary>
      <value>An integer value specifying the chart&#39;s height, in pixels.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.ImageType">
      <summary>
        <para>Gets or sets the image format for a chart&#39;s representation in a Print Preview form and exported document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.Printing.PrintImageFormat"/> enumeration value specifying the image format for the chart.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.IndicatorsPaletteName">
      <summary>
        <para>Specifies the palette that is used to paint all indicators that exist in a chart.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value specifying the palette name.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.IndicatorsPaletteRepository">
      <summary>
        <para>Gets the indicators palette repository of the chart.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.PaletteRepository"/> object which represents the indicators palette repository of the chart.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.Legend">
      <summary>
        <para>Returns the chart&#39;s legend and provides access to its settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.Legend"/> object that specifies a legend on the chart.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.Legends">
      <summary>
        <para>Provides access to a collection of additional legends.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.XtraCharts.Legend"/> objects.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.PaletteBaseColorNumber">
      <summary>
        <para>Gets or sets the number of a palette color that should be used as a base color for painting chart series.</para>
      </summary>
      <value>An integer value that specifies the number of a palette base color.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.PaletteName">
      <summary>
        <para>Gets or sets the name of the palette currently used to draw the chart&#39;s series.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value which is the palette name.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.PaletteRepository">
      <summary>
        <para>Gets the palette repository of the chart.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.PaletteRepository"/> object which represents the palette repository of the chart.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.Series">
      <summary>
        <para>Provides access to the chart&#39;s collection of series objects.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.SeriesCollection"/> object that specifies the collection of chart series.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.SeriesDataMember">
      <summary>
        <para>Gets or sets the name of the data field whose values are used to automatically generate and populate chart series.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the data field&#39;s name.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.SeriesNameTemplate">
      <summary>
        <para>Provides access to options used to name data bound series.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.SeriesNameTemplate"/> object that contains naming settings.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.SeriesSorting">
      <summary>
        <para>Gets or sets a value that specifies how series are sorted in the chart based upon the series names.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.SortingMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.SeriesTemplate">
      <summary>
        <para>Provides access to the template settings for data bound series.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.SeriesBase"/> object that contains template settings for bound series.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.Size">
      <summary>
        <para>Gets or sets the size of the chart.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> object composed of a pair of integers, which specify the width and height of the chart, in pixels.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.SmallChartText">
      <summary>
        <para>Gets settings for the text displayed in the <see cref="T:DevExpress.Snap.Core.API.SnapChart"/>, when it&#39;s too small to fit the diagram.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.SmallChartText"/> object containing the small chart text options.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.Titles">
      <summary>
        <para>Provides access to the collection of chart titles.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraCharts.ChartTitleCollection"/> object that is a collection of chart titles.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.UseExplicitSize">
      <summary>
        <para>Gets or sets a value indicating whether the chart should keep its size unchangeable.</para>
      </summary>
      <value>true, to preserve the chart&#39;s size; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapChart.Width">
      <summary>
        <para>Gets or sets the width of the chart.</para>
      </summary>
      <value>An integer value specifying the chart&#39;s width, in pixels.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapCheckBox">
      <summary>
        <para>Provides functionality to manipulate check boxes in Snap documents.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapCheckBox.State">
      <summary>
        <para>Specifies the check state of the <see cref="T:DevExpress.Snap.Core.API.SnapCheckBox"/>.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.CheckState"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapDocument">
      <summary>
        <para>A central element of a Snap application that constitutes a report document and accounts for the creation and editing of a Snap report.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapDocument.ActiveThemeName">
      <summary>
        <para>Returns the name of the currently applied theme.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the name of the current theme.</value>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.AfterInsertSnList">
      <summary>
        <para>Occurs after the list has been inserted into a document.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.AfterInsertSnListColumns">
      <summary>
        <para>Occurs after inserting columns into the target list.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.AfterInsertSnListDetail">
      <summary>
        <para>Occurs after the detail section has been added to the list.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.AfterInsertSnListRecordData">
      <summary>
        <para>Fires after adding new fields to a document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.AppendSection">
      <summary>
        <para>Appends a new empty section to the end of the Snap document.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Snap.API.Native.SnapSection"/> object that is the section in the document to which a new section is appended.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.ApplyTheme(System.String)">
      <summary>
        <para>Applies the specified theme to the current document.</para>
      </summary>
      <param name="themeName">A <see cref="T:System.String"/> value, specifying the theme name.</param>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.BeforeFillExcelDataSource">
      <summary>
        <para>Occurs before data is extracted from the Microsoft Excel workbooks (XLS, XLSX or XLSM files) or CSV files.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.BeforeInsertSnList">
      <summary>
        <para>Occurs before a new list is created.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.BeforeInsertSnListColumns">
      <summary>
        <para>Occurs before a new set of columns is created.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.BeforeInsertSnListDetail">
      <summary>
        <para>Occurs before a new detail list is created.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.BeforeInsertSnListRecordData">
      <summary>
        <para>Fires when adding fields to a list without using hot zones (this is only possible if the target list has been previously converted to paragraphs).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.BeginUpdateDataSource">
      <summary>
        <para>Updates the <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> collection.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.ConfigureDataConnection">
      <summary>
        <para>Allows you to customize connection settings before connecting to a database.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.ConnectionError">
      <summary>
        <para>Fires after an attempt to establish a data connection has failed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.CreateMailMergeOptions">
      <summary>
        <para>Obsolete. Use the <see cref="M:DevExpress.Snap.Core.API.SnapDocument.CreateSnapMailMergeExportOptions"/> method instead.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraRichEdit.API.Native.MailMergeOptions"/> object.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.CreateSnapMailMergeExportOptions">
      <summary>
        <para>Creates the options that determine how a document is rendered when finishing a mail-merge report.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions"/> object.</returns>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.CustomFilterExpression">
      <summary>
        <para>Allows you to include WHERE clauses in SQL queries.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.DataSourceChanged">
      <summary>
        <para>Occurs when the data source has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapDocument.DataSources">
      <summary>
        <para>Provides access to the collection of a document&#39;s data sources.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.DataSourceInfoCollection"/> object, specifying the collection of the data sources.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.EndUpdateDataSource">
      <summary>
        <para>Ends the initialization of the <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> collection.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.ExportDocument(System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Exports the document to a stream in the specified format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> structure.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.ExportDocument(System.String,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Exports the document to a file in the specified format.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> value, specifying the file path.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> structure.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.GetDataSourceOwner(System.Object)">
      <summary>
        <para>Returns an object that contains information about the specified data source.</para>
      </summary>
      <param name="dataSource">A <see cref="T:System.Object"/> value specifying the data source.</param>
      <returns>An object that implements the <see cref="T:DevExpress.Snap.Core.API.IDataSourceOwner"/> interface (typically, this is a <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> object).</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.GetSection(DevExpress.XtraRichEdit.API.Native.DocumentPosition)">
      <summary>
        <para>Gets the section encompassing the specified position.</para>
      </summary>
      <param name="pos">A <see cref="T:DevExpress.Snap.API.Native.SnapSection"/> instance, specifying the position in the document.</param>
      <returns>A <see cref="T:DevExpress.XtraRichEdit.API.Native.Section"/> object, specifying a section containing the position.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.InsertSection(DevExpress.XtraRichEdit.API.Native.DocumentPosition)">
      <summary>
        <para>Inserts a new section into the document&#39;s <see cref="P:DevExpress.XtraRichEdit.API.Native.Document.Sections"/> collection at a specified position.</para>
      </summary>
      <param name="pos">A <see cref="T:DevExpress.Snap.API.Native.SnapSection"/> object, specifying a position to insert a new section.</param>
      <returns>A <see cref="T:DevExpress.XtraRichEdit.API.Native.Section"/> object that is the section in the document located before the newly inserted section.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.LoadTheme(System.IO.Stream)">
      <summary>
        <para>Loads the specified theme from a stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, from which to load a theme.</param>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.Theme"/> object, specifying the theme name.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.LoadTheme(System.String)">
      <summary>
        <para>Loads the specified theme from a file.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> object, specifying the file that contains a theme.</param>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.Theme"/> object, specifying the theme name.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.MailMerge(DevExpress.XtraRichEdit.API.Native.Document)">
      <summary>
        <para>Obsolete. Use the SnapDocument.SnapMailMerge method instead.</para>
      </summary>
      <param name="targetDocument">An object implementing the <see cref="T:DevExpress.XtraRichEdit.API.Native.Document"/> interface.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.MailMerge(DevExpress.XtraRichEdit.API.Native.MailMergeOptions,DevExpress.XtraRichEdit.API.Native.Document)">
      <summary>
        <para>Obsolete. Use the SnapDocument.SnapMailMerge method instead.</para>
      </summary>
      <param name="options">An object implementing the <see cref="T:DevExpress.XtraRichEdit.API.Native.MailMergeOptions"/> interface.</param>
      <param name="targetDocument">An object implementing the <see cref="T:DevExpress.XtraRichEdit.API.Native.Document"/> interface.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.MailMerge(DevExpress.XtraRichEdit.API.Native.MailMergeOptions,System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Obsolete. Use the SnapDocument.SnapMailMerge method instead.</para>
      </summary>
      <param name="options">An object implementing the <see cref="T:DevExpress.XtraRichEdit.API.Native.MailMergeOptions"/> interface.</param>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> structure.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.MailMerge(DevExpress.XtraRichEdit.API.Native.MailMergeOptions,System.String,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Obsolete. Use the SnapDocument.SnapMailMerge method instead.</para>
      </summary>
      <param name="options">An object implementing the <see cref="T:DevExpress.XtraRichEdit.API.Native.MailMergeOptions"/> interface.</param>
      <param name="fileName">A <see cref="T:System.String"/> value.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> structure.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.MailMerge(System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Obsolete. Use the SnapDocument.SnapMailMerge method instead.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> structure.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.MailMerge(System.String,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Obsolete. Use the SnapDocument.SnapMailMerge method instead.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> value.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> structure.</param>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapDocument.Parameters">
      <summary>
        <para>Provides access to the report&#39;s parameters collection.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.ParameterCollection"/> object.</value>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.PrepareSnList">
      <summary>
        <para>Occurs after generating a list template.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.PrepareSnListColumns">
      <summary>
        <para>Occurs for every added column after its body and header templates are created.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.API.SnapDocument.PrepareSnListDetail">
      <summary>
        <para>Occurs after generating a detail list template.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.SaveCurrentTheme(System.IO.Stream)">
      <summary>
        <para>Saves the current theme to a specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which to save the current theme.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.SaveCurrentTheme(System.IO.Stream,System.String)">
      <summary>
        <para>Saves the current theme to a specified stream with a new name.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which to save the current theme.</param>
      <param name="newName">A <see cref="T:System.String"/> value, specifying the new theme name.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.SaveCurrentTheme(System.String)">
      <summary>
        <para>Saves the current theme to a specified file.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> object, specifying the file to which to save the current theme.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.SaveDocument(System.IO.Stream)">
      <summary>
        <para>Saves the document to a stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.SaveDocument(System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Saves the document to a stream in the specified format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> structure.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.SaveDocument(System.String)">
      <summary>
        <para>Saves the document to a file.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> value, specifying the file name.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.SaveDocument(System.String,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Saves the document to a file in the specified format.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> value, specifying the file name.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> structure.</param>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapDocument.Sections">
      <summary>
        <para>Provides access to a Snap document&#39;s collection of sections.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.API.Native.SnapSectionCollection"/> interface.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.SnapMailMerge(DevExpress.Snap.Core.API.SnapDocument)">
      <summary>
        <para>Starts rendering the specified mail-merge document.</para>
      </summary>
      <param name="document">An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> interface.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.SnapMailMerge(DevExpress.Snap.Core.Options.SnapMailMergeExportOptions,DevExpress.Snap.Core.API.SnapDocument)">
      <summary>
        <para>Starts rendering a mail-merge document based on the applied export options and saving it to the specified target document.</para>
      </summary>
      <param name="options">An object implementing the <see cref="T:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions"/> interface.</param>
      <param name="targetDocument">An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> interface, storing the resulting document.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.SnapMailMerge(DevExpress.Snap.Core.Options.SnapMailMergeExportOptions,System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Starts rendering a mail-merge document based on the applied export options and saving it to a stream in the specified format.</para>
      </summary>
      <param name="options">An object implementing the <see cref="T:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions"/> interface.</param>
      <param name="stream">A <see cref="T:System.IO.Stream"/>, containing the document bytes.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> structure, specifying the document format.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.SnapMailMerge(DevExpress.Snap.Core.Options.SnapMailMergeExportOptions,System.String,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Starts rendering a mail-merge document based on the applied export options and saving it to a file in the specified format.</para>
      </summary>
      <param name="options">An object implementing the <see cref="T:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions"/> interface.</param>
      <param name="fileName">A <see cref="T:System.String"/> value, specifying the file name.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> structure, specifying the document format.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.SnapMailMerge(System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Starts rendering a mail-merge document and saving it to a stream in the specified format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/>, containing the document bytes.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> structure, specifying the document format.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.SnapMailMerge(System.String,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Starts rendering a mail-merge document and saving it to a file in the specified format.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> value, specifying the file name.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> structure, specifying the document format.</param>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapDocument.TableCellStyles">
      <summary>
        <para>Provides access to the collection of styles defined for cells in the table.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.API.Native.TableCellStyleCollection"/> object.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapDocument.Themes">
      <summary>
        <para>Provides access to the collection of available themes.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.ThemeCollection"/> object.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocument.UpdateTemplate">
      <summary>
        <para>Forces the update of the template area.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapDocumentFormat">
      <summary>
        <para>A document format to which a Snap document can be saved.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.API.SnapDocumentFormat.Pdf">
      <summary>
        <para>Saves the document to a PDF file.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Snap.Core.API.SnapDocumentFormat.Snap">
      <summary>
        <para>Saves the document to a SNX file (Snap native format).</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapDocumentPosition">
      <summary>
        <para>A position within a Snap document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapDocumentPosition.BeginUpdateDocument">
      <summary>
        <para>Starts editing the Snap document.</para>
      </summary>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapSubDocument"/> interface.</returns>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapDocumentRange">
      <summary>
        <para>A range within a Snap document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapDocumentRange.BeginUpdateDocument">
      <summary>
        <para>Starts editing the Snap document.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapSubDocument"/> interface.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapDocumentRange.End">
      <summary>
        <para>Gets the end position of the <see cref="T:DevExpress.Snap.Core.API.SnapDocumentRange"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocumentPosition"/> object, specifying the end of the text range.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapDocumentRange.Start">
      <summary>
        <para>Gets the start position of the <see cref="T:DevExpress.Snap.Core.API.SnapDocumentRange"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocumentPosition"/> object, specifying the start of the text range.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapEntity">
      <summary>
        <para>The base for classes that provide functionality to manipulate various entities in Snap documents.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapEntity.Active">
      <summary>
        <para>Indicates whether or not the <see cref="T:DevExpress.Snap.Core.API.SnapEntity"/> is locked.</para>
      </summary>
      <value>true if the entity is locked; otherwise false.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapEntity.BeginUpdate">
      <summary>
        <para>Enables runtime customization of a <see cref="T:DevExpress.Snap.Core.API.SnapEntity"/>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapEntity.Document">
      <summary>
        <para>Provides access to a <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/>, to which the <see cref="T:DevExpress.Snap.Core.API.SnapEntity"/> belongs.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> object.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapEntity.EndUpdate">
      <summary>
        <para>Unlocks the <see cref="T:DevExpress.Snap.Core.API.SnapEntity"/> after a call to the <see cref="M:DevExpress.Snap.Core.API.SnapEntity.BeginUpdate"/> method and causes an immediate visual update of the object.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapEntity.Field">
      <summary>
        <para>Provides access to a <see cref="T:DevExpress.XtraRichEdit.API.Native.Field"/> associated with the <see cref="T:DevExpress.Snap.Core.API.SnapEntity"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.API.Native.Field"/> object.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapEntity.SubDocument">
      <summary>
        <para>Provides access to an object implementing the basic document functionality that is common for the header, footer and the main document body.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapSubDocument"/> interface.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapHyperlink">
      <summary>
        <para>Provides functionality to insert hyperlinks in Snap documents.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapHyperlink.DisplayField">
      <summary>
        <para>Specifies the hyperlink text displayed in a document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapHyperlink.FieldName">
      <summary>
        <para>Specifies the data source field name that provides a URL to a hyperlink.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapHyperlink.ScreenTip">
      <summary>
        <para>Specifies the text of a tooltip displayed when pointing at a hyperlink with the mouse.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapHyperlink.Target">
      <summary>
        <para>Specifies the destination anchor of a hyperlink.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapImage">
      <summary>
        <para>Provides functionality to manipulate images in Snap documents.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapImage.Height">
      <summary>
        <para>Specifies the image height.</para>
      </summary>
      <value>An integer value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapImage.ImageSizeMode">
      <summary>
        <para>Specifies how the image is resized within the <see cref="T:DevExpress.Snap.Core.API.SnapImage"/>&#39;s dimensions.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPrinting.ImageSizeMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapImage.ScaleX">
      <summary>
        <para>Specifies the image horizontal scale relative to the <see cref="T:DevExpress.Snap.Core.API.SnapImage"/> size.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapImage.ScaleY">
      <summary>
        <para>Specifies the image vertical scale relative to the <see cref="T:DevExpress.Snap.Core.API.SnapImage"/> size.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapImage.ShowPlaceholder">
      <summary>
        <para>Specifies whether or not to display the field&#39;s placeholder if a Snap field receives an empty data source record.</para>
      </summary>
      <value>true to display the field&#39;s placeholder; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapImage.Size">
      <summary>
        <para>Specifies the image size.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure, specifying the image size.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapImage.UpdateMode">
      <summary>
        <para>Specifies whether to preserve the image box size or the original image proportions when the <see cref="T:DevExpress.Snap.Core.API.SnapImage"/> is resized.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Snap.Core.Fields.UpdateMergeImageFieldMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapImage.Width">
      <summary>
        <para>Specifies the image width.</para>
      </summary>
      <value>An integer value.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapList">
      <summary>
        <para>Provides functionality to manipulate lists in Snap documents.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapList.ApplyTableStyles">
      <summary>
        <para>Applies the selected table style to the current list.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapList.ApplyTableStyles(System.Int32)">
      <summary>
        <para>Applies the selected table style to the current list at the specified nesting level.</para>
      </summary>
      <param name="level">An integer value, specifying the document nesting level.</param>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.BottomBorderVisibleWhenNested">
      <summary>
        <para>Specifies whether the bottom border for a SnapList table located inside a table cell should be visible.</para>
      </summary>
      <value>true, to enable a SnapList table&#39;s bottom border; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.DataMember">
      <summary>
        <para>Specifies the data member from the <see cref="T:DevExpress.Snap.Core.API.SnapList"/>&#39;s connected data source.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.DataSourceName">
      <summary>
        <para>Specifies the name of the <see cref="T:DevExpress.Snap.Core.API.SnapList"/> data source.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the data source name.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.EditorRowLimit">
      <summary>
        <para>Specifies the maximum number of data rows to include in the document during the design session.</para>
      </summary>
      <value>An integer value, specifying the maximum number of data rows to include in the document during the design session. The default is 20.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.Filters">
      <summary>
        <para>Provides access to the <see cref="T:DevExpress.Snap.Core.API.SnapList"/> filter settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapListFilters"/> object.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.Groups">
      <summary>
        <para>Provides access to the <see cref="T:DevExpress.Snap.Core.API.SnapList"/>&#39;s groups</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapListGroups"/> object.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.KeepLastSeparator">
      <summary>
        <para>Specifies whether or not to display the separator that goes after the last <see cref="T:DevExpress.Snap.Core.API.SnapList"/> record.</para>
      </summary>
      <value>true, to always display the separator; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.ListFooter">
      <summary>
        <para>Provides access to the <see cref="T:DevExpress.Snap.Core.API.SnapList"/>&#39;s footer.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> object.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.ListHeader">
      <summary>
        <para>Provides access to the <see cref="T:DevExpress.Snap.Core.API.SnapList"/>&#39;s header.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> object.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.Name">
      <summary>
        <para>Specifies the <see cref="T:DevExpress.Snap.Core.API.SnapList"/> name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.RowTemplate">
      <summary>
        <para>Provides access to the <see cref="T:DevExpress.Snap.Core.API.SnapList"/> row template that determines the layout and appearance of each data record in a document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> object.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.Separator">
      <summary>
        <para>Provides access to the separator settings of a Snap list.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> interface.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.SkipEmptyListOnExport">
      <summary>
        <para>Gets or sets whether an empty list should appear in a printout or an export output.</para>
      </summary>
      <value>true, to skip an empty list when a document is printed or exported; otherwise, false. The default is false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapList.Sorting">
      <summary>
        <para>Provides access to the sorting options of a <see cref="T:DevExpress.Snap.Core.API.SnapList"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapListSorting"/> object.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapListFilters">
      <summary>
        <para>Provides functionality to data filtering.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListFilters.AddRange(System.Collections.Generic.IEnumerable{System.String})">
      <summary>
        <para>Add the specified range of conditions to the filtering expression of a Snap list.</para>
      </summary>
      <param name="collection">A collection of <see cref="T:System.String"/> values, specifying the filter conditions.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapListGroupInfo">
      <summary>
        <para>Maintains the header, footer and separator options of document groups.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroupInfo.CreateFooter">
      <summary>
        <para>Displays the group footer.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> object.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroupInfo.CreateHeader">
      <summary>
        <para>Displays the group header.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> object.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroupInfo.CreateSeparator">
      <summary>
        <para>Displays the separator between each group entry.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> object.</returns>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapListGroupInfo.Footer">
      <summary>
        <para>Provides access to the group footer.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> object.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapListGroupInfo.Header">
      <summary>
        <para>Provides access to the group header.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> object.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroupInfo.RemoveFooter">
      <summary>
        <para>Removes the group footer from the document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroupInfo.RemoveHeader">
      <summary>
        <para>Removes the group header from the document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroupInfo.RemoveSeparator">
      <summary>
        <para>Removes the group separator from the document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapListGroupInfo.Separator">
      <summary>
        <para>Provides access to the separator displayed between each group entry.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> object.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapListGroupParam">
      <summary>
        <para>Assists in performing tasks related to data sorting in code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroupParam.#ctor(System.String,DevExpress.Data.ColumnSortOrder)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.SnapListGroupParam"/> class with the specified settings.</para>
      </summary>
      <param name="fieldName">A <see cref="T:System.String"/> value, specifying the field name.</param>
      <param name="sortOrder">A <see cref="T:DevExpress.Data.ColumnSortOrder"/> enumeration value.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroupParam.#ctor(System.String,DevExpress.Data.ColumnSortOrder,DevExpress.Snap.Core.API.GroupInterval)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.API.SnapListGroupParam"/> class with the specified settings.</para>
      </summary>
      <param name="fieldName">A <see cref="T:System.String"/> value. This value is assigned to the <see cref="P:DevExpress.Snap.Core.API.SnapListGroupParam.FieldName"/> property.</param>
      <param name="sortOrder">A <see cref="T:DevExpress.Data.ColumnSortOrder"/> enumeration value. This value is assigned to the <see cref="P:DevExpress.Snap.Core.API.SnapListGroupParam.SortOrder"/> property.</param>
      <param name="interval">A <see cref="T:DevExpress.Snap.Core.API.GroupInterval"/> enumeration value. This value is assigned to the <see cref="P:DevExpress.Snap.Core.API.SnapListGroupParam.Interval"/> property.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroupParam.Equals(System.Object)">
      <summary>
        <para>Determines whether or not the specified object is equal to the current <see cref="T:DevExpress.Snap.Core.API.SnapListGroupParam"/> instance.</para>
      </summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>true if the specified object is equal to the current <see cref="T:DevExpress.Snap.Core.API.SnapListGroupParam"/> instance; otherwise false.</returns>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapListGroupParam.FieldName">
      <summary>
        <para>Specifies the data field against which the <see cref="T:DevExpress.Snap.Core.API.SnapList"/> data is grouped.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the data field name.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroupParam.GetHashCode">
      <summary>
        <para>Serves as the default hash function.</para>
      </summary>
      <returns>An integer value, specifying the hash code for the current object.</returns>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapListGroupParam.Interval">
      <summary>
        <para>Specifies a date-time group interval.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.GroupInterval"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapListGroupParam.SortOrder">
      <summary>
        <para>Specifies the sort order of the created group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Data.ColumnSortOrder"/> enumeration value.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroupParam.ToString">
      <summary>
        <para>Returns the textual representation of <see cref="T:DevExpress.Snap.Core.API.SnapListGroupParam"/>.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value.</returns>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapListGroups">
      <summary>
        <para>Provides functionality to data grouping.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroups.CreateSnapListGroupInfo">
      <summary>
        <para>Creates a group in a document.</para>
      </summary>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapListGroupInfo"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroups.CreateSnapListGroupInfo(DevExpress.Snap.Core.API.SnapListGroupParam)">
      <summary>
        <para>Creates a group in a document, using the specified parameter.</para>
      </summary>
      <param name="groupParameter">A <see cref="T:DevExpress.Snap.Core.API.SnapListGroupParam"/> structure.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapListGroupInfo"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListGroups.CreateSnapListGroupInfo(System.Collections.Generic.IEnumerable{DevExpress.Snap.Core.API.SnapListGroupParam})">
      <summary>
        <para>Creates a group in a document, using the specified parameters.</para>
      </summary>
      <param name="groupParameters">A collection of <see cref="T:DevExpress.Snap.Core.API.SnapListGroupParam"/> structures.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapListGroupInfo"/> interface.</returns>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapListSorting">
      <summary>
        <para>Provides functionality to sort lists.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapListSorting.AddRange(System.Collections.Generic.IEnumerable{DevExpress.Snap.Core.API.SnapListGroupParam})">
      <summary>
        <para>Appends an array of sorting criteria to the storage collection.</para>
      </summary>
      <param name="collection">An array of <see cref="T:DevExpress.Snap.Core.API.Parameter"/> objects to append to the collection.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapSingleListItemEntity">
      <summary>
        <para>If implemented by a class, provides functionality to items of a Snap list.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSingleListItemEntity.DataFieldName">
      <summary>
        <para>Specifies the name of a data field that is assigned to the Snap field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the data member name.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSingleListItemEntity.EmptyFieldDataAlias">
      <summary>
        <para>Specifies the text to show in a document instead of a blank space if a Snap field receives an empty data source record.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the empty field data alias.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSingleListItemEntity.EnableEmptyFieldDataAlias">
      <summary>
        <para>Enables the application displaying a custom text instead of a blank space if a Snap field receives an empty data source record.</para>
      </summary>
      <value>true to replace empty data records with an alias in the document; otherwise false.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapSingleListItemEntitySupportsParameters">
      <summary>
        <para>If implemented by a class, provides functionality to items of a Snap list.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSingleListItemEntitySupportsParameters.IsParameter">
      <summary>
        <para>Gets or sets whether the data field associated with a SnapEntity object is a parameter.</para>
      </summary>
      <value>true, if the associated data field is a parameter; otherwise, false</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapSparkline">
      <summary>
        <para>Provides functionality to manipulate sparklines in Snap documents.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.AreaOpacity">
      <summary>
        <para>Specifies the opacity (0-255) of an area sparkline.</para>
      </summary>
      <value>A <see cref="T:System.Byte"/> value from 0 (transparent) to 255 (opaque).</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.BarDistance">
      <summary>
        <para>Specifies the distance between two bars of a bar sparkline.</para>
      </summary>
      <value>An integer value (in pixels).</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.Color">
      <summary>
        <para>Specifies the color to draw a <see cref="T:DevExpress.Snap.Core.API.SnapSparkline"/>.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> structure that defines the color to draw a sparkline.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.DataSourceName">
      <summary>
        <para>Specifies the name of a sparkline data source.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the data source name.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.EndPointColor">
      <summary>
        <para>Gets or sets the color to draw the end point of a sparkline.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> structure that defines the color to draw the end point.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.EndPointMarkerSize">
      <summary>
        <para>Specifies the size of a sparkline&#39;s end point marker.</para>
      </summary>
      <value>An integer value, specifying the marker size, in pixels.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.Height">
      <summary>
        <para>Specifies the sparkline height.</para>
      </summary>
      <value>An integer value, specifying the height of a sparkline.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.HighlightEndPoint">
      <summary>
        <para>Specifies whether or not to highlight the end point of a sparkline.</para>
      </summary>
      <value>true, to highlight the end point; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.HighlightMaxPoint">
      <summary>
        <para>Specifies whether or not to highlight the maximum point of a sparkline.</para>
      </summary>
      <value>true, to highlight the maximum point; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.HighlightMinPoint">
      <summary>
        <para>Specifies whether or not to highlight the minimum point of a sparkline.</para>
      </summary>
      <value>true, to highlight the minimum point; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.HighlightNegativePoints">
      <summary>
        <para>Specifies whether or not to highlight negative points of a sparkline.</para>
      </summary>
      <value>true, to highlight all negative points; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.HighlightStartPoint">
      <summary>
        <para>Specifies whether or not to highlight the start point of a sparkline.</para>
      </summary>
      <value>true to highlight the start point; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.LineWidth">
      <summary>
        <para>Specifies the width of a line in a <see cref="T:DevExpress.Snap.Core.API.SnapSparkline"/>.</para>
      </summary>
      <value>An integer value, specifying the line width.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.MarkerColor">
      <summary>
        <para>Specifies the color to draw line markers.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> structure that defines the color to draw line markers.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.MarkerSize">
      <summary>
        <para>Specifies the size of line markers.</para>
      </summary>
      <value>An integer value, specifying the marker size, in pixels.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.MaxPointColor">
      <summary>
        <para>Specifies the color to draw the maximum point of a sparkline.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> structure that defines the color to draw the maximum point.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.MaxPointMarkerSize">
      <summary>
        <para>Specifies the size of a maximum point&#39;s marker.</para>
      </summary>
      <value>An integer value, specifying the marker size, in pixels.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.MinPointColor">
      <summary>
        <para>Specifies the color to draw the minimum point of a sparkline.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> structure that defines the color to draw the minimum point.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.MinPointMarkerSize">
      <summary>
        <para>Specifies the size of a minimum point&#39;s marker.</para>
      </summary>
      <value>An integer value, specifying the marker size, in pixels.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.NegativePointColor">
      <summary>
        <para>Specifies the color to draw negative points.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> structure that defines the color to draw negative points.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.NegativePointMarkerSize">
      <summary>
        <para>Specifies the size of markers for negative points.</para>
      </summary>
      <value>An integer value, specifying the marker size, in pixels.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.ShowMarkers">
      <summary>
        <para>Specifies the visibility of point markers on a sparkline.</para>
      </summary>
      <value>true to show markers for each data point; false to hide them.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.Size">
      <summary>
        <para>Specifies the sparkline dimensions.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure, specifying the sparkline dimensions.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.StartPointColor">
      <summary>
        <para>Specifies the color to draw the start point of a sparkline.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> structure that defines the color to draw the start point.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.StartPointMarkerSize">
      <summary>
        <para>Specifies the size of a start point&#39;s marker.</para>
      </summary>
      <value>An integer value, specifying the marker size, in pixels.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.ViewType">
      <summary>
        <para>Specifies a sparkline view type.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Sparkline.SparklineViewType"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSparkline.Width">
      <summary>
        <para>Specifies the sparkline width.</para>
      </summary>
      <value>An integer value, specifying the width of a sparkline.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapSubDocument">
      <summary>
        <para>Provides the essential Snap document functionality.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapSubDocument.CreatePosition(System.Int32)">
      <summary>
        <para>Creates a new position in the current document.</para>
      </summary>
      <param name="start">An integer value, specifying the start position.</param>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.SnapDocumentPosition"/> object, specifying the position in the current document.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapSubDocument.CreateRange(DevExpress.XtraRichEdit.API.Native.DocumentPosition,System.Int32)">
      <summary>
        <para>Creates a new document range using the specified start position and range length.</para>
      </summary>
      <param name="start">A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> object, specifying the start position.</param>
      <param name="length">An integer value, specifying the range length.</param>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.SnapDocumentRange"/> object.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.SnapSubDocument.CreateRange(System.Int32,System.Int32)">
      <summary>
        <para>Creates a new document range using the specified start position and range length.</para>
      </summary>
      <param name="start">An integer value, specifying the start position.</param>
      <param name="length">An integer value, specifying the range length.</param>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.SnapDocumentRange"/> object.</returns>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapSubDocument.Range">
      <summary>
        <para>Gets the range for the current Snap document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocumentRange"/> value.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.SnapText">
      <summary>
        <para>Provides functionality to manipulate text in Snap documents.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapText.FieldName">
      <summary>
        <para>Specifies the name of a data field that is bound to the <see cref="T:DevExpress.Snap.Core.API.SnapText"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapText.FormatString">
      <summary>
        <para>Specifies a string format applied to the <see cref="T:DevExpress.Snap.Core.API.SnapText"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapText.KeepLastParagraph">
      <summary>
        <para>Specifies whether or not to insert the current paragraph spacing after its last occurrence in the document.</para>
      </summary>
      <value>true, to maintain the last paragraph; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapText.SummaryFunc">
      <summary>
        <para>Specifies a summary function applied to the <see cref="T:DevExpress.Snap.Core.API.SnapText"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeList.SummaryItemType"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapText.SummaryIgnoreNullValues">
      <summary>
        <para>Specifies whether or not to ignore empty fields when calculating a summary for the <see cref="T:DevExpress.Snap.Core.API.SnapText"/>.</para>
      </summary>
      <value>true to ignore null values; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapText.SummaryRunning">
      <summary>
        <para>Specifies the range across which a summary function is calculated.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.Fields.SummaryRunning"/> enumeration value.$</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapText.TextAfterIfFieldNotBlank">
      <summary>
        <para>Specifies the text added to the <see cref="T:DevExpress.Snap.Core.API.SnapText"/> after the content obtained from the bound data field (if this content is not empty).</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapText.TextBeforeIfFieldNotBlank">
      <summary>
        <para>Specifies the text added to the <see cref="T:DevExpress.Snap.Core.API.SnapText"/> before the content obtained from the bound data field (if this content is not empty).</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.SnapText.TextFormat">
      <summary>
        <para>Gets or sets format of the text obtained as the field result.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the format of the text content.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.Theme">
      <summary>
        <para>Maintains the overall look and feel of a report, by specifying the appearance of its elements.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.API.Theme.Icon">
      <summary>
        <para>Specifies an icon that is assigned to the <see cref="T:DevExpress.Snap.Core.API.Theme"/>.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Image"/> object, specifying the theme icon.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.Theme.IsDefault">
      <summary>
        <para>Indicates whether or not the <see cref="T:DevExpress.Snap.Core.API.Theme"/> is used by default.</para>
      </summary>
      <value>true if the theme is used by default; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.API.Theme.Name">
      <summary>
        <para>Specifies the <see cref="T:DevExpress.Snap.Core.API.Theme"/> name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.API.Theme.UpdateToMatchDocumentStyles">
      <summary>
        <para>Assigns the settings of a current document table to the selected theme.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.API.ThemeCollection">
      <summary>
        <para>A collection of <see cref="T:DevExpress.Snap.Core.API.Theme"/> objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ThemeCollection.Add(DevExpress.Snap.Core.API.Theme)">
      <summary>
        <para>Adds the specified theme to the collection.</para>
      </summary>
      <param name="theme">A <see cref="T:DevExpress.Snap.Core.API.Theme"/> object.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ThemeCollection.CreateNew">
      <summary>
        <para>Adds a new default theme to the collection.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.Theme"/> object.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ThemeCollection.CreateNew(System.String)">
      <summary>
        <para>Adds a new default theme to the collection.</para>
      </summary>
      <param name="baseThemeName">A <see cref="T:System.String"/> value, specifying the base theme name.</param>
      <returns>A <see cref="T:DevExpress.Snap.Core.API.Theme"/> object.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.API.ThemeCollection.Delete(DevExpress.Snap.Core.API.Theme)">
      <summary>
        <para>Removes the specified theme from the collection.</para>
      </summary>
      <param name="theme">A <see cref="T:DevExpress.Snap.Core.API.Theme"/> object.</param>
    </member>
    <member name="P:DevExpress.Snap.Core.API.ThemeCollection.Item(System.String)">
      <summary>
        <para>Obtains an individual <see cref="T:DevExpress.Snap.Core.API.Theme"/> by its name.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> value, specifying the theme name.</param>
      <value>A <see cref="T:DevExpress.Snap.Core.API.Theme"/>.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.API.Themes">
      <summary>
        <para>Lists the available <see cref="T:DevExpress.Snap.Core.API.Theme"/> objects by their names.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.API.Themes.Casual">
      <summary>
        <para>&quot;Casual&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Snap.Core.API.Themes.ContrastCyan">
      <summary>
        <para>&quot;Contrast Cyan&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Snap.Core.API.Themes.ContrastOrange">
      <summary>
        <para>&quot;Contrast Orange&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Snap.Core.API.Themes.ContrastRed">
      <summary>
        <para>&quot;Contrast Red&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Snap.Core.API.Themes.ContrastSalmon">
      <summary>
        <para>&quot;Contrast Salmon&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Snap.Core.API.Themes.ContrastYellow">
      <summary>
        <para>&quot;Contrast Yellow&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Snap.Core.API.Themes.DodgerBlue">
      <summary>
        <para>&quot;Dodger Blue&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Snap.Core.API.Themes.FormalBlue">
      <summary>
        <para>&quot;Formal Blue&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Snap.Core.API.Themes.MildBlue">
      <summary>
        <para>&quot;Mild Blue&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Snap.Core.API.Themes.MildBrown">
      <summary>
        <para>&quot;Mild Brown&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Snap.Core.API.Themes.MildCyan">
      <summary>
        <para>&quot;Mild Cyan&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Snap.Core.API.Themes.MildViolet">
      <summary>
        <para>&quot;Mild Violet&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Snap.Core.API.Themes.SoftLilac">
      <summary>
        <para>&quot;Soft Lilac&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="N:DevExpress.Snap.Core.Fields">
      <summary>
        <para>Contains classes that provide the core functionality to Snap fields.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.Fields.SummaryRunning">
      <summary>
        <para>Lists the values that specify the running scope of a summary function.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Fields.SummaryRunning.Group">
      <summary>
        <para>The summary function is calculated across the specified group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Fields.SummaryRunning.None">
      <summary>
        <para>The running scope of the summary function is not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Fields.SummaryRunning.Report">
      <summary>
        <para>The summary function is calculated across the entire report.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.Fields.UpdateMergeImageFieldMode">
      <summary>
        <para>Lists the values that specify how the <see cref="T:DevExpress.Snap.Core.API.SnapImage"/> dimensions are controlled.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Fields.UpdateMergeImageFieldMode.KeepScale">
      <summary>
        <para>The <see cref="T:DevExpress.Snap.Core.API.SnapImage"/> dimensions are controlled by its ScaleX and ScaleY properties.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Fields.UpdateMergeImageFieldMode.KeepSize">
      <summary>
        <para>The <see cref="T:DevExpress.Snap.Core.API.SnapImage"/> dimensions are controlled by its Size property.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Snap.Core.Forms">
      <summary>
        <para>Contains the classes that define the parameters of dialog windows in Snap.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.Forms.MailMergeExportFormControllerParameters">
      <summary>
        <para>Defines the parameters of the Export Range dialog window that are eligible to be changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.Forms.MailMergeExportFormControllerParameters.#ctor(DevExpress.Snap.Core.ISnapControl,DevExpress.Snap.Core.Options.SnapMailMergeExportOptions)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.Forms.MailMergeExportFormControllerParameters"/> class with the specified settings.</para>
      </summary>
      <param name="control">An object implementing the <see cref="T:DevExpress.Snap.Core.ISnapControl"/> interface.</param>
      <param name="properties">A <see cref="T:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions"/> object.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.Forms.ReportStructureEditorFormControllerParameters">
      <summary>
        <para>Defines the parameters of the Groups Order Editor window that are eligible to be changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.Forms.ReportStructureEditorFormControllerParameters.Control">
      <summary>
        <para>Indicates the <see cref="T:DevExpress.Snap.SnapControl"/> that is the dialog owner.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.ISnapControl"/> interface.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.Forms.TableCellStyleFormControllerParameters">
      <summary>
        <para>Defines the parameters of the Modify Style dialog window that are eligible to be changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.Forms.TableCellStyleFormControllerParameters.#ctor(DevExpress.XtraRichEdit.IRichEditControl,DevExpress.XtraRichEdit.Model.TableCellStyle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.Forms.TableCellStyleFormControllerParameters"/> class with the specified settings.</para>
      </summary>
      <param name="control">An object implementing the <see cref="T:DevExpress.XtraRichEdit.IRichEditControl"/> interface.</param>
      <param name="sourceCellStyle">A DevExpress.XtraRichEdit.Model.TableCellStyle object.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.ISnapControl">
      <summary>
        <para>Implements the main functionality of Snap Control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.ISnapControl.AddNewDataSource">
      <summary>
        <para>Runs the Create Data Source wizard.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.ISnapControl.BeforeConversion">
      <summary>
        <para>Occurs before a snap document is exported to format other than the native .SNX.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Snap.Core.ISnapControl.BeforeLoadCustomAssembly">
      <summary>
        <para>Occurs when the control loads a report template (.snx file) containing the Entity Framework data source originated from a compiled assembly.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.ISnapControl.DataSources">
      <summary>
        <para>Provides access to the collection of <see cref="T:DevExpress.Snap.SnapControl"/> data sources.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.DataSourceInfoCollection"/> object.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.ISnapControl.LoadTheme">
      <summary>
        <para>Loads a report theme from a file.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.ISnapControl.SaveTheme">
      <summary>
        <para>Saves the current report theme to a file.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.ISnapControl.ShowChartDesigner(DevExpress.XtraCharts.Native.IChartContainer)">
      <summary>
        <para>For internal use. Invokes the Chart Designer dialog.</para>
      </summary>
      <param name="chartContainer">An object implementing the IChartContainer interface.</param>
      <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.ISnapControl.ShowDataSourceSaveForm">
      <summary>
        <para>For internal use. Invokes the Import Template dialog.</para>
      </summary>
      <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.ISnapControl.ShowFilterStringEditorForm(DevExpress.Data.Browsing.Design.DesignBinding,System.String@)">
      <summary>
        <para>For internal use. Invokes the FilterString Editor.</para>
      </summary>
      <param name="binding">A DevExpress.Data.Browsing.Design.DesignBinding object.</param>
      <param name="filterString">A <see cref="T:System.String"/> value.</param>
      <returns>A Boolean value.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.ISnapControl.ShowGroupFieldsForm(System.Object,System.IServiceProvider)">
      <summary>
        <para>For internal use. Invokes the Group Fields Collection Editor.</para>
      </summary>
      <param name="objectToChange">A <see cref="T:System.Object"/> value.</param>
      <param name="serviceProvider">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.ISnapControl.ShowMailMergeExportOptionsForm(DevExpress.Snap.Core.Options.SnapMailMergeExportOptions,DevExpress.Snap.Core.ShowMailMergeExportOptionsFormCallback,System.Object)">
      <summary>
        <para>For internal use. Invokes the Export Range dialog.</para>
      </summary>
      <param name="properties">An object implementing the <see cref="T:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions"/> interface.</param>
      <param name="callback">A DevExpress.Snap.Core.ShowMailMergeExportOptionsFormCallback delegate.</param>
      <param name="data">A <see cref="T:System.Object"/> value.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.ISnapControl.ShowMailMergeSortingForm">
      <summary>
        <para>For internal use. Invokes the Sort dialog.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.ISnapControl.ShowReportStructureEditorForm">
      <summary>
        <para>For internal use. Invokes the Groups Order Editor.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.ISnapControl.ShowTableCellStyleForm(DevExpress.XtraRichEdit.Model.TableCellStyle)">
      <summary>
        <para>For internal use. Invokes the Modify Styles dialog.</para>
      </summary>
      <param name="style">A DevExpress.XtraRichEdit.Model.TableCellStyle object.</param>
    </member>
    <member name="E:DevExpress.Snap.Core.ISnapControl.ValidateCustomSql">
      <summary>
        <para>Allows validation of the custom SQL query created using the Data Source Wizard or the Query Builder.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Snap.Core.Options">
      <summary>
        <para>Contains the classes that provide the options of user interface elements in Snap.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.Options.FieldSelectionOnMouseClickMode">
      <summary>
        <para>Lists the values specifying the mode of selecting Snap fields when clicking them using the mouse.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Options.FieldSelectionOnMouseClickMode.Always">
      <summary>
        <para>A field is selected when clicking it using the mouse.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Options.FieldSelectionOnMouseClickMode.Auto">
      <summary>
        <para>The field selection mode is automatically defined.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Options.FieldSelectionOnMouseClickMode.Never">
      <summary>
        <para>A field is not selected when clicking it with the mouse.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.Options.IDataDispatcherOptions">
      <summary>
        <para>If implemented by a class, provides functionality to bind a Snap document to a data source.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.IDataDispatcherOptions.DataSourceName">
      <summary>
        <para>Specifies the name of a data source used for mail merge.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the data source name.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.IDataDispatcherOptions.FilterString">
      <summary>
        <para>Specifies the filter string applied to a Snap field data source.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the filter criteria.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.IDataDispatcherOptions.Sorting">
      <summary>
        <para>Specifies the data sorting criteria applied to a Snap field data source.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapListSorting"/> interface, specifying the data sorting criteria.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.Options.ISnapControlOptions">
      <summary>
        <para>Maintains the visual options of Snap mail merge.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.ISnapControlOptions.SnapMailMergeVisualOptions">
      <summary>
        <para>Provides access to the options that determine how a mail-merge document is displayed in a Snap application.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.Options.SnapMailMergeVisualOptions"/> object.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.Options.MailMergeNumberingRestart">
      <summary>
        <para>List types of the behavior of numbered lists when the mail merge operation is performed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Options.MailMergeNumberingRestart.Always">
      <summary>
        <para>Restart a numbered list for each record.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Options.MailMergeNumberingRestart.AlwaysExceptParagraphStyles">
      <summary>
        <para>Restart the numbered list for each record, but ignore lists that created by applying paragraph styles with numbering (e.g., &quot;Chapter 1&quot;, &quot;Chapter 2&quot;, etc. ) .</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Options.MailMergeNumberingRestart.None">
      <summary>
        <para>Continue the list numbering unless otherwise specified in the list settings (e.g, when a specific numbered list is manually restarted).</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.Options.RecordSeparator">
      <summary>
        <para>Lists the separators that can be inserted between each pair of master sections in a mail-merge document.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Options.RecordSeparator.Custom">
      <summary>
        <para>Records are separated in a custom way.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Options.RecordSeparator.None">
      <summary>
        <para>Records are not separated.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Options.RecordSeparator.PageBreak">
      <summary>
        <para>Records are separated by page breaks.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Options.RecordSeparator.Paragraph">
      <summary>
        <para>Records are separated by paragraphs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Options.RecordSeparator.SectionEvenPage">
      <summary>
        <para>Records are separated by section breaks. New sections are started on even-numbered pages.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Options.RecordSeparator.SectionNextPage">
      <summary>
        <para>Records are separated by section breaks.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Core.Options.RecordSeparator.SectionOddPage">
      <summary>
        <para>Records are separated by section breaks. New sections are started on odd-numbered pages.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.Options.SnapFieldOptions">
      <summary>
        <para>Maintains the options that define the appearance and behavior of Snap fields.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.Options.SnapFieldOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.Options.SnapFieldOptions"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapFieldOptions.EnableEmptyFieldDataAlias">
      <summary>
        <para>Enables the application displaying a custom text instead of a blank space if a Snap field receives an empty data source record.</para>
      </summary>
      <value>true to replace empty data records with an alias in the document; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapFieldOptions.FieldSelection">
      <summary>
        <para>Specifies the mode for selecting Snap fields when clicking them with the mouse.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.Options.FieldSelectionOnMouseClickMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapFieldOptions.ShowChartInfoPanel">
      <summary>
        <para>Gets or sets whether the Chart Info panel is displayed in a chart element.</para>
      </summary>
      <value>True, to display Chart Info panel; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions">
      <summary>
        <para>Maintains the options that determine how a document is rendered when finishing a mail-merge report.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions.CustomSeparator">
      <summary>
        <para>Returns a custom separator that is inserted between each pair of master sections in a mail-merge document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> object.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions.ExportFrom">
      <summary>
        <para>Specifies the data record number, from which the specified range of data records that is included in a published mail-merge document starts.</para>
      </summary>
      <value>An integer value, specifying the number of the first record.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions.ExportRecordsCount">
      <summary>
        <para>Specifies the number of data records to include in the published mail-merge document, counting from the current <see cref="P:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions.ExportFrom"/> value.</para>
      </summary>
      <value>An integer value, specifying the number of data records to include in a document.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions.NumberedListRestart">
      <summary>
        <para>Sets the behavior of numbered lists when the mail merge operation is performed.</para>
      </summary>
      <value>One of the <see cref="T:DevExpress.Snap.Core.Options.MailMergeNumberingRestart"/> enumeration values.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions.ProgressIndicationFormVisible">
      <summary>
        <para>Specifies whether or not to show the progress indicator while a mail-merge document is being rendered.</para>
      </summary>
      <value>true, to indicate the current progress status; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions.RecordSeparator">
      <summary>
        <para>Specifies the type of separator to insert between each pair of master sections in a mail-merge document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.Options.RecordSeparator"/> enumeration value, specifying the separator type.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapMailMergeExportOptions.StartEachRecordFromNewParagraph">
      <summary>
        <para>Specifies whether or not to start printing each data record from a new paragraph.</para>
      </summary>
      <value>true, to start printing each data record from a new paragraph; otherwise false.</value>
    </member>
    <member name="T:DevExpress.Snap.Core.Options.SnapMailMergeVisualOptions">
      <summary>
        <para>Maintains the options that determine how a mail-merge document is displayed in a Snap application.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapMailMergeVisualOptions.CurrentRecordIndex">
      <summary>
        <para>Specifies the number of the currently displayed data record.</para>
      </summary>
      <value>An integer value.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapMailMergeVisualOptions.IsMailMergeEnabled">
      <summary>
        <para>Indicates whether or not the mail merge is enabled for a data source.</para>
      </summary>
      <value>true, if mail merge is enabled for the data source; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapMailMergeVisualOptions.ProgressIndicationFormVisible">
      <summary>
        <para>Primary option that specifies whether or not to show the progress dialog while a mail-merge document is being rendered.</para>
      </summary>
      <value>True, to show the progress dialog that indicates the current progress status; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnapMailMergeVisualOptions.RecordCount">
      <summary>
        <para>Indicates the total number of data records in a mail-merge document.</para>
      </summary>
      <value>An integer value, indicating the total number of data records in a document.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.Options.SnapMailMergeVisualOptions.RefreshRecordCount">
      <summary>
        <para>Updates the displayed record count after it has been changed in a connected data source.</para>
      </summary>
      <returns>An integer value, specifying the current number of data records in a mail-merge document. This value is assigned to the <see cref="P:DevExpress.Snap.Core.Options.SnapMailMergeVisualOptions.RecordCount"/> property.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.Options.SnapMailMergeVisualOptions.ResetMailMerge">
      <summary>
        <para>Restores the mail-merge options to their default values.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.Options.SnxDocumentSaveOptions">
      <summary>
        <para>Settings which define file formats used when saving and loading the document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.Options.SnxDocumentSaveOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.Options.SnxDocumentSaveOptions"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnxDocumentSaveOptions.CurrentFormat">
      <summary>
        <para>Gets or sets the file format into which the document is saved or from which it is loaded.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> member representing the current file format.</value>
    </member>
    <member name="P:DevExpress.Snap.Core.Options.SnxDocumentSaveOptions.DefaultFormat">
      <summary>
        <para>Gets or sets the default file format used for saving a newly created document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> member, representing the default file format.</value>
    </member>
    <member name="N:DevExpress.Snap.Core.Services">
      <summary>
        <para>Contains interfaces and classes that provide auxiliary functionality as services in SnapControl.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.Services.DefaultSnapProgressIndicationService">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.Services.DefaultSnapProgressIndicationService.#ctor(System.IServiceProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Core.Services.DefaultSnapProgressIndicationService"/> class with the specified service provider.</para>
      </summary>
      <param name="provider">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.Services.DefaultSnapProgressIndicationService.Begin(System.String,System.Int32,System.Int32,System.Int32)">
      <summary>
        <para>For internal use. Use the <see cref="M:DevExpress.Snap.Core.Services.ISnapProgressIndicationService.Begin(System.String,System.Int32,System.Int32,System.Int32)"/> property instead.</para>
      </summary>
      <param name="displayName">A <see cref="T:System.String"/> value.</param>
      <param name="minProgress">An integer value.</param>
      <param name="maxProgress">An integer value.</param>
      <param name="currentProgress">An integer value.</param>
      <returns>An integer value.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.Services.DefaultSnapProgressIndicationService.End(System.Int32)">
      <summary>
        <para>For internal use. Use the <see cref="M:DevExpress.Snap.Core.Services.ISnapProgressIndicationService.End(System.Int32)"/> property instead.</para>
      </summary>
      <param name="token">An integer value.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.Services.DefaultSnapProgressIndicationService.SetProgress(System.Int32,System.Int32)">
      <summary>
        <para>For internal use. Use the <see cref="M:DevExpress.Snap.Core.Services.ISnapProgressIndicationService.SetProgress(System.Int32,System.Int32)"/> property instead.</para>
      </summary>
      <param name="token">An integer value.</param>
      <param name="currentProgress">An integer value.</param>
    </member>
    <member name="T:DevExpress.Snap.Core.Services.ISnapMailMergeProgressIndicationService">
      <summary>
        <para>Provides a progress indicator while executing resource-intensive tasks.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Core.Services.ISnapMailMergeProgressIndicationService.CancellationToken">
      <summary>
        <para>Allows tracing the requests to cancel an operation.</para>
      </summary>
      <value>A <see cref="T:System.Threading.CancellationToken"/> structure.</value>
    </member>
    <member name="M:DevExpress.Snap.Core.Services.ISnapMailMergeProgressIndicationService.Reset">
      <summary>
        <para>Resets the progress indicator to the default value.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Core.Services.ISnapProgressIndicationService">
      <summary>
        <para>If implemented by a class, enables an application to indicate the current progress of long-lasting tasks.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Core.Services.ISnapProgressIndicationService.Begin(System.String,System.Int32,System.Int32,System.Int32)">
      <summary>
        <para>Initializes and displays a progress indicator.</para>
      </summary>
      <param name="displayName">A <see cref="T:System.String"/> value, indicating the action that is currently being performed.</param>
      <param name="minProgress">An integer value, specifying the minimum indicator value.</param>
      <param name="maxProgress">An integer value, specifying the maximum indicator value.</param>
      <param name="currentProgress">An integer value, specifying the current indicator value.</param>
      <returns>An integer value, identifying the task.</returns>
    </member>
    <member name="M:DevExpress.Snap.Core.Services.ISnapProgressIndicationService.End(System.Int32)">
      <summary>
        <para>Finalizes progress indication.</para>
      </summary>
      <param name="token">An integer value, identifying the task.</param>
    </member>
    <member name="M:DevExpress.Snap.Core.Services.ISnapProgressIndicationService.SetProgress(System.Int32,System.Int32)">
      <summary>
        <para>Modifies the indicator value to track progress.</para>
      </summary>
      <param name="token">An integer value, identifying the task.</param>
      <param name="currentProgress">An integer value, specifying the current progress.</param>
    </member>
    <member name="T:DevExpress.Snap.DataSourceInfoChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.SnapControl.DataSourceChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.DataSourceInfoChangedEventArgs.#ctor(DevExpress.Snap.DataSourceOwner)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.DataSourceInfoChangedEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="dataSourceOwner">A <see cref="T:DevExpress.Snap.DataSourceOwner"/> enumeration value. This value is assigned to the <see cref="P:DevExpress.Snap.DataSourceInfoChangedEventArgs.DataSourceOwner"/> property.</param>
    </member>
    <member name="P:DevExpress.Snap.DataSourceInfoChangedEventArgs.DataSourceOwner">
      <summary>
        <para>Returns the owner of a connected data source.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.DataSourceOwner"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.Snap.DataSourceInfoChangedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.SnapControl.DataSourceChanged"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">A <see cref="T:DevExpress.Snap.Core.API.DataSourceChangedEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.DataSourceOwner">
      <summary>
        <para>Lists the possible contractors to which a data source is connected.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.DataSourceOwner.Control">
      <summary>
        <para>The data source belongs to a <see cref="T:DevExpress.Snap.SnapControl"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.DataSourceOwner.Document">
      <summary>
        <para>The data source belongs to a <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.DataSourceRemovedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.Core.API.DataSourceInfoCollection.DataSourceRemoved"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.DataSourceRemovedEventArgs.#ctor(DevExpress.Snap.Core.API.DataSourceInfo,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.DataSourceRemovedEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="dataSourceInfo">A <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> object containing information about the target data source.</param>
      <param name="isDataSourceInternal">true, if the data source is not used outside the scope of the Snap module; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Snap.DataSourceRemovedEventArgs.DataSourceInfo">
      <summary>
        <para>Provides access to information about the target data source.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> object containing information about the target data source.</value>
    </member>
    <member name="P:DevExpress.Snap.DataSourceRemovedEventArgs.IsDataSourceInternal">
      <summary>
        <para>Gets whether the data source is internal (loaded with the document template or added in the UI using the Data Source Wizard) or external (added manually or used in other parts of the application).</para>
      </summary>
      <value>true, if the data source was loaded with the document template or added in the UI using the Data Source Wizard; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Snap.DocumentClosingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.SnapControl.DocumentClosing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.DocumentClosingEventArgs.#ctor(DevExpress.Snap.DocumentDataSources)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.DocumentClosingEventArgs"/> class with the specified data sources.</para>
      </summary>
      <param name="interimDataSources">A <see cref="T:DevExpress.Snap.DocumentDataSources"/> object.</param>
    </member>
    <member name="P:DevExpress.Snap.DocumentClosingEventArgs.Handled">
      <summary>
        <para>Specifies whether or not the <see cref="E:DevExpress.Snap.SnapControl.DocumentClosing"/> event was handled.</para>
      </summary>
      <value>true if the event was handled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Snap.DocumentClosingEventArgs.InterimDataSources">
      <summary>
        <para>Provides access to the interim document data sources that have been created by an end-user during a document editing session.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.DocumentDataSources"/> object.</value>
    </member>
    <member name="T:DevExpress.Snap.DocumentClosingEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.SnapControl.DocumentClosing"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">A <see cref="T:DevExpress.Snap.DocumentClosingEventArgs"/> object, containing data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.DocumentDataSources">
      <summary>
        <para>Stores the interim data sources that have been created by an end-user during a document editing session.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.DocumentDataSources.DataSources">
      <summary>
        <para>Specifies the document data sources.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.DataSourceInfoCollection"/> object, storing the information about the document data sources.</value>
    </member>
    <member name="P:DevExpress.Snap.DocumentDataSources.DefaultDataSourceInfo">
      <summary>
        <para>Specifies the default data source of a document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.Core.API.DataSourceInfo"/> object, storing the information about the default document data source.</value>
    </member>
    <member name="T:DevExpress.Snap.DocumentImportedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.SnapControl.DocumentLoaded"/> and <see cref="E:DevExpress.Snap.SnapControl.EmptyDocumentCreated"/> events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.DocumentImportedEventArgs.#ctor(DevExpress.Snap.DocumentDataSources)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.DocumentImportedEventArgs"/> class with the specified data sources.</para>
      </summary>
      <param name="interimDataSources">A <see cref="T:DevExpress.Snap.DocumentDataSources"/> object. This value is assigned to the <see cref="P:DevExpress.Snap.DocumentImportedEventArgs.InterimDataSources"/> property.</param>
    </member>
    <member name="P:DevExpress.Snap.DocumentImportedEventArgs.Handled">
      <summary>
        <para>Specifies whether or not the corresponding event of the <see cref="T:DevExpress.Snap.SnapControl"/> was handled.</para>
      </summary>
      <value>true if the event was handled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Snap.DocumentImportedEventArgs.InterimDataSources">
      <summary>
        <para>Provides access to the interim document data sources that have been created by an end-user during a document editing session.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Snap.DocumentDataSources"/> object.</value>
    </member>
    <member name="T:DevExpress.Snap.DocumentImportedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.DocumentLoaded"/> and <see cref="E:DevExpress.XtraRichEdit.RichEditControl.EmptyDocumentCreated"/> events of the <see cref="T:DevExpress.Snap.SnapControl"/>.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">A <see cref="T:DevExpress.Snap.DocumentImportedEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="N:DevExpress.Snap.Localization">
      <summary>
        <para>Provides means to localize the elements of a Snap application.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Localization.SnapLocalizer">
      <summary>
        <para>Provides the means to localize the user interface elements of a Snap application.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Localization.SnapLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Localization.SnapLocalizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.Localization.SnapLocalizer.Active">
      <summary>
        <para>Specifies a localizer object providing localization of a Snap application at runtime.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> descendant, used to localize the user interface at runtime.</value>
    </member>
    <member name="M:DevExpress.Snap.Localization.SnapLocalizer.CreateDefaultLocalizer">
      <summary>
        <para>Returns a Localizer object storing resources based on the thread&#39;s language and regional settings (culture).</para>
      </summary>
      <returns>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object, storing resources based on the thread&#39;s language and regional settings (culture).</returns>
    </member>
    <member name="M:DevExpress.Snap.Localization.SnapLocalizer.CreateResXLocalizer">
      <summary>
        <para>For internal use.</para>
      </summary>
      <returns>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object.</returns>
    </member>
    <member name="M:DevExpress.Snap.Localization.SnapLocalizer.GetString(DevExpress.Snap.Localization.SnapStringId)">
      <summary>
        <para>Returns a localized string for the given string identifier.</para>
      </summary>
      <param name="id">A <see cref="T:DevExpress.Snap.Extensions.Localization.SnapExtensionsStringId"/> enumeration value, identifying the string to localize.</param>
      <returns>A <see cref="T:System.String"/> value, corresponding to the specified identifier.</returns>
    </member>
    <member name="T:DevExpress.Snap.Localization.SnapResLocalizer">
      <summary>
        <para>A default localizer to translate a Snap application&#39;s resources.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Localization.SnapResLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.Localization.SnapResLocalizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapResLocalizer.DefaultResourceFile">
      <summary>
        <para>&quot;LocalizationRes&quot;</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Snap.Localization.SnapStringId">
      <summary>
        <para>Contains values corresponding to strings that can be localized for a Snap application.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ArraysLengthsMismatchException">
      <summary>
        <para>&quot;Arrays must have same length&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.CalculatedField_DataMember">
      <summary>
        <para>&quot;Data Member&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.CalculatedField_DataSourceName">
      <summary>
        <para>&quot;Data Source Name&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.CalculatedField_Expression">
      <summary>
        <para>&quot;Expression&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.CalculatedField_FieldType">
      <summary>
        <para>&quot;Field Type&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.CalculatedField_Name">
      <summary>
        <para>&quot;Name&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ChangeEditorRowLimitCommand_Description">
      <summary>
        <para>&quot;Define the maximum number of rows shown in the document lists and groups during your editing session.\r\nThis option allows you to save time when working with large datasources. It does not affect the document&#39;s data in the print preview.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ChangeEditorRowLimitCommand_MenuCaption">
      <summary>
        <para>&quot;Editor Row Limit&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ChangeThemeCommand_Description">
      <summary>
        <para>&quot;Change Theme&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ChangeThemeCommand_MenuCaption">
      <summary>
        <para>&quot;Change Theme&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ConvertToParagraphsCommand_Description">
      <summary>
        <para>&quot;Click to convert the tables to paragraphs.\r\nEvery field will then be presented in a separate paragraph, and you can highlight fields by using the Highlight option in the View tab of this toolbar.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ConvertToParagraphsCommand_MenuCaption">
      <summary>
        <para>&quot;Convert to Paragraphs&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.CustomAssemblyWarning">
      <summary>
        <para>You are about to load a third-party library: &#39;{0}&#39;.\r\n\r\nBefore you proceed, please consider resulting security risks and make sure that this library is supplied from a trusted source.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.CustomSortForm_Text">
      <summary>
        <para>&quot;Sort&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.DataExplorer_Text">
      <summary>
        <para>&quot;Data Explorer&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.DeleteListCommand_Description">
      <summary>
        <para>&quot;Delete the list.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.DeleteListCommand_MenuCaption">
      <summary>
        <para>&quot;Delete List&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.EditorRowLimitShowAll">
      <summary>
        <para>&quot;(Show All)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ExportDocumentCommand_Description">
      <summary>
        <para>&quot;Export Document&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ExportDocumentCommand_MenuCaption">
      <summary>
        <para>&quot;Export Document&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.FileFilterDescription_SnapFiles">
      <summary>
        <para>&quot;Snap Document&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.FileFilterDescription_SnapThemeFiles">
      <summary>
        <para>&quot;Snap Theme&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.FilterFieldCommand_Description">
      <summary>
        <para>&quot;Select which rows from the field to include in the list.\r\nTo define more complex filtering criteria, use the Filter option in the List tab.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.FilterFieldCommand_MenuCaption">
      <summary>
        <para>&quot;Filter&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.FilterListCommand_Description">
      <summary>
        <para>&quot;Click to define a filter criterion for the list.\r\nTo only select which data records to display for a specific field, use the Filter option in the Field tab.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.FilterListCommand_MenuCaption">
      <summary>
        <para>&quot;Filter&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.FinishAndMergeCommand_Description">
      <summary>
        <para>&quot;Export, preview or print the document.\r\n\r\nThe document can include all available data records, or only part of them (i.e., the specified data row range, or only the single row that is currently displayed). Data records can be split off by using a separator of a selected type.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.FinishAndMergeCommand_MenuCaption">
      <summary>
        <para>&quot;Finish and Merge&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.GroupByFieldCommand_Description">
      <summary>
        <para>&quot;Click to enable/disable grouping by field.\r\nThis will break the list into groups with the selected field being used as a grouping criterion.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.GroupByFieldCommand_MenuCaption">
      <summary>
        <para>&quot;Group By Field&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.GroupField_FieldName">
      <summary>
        <para>&quot;Field Name&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.GroupField_SortOrder">
      <summary>
        <para>&quot;Sort Order&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.GroupFieldsCollectionCommand_Description">
      <summary>
        <para>&quot;Click to manage the group&#39;s criteria.\r\nEvery group can have multiple criteria. In the document, a separate group section is created for every grouping criterion, with its own header and footer.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.GroupFieldsCollectionCommand_MenuCaption">
      <summary>
        <para>&quot;Group Fields&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.GroupFooterCommand_Description">
      <summary>
        <para>&quot;Click to add or remove a group footer.\r\nThe footer displays the group summary function result. By default, the Count summary is being calculated, which you can change using the Summary option in the Field tab.\r\nHiding both the header and footer of a group will disable grouping altogether.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.GroupFooterCommand_MenuCaption">
      <summary>
        <para>&quot;Footer&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.GroupHeaderCommand_Description">
      <summary>
        <para>&quot;Click to add or remove a group header.\r\nThe group header is created after a grouping has been applied by a field. The header displays the grouping criterion field.\r\nHiding both the header and footer of a group will disable grouping altogether.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.GroupHeaderCommand_MenuCaption">
      <summary>
        <para>&quot;Header&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.HighlightActiveElementCommand_Description">
      <summary>
        <para>&quot;Click to show the information about the current element&#39;s type and bounds&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.HighlightActiveElementCommand_MenuCaption">
      <summary>
        <para>&quot;Highlight&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.HotZonePainter_DropArguments">
      <summary>
        <para>&quot;Drop arguments&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.HotZonePainter_DropValues">
      <summary>
        <para>&quot;Drop values&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.HotZonePainter_SecondLine">
      <summary>
        <para>&quot;here&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ImportTemplate_Text">
      <summary>
        <para>&quot;Import Template&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertBarCodeCommand_Description">
      <summary>
        <para>&quot;Insert Bar Code&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertBarCodeCommand_MenuCaption">
      <summary>
        <para>&quot;Bar Code&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertChartCommand_Description">
      <summary>
        <para>&quot;Insert Chart&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertChartCommand_MenuCaption">
      <summary>
        <para>&quot;Chart&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertCheckBoxCommand_Description">
      <summary>
        <para>&quot;Insert Check Box&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertCheckBoxCommand_MenuCaption">
      <summary>
        <para>&quot;Check Box&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertDataRowSeparatorCommand_Description">
      <summary>
        <para>&quot;Split the document into sections by inserting an appropriate separator in the current carriage position.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertDataRowSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;Separator&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertEmptyParagraphDataRowSeparatorCommand_Description">
      <summary>
        <para>&quot;Empty Paragraph&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertEmptyParagraphDataRowSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;Empty Paragraph&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertEmptyParagraphGroupSeparatorCommand_Description">
      <summary>
        <para>&quot;Empty Paragraph&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertEmptyParagraphGroupSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;Empty Paragraph&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertEmptyRowDataRowSeparatorCommand_Description">
      <summary>
        <para>&quot;Empty Row&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertEmptyRowDataRowSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;Empty Row&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertEmptyRowGroupSeparatorCommand_Description">
      <summary>
        <para>&quot;Empty Row&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertEmptyRowGroupSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;Empty Row&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertGroupFooterCommand_Description">
      <summary>
        <para>&quot;Add Footer&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertGroupFooterCommand_MenuCaption">
      <summary>
        <para>&quot;Add Footer&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertGroupHeaderCommand_Description">
      <summary>
        <para>&quot;Add Header&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertGroupHeaderCommand_MenuCaption">
      <summary>
        <para>&quot;Add Header&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertGroupSeparatorCommand_Description">
      <summary>
        <para>&quot;Choose a separator to delimit groups in the document.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertGroupSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;Separator&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertIndexCommand_Description">
      <summary>
        <para>&quot;Enumerates records of a data source column within the document.\r\n\r\nTo specify the format string of the currently selected index and define its behavior when within groups, click the Properties button in the Field tab.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertIndexCommand_MenuCaption">
      <summary>
        <para>&quot;Row Index&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertListFooterCommand_Description">
      <summary>
        <para>&quot;Add Footer&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertListFooterCommand_MenuCaption">
      <summary>
        <para>&quot;Add Footer&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertListHeaderCommand_Description">
      <summary>
        <para>&quot;Add Header&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertListHeaderCommand_MenuCaption">
      <summary>
        <para>&quot;Add Header&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertNoneDataRowSeparatorCommand_Description">
      <summary>
        <para>&quot;None&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertNoneDataRowSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;None&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertNoneGroupSeparatorCommand_Description">
      <summary>
        <para>&quot;None&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertNoneGroupSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;None&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertPageBreakDataRowSeparatorCommand_Description">
      <summary>
        <para>&quot;PageBreak&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertPageBreakDataRowSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;None&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertPageBreakGroupSeparatorCommand_Description">
      <summary>
        <para>&quot;Page Break</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertPageBreakGroupSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;Page Break&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSectionBreakEvenPageDataRowSeparatorCommand_Description">
      <summary>
        <para>&quot;Section (Even Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSectionBreakEvenPageDataRowSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;Section (Even Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSectionBreakEvenPageGroupSeparatorCommand_Description">
      <summary>
        <para>&quot;Section (Even Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSectionBreakEvenPageGroupSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;Section (Even Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSectionBreakNextPageDataRowSeparatorCommand_Description">
      <summary>
        <para>&quot;Section (Next Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSectionBreakNextPageDataRowSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;Section (Next Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSectionBreakNextPageGroupSeparatorCommand_Description">
      <summary>
        <para>&quot;Section (Next Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSectionBreakNextPageGroupSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;Section (Next Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSectionBreakOddPageDataRowSeparatorCommand_Description">
      <summary>
        <para>&quot;Section (Odd Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSectionBreakOddPageDataRowSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;Section (Odd Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSectionBreakOddPageGroupSeparatorCommand_Description">
      <summary>
        <para>&quot;Section (Odd Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSectionBreakOddPageGroupSeparatorCommand_MenuCaption">
      <summary>
        <para>&quot;Section (Odd Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSparklineCommand_Description">
      <summary>
        <para>&quot;Insert Sparkline&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.InsertSparklineCommand_MenuCaption">
      <summary>
        <para>&quot;Sparkline&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ListFooterCommand_Description">
      <summary>
        <para>&quot;Click to add or remove a list footer.\r\n It is blank by default. You can use it to present list summaries and other information.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ListFooterCommand_MenuCaption">
      <summary>
        <para>&quot;Footer&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ListHeaderCommand_Description">
      <summary>
        <para>&quot;Click to add or remove a list header.\r\nIt contains columns captions by default.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ListHeaderCommand_MenuCaption">
      <summary>
        <para>&quot;Header&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MailMergeCurrentRecord_Description">
      <summary>
        <para>&quot;Mail Merge Data Source record visible&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MailMergeCurrentRecord_MenuCaption">
      <summary>
        <para>&quot;Current Record&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MailMergeDataSource_Description">
      <summary>
        <para>&quot;Select Mail Merge Data Source&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MailMergeDataSource_MenuCaption">
      <summary>
        <para>&quot;Data Source&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MailMergeFilters_Description">
      <summary>
        <para>&quot;Filter Mail Merge data&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MailMergeFilters_MenuCaption">
      <summary>
        <para>&quot;Filter&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MailMergeSorting_Description">
      <summary>
        <para>&quot;Sort Mail Merge data&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MailMergeSorting_MenuCaption">
      <summary>
        <para>&quot;Sorting&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MenuCmd_DeleteTableCellStyle">
      <summary>
        <para>&quot;Delete Table Cell Style&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MenuCmd_ModifyTableCellStyle">
      <summary>
        <para>&quot;Modify Table Cell Style&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MenuCmd_NewTableCellStyle">
      <summary>
        <para>&quot;New Table Cell Style&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MessageBoxWarningTitle">
      <summary>
        <para>Warning</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MoveDown_Description">
      <summary>
        <para>&quot;Move Down&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MoveDown_MenuCaption">
      <summary>
        <para>&quot;Move Down&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MoveUp_Description">
      <summary>
        <para>&quot;Move Up&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.MoveUp_MenuCaption">
      <summary>
        <para>&quot;Move Up&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_AssignChartDataSourceError">
      <summary>
        <para>&quot;Cannot bind a chart to the data source. The following exception is thrown:\r\n{0}.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_BindChartSeriesError">
      <summary>
        <para>&quot;A chart series cannot use data from different data sources.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_CannotChangeDataSourceName">
      <summary>
        <para>&quot;Cannot change the data source name.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_CannotDeleteDefaultTheme">
      <summary>
        <para>&quot;Cannot delete the default theme.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_CannotPerformAsynchronousOperation">
      <summary>
        <para>&quot;Cannot perform the same asynchronous operation in multiple simultaneous threads.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_CollectionAlreadyContainsTheme">
      <summary>
        <para>&quot;The collection already contains a theme named {0}.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_DataSourceNameExists">
      <summary>
        <para>&quot;A datasource with the same name already exists&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_FieldAlreadyDefinedAsGroupingCriterion">
      <summary>
        <para>&quot;The {0} field has already been defined as a grouping criterion.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_FieldDefinedAsSortingCriterionMoreThanOnce">
      <summary>
        <para>&quot;The {0} field can be defined as a sorting criterion only once.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_FillDataError">
      <summary>
        <para>&quot;Error when trying to populate the datasource. The following exception was thrown:\r\n{0}.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_FilterCriteriaInvalidExpression">
      <summary>
        <para>&quot;The specified expression contains invalid symbols (line {0}, character {1}).&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_FilterCriteriaInvalidExpressionEx">
      <summary>
        <para>&quot;The specified expression is invalid.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_IncompatibleArgumentDataMember">
      <summary>
        <para>&quot;The type of the {0} argument data member isn&#39;t compatible with the {1} scale.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_IncompatibleValueDataMember">
      <summary>
        <para>&quot;The type of the {0} value data member isn&#39;t compatible with the {1} scale.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_InvalidCalculatedFieldName">
      <summary>
        <para>&quot;The specified calculated field name is not valid: {0}.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_InvalidDataSourceName">
      <summary>
        <para>&quot;The specified data source name is not valid: {0}.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_InvalidEditorRowLimit">
      <summary>
        <para>&quot;Invalid value&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_InvalidMailMergeCurrentRecord">
      <summary>
        <para>&quot;Invalid value&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_MaintainDataSourceConnections">
      <summary>
        <para>&quot;Maintain data source connections that exist in the currently open document?&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_RemoveSelectedDataSource">
      <summary>
        <para>&quot;Remove the selected data source?&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_StopMailMerge">
      <summary>
        <para>&quot;Interrupt the report execution?&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_ThemeIsNotLoaded">
      <summary>
        <para>&quot;The theme is not loaded. Cannot use the {0} theme.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Msg_UnsupportedDocumentVersion">
      <summary>
        <para>&quot;Unsupported Snap document version&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.NewDataSourceCommand_Description">
      <summary>
        <para>&quot;Add new Data Source&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.NewDataSourceCommand_MenuCaption">
      <summary>
        <para>&quot;Add new Data Source&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ParametersErrorInvalidCharacters">
      <summary>
        <para>&quot;Cannot create a parameter with invalid name.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ParametersErrorNoName">
      <summary>
        <para>&quot;Cannot create a parameter without specifying its name.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ParameterService_AddParameter">
      <summary>
        <para>&quot;New Parameter&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ParameterService_CreateParameter">
      <summary>
        <para>&quot;Query Parameter&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ProgressIndicationForm_Text">
      <summary>
        <para>&quot;Performing mail merge...&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.PropertiesCommand_Description">
      <summary>
        <para>&quot;Click to browse and customize the field&#39;s properties.\r\nThis will invoke a menu that lists the main properties specific to the selected element&#39;s type.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.PropertiesCommand_MenuCaption">
      <summary>
        <para>&quot;Properties&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RecordSeparator_None">
      <summary>
        <para>&quot;None&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RecordSeparator_PageBreak">
      <summary>
        <para>&quot;Page Break&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RecordSeparator_Paragraph">
      <summary>
        <para>&quot;Paragraph&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RecordSeparator_SectionEvenPage">
      <summary>
        <para>&quot;Section (Even Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RecordSeparator_SectionNextPage">
      <summary>
        <para>&quot;Section (Next Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RecordSeparator_SectionOddPage">
      <summary>
        <para>&quot;Section (Odd Page)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RemoveDataSourceCommand_Description">
      <summary>
        <para>&quot;Remove the specified data source.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RemoveDataSourceCommand_MenuCaption">
      <summary>
        <para>&quot;Remove Data Source&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RemoveGroupFooterCommand_Description">
      <summary>
        <para>&quot;Remove Footer&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RemoveGroupFooterCommand_MenuCaption">
      <summary>
        <para>&quot;Remove Footer&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RemoveGroupHeaderCommand_Description">
      <summary>
        <para>&quot;Remove Header&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RemoveGroupHeaderCommand_MenuCaption">
      <summary>
        <para>&quot;Remove Header&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RemoveListFooterCommand_Description">
      <summary>
        <para>&quot;Remove Footer&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RemoveListFooterCommand_MenuCaption">
      <summary>
        <para>&quot;Remove Footer&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RemoveListHeaderCommand_Description">
      <summary>
        <para>&quot;Remove Header&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RemoveListHeaderCommand_MenuCaption">
      <summary>
        <para>&quot;Remove Header&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ReorderReportStructureForm_Text">
      <summary>
        <para>&quot;Groups Order Editor&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ReportExplorer_GroupNode">
      <summary>
        <para>&quot;Group&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ReportExplorer_ListNode">
      <summary>
        <para>&quot;List&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RunChartDesignerCommand_Description">
      <summary>
        <para>&quot;Run designer...&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.RunChartDesignerCommand_MenuCaption">
      <summary>
        <para>&quot;Run designer...&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ShowGroupSortingsCheckBox_Text">
      <summary>
        <para>&quot;Show Group Sortings&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ShowReportStructureEditorForm_Description">
      <summary>
        <para>&quot;Arrange Groups&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ShowReportStructureEditorForm_MenuCaption">
      <summary>
        <para>&quot;Arrange Groups&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SnapEntityAddLock">
      <summary>
        <para>&quot;Unable to add new {0} while updating another field&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SnapEntityRemoveLock">
      <summary>
        <para>&quot;Unable to remove field while updating another one&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SnapListLockException">
      <summary>
        <para>&quot;Modify attempt before BeginUpdate() call&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SnapListPropertyOutOfDataException">
      <summary>
        <para>&quot;Value is out of data, list property must be reread after EndUpdate() call&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SnapListSecondBeginUpdateException">
      <summary>
        <para>&quot;Previous update should be finished before start new one&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SnapPrintCommand_Description">
      <summary>
        <para>&quot;Select a printer, number of copies, and other printing options before printing.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SnapPrintCommand_MenuCaption">
      <summary>
        <para>&quot;&amp;Print...&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SnapPrintPreviewCommand_Description">
      <summary>
        <para>&quot;Preview pages before printing.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SnapPrintPreviewCommand_MenuCaption">
      <summary>
        <para>&quot;Print Pre&amp;view...&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SnapQuickPrintCommand_Description">
      <summary>
        <para>&quot;Send the document directly to the default printer without making changes.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SnapQuickPrintCommand_MenuCaption">
      <summary>
        <para>&quot;&amp;Quick Print&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SortFieldAscendingCommand_Description">
      <summary>
        <para>&quot;Click to sort (in ascending order) by the field.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SortFieldAscendingCommand_MenuCaption">
      <summary>
        <para>&quot;Sort Ascending&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SortFieldDescendingCommand_Description">
      <summary>
        <para>&quot;Click to sort (in descending order) by the field.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SortFieldDescendingCommand_MenuCaption">
      <summary>
        <para>&quot;Sort Descending&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.Sorting_MenuCaption">
      <summary>
        <para>&quot;Sort&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SortingForm_OrderColumnCaption">
      <summary>
        <para>&quot;Order&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SortingForm_SortByColumnCaption">
      <summary>
        <para>&quot;Sort by&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SummaryAverageCommand_Description">
      <summary>
        <para>&quot;Average&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SummaryAverageCommand_MenuCaption">
      <summary>
        <para>&quot;Average&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SummaryCommand_Description">
      <summary>
        <para>&quot;Choose the summary function to calculate for the field.\r\nThe summary function result will be shown at the list footer which is made visible if it previously was not.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SummaryCommand_MenuCaption">
      <summary>
        <para>&quot;Summary&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SummaryCountCommand_Description">
      <summary>
        <para>&quot;Count&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SummaryCountCommand_MenuCaption">
      <summary>
        <para>&quot;Count&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SummaryMaxCommand_Description">
      <summary>
        <para>&quot;Max&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SummaryMaxCommand_MenuCaption">
      <summary>
        <para>&quot;Max&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SummaryMinCommand_Description">
      <summary>
        <para>&quot;Min&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SummaryMinCommand_MenuCaption">
      <summary>
        <para>&quot;Min&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SummarySumCommand_Description">
      <summary>
        <para>&quot;Sum&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SummarySumCommand_MenuCaption">
      <summary>
        <para>&quot;Sum&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.SummaryTooltip">
      <summary>
        <para>&quot;Calculates the {0} summary function by the {1} field for the {2}&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.TemplateDecoratorType_DataRow">
      <summary>
        <para>&quot;DataRow&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.TemplateDecoratorType_GroupFooter">
      <summary>
        <para>&quot;GroupFooter&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.TemplateDecoratorType_GroupHeader">
      <summary>
        <para>&quot;GroupHeader&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.TemplateDecoratorType_GroupSeparator">
      <summary>
        <para>&quot;GroupSeparator&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.TemplateDecoratorType_ListFooter">
      <summary>
        <para>&quot;ListFooter&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.TemplateDecoratorType_ListHeader">
      <summary>
        <para>&quot;ListHeader&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.TemplateDecoratorType_Separator">
      <summary>
        <para>&quot;Separator&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.TemplateDecoratorType_WholeGroup">
      <summary>
        <para>&quot;WholeGroup&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.TemplateDecoratorType_WholeList">
      <summary>
        <para>&quot;WholeList&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ThemeName_Casual">
      <summary>
        <para>&quot;Casual&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ThemeName_ContrastCyan">
      <summary>
        <para>&quot;Contrast Cyan&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ThemeName_ContrastOrange">
      <summary>
        <para>&quot;Contrast Orange&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ThemeName_ContrastRed">
      <summary>
        <para>&quot;Contrast Red&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ThemeName_ContrastSalmon">
      <summary>
        <para>&quot;Contrast Salmon&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ThemeName_ContrastYellow">
      <summary>
        <para>&quot;Contrast Yellow&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ThemeName_DodgerBlue">
      <summary>
        <para>&quot;Dodger Blue&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ThemeName_FormalBlue">
      <summary>
        <para>&quot;Formal Blue&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ThemeName_MildBlue">
      <summary>
        <para>&quot;Mild Blue&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ThemeName_MildBrown">
      <summary>
        <para>&quot;Mild Brown&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ThemeName_MildCyan">
      <summary>
        <para>&quot;Mild Cyan&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ThemeName_MildViolet">
      <summary>
        <para>&quot;Mild Violet&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ThemeName_SoftLilac">
      <summary>
        <para>&quot;Soft Lilac&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ToggleFieldHighlighting_Description">
      <summary>
        <para>&quot;Click to highlight all data fields in the document&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.ToggleFieldHighlighting_MenuCaption">
      <summary>
        <para>&quot;Highlight Fields&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Snap.Localization.SnapStringId.WizardPageDataSourceName">
      <summary>
        <para>&quot;Enter the data source name&quot;</para>
      </summary>
    </member>
    <member name="N:DevExpress.Snap.Services">
      <summary>
        <para>Contains classes and interfaces that define services implemented in the <see cref="T:DevExpress.Snap.SnapControl"/>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Snap.Services.DataAccessService">
      <summary>
        <para>Provides information on the data source bound to the SnapControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Services.DataAccessService.GetColumnType(System.Object,System.String,System.String)">
      <summary>
        <para>Obtains the field type of the specified field in the data source bound to the SnapControl.</para>
      </summary>
      <param name="dataSource">An object that is the data source bound to the SnapControl.</param>
      <param name="dataMember">A string that specifies the data member in the data source. Null (Nothing in Visual Basic) if the data source does not include data members.</param>
      <param name="columnName">A string that is the field name.</param>
      <returns>A <see cref="T:System.Type"/> object that is the object type declaration.</returns>
    </member>
    <member name="T:DevExpress.Snap.Services.IObjectDataSourceValidationService">
      <summary>
        <para>Allows custom validation of the <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/> data sources before using them in the document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.Services.IObjectDataSourceValidationService.Validate(System.Collections.Generic.IEnumerable{DevExpress.DataAccess.ObjectBinding.ObjectDataSource})">
      <summary>
        <para>Validates the ObjectDataSource data objects before data retrieval.</para>
      </summary>
      <param name="dataSources">An <see cref="T:System.Collections.Generic.IEnumerable`1"/>&lt;<see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>,&gt; collection to validate.</param>
    </member>
    <member name="T:DevExpress.Snap.SnapControlCompatibility">
      <summary>
        <para>Contains static properties which can be set to ensure compatibility with previous versions.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.SnapControlCompatibility.DefaultReportThemeName">
      <summary>
        <para>Gets or sets the default report theme for the <see cref="T:DevExpress.Snap.SnapControl"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the theme name. The default is Casual.</value>
    </member>
    <member name="P:DevExpress.Snap.SnapControlCompatibility.DisableParameterNameValidation">
      <summary>
        <para>Specifies whether to disable parameter name validation when loading .snx templates created in versions earlier than 14.2.8 and containing invalid symbols.</para>
      </summary>
      <value>True, to disable parameter names validation; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Snap.SnapMailMergeFinishedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.SnapControl.SnapMailMergeFinished"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.SnapMailMergeFinishedEventArgs.Document">
      <summary>
        <para>Returns a mail merge document that has been rendered.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> interface.</value>
    </member>
    <member name="T:DevExpress.Snap.SnapMailMergeFinishedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.SnapControl.SnapMailMergeFinished"/> event.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">A <see cref="T:DevExpress.Snap.SnapMailMergeFinishedEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.SnapMailMergeRecordFinishedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.SnapControl.SnapMailMergeRecordFinished"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.SnapMailMergeRecordFinishedEventArgs.Document">
      <summary>
        <para>Returns the document that is being merged.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> interface.</value>
    </member>
    <member name="P:DevExpress.Snap.SnapMailMergeRecordFinishedEventArgs.RecordDocument">
      <summary>
        <para>Provides access to the document created for a single record after the fields are processed.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> interface.</value>
    </member>
    <member name="P:DevExpress.Snap.SnapMailMergeRecordFinishedEventArgs.RecordIndex">
      <summary>
        <para>Returns the data record index.</para>
      </summary>
      <value>An integer value, specifying the data record index.</value>
    </member>
    <member name="T:DevExpress.Snap.SnapMailMergeRecordFinishedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.SnapControl.SnapMailMergeRecordFinished"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> that is the event source.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.SnapMailMergeRecordFinishedEventArgs"/> object, containing the event arguments.</param>
    </member>
    <member name="T:DevExpress.Snap.SnapMailMergeRecordStartedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.SnapControl.SnapMailMergeRecordStarted"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.SnapMailMergeRecordStartedEventArgs.Document">
      <summary>
        <para>Returns the document that is being merged.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> interface.</value>
    </member>
    <member name="P:DevExpress.Snap.SnapMailMergeRecordStartedEventArgs.RecordDocument">
      <summary>
        <para>Provides access to the document created for a single record before the fields are processed.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> interface.</value>
    </member>
    <member name="P:DevExpress.Snap.SnapMailMergeRecordStartedEventArgs.RecordIndex">
      <summary>
        <para>Returns the data record index.</para>
      </summary>
      <value>An integer value, specifying the data record index.</value>
    </member>
    <member name="T:DevExpress.Snap.SnapMailMergeRecordStartedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.SnapControl.SnapMailMergeRecordStarted"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> that is the event source.</param>
      <param name="e">An <see cref="T:DevExpress.Snap.SnapMailMergeRecordStartedEventArgs"/> object, containing the event arguments.</param>
    </member>
    <member name="T:DevExpress.Snap.SnapMailMergeStartedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.SnapControl.SnapMailMergeStarted"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Snap.SnapMailMergeStartedEventArgs.Document">
      <summary>
        <para>Returns a mail merge document that has started rendering.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Snap.Core.API.SnapDocument"/> interface.</value>
    </member>
    <member name="P:DevExpress.Snap.SnapMailMergeStartedEventArgs.OperationDescription">
      <summary>
        <para>Provides access to the operation description.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.Snap.SnapMailMergeStartedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Snap.SnapControl.SnapMailMergeStarted"/> event.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">A <see cref="T:DevExpress.Snap.SnapMailMergeStartedEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.Snap.ValidateSqlEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Snap.SnapControl.ValidateCustomSql"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Snap.ValidateSqlEventArgs.#ctor(DevExpress.DataAccess.ConnectionParameters.DataConnectionParametersBase,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.ValidateSqlEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="connectionParameters">A <see cref="T:DevExpress.DataAccess.ConnectionParameters.DataConnectionParametersBase"/> object containing parameters used to establish a connection to a data source.</param>
      <param name="sql">A <see cref="T:System.String"/> value that is the custom SQL query being validated.</param>
    </member>
    <member name="M:DevExpress.Snap.ValidateSqlEventArgs.#ctor(DevExpress.DataAccess.Sql.CustomSqlQuery)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Snap.ValidateSqlEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="customSqlQuery">A <see cref="T:DevExpress.DataAccess.Sql.CustomSqlQuery"/> object that specifies the custom SQL query.</param>
    </member>
    <member name="P:DevExpress.Snap.ValidateSqlEventArgs.ConnectionParameters">
      <summary>
        <para>Gets parameters used to establish a connection to the data source containing a custom SQL query.</para>
      </summary>
      <value>A <see cref="T:DevExpress.DataAccess.ConnectionParameters.DataConnectionParametersBase"/> descendant containing settings used to establish a data connection.</value>
    </member>
    <member name="P:DevExpress.Snap.ValidateSqlEventArgs.Message">
      <summary>
        <para>Gets or sets the exception message displayed if validation fails.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the text of the message displayed if validation fails.</value>
    </member>
    <member name="P:DevExpress.Snap.ValidateSqlEventArgs.Sql">
      <summary>
        <para>Gets the text of a custom SQL query being validated.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the text of a custom SQL query being validated.</value>
    </member>
    <member name="P:DevExpress.Snap.ValidateSqlEventArgs.Valid">
      <summary>
        <para>Gets or sets whether or not the current SQL query is valid.</para>
      </summary>
      <value>true, if the current SQL query is valid; otherwise, false.</value>
    </member>
  </members>
</doc>