<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.Pdf.v20.2.Core</name>
  </assembly>
  <members>
    <member name="N:DevExpress.Office.DigitalSignatures">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Office.DigitalSignatures.CertificateStoreProvider">
      <summary>
        <para>Provides certificates used to build a certificate chain and register the signature in the Document Security Store (DSS).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.CertificateStoreProvider.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.DigitalSignatures.CertificateStoreProvider"/> class with specified settings.</para>
      </summary>
      <param name="collection">A collection of X.509 certificates.</param>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.CertificateStoreProvider.#ctor(System.Security.Cryptography.X509Certificates.X509Store,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.DigitalSignatures.CertificateStoreProvider"/> class with specified settings.</para>
      </summary>
      <param name="store">An X.509 store that contains the certificates.</param>
      <param name="closeStore">true to transfer the store ownership to the CertificateStoreProvider instance; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.CertificateStoreProvider.Dispose">
      <summary>
        <para>Closes the underlying store.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.CertificateStoreProvider.GetCertificate(System.Byte[])">
      <summary>
        <para>Gets the certificate by its subject&#39;s distinguished name.</para>
      </summary>
      <param name="subjectDN">The BER-encoded DistinguishedName ASN.1 object (rfc5280) that represents the subject&#39;s distinguished name.</param>
      <returns>The DER- or BER-encoded Certificate ASN.1 object (rfc5280)</returns>
    </member>
    <member name="T:DevExpress.Office.DigitalSignatures.CrlClient">
      <summary>
        <para>Allows you to send a CLR request for a certificate.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.CrlClient.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.DigitalSignatures.CrlClient"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.CrlClient.#ctor(System.Collections.Generic.IList{System.Uri})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.DigitalSignatures.CrlClient"/> class with specified settings.</para>
      </summary>
      <param name="uriList">A list of OCSP server Uri&#39;s.</param>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.CrlClient.#ctor(System.Collections.Generic.IList{System.Uri},System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.DigitalSignatures.CrlClient"/> class with specified settings.</para>
      </summary>
      <param name="uriList">A list of OCSP server Uri&#39;s.</param>
      <param name="throwOnFail">true to throw a WebException when the response failed; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.CrlClient.GetEncoded(System.Collections.Generic.IList{System.Byte[]})">
      <summary>
        <para>Returns a list of CertificateList ASN.1 objects.</para>
      </summary>
      <param name="chain">A list of DER-encoded x509 certificates to check.</param>
      <returns>A collection of DER- or BER-encoded CertificateList ASN.1 objects.</returns>
    </member>
    <member name="T:DevExpress.Office.DigitalSignatures.DigestCalculator">
      <summary>
        <para>Allows you to calculate a digest from a stream. Supports hash algorithms listed in <see cref="T:DevExpress.Office.DigitalSignatures.HashAlgorithmType"/> enumeration.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.DigestCalculator.#ctor(DevExpress.Office.DigitalSignatures.HashAlgorithmType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.DigitalSignatures.DigestCalculator"/> class with specified settings.</para>
      </summary>
      <param name="algorithm">The hashing algorithm.</param>
    </member>
    <member name="P:DevExpress.Office.DigitalSignatures.DigestCalculator.AlgorithmOid">
      <summary>
        <para>Retrieves the hash algorithm&#39;s object identifier.</para>
      </summary>
      <value>An algorithm OID.</value>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.DigestCalculator.ComputeDigest(System.IO.Stream)">
      <summary>
        <para>Calculates the digest value for specified data.</para>
      </summary>
      <param name="stream">The stream that contains data used to calculate the digest value.</param>
      <returns>The digest value.</returns>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.DigestCalculator.GetDigestSize">
      <summary>
        <para>Returns the digest value&#39;s size.</para>
      </summary>
      <returns>The digest value&#39;s size (in bytes).</returns>
    </member>
    <member name="T:DevExpress.Office.DigitalSignatures.HashAlgorithmType">
      <summary>
        <para>Lists values used to specify the PKCS#7 signature&#39;s secure hash algorithm (SHA) type.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Office.DigitalSignatures.HashAlgorithmType.SHA1">
      <summary>
        <para>SHA1 hashing algorithm. This type can affect the signature&#39;s integrity, authenticity, and legal validity.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Office.DigitalSignatures.HashAlgorithmType.SHA256">
      <summary>
        <para>SHA256 hashing algorithm.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Office.DigitalSignatures.HashAlgorithmType.SHA384">
      <summary>
        <para>SHA384 hashing algorithm.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Office.DigitalSignatures.HashAlgorithmType.SHA512">
      <summary>
        <para>SHA512 hashing algorithm.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Office.DigitalSignatures.ICertificateStoreProvider">
      <summary>
        <para>Provides certificates used to build a certificate chain and register the signature in the Document Security Store (DSS).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.ICertificateStoreProvider.GetCertificate(System.Byte[])">
      <summary>
        <para>Gets the certificate by its subject&#39;s distinguished name.</para>
      </summary>
      <param name="subjectDN">The BER-encoded DistinguishedName ASN.1 object (rfc5280) that represents the subject&#39;s distinguished name.</param>
      <returns>The DER- or BER-encoded Certificate ASN.1 object (rfc5280).</returns>
    </member>
    <member name="T:DevExpress.Office.DigitalSignatures.ICrlClient">
      <summary>
        <para>Allows you to send a CLR request for specific a certificate.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.ICrlClient.GetEncoded(System.Collections.Generic.IList{System.Byte[]})">
      <summary>
        <para></para>
      </summary>
      <param name="chain"></param>
      <returns></returns>
    </member>
    <member name="T:DevExpress.Office.DigitalSignatures.IDigestCalculator">
      <summary>
        <para>Allows you to calculate a digest from a stream.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Office.DigitalSignatures.IDigestCalculator.AlgorithmOid">
      <summary>
        <para>Retrieves the hash algorithm&#39;s object identifier.</para>
      </summary>
      <value>An algorithm OID.</value>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.IDigestCalculator.ComputeDigest(System.IO.Stream)">
      <summary>
        <para>Calculates the digest value for the specified data.</para>
      </summary>
      <param name="stream">The stream that contains data used to calculate the digest value.</param>
      <returns>The digest value.</returns>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.IDigestCalculator.GetDigestSize">
      <summary>
        <para>Returns the digest value&#39;s size.</para>
      </summary>
      <returns>The digest value&#39;s size (in bytes).</returns>
    </member>
    <member name="T:DevExpress.Office.DigitalSignatures.IOcspClient">
      <summary>
        <para>Allows you to send an Online Certificate Status Protocol (OCSP) request for a certificate.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.IOcspClient.GetEncoded(System.Byte[],System.Byte[])">
      <summary>
        <para>Returns a DER- or BER-encoded OCSPResponse ASN.1 object.</para>
      </summary>
      <param name="checkCertificateBytes">A DER-encoded x509 certificate to check.</param>
      <param name="issuerCertificateBytes">A DER-encoded x509 issuers certificate.</param>
      <returns>A DER- or BER-encoded OCSPResponse ASN.1 object or null.</returns>
    </member>
    <member name="T:DevExpress.Office.DigitalSignatures.OcspClient">
      <summary>
        <para>Allows you to send an Online Certificate Status Protocol (OCSP) request for a certificate.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.OcspClient.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.DigitalSignatures.OcspClient"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.OcspClient.#ctor(System.Uri)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.DigitalSignatures.OcspClient"/> class with specified settings.</para>
      </summary>
      <param name="uri">An OCSP server URI.</param>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.OcspClient.#ctor(System.Uri,DevExpress.Office.DigitalSignatures.HashAlgorithmType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.DigitalSignatures.OcspClient"/> class with specified settings.</para>
      </summary>
      <param name="uri">An OCSP server URI.</param>
      <param name="hashAlgorithm">The hashing algorithm used to hash the issuer name and issuer public key. Default value is <see cref="F:DevExpress.Office.DigitalSignatures.HashAlgorithmType.SHA1"/>.</param>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.OcspClient.GetEncoded(System.Byte[],System.Byte[])">
      <summary>
        <para>Returns a DER- or BER-encoded OCSPResponse ASN.1 object.</para>
      </summary>
      <param name="checkCertificateBytes">A DER-encoded x509 certificate to check.</param>
      <param name="issuerCertificateBytes">A DER-encoded x509 issuers certificate.</param>
      <returns>DER- or BER-encoded OCSPResponse ASN.1 object or null.</returns>
    </member>
    <member name="T:DevExpress.Office.DigitalSignatures.OcspException">
      <summary>
        <para>Fires when the OCSP response fails.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.DigitalSignatures.OcspException.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.DigitalSignatures.OcspException"/> class with specified settings.</para>
      </summary>
      <param name="message">An exception message.</param>
    </member>
    <member name="N:DevExpress.Office.Tsp">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Office.Tsp.ITsaClient">
      <summary>
        <para>Interface used to generate time stamps.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.Tsp.ITsaClient.GenerateTimeStamp(System.Byte[],System.String)">
      <summary>
        <para>Generates a timestamp and returns its contents.</para>
      </summary>
      <param name="digest">The document&#39;s hash value.</param>
      <param name="digestAlgorithmOID">The hash algorithm&#39;s object identifier.</param>
      <returns>The timestamp contents.</returns>
    </member>
    <member name="M:DevExpress.Office.Tsp.ITsaClient.GenerateTimeStamp(System.IO.Stream)">
      <summary>
        <para>Generates a timestamp for the specific data.</para>
      </summary>
      <param name="stream">A stream that contains data used to generate a timestamp.</param>
      <returns>An ASN.1 BER-encoded TimeStampToken (see rfc3161) object.</returns>
    </member>
    <member name="T:DevExpress.Office.Tsp.TsaClient">
      <summary>
        <para>Allows you to generate timestamps.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.Tsp.TsaClient.#ctor(System.Uri,DevExpress.Office.DigitalSignatures.HashAlgorithmType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.Tsp.TsaClient"/> class with specified settings.</para>
      </summary>
      <param name="tsaServerURI">A URI of a timestamp server.</param>
      <param name="algorithm">A timestamp&#39;s hashing algorithm.</param>
    </member>
    <member name="M:DevExpress.Office.Tsp.TsaClient.#ctor(System.Uri,DevExpress.Office.DigitalSignatures.HashAlgorithmType,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.Tsp.TsaClient"/> class with specified settings.</para>
      </summary>
      <param name="tsaServerURI">A URI of a timestamp server.</param>
      <param name="algorithm">A timestamp&#39;s hashing algorithm.</param>
      <param name="userName">A username to log into the server.</param>
      <param name="password">A password to log into the server.</param>
    </member>
    <member name="M:DevExpress.Office.Tsp.TsaClient.#ctor(System.Uri,DevExpress.Office.DigitalSignatures.IDigestCalculator)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.Tsp.TsaClient"/> class with specified settings.</para>
      </summary>
      <param name="tsaServerURI"></param>
      <param name="digestCalculator"></param>
    </member>
    <member name="M:DevExpress.Office.Tsp.TsaClient.#ctor(System.Uri,DevExpress.Office.DigitalSignatures.IDigestCalculator,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.Tsp.TsaClient"/> class with specified settings.</para>
      </summary>
      <param name="tsaServerURI">A URI of a timestamp server.</param>
      <param name="digestCalculator">An objects used to calculate the digest value.</param>
      <param name="userName">A username to log in to the server.</param>
      <param name="password">A password to log in to the server.</param>
    </member>
    <member name="M:DevExpress.Office.Tsp.TsaClient.GenerateTimeStamp(System.Byte[],System.String)">
      <summary>
        <para>Generates a timestamp and returns its contents.</para>
      </summary>
      <param name="digest">The document&#39;s hash value.</param>
      <param name="digestAlgorithmOID">The hash algorithm&#39;s object identifier.</param>
      <returns>The timestamp contents.</returns>
    </member>
    <member name="M:DevExpress.Office.Tsp.TsaClient.GenerateTimeStamp(System.IO.Stream)">
      <summary>
        <para>Generates a timestamp for the specific data.</para>
      </summary>
      <param name="stream">A stream that contains data used to generate a timestamp.</param>
      <returns>An ASN.1 BER-encoded TimeStampToken (see rfc3161) object.</returns>
    </member>
    <member name="T:DevExpress.Office.Tsp.TspValidationException">
      <summary>
        <para>Fires when timestamp request was rejected by the server.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Office.Tsp.TspValidationException.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Office.Tsp.TspValidationException"/> class with specified settings.</para>
      </summary>
      <param name="message">An error code with a description.</param>
    </member>
    <member name="N:DevExpress.Pdf">
      <summary>
        <para>Contains classes and enumerations that are used to implement the main functionality of WinForms and WPF PDF Viewers, and the PDF Document API.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.ExternalSignerInfo">
      <summary>
        <para>Contains information about the signature contents.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.ExternalSignerInfo.#ctor(DevExpress.Pdf.PdfSignatureType,System.Int32,DevExpress.Office.DigitalSignatures.HashAlgorithmType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.ExternalSignerInfo"/> class with specified settings.</para>
      </summary>
      <param name="type">The signature type.</param>
      <param name="signatureSize">The signature size (in bytes).</param>
      <param name="hashAlgorithm">The signature&#39;s hashing algorithm.</param>
    </member>
    <member name="M:DevExpress.Pdf.ExternalSignerInfo.#ctor(DevExpress.Pdf.PdfSignatureType,System.Int32,DevExpress.Office.DigitalSignatures.IDigestCalculator)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.ExternalSignerInfo"/> class with specified settings.</para>
      </summary>
      <param name="type">The signature type.</param>
      <param name="signatureSize">The signature size (in bytes).</param>
      <param name="digestCalculator">The signature&#39;s digest calculator.</param>
    </member>
    <member name="M:DevExpress.Pdf.ExternalSignerInfo.#ctor(System.String,System.String,System.String,System.Int32,DevExpress.Office.DigitalSignatures.HashAlgorithmType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.ExternalSignerInfo"/> class with specified settings.</para>
      </summary>
      <param name="type">The signature type.</param>
      <param name="filter">The name of the preferred signature handler.</param>
      <param name="subFilter">The signature&#39;s encoding and key information.</param>
      <param name="signatureSize">The signature size (in bytes).</param>
      <param name="hashAlgorithm">The signature&#39;s hashing algorithm.</param>
    </member>
    <member name="M:DevExpress.Pdf.ExternalSignerInfo.#ctor(System.String,System.String,System.String,System.Int32,DevExpress.Office.DigitalSignatures.IDigestCalculator)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.ExternalSignerInfo"/> class with specified settings.</para>
      </summary>
      <param name="type">The signature type.</param>
      <param name="filter">The name of the preferred signature handler.</param>
      <param name="subFilter">The signature&#39;s encoding and key information.</param>
      <param name="signatureSize">The signature size.</param>
      <param name="digestCalculator">The signature&#39;s digest calculator.</param>
    </member>
    <member name="P:DevExpress.Pdf.ExternalSignerInfo.DigestCalculator">
      <summary>
        <para>Returns the signature&#39;s digest calculator.</para>
      </summary>
      <value>An object used to calculate the digest value.</value>
    </member>
    <member name="P:DevExpress.Pdf.ExternalSignerInfo.Filter">
      <summary>
        <para>Retrieves the name of the preferred signature handler.</para>
      </summary>
      <value>Preferred signature handler&#39;s name.</value>
    </member>
    <member name="P:DevExpress.Pdf.ExternalSignerInfo.SignatureSize">
      <summary>
        <para>Returns the signature size (in bytes).</para>
      </summary>
      <value>The signature size (in bytes).</value>
    </member>
    <member name="P:DevExpress.Pdf.ExternalSignerInfo.SubFilter">
      <summary>
        <para>Returns information about the signature&#39;s format.</para>
      </summary>
      <value>The signature&#39;s encoding and key information.</value>
    </member>
    <member name="P:DevExpress.Pdf.ExternalSignerInfo.Type">
      <summary>
        <para>Returns the signature type.</para>
      </summary>
      <value>The signature type.</value>
    </member>
    <member name="T:DevExpress.Pdf.IExternalSigner">
      <summary>
        <para>Exposes the basic functionality to generate signatures.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.IExternalSigner.BuildSignature(System.Byte[],System.String)">
      <summary>
        <para>Builds the signature and returns its contents.</para>
      </summary>
      <param name="digest">The document&#39;s hash value.</param>
      <param name="digestAlgorithmOID">The hash algorithm&#39;s object identifier.</param>
      <returns>The signature contents.</returns>
    </member>
    <member name="M:DevExpress.Pdf.IExternalSigner.BuildSignature(System.IO.Stream)">
      <summary>
        <para>Builds the signature for the specified document.</para>
      </summary>
      <param name="stream">A stream that contains the document to sign.</param>
      <returns>A signature.</returns>
    </member>
    <member name="P:DevExpress.Pdf.IExternalSigner.Filter">
      <summary>
        <para>Retrieves the name of the preferred signature handler.</para>
      </summary>
      <value>Preferred signature handler&#39;s name.</value>
    </member>
    <member name="M:DevExpress.Pdf.IExternalSigner.GetSignatureSize">
      <summary>
        <para>Returns the signature size (in bytes).</para>
      </summary>
      <returns>A signature&#39;s size (in bytes).</returns>
    </member>
    <member name="P:DevExpress.Pdf.IExternalSigner.SubFilter">
      <summary>
        <para>Returns information about the signature&#39;s format.</para>
      </summary>
      <value>The signature&#39;s encoding and key information.</value>
    </member>
    <member name="P:DevExpress.Pdf.IExternalSigner.Type">
      <summary>
        <para>Returns the signature type.</para>
      </summary>
      <value>The signature type.</value>
    </member>
    <member name="T:DevExpress.Pdf.IPdfViewer">
      <summary>
        <para>For internal use. Provides the functionality of  <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> and <see cref="T:DevExpress.Xpf.PdfViewer.PdfViewerControl"/> classes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.IPdfViewer.GetDocumentProcessorHelper">
      <summary>
        <para>For internal use.</para>
      </summary>
      <returns>A  <see cref="T:DevExpress.Pdf.PdfDocumentProcessorHelper"/> object.</returns>
    </member>
    <member name="N:DevExpress.Pdf.Localization">
      <summary>
        <para>Contains classes and an enumeration that provides the localization functionality common to WinForms and WPF PDF Viewers, and the PDF Document API.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.Localization.PdfCoreLocalizer">
      <summary>
        <para>Provides the means to localize the dialog messages of a PDF Viewer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.Localization.PdfCoreLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Localization.PdfCoreLocalizer"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.Localization.PdfCoreLocalizer.Active">
      <summary>
        <para>Specifies a localizer object providing localization of a PDF Viewer&#39;s dialog messages at runtime.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> descendant, used to localize the dialog messages at runtime.</value>
    </member>
    <member name="M:DevExpress.Pdf.Localization.PdfCoreLocalizer.CreateResXLocalizer">
      <summary>
        <para>For internal use. Returns a Localizer object storing resources based on the thread&#39;s language and regional settings (culture).</para>
      </summary>
      <returns>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object, storing resources based on the thread&#39;s language and regional settings (culture).</returns>
    </member>
    <member name="M:DevExpress.Pdf.Localization.PdfCoreLocalizer.GetString(DevExpress.Pdf.Localization.PdfCoreStringId)">
      <summary>
        <para>Returns a localized string for the given string identifier.</para>
      </summary>
      <param name="id">A <see cref="T:DevExpress.Pdf.Localization.PdfCoreStringId"/> enumeration value, identifying the string to localize.</param>
      <returns>A <see cref="T:System.String"/> value, corresponding to the specified identifier.</returns>
    </member>
    <member name="P:DevExpress.Pdf.Localization.PdfCoreLocalizer.Language">
      <summary>
        <para>Returns the name of the language currently used by this localizer object.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the language of the user interface localization.</value>
    </member>
    <member name="T:DevExpress.Pdf.Localization.PdfCoreResLocalizer">
      <summary>
        <para>A default localizer to translate the PDF Viewer&#39;s resources.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.Localization.PdfCoreResLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Localization.PdfCoreResLocalizer"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.Localization.PdfCoreResLocalizer.GetLocalizedString(DevExpress.Pdf.Localization.PdfCoreStringId)">
      <summary>
        <para>Gets the string, localized by the current <see cref="T:DevExpress.Pdf.Localization.PdfCoreResLocalizer"/>, for the specified dialog message.</para>
      </summary>
      <param name="id">A <see cref="T:DevExpress.Pdf.Localization.PdfCoreStringId"/> enumeration value, specifying the dialog message whose caption (text) is to be localized.</param>
      <returns>A <see cref="T:System.String"/> value, specifying the text to be displayed within the specified dialog message.</returns>
    </member>
    <member name="T:DevExpress.Pdf.Localization.PdfCoreStringId">
      <summary>
        <para>Contains strings that correspond to the dialog messages of the PDF Viewer that are subject to localization.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeCaret">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeCircle">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeCustom">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeFileAttachment">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeFreeText">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeHighlight">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeInk">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeLine">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypePolygon">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypePolyLine">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeRedaction">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeRubberStamp">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeSound">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeSquare">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeSquiggly">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeStrikethrough">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeText">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.AnnotationFilterTypeUnderline">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.DefaultDocumentName">
      <summary>
        <para>&quot;Document&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.FileSize">
      <summary>
        <para>&quot;{0:0.00} {1} ({2} Bytes)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.FileSizeInBytes">
      <summary>
        <para>&quot;{0} Bytes&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgAcroFormFieldNameCantBeEmpty">
      <summary>
        <para>&quot;The form field name cannot be null or an empty string.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgAcroFormFieldNameDuplication">
      <summary>
        <para>&quot;The siblings can&#39;t have the same names in the form field hierarchy.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgAttachmentHintDescription">
      <summary>
        <para>&quot;\r\nDescription: {0}&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgAttachmentHintFileName">
      <summary>
        <para>&quot;Name: {0}&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgAttachmentHintModificationDate">
      <summary>
        <para>&quot;\r\nModification Date: {0}&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgAttachmentHintSize">
      <summary>
        <para>&quot;\r\nSize: {0}&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgCantSetSelectedIndexWithoutValues">
      <summary>
        <para>&quot;The selected index cannot be set because there are no possible values.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgEmptyCustomPropertyName">
      <summary>
        <para>Custom property name cannot be null or empty string.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgEntryPointNotFound">
      <summary>
        <para>&quot;Unable to find an entry point named &quot;{0}&quot; in shared library &quot;{1}&quot;&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgFormDataNotFound">
      <summary>
        <para>&quot;The document structure does not contain a field with the specified name: {0}.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgICULibraryNotFound">
      <summary>
        <para>&quot;Unable to load ICU library.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncompatibleOperationWithCurrentDocumentFormat">
      <summary>
        <para>&quot;The operation is not supported in {0} compatability mode. Please create document in PdfCompatibility.Pdf compatibility mode.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectAcroFormExportValue">
      <summary>
        <para>&quot;The export value cannot be null or an empty string.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectAcroFormFieldNameContainsPeriod">
      <summary>
        <para>&quot;The form field name cannot contain a period character.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectAction">
      <summary>
        <para>&quot;The specified action does not belong to the current document.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectBookmarkListValue">
      <summary>
        <para>&quot;Bookmark list can&#39;t be null.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectButtonFormFieldValue">
      <summary>
        <para>&quot;The argument should be the name of a button appearance or a value from the options array.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectChoiceFormFieldValue">
      <summary>
        <para>&quot;The argument should be a string value or a list of string values from the options array.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectColorComponentValue">
      <summary>
        <para>&quot;The color component value should be greater than or equal to 0 and less than or equal to 1.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectDashLength">
      <summary>
        <para>&quot;The dash length should be greater than or equal to 0.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectDashPattern">
      <summary>
        <para>&quot;The sum of dash and gap lengths should be greater than 0.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectDashPatternArraySize">
      <summary>
        <para>&quot;The dash pattern array must not be empty.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectDestination">
      <summary>
        <para>&quot;The specified destination does not belong to the current document.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectDestinationPage">
      <summary>
        <para>&quot;The destination of a bookmark can&#39;t be linked to a page which belongs to another document.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectDpi">
      <summary>
        <para>&quot;Resolution (in dots per inch) should be greater than 0.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectFlatnessTolerance">
      <summary>
        <para>&quot;The flatness tolerance should be greater than or equal to 0 and less than or equal to 100.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectFormDataFile">
      <summary>
        <para>&quot;Error while reading the PDF Form from the specified file.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectGapLength">
      <summary>
        <para>&quot;The gap length should be greater than or equal to 0.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectGlyphPosition">
      <summary>
        <para>&quot;The glyph position should be less or equal than text length.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectInsertingPageNumber">
      <summary>
        <para>&quot;The page number should be greater than 0, and less than or equal to the next available page number (next to the document page count)&quot;.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectLargestEdgeLength">
      <summary>
        <para>&quot;The largest edge length should be greater than 0.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectLineWidth">
      <summary>
        <para>&quot;The line width should be greater than or equal to 0.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectListSize">
      <summary>
        <para>&quot;The list should contain at least one value.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectMarkedContentTag">
      <summary>
        <para>&quot;The marked content tag can&#39;t be empty.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectMarkupAnnotation">
      <summary>
        <para>&quot;The specified annotation does not belong to the current document.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectMiterLimit">
      <summary>
        <para>&quot;The miter limit should be greater than 0.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectOpacity">
      <summary>
        <para>&quot;The opacity value should be greater than or equal to 0 and less than or equal to 1.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectPageArtBox">
      <summary>
        <para>&quot;The page art box should be less than or equal to the media box.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectPageBleedBox">
      <summary>
        <para>&quot;The page bleeding box should be less than or equal to the media box.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectPageCropBox">
      <summary>
        <para>&quot;The page croppoing box should be less than or equal to the media box.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectPageNumber">
      <summary>
        <para>&quot;The page number should be greater than 0.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectPageRotate">
      <summary>
        <para>&quot;The page rotation angle can have one of the following values: 0, 90, 180 or 270 degrees.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectPageTrimBox">
      <summary>
        <para>&quot;The page trimming box should be less than or equal to the media box.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectPdfData">
      <summary>
        <para>&quot;Input data are not recognized as valid pdf.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectPdfPassword">
      <summary>
        <para>&quot;The Document Open password is empty or incorrect.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectPrintableFilePath">
      <summary>
        <para>&quot;Unable to create a print document with the specified name: {0}.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectRectangleHeight">
      <summary>
        <para>&quot;The top coordinate of the rectangle should be greater than the bottom.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectRectangleWidth">
      <summary>
        <para>&quot;The right coordinate of the rectangle should be greater than the left one.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectSelectedIndexValue">
      <summary>
        <para>&quot;The index should be in the range from 0 to {0}.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectText">
      <summary>
        <para>&quot;The text value can&#39;t be null.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectTextFormFieldValue">
      <summary>
        <para>&quot;The argument should be a string value.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectTextHorizontalScaling">
      <summary>
        <para>&quot;The text horizontal scaling value should be greater than 0.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgIncorrectZoom">
      <summary>
        <para>&quot;The zoom value should be greater than or equal to 0&quot;.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgMissingPageNumbers">
      <summary>
        <para>&quot;At least one page number should be specified.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgPageNumberShouldBePositive">
      <summary>
        <para>&quot;The page number should be greater than or equal to 1.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgPartialTrustEnvironmentLimitation">
      <summary>
        <para>&quot;This operation cannot be performed in the Partial Trust environment.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgSharedLibraryNotFound">
      <summary>
        <para>&quot;Unable to load shared library &quot;{0}&quot; or one of its dependencies: &quot;{1}&quot;&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgShouldEmbedFonts">
      <summary>
        <para>&quot;All fonts should be embedded to a PDF/A document.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgStreamIsInUse">
      <summary>
        <para>&quot;The stream is already used for a document. Please use another stream.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgUnavailableOperation">
      <summary>
        <para>&quot;This operation is not available while no document is being loaded.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgUnsupportedAnnotationType">
      <summary>
        <para>&quot;The text highlight annotation is not supported in a PDF/A-1 document.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgUnsupportedBrushType">
      <summary>
        <para>&quot;Custom brushes are not supported.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgUnsupportedEncryption">
      <summary>
        <para>&quot;Encryption is not supported in a PDF/A document.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgUnsupportedFileAttachments">
      <summary>
        <para>&quot;The file attachments are not supported in a PDF/A&#0045;2b document.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgUnsupportedGraphicsOperation">
      <summary>
        <para>&quot;This operation is not supported because the PdfGraphics object does not belong to the current document.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgUnsupportedGraphicsUnit">
      <summary>
        <para>&quot;The Display and World units are not supported for the source image coordinate system.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgUnsupportedStream">
      <summary>
        <para>&quot;Stream should support the read, write, and seek operations.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgUnsupportedStreamForLoadOperation">
      <summary>
        <para>&quot;Stream should support the read and seek operations for loading.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgUnsupportedStreamForSaveOperation">
      <summary>
        <para>&quot;Stream should support the write and seek operations for saving.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgWinOnlyLimitation">
      <summary>
        <para>&quot;This action can be performed only on Windows operating system.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.MsgZeroColorComponentsCount">
      <summary>
        <para>&quot;The color should have at least one component.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.StickyNoteDefaultSubject">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.TextHighlightDefaultSubject">
      <summary>
        <para>&quot;Highlight&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.TextStrikethroughDefaultSubject">
      <summary>
        <para>&quot;Strikethrough&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.TextUnderlineDefaultSubject">
      <summary>
        <para>&quot;Underline&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.UnitExaBytes">
      <summary>
        <para>&quot;EB&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.UnitGigaBytes">
      <summary>
        <para>&quot;GB&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.UnitKiloBytes">
      <summary>
        <para>&quot;KB&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.UnitMegaBytes">
      <summary>
        <para>&quot;MB&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.UnitPetaBytes">
      <summary>
        <para>&quot;PB&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.UnitTeraBytes">
      <summary>
        <para>&quot;TB&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.Localization.PdfCoreStringId.UnitZettaBytes">
      <summary>
        <para>&quot;ZB&quot;</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormBorderAppearance">
      <summary>
        <para>Provides appearance settings used to paint the border of an interactive form field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormBorderAppearance.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfAcroFormBorderAppearance"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormBorderAppearance.Color">
      <summary>
        <para>Specifies the color of an interactive form field border.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRGBColor"/> object that specifies the border color.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormBorderAppearance.Style">
      <summary>
        <para>Specifies the style of an interactive form field border.</para>
      </summary>
      <value>One of the <see cref="T:DevExpress.Pdf.PdfAcroFormBorderStyle"/> enumeration values that represents the style of an interactive form field border.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormBorderAppearance.Width">
      <summary>
        <para>Specifies the width of the interactive form field border.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that is the width of the interactive form field border. The default value is 1.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormBorderStyle">
      <summary>
        <para>Specifies the border style for an interactive form field.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormBorderStyle.Beveled">
      <summary>
        <para>Beveled border.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormBorderStyle.Dashed">
      <summary>
        <para>Dashed border.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormBorderStyle.Inset">
      <summary>
        <para>Inset border.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormBorderStyle.Solid">
      <summary>
        <para>Solid border.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormButtonStyle">
      <summary>
        <para>Lists values specifying the appearance of the form field&#39;s  button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormButtonStyle.Check">
      <summary>
        <para>Check button style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormButtonStyle.Circle">
      <summary>
        <para>Circle button style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormButtonStyle.Cross">
      <summary>
        <para>Cross button style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormButtonStyle.Diamond">
      <summary>
        <para>Diamond button style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormButtonStyle.Square">
      <summary>
        <para>Square button style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormButtonStyle.Star">
      <summary>
        <para>Star button style.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormCheckBoxField">
      <summary>
        <para>Represents a check box field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormCheckBoxField.#ctor(System.String,System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfAcroFormCheckBoxField"/> class with the specified check box field name, page number, where the field will be created, and the field rectangle.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of check box field.</param>
      <param name="pageNumber">An integer value that specifies the page number where the form field will be created.</param>
      <param name="rect">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents a rectangle inside which check box field is located on a page.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormCheckBoxField.ButtonStyle">
      <summary>
        <para>Specifies a button style which is applied to a check box field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormButtonStyle"/> enumeration value that determines the button style applied to a check box field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormCheckBoxField.ExportValue">
      <summary>
        <para>Specifies an export value of a check box field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value which represents an export value of a check box field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormCheckBoxField.IsChecked">
      <summary>
        <para>Gets or sets whether a check box is checked.</para>
      </summary>
      <value>true, if the check box is checked; otherwise, false. Default value is false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormCheckBoxField.ShouldGeneratePressedAppearance">
      <summary>
        <para>Specifies whether to generate a down appearance that will appear in a check box when the mouse button is pressed within the check box area.</para>
      </summary>
      <value>true, if a down appearance is generated within the check box area; otherwise, false. Default value is true.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormChoiceField">
      <summary>
        <para>Represents the base class for combo box and list box fields.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormChoiceField.AddValue(System.String)">
      <summary>
        <para>Adds an item to a combo box and list box by their display value.</para>
      </summary>
      <param name="displayValue">A <see cref="T:System.String"/> that is a value to be added to a combo box and list box.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormChoiceField.AddValue(System.String,System.String)">
      <summary>
        <para>Adds an item to a combo box and list box using their display and export values.</para>
      </summary>
      <param name="displayValue">A <see cref="T:System.String"/> that is a value to be added to a combo box and list box.</param>
      <param name="exportValue">A <see cref="T:System.String"/> that is an export value.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormChoiceField.ClearSelection">
      <summary>
        <para>Clears the combo box or list box selection.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormChoiceField.ClearValues">
      <summary>
        <para>Removes all values to be added to the list box and combo box.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormChoiceField.SelectValue(System.String)">
      <summary>
        <para>Selects an item of a combo box and list box by their export value.</para>
      </summary>
      <param name="exportValue">A <see cref="T:System.String"/> value that represents the item&#39;s export value.</param>
      <returns>true, if the combo box or list box item was successfully selected; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormChoiceField.SetSelected(System.Int32,System.Boolean)">
      <summary>
        <para>Sets the specified item&#39;s selection state in combo box and list box fields.</para>
      </summary>
      <param name="index">The zero-based index of the item whose selection state will be changed.</param>
      <param name="value">true, to select the item; false, to unselect the item.</param>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormComboBoxField">
      <summary>
        <para>Represents a combo box field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormComboBoxField.#ctor(System.String,System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfAcroFormComboBoxField"/> class with the specified combo box field name, page number, where the field will be created, and the field rectangle.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of combo box field.</param>
      <param name="pageNumber">An integer value that specifies the page number where the form field will be created.</param>
      <param name="rect">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents a rectangle inside which a combo box field is located on a page.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormComboBoxField.Editable">
      <summary>
        <para>Gets or sets a value that determines whether text within a text box of the combo box field can be edited.</para>
      </summary>
      <value>true, if text within a text box of the combo box field can be edited; otherwise, false. The default value is false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormComboBoxField.ValueFormat">
      <summary>
        <para>Provides access to the field value format options.</para>
      </summary>
      <value>An object that allows you to specify the value format.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormCommonVisualField">
      <summary>
        <para>Represents the base class for common field types such as text box, check box, list box, combo box, and signature.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormCommonVisualField.Rectangle">
      <summary>
        <para>Specifies a rectangle inside which an interactive form field is located on a page.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents a field rectangle.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormCommonVisualField.TextAlignment">
      <summary>
        <para>Specifies the horizontal alignment of the interactive form field&#39;s text.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormStringAlignment"/> enumeration value that specifies how the text is horizontally aligned within the interactive form field.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormCurrencyStyle">
      <summary>
        <para>Lists values used to specify currency style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormCurrencyStyle.After">
      <summary>
        <para>The currency symbol is placed after the number.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormCurrencyStyle.AfterWithSpace">
      <summary>
        <para>The currency symbol with space is placed after the number.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormCurrencyStyle.Before">
      <summary>
        <para>The currency symbol is placed before the number.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormCurrencyStyle.BeforeWithSpace">
      <summary>
        <para>The currency symbol with space is placed before the number.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormCurrencyStyle.None">
      <summary>
        <para>No currency sign.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormField">
      <summary>
        <para>Represents the base class for all interactive form fields.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormField.CreateCheckBox(System.String,System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Creates a check box field using the field name, page number, and a rectangle where the check box field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a check box field.</param>
      <param name="pageNumber">An integer value that specifies the page number where the form field will be created.</param>
      <param name="rect">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents a rectangle inside which a check box field is located on a page.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfAcroFormCheckBoxField"/> object that represents a created check box field.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormField.CreateComboBox(System.String,System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Creates a combo box field using the field name, page number, and a rectangle where the combo box field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a combo box field.</param>
      <param name="pageNumber">An integer value that specifies the page number where the form field will be created.</param>
      <param name="rect">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents a rectangle inside which a combo box field is located on a page.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfAcroFormComboBoxField"/> object that represents a created combo box field.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormField.CreateGroup(System.String)">
      <summary>
        <para>Creates a group form field using the field name.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a group field.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfAcroFormGroupField"/> object that represents a created group form field.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormField.CreateListBox(System.String,System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Creates a list box field using the field name, page number and a rectangle where the list box field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a list box field.</param>
      <param name="pageNumber">An integer value that specifies the page number where the form field will be created.</param>
      <param name="rect">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents a rectangle inside which a list box field is located on a page.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfAcroFormListBoxField"/> object that represents a created list box field.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormField.CreateRadioGroup(System.String,System.Int32)">
      <summary>
        <para>Creates a radio group field using the field name, and page number.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a radio group field.</param>
      <param name="pageNumber">An integer value that specifies the page number where the form field will be created.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfAcroFormRadioGroupField"/> object that represents a created radio group field.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormField.CreateSignature(System.String,System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Creates a signature field using the field name, page number and a rectangle where a signature field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a signature field.</param>
      <param name="pageNumber">An integer value that specifies the page number where the form field will be created.</param>
      <param name="rect">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents a rectangle inside which a signature field is located on a page.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfAcroFormSignatureField"/> object that represents a created signature field.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormField.CreateTextBox(System.String,System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Creates a text box field using the field name, page number, and a rectangle where a text box field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a text box field.</param>
      <param name="pageNumber">An integer value that specifies the page number where the form field will be created.</param>
      <param name="rect">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents a rectangle inside which a text box field is located on a page.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfAcroFormTextBoxField"/> object that represents a created text box field.</returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormField.Name">
      <summary>
        <para>Specifies the interactive form field name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the field name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormField.ToolTip">
      <summary>
        <para>Specifies the form field&#39;s tooltip text.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the text displayed within the form field&#39;s tooltip.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormFieldAppearance">
      <summary>
        <para>Provides appearance settings used to paint an interactive form field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormFieldAppearance.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfAcroFormFieldAppearance"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormFieldAppearance.BackgroundColor">
      <summary>
        <para>Specifies the form field&#39;s background color.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRGBColor"/> object that represents the form field&#39;s background. The default value is null that corresponds to a transparent background color.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormFieldAppearance.BorderAppearance">
      <summary>
        <para>Specifies the border appearance settings for an interactive form field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormBorderAppearance"/> object that specifies the border appearance for the interactive form field. The default value is null (a border is not displayed in a form field).</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormFieldAppearance.FontFamily">
      <summary>
        <para>Specifies the font family name for an interactive form field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that specifies the font family name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormFieldAppearance.FontSize">
      <summary>
        <para>Specifies the size of the interactive form field&#39;s font.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value representing the font size for the interactive form field. The default value: 12.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormFieldAppearance.FontStyle">
      <summary>
        <para>Specifies the style of the form field font.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfFontStyle"/> value, representing the font style. The default value is <see cref="F:DevExpress.Pdf.PdfFontStyle.Regular"/>.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormFieldAppearance.ForeColor">
      <summary>
        <para>Specifies the foreground color of an interactive form field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRGBColor"/> object that specifies the interactive form field&#39;s foreground color.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormFieldNameCollision">
      <summary>
        <para>Represents a structure which stores information about a collision found in interactive form field names.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormFieldNameCollision.Field">
      <summary>
        <para>Gets the form field in which a collision with a name was detected.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormField"/> object that represents the interactive form field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormFieldNameCollision.ForbiddenNames">
      <summary>
        <para>Provides access to the collection of forbidden field names that was generated as a result of a collision found in the field names.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object implementing the System.Collections.Generic.ISet interface that represent the forbidden names.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormFieldRotation">
      <summary>
        <para>Specifies the rotation to apply to a form field.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormFieldRotation.Rotate0">
      <summary>
        <para>The form field is not rotated. This is the default value.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormFieldRotation.Rotate180">
      <summary>
        <para>Rotate the form field clockwise by 180 degrees.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormFieldRotation.Rotate270">
      <summary>
        <para>Rotate the form field clockwise by 270 degrees.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormFieldRotation.Rotate90">
      <summary>
        <para>Rotate the form field clockwise by 90 degrees.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormGroupField">
      <summary>
        <para>Represents a group field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormGroupField.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfAcroFormGroupField"/> class with the specified group field name.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of the group field.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormGroupField.Children">
      <summary>
        <para>Returns the collection of group form field children.</para>
      </summary>
      <value>A list of <see cref="T:DevExpress.Pdf.PdfAcroFormField"/> objects that are the collection of form field child nodes.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormListBoxField">
      <summary>
        <para>Represents a list box field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormListBoxField.#ctor(System.String,System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfAcroFormListBoxField"/> class with the specified list box field name, page number, where the field will be created, and the field rectangle.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of list box field.</param>
      <param name="pageNumber">An integer value that specifies the page number where the form field will be created.</param>
      <param name="rectangle">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents a rectangle inside which a list box field is located on a page.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormListBoxField.MultiSelect">
      <summary>
        <para>Specifies whether multiple items can be selected simultaneously in the list box field.</para>
      </summary>
      <value>true, if multiple selections of list box items are allowed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormListBoxField.TopIndex">
      <summary>
        <para>Specifies the index of the first visible item in the list box field.</para>
      </summary>
      <value>An integer value that is the zero-based index of the first visible item in the list box. Default value is 0.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormNegativeNumberStyle">
      <summary>
        <para>Lists values used to specify negative number format.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormNegativeNumberStyle.None">
      <summary>
        <para>No negative number format.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormNegativeNumberStyle.ShowParentheses">
      <summary>
        <para>Displays a negative number in brackets.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormNegativeNumberStyle.UseRedText">
      <summary>
        <para>Displays a negative number in red.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormNumberSeparatorStyle">
      <summary>
        <para>Lists values used to specify the separator format.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormNumberSeparatorStyle.ApostropheDot">
      <summary>
        <para>Apostrophe separates thousands, dot separates decimals.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormNumberSeparatorStyle.Comma">
      <summary>
        <para>Comma is a decimals separator.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormNumberSeparatorStyle.CommaDot">
      <summary>
        <para>Comma is a thousands separator, dot is a decimals separator.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormNumberSeparatorStyle.Dot">
      <summary>
        <para>Dot is a decimals separator.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormNumberSeparatorStyle.DotComma">
      <summary>
        <para>Dot is a thousands separator, comma is a decimals separator.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormRadioGroupField">
      <summary>
        <para>Represents a radio group field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormRadioGroupField.#ctor(System.String,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfAcroFormRadioGroupField"/> class with the specified radio group field name and page number where the field will be created.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of radio group field.</param>
      <param name="pageNumber">An integer value that specifies the page number where the form field will be created.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormRadioGroupField.AddButton(System.String,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Adds a radio button to the radio group field using the radio button name and a rectangle that specifies the location of this button on a PDF page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a radio button.</param>
      <param name="rect">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents a rectangle inside which a radio button is located on a page.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormRadioGroupField.ButtonStyle">
      <summary>
        <para>Specifies the style that defines the appearance of a radio button within a radio group field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormButtonStyle"/> enumeration value that determines the style of a radio button within a radio group field.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormRadioGroupField.ClearButtons">
      <summary>
        <para>Removes all radio buttons from the radio group field.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormRadioGroupField.RadioButtonCount">
      <summary>
        <para>Gets the number of radio buttons in the radio group field.</para>
      </summary>
      <value>An integer value that is the number of radio buttons in the radio group field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormRadioGroupField.SelectedIndex">
      <summary>
        <para>Specifies index of the selected item in a radio group field.</para>
      </summary>
      <value>An integer value, representing the zero-based index of the radio group&#39;s selected item.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormRadioGroupField.ShouldGeneratePressedAppearance">
      <summary>
        <para>Specifies whether to generate a down appearance that will appear when the mouse button is pressed within a radio button area.</para>
      </summary>
      <value>true, if a down appearance is generated within the radio button area; otherwise, false. Default value is true.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormSignatureField">
      <summary>
        <para>Represents a signature field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormSignatureField.#ctor(System.String,System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfAcroFormSignatureField"/> class with the specified signature field name, page number, where the field will be created, and the field rectangle.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of the signature field.</param>
      <param name="pageNumber">An integer value that specifies the page number where the form field will be created.</param>
      <param name="rect">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents a rectangle inside which a signature field is located on a page.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormSignatureField.LineAlignment">
      <summary>
        <para>Specifies the vertical alignment of the string within the signature field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormStringAlignment"/> enumeration value that represents vertical line alignment.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormSignatureField.Text">
      <summary>
        <para>Specifies text displayed in a signature field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value which represents the text displayed in the signature field.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormSpecialFormatType">
      <summary>
        <para>Lists values used to specify special value format types.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormSpecialFormatType.FiveDigitZipCode">
      <summary>
        <para>A five-digit postal code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormSpecialFormatType.NineDigitZipCode">
      <summary>
        <para>A nine-digit postal code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormSpecialFormatType.PhoneNumber">
      <summary>
        <para>A ten-digit telephone number.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormSpecialFormatType.SocialSecurityNumber">
      <summary>
        <para>A nine-digit US social security number.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormStringAlignment">
      <summary>
        <para>Specifies the alignment of a text string within an interactive form field.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormStringAlignment.Center">
      <summary>
        <para>Specifies that text is aligned in the center of the field rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormStringAlignment.Far">
      <summary>
        <para>Specifies that text is aligned far from the origin position of the field rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormStringAlignment.Near">
      <summary>
        <para>Specifies that text is aligned near to the origin position of the field rectangle.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormTextBoxField">
      <summary>
        <para>Represents a text box field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormTextBoxField.#ctor(System.String,System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfAcroFormTextBoxField"/> class with the specified text box field name, page number, where the field will be created, and the field rectangle in user coordinate system.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of text box field.</param>
      <param name="pageNumber">An integer value that specifies the page number where the form field will be created.</param>
      <param name="rectangle">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents a rectangle in user coordinate system used to place a text box field.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormTextBoxField.MaxLength">
      <summary>
        <para>Specifies the maximum text length for a text box field.</para>
      </summary>
      <value>A positive integer value specifying the maximum number of characters allowed in a text box field or 0 indicating that the number of characters in a text box field is not limited.The default value is 0.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormTextBoxField.Multiline">
      <summary>
        <para>Specifies whether the text box field should contain multiple lines of text.</para>
      </summary>
      <value>true, if the text box field can contain multiple lines of text; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormTextBoxField.Scrollable">
      <summary>
        <para>Specifies whether a text box field can be scrollable when the entered text exceeds the field area.</para>
      </summary>
      <value>true, if a text box field is scrollable when the entered text exceeds the field area; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormTextBoxField.SpellCheck">
      <summary>
        <para>Specifies whether text entered to a text box field is spell checked.</para>
      </summary>
      <value>true, if text entered to a text box field is spell checked; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormTextBoxField.Text">
      <summary>
        <para>Specifies text of the text box field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value which represents the text displayed in the text box field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormTextBoxField.Type">
      <summary>
        <para>Specifies the type of the text box field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormTextFieldType"/> enumeration value that represents the text box field type.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormTextBoxField.ValueFormat">
      <summary>
        <para>Provides access to the field value format options.</para>
      </summary>
      <value>An object that allows you to specify the value format.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormTextFieldType">
      <summary>
        <para>Specifies the type of text field.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormTextFieldType.FileSelect">
      <summary>
        <para>The text entered in the field represents the pathname of a file whose contents will be submitted as the value of the field.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormTextFieldType.Password">
      <summary>
        <para>The value of this field will be displayed in an unreadable form.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAcroFormTextFieldType.PlainText">
      <summary>
        <para>The value of this field will be displayed as a plain text.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormValueFormat">
      <summary>
        <para>Allows you to specify format for form field values.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormValueFormat.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfAcroFormValueFormat"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormValueFormat.CalculateScript">
      <summary>
        <para>Gets or sets the JavaScript to be performed when the field’s value is changed. This action may check the new value for validity.</para>
      </summary>
      <value>The calculation JavaScript. For JS API reference, use JavaScript for Acrobat API Reference</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormValueFormat.CreateDateTimeFormat(System.String)">
      <summary>
        <para>Create a date and time format for a form field value.</para>
      </summary>
      <param name="format">Specifies the date and time format.</param>
      <returns>An object that contains the date and time format.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormValueFormat.CreateNumberFormat(System.Int32,DevExpress.Pdf.PdfAcroFormNumberSeparatorStyle)">
      <summary>
        <para>Creates a number format for a form field value.</para>
      </summary>
      <param name="decimalPlaces">A number of decimal places.</param>
      <param name="separatorStyle">An enumeration value that indicates the number separator style.</param>
      <returns>An object that contains the number format.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormValueFormat.CreateNumberFormat(System.Int32,DevExpress.Pdf.PdfAcroFormNumberSeparatorStyle,System.String,DevExpress.Pdf.PdfAcroFormCurrencyStyle,DevExpress.Pdf.PdfAcroFormNegativeNumberStyle)">
      <summary>
        <para>Creates a number format for a form field value.</para>
      </summary>
      <param name="decimalPlaces">A number of decimal places.</param>
      <param name="separatorStyle">An enumeration value that indicates the number separator style.</param>
      <param name="currencySymbol">A currency symbol.</param>
      <param name="currencyStyle">An enumeration value that indicates the currency style.</param>
      <param name="negativeNumberStyle">An enumeration value that indicates the negative number style.</param>
      <returns>An object that contains the number format.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormValueFormat.CreatePercentFormat(System.Int32,DevExpress.Pdf.PdfAcroFormNumberSeparatorStyle)">
      <summary>
        <para>Creates a percent format for a form field value.</para>
      </summary>
      <param name="decimalPlaces">A number of decimal places.</param>
      <param name="separatorStyle">An enumeration value that indicates the separator style.</param>
      <returns>An object that contains the percent format.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormValueFormat.CreateSpecialFormat(DevExpress.Pdf.PdfAcroFormSpecialFormatType)">
      <summary>
        <para>Creates a special format for the form field values.</para>
      </summary>
      <param name="format">An enumeration value that indicates the special format style.</param>
      <returns>An object that contains the special format.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormValueFormat.CreateSpecialFormat(System.String)">
      <summary>
        <para>Creates a special format for the form field values.</para>
      </summary>
      <param name="formatMask">Specifies what characters can be inserted and how they are displayed.</param>
      <returns>An object that contains the special format.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfAcroFormValueFormat.CreateTimeFormat(System.String)">
      <summary>
        <para>Creates a time format for a form field value.</para>
      </summary>
      <param name="format">Specifies the time format.</param>
      <returns>An object that contains time format.</returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormValueFormat.FormatScript">
      <summary>
        <para>Gets or sets the JavaScript to be performed before the field is formatted. This action may modify the field’s value before formatting.</para>
      </summary>
      <value>The format JavaScript. For JS API reference, use JavaScript for Acrobat API Reference</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormValueFormat.KeystrokeScript">
      <summary>
        <para>Gets or sets the JavaScript to be performed when the user modifies a character in a This action may check the added text for validity and reject or modify it.</para>
      </summary>
      <value>The keystroke JavaScript. For JS API reference, use JavaScript for Acrobat API Reference</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormValueFormat.ValidateScript">
      <summary>
        <para>Gets os sets the JavaScript to be performed to recalculate the value of this field when that of another field changes.</para>
      </summary>
      <value>The validation JavaScript. For JS API reference, use JavaScript for Acrobat API Reference</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAcroFormVisualField">
      <summary>
        <para>Represents the base class for visual form fields.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormVisualField.Appearance">
      <summary>
        <para>Specifies the appearance settings for the interactive form field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormFieldAppearance"/> object that specifies the appearance for the interactive form field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormVisualField.PageNumber">
      <summary>
        <para>Specifies page number where the interactive form field will be created.</para>
      </summary>
      <value>An integer value specifying the number of a page.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormVisualField.Print">
      <summary>
        <para>Specifies whether the current form field is printed.</para>
      </summary>
      <value>true, if the current form field is printed on the page, otherwise, false. The default value is true.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormVisualField.ReadOnly">
      <summary>
        <para>Gets or sets whether the interactive form field allows editing.</para>
      </summary>
      <value>true, if end-users cannot modify the form field&#39;s value; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormVisualField.Required">
      <summary>
        <para>Gets or sets the required status of the interactive form field.</para>
      </summary>
      <value>true, if the form field must have a value at the time it is exported by a submit-form action; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormVisualField.Rotation">
      <summary>
        <para>Specifies the rotation to apply to interactive form field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormFieldRotation"/> enumeration value that represents the degree by which a form field is rotated counterclockwise relative to the page.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAcroFormVisualField.Visible">
      <summary>
        <para>Specifies whether the form field is visible on the page.</para>
      </summary>
      <value>true, if the form field is visible; otherwise, false. The default value is true.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAction">
      <summary>
        <para>An action that is performed with interactive elements (e.g., bookmarks, annotations) in the PDF document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAction.Next">
      <summary>
        <para>Gets the next action that is performed after the action from the list of  <see cref="T:DevExpress.Pdf.PdfAction"/> objects.</para>
      </summary>
      <value>A list of <see cref="T:DevExpress.Pdf.PdfAction"/> objects that is the sequence of actions that should be performed.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAnnotation">
      <summary>
        <para>Represents an annotation that is used for adding text notes and other ancillary information to the document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotation.Appearance">
      <summary>
        <para>Gets the annotation&#39;s appearance state on the PDF form.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAnnotationAppearances"/> object that represents the current annotation&#39;s appearance state on the PDF form.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotation.AppearanceName">
      <summary>
        <para>Gets or sets the name of the appearance which is currently being used to draw the annotation on the PDF form.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the appearance name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotation.Border">
      <summary>
        <para>Gets the annotation&#39;s border, which will be drawn as a rounded rectangle.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAnnotationBorder"/> object that represents the annotation&#39;s border.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotation.Color">
      <summary>
        <para>Gets the color of the annotation.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfColor"/> object that is the annotation color.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotation.Contents">
      <summary>
        <para>Gets the text that will be displayed for the annotation on the PDF form.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the text that will be displayed for the annotation.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotation.Flags">
      <summary>
        <para>Gets a set of flags specifying various characteristics of the annotation.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAnnotationFlags"/> object that represents a set of annotation flags.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotation.Modified">
      <summary>
        <para>Gets the date and time when the annotation was most recently modified.</para>
      </summary>
      <value>A <see cref="T:System.DateTimeOffset"/> structure.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotation.Name">
      <summary>
        <para>Gets the annotation name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that defines the string that identifies the annotation on the page.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotation.OptionalContent">
      <summary>
        <para>Gets the optional content specifying the optional content properties for the annotation.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOptionalContent"/> object that is the optional content for the annotation.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotation.Page">
      <summary>
        <para>Gets the page with which the annotation is associated.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the individual page in the document.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotation.Rect">
      <summary>
        <para>Gets the annotation rectangle that defines the location of the annotation on the page in default user space units.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents the annotation rectangle on the page.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotation.StructParent">
      <summary>
        <para>Gets the integer key of the annotation&#39;s entry in the structural parent tree.</para>
      </summary>
      <value>A nullable integer value specifying the key of the annotation&#39;s entry.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAnnotationActions">
      <summary>
        <para>Represents actions that can be performed with the annotation in the document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationActions.CursorEntered">
      <summary>
        <para>Gets an action that is performed when the cursor enters the annotation&#39;s active area.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that represents the action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationActions.CursorExited">
      <summary>
        <para>Gets an action that is performed when the cursor exits the annotation&#39;s active area.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that represents the action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationActions.InputFocusLost">
      <summary>
        <para>Gets an action that is performed when the annotation loses the input focus.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that represents the action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationActions.InputFocusReceived">
      <summary>
        <para>Gets an action that is performed when the annotation receives the input focus.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that represents the action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationActions.MouseButtonPressed">
      <summary>
        <para>Gets an action that is performed when the mouse button is pressed inside the annotation&#39;s active area.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that represents the action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationActions.MouseButtonReleased">
      <summary>
        <para>Gets an action that is performed when the mouse button is released inside the annotation&#39;s active area.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that represents the action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationActions.PageBecameInvisible">
      <summary>
        <para>Gets an action that is performed when the page containing the annotation is no longer visible in the reader&#39;s user interface.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that represents the action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationActions.PageBecameVisible">
      <summary>
        <para>Gets an action that is performed when the page containing the annotation becomes visible.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that represents the action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationActions.PageClosed">
      <summary>
        <para>Gets an action that is performed when the page containing the annotation is closed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that represents the action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationActions.PageOpened">
      <summary>
        <para>Gets an action that is performed when the page containing the annotation is opened.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that represent the action.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAnnotationAppearance">
      <summary>
        <para>Determines a form with the annotation appearance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationAppearance.DefaultForm">
      <summary>
        <para>Gets a default form in which an annotation appearance is represented.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfForm"/> object that is a default form.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationAppearance.Forms">
      <summary>
        <para>Gets custom forms with annotation appearances.</para>
      </summary>
      <value>A dictionary, containing the <see cref="T:DevExpress.Pdf.PdfForm"/> objects, along with their <see cref="T:System.String"/> key values.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAnnotationAppearances">
      <summary>
        <para>Represents annotation appearances on the PDF form.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationAppearances.Down">
      <summary>
        <para>Gets the annotation&#39;s down appearance.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAnnotationAppearance"/> object that is the down appearance.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationAppearances.Form">
      <summary>
        <para>Gets a form on the page with an annotation appearance.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfForm"/> object that is the form on the page.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationAppearances.Normal">
      <summary>
        <para>Gets the annotation&#39;s normal appearance.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAnnotationAppearance"/> object that is the normal appearance.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationAppearances.Rollover">
      <summary>
        <para>Gets the annotation&#39;s rollover appearance.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAnnotationAppearance"/> object that is the rollover appearance.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAnnotationBorder">
      <summary>
        <para>Represents the annotation&#39;s border settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfAnnotationBorder.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfAnnotationBorder"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationBorder.HorizontalCornerRadius">
      <summary>
        <para>Gets the horizontal corner radius of the annotation&#39;s border, which will be drawn as a rounded rectangle.</para>
      </summary>
      <value>A double value that is the horizontal corner radius of the annotation&#39;s border.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationBorder.IsDefault">
      <summary>
        <para>Gets a value that defines whether the default appearance of the annotation border is drawn on the page.</para>
      </summary>
      <value>true, if the annotation border is drawn with the default appearance; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationBorder.LineStyle">
      <summary>
        <para>Gets the line style of the annotation border.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfLineStyle"/> enumeration member that is the annotation border line style.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationBorder.LineWidth">
      <summary>
        <para>Gets the thickness of the line to be used in drawing the annotation&#39;s border.</para>
      </summary>
      <value>A double value that is the line width of the annotation&#39;s border.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationBorder.VerticalCornerRadius">
      <summary>
        <para>Gets the vertical corner radius of the annotation&#39;s border, which will be drawn as a rounded rectangle.</para>
      </summary>
      <value>A double value that is the vertical corner radius of the annotation&#39;s border.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAnnotationBorderStyle">
      <summary>
        <para>Contains style settings of the annotation&#39;s border.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationBorderStyle.LineStyle">
      <summary>
        <para>Gets the line style settings of the annotation&#39;s border.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfLineStyle"/> object that represents the line style settings of the annotation&#39;s border.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationBorderStyle.StyleName">
      <summary>
        <para>Gets the name of the style to be applied to the annotation&#39;s border.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that is the name of the annotation&#39;s border style.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationBorderStyle.Width">
      <summary>
        <para>Gets the annotation&#39;s border width in points.</para>
      </summary>
      <value>A double value that is the width of the annotation&#39;s border. Default value: 1.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAnnotationData">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationData.AnnotationType">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationData.Bounds">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationData.Color">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationData.Contents">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationData.ModificationDate">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Pdf.PdfAnnotationData.Name">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Pdf.PdfAnnotationFlags">
      <summary>
        <para>Lists the values specifying various characteristics of the annotation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationFlags.Hidden">
      <summary>
        <para>Do not display or print the annotation or allow a user to interact with the annotation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationFlags.Invisible">
      <summary>
        <para>Do not display the annotation if it does not belong to one of the standard annotation types and no annotation handler is available.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationFlags.Locked">
      <summary>
        <para>Do not allow the annotation to be deleted or its properties to be modified by the user.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationFlags.LockedContents">
      <summary>
        <para>Do not allow the contents of the annotation to be modified by the user.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationFlags.None">
      <summary>
        <para>If active, deactivates all other options.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationFlags.NoRotate">
      <summary>
        <para>Do not rotate the annotation&#39;s appearance to match the rotation of the page.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationFlags.NoView">
      <summary>
        <para>Do not display the annotation on the screen or allow a user to interact with the annotation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationFlags.NoZoom">
      <summary>
        <para>Do not scale the annotation&#39;s appearance to match the magnification of the page.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationFlags.Print">
      <summary>
        <para>Print the annotation when the page is printed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationFlags.ReadOnly">
      <summary>
        <para>Do not let a user interact with the annotation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationFlags.ToggleNoView">
      <summary>
        <para>Invert the interpretation of the NoView flag for certain events.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAnnotationHighlightingMode">
      <summary>
        <para>Lists values that specify the visual effect that will be used when the mouse button is pressed or held down inside its active area.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationHighlightingMode.Invert">
      <summary>
        <para>Invert the contents of the annotation rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationHighlightingMode.None">
      <summary>
        <para>No highlighting.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationHighlightingMode.Outline">
      <summary>
        <para>Invert the annotation&#39;s border.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationHighlightingMode.Push">
      <summary>
        <para>Display the annotation as if it was being pushed below the surface of the page.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationHighlightingMode.Toggle">
      <summary>
        <para>Display the annotation&#39;s down appearance.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAnnotationReviewStatus">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationReviewStatus.Accepted">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationReviewStatus.Cancelled">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationReviewStatus.Completed">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationReviewStatus.None">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationReviewStatus.Rejected">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAnnotationType">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Annotation3D">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Caret">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Circle">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Custom">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.FileAttachment">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.FreeText">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Ink">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Line">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Link">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Movie">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Polygon">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.PolyLine">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Popup">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.PrinterMark">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Redaction">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.RichMedia">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.RubberStamp">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Screen">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Sound">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Square">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Text">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.TextMarkup">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.TrapNet">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Watermark">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAnnotationType.Widget">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfAssociatedFileRelationship">
      <summary>
        <para>Lists the values used to specify the type of relationship between the document and the attached file.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAssociatedFileRelationship.Alternative">
      <summary>
        <para>The attached file specification has an alternative representation of the content (e.g., audio).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAssociatedFileRelationship.Data">
      <summary>
        <para>The attached file represents information used to derive a visual presentation (e.g. for a table or a graph).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAssociatedFileRelationship.EncryptedPayload">
      <summary>
        <para>The file specification is an encrypted payload document that should be displayed to the user if the PDF Document Processor has the cryptographic filter needed to decrypt the document.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAssociatedFileRelationship.Source">
      <summary>
        <para>The attached file has the original source material for the associated content.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAssociatedFileRelationship.Supplement">
      <summary>
        <para>The attached file represents a supplemental representation of the original source or data.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfAssociatedFileRelationship.Unspecified">
      <summary>
        <para>The relationship is unknown or does not match any relationship above.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfBookmark">
      <summary>
        <para>Contains settings that are used to specify bookmarks in a document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfBookmark.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfBookmark"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfBookmark.Action">
      <summary>
        <para>Provides access to the bookmark action being executed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that is an action that is performed with bookmarks.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfBookmark.Children">
      <summary>
        <para>Gets or sets the collection of bookmark children for a document with a tree-structured hierarchy.</para>
      </summary>
      <value>A list of <see cref="T:DevExpress.Pdf.PdfBookmark"/> objects that are the collection of bookmark child nodes.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfBookmark.Destination">
      <summary>
        <para>Gets or sets a destination (a particular view of a document) to which a bookmark is referred to.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfDestination"/> object that is a bookmark destination.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfBookmark.IsBold">
      <summary>
        <para>Gets or sets the value indicating whether the bookmark text is bold.</para>
      </summary>
      <value>true, if the bookmark text is bold; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfBookmark.IsInitiallyClosed">
      <summary>
        <para>Gets or sets a value that indicates whether bookmarks are initially closed (bookmark children are hidden) in the navigation panel after a document is loaded.</para>
      </summary>
      <value>true, if the bookmark children are initially hidden (closed) in the navigation panel; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfBookmark.IsItalic">
      <summary>
        <para>Gets or sets the value indicating whether the bookmark text is italic.</para>
      </summary>
      <value>true, if the bookmark text is italic; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfBookmark.TextColor">
      <summary>
        <para>Gets or sets the color for a bookmark&#39;s text in the navigation pane.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRGBColor"/> object that is a single color in the RGB (red, green, blue) color scheme for a bookmark&#39;s text.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfBookmark.Title">
      <summary>
        <para>Gets or sets the bookmark&#39;s text on the navigation pane.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that is the text for a bookmark on the navigation pane.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfButtonFormField">
      <summary>
        <para>Represents a button field (a push button, radio button, check box) on a PDF interactive form that the end user can manipulate with the mouse.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfButtonFormField.DefaultState">
      <summary>
        <para>Gets the default state of the button form field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that represents the default state of the button form field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfButtonFormField.KidsState">
      <summary>
        <para>Gets the kids state of the button form field.</para>
      </summary>
      <value>A collection of <see cref="T:System.String"/> objects that represent the kids state of the button form field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfButtonFormField.State">
      <summary>
        <para>Gets the state of the button form field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that represents the state of the button form field.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfCertificationLevel">
      <summary>
        <para>Lists values used to specify permissions granted by a signature.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfCertificationLevel.FillForms">
      <summary>
        <para>Allows users to fill in forms and sign the document.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfCertificationLevel.FillFormsAndAnnotate">
      <summary>
        <para>Allows users to fill in forms, create, modify and delete annotations, and sign the document.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfCertificationLevel.NoCertification">
      <summary>
        <para>All changes are allowed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfCertificationLevel.NoChangesAllowed">
      <summary>
        <para>No changes are allowed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfCharacter">
      <summary>
        <para>An individual character in a document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfCharacter.#ctor(System.String,DevExpress.Pdf.PdfFont,System.Double,DevExpress.Pdf.PdfOrientedRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfCharacter"/> class with the specified settings.</para>
      </summary>
      <param name="unicodeData">A <see cref="T:System.String"/> value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfCharacter.UnicodeData"/> property.</param>
      <param name="font">A <see cref="T:DevExpress.Pdf.PdfFont"/> value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfCharacter.Font"/> property.</param>
      <param name="fontSize">A <see cref="T:System.Double"/> value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfCharacter.FontSize"/> property.</param>
      <param name="rectangle">A <see cref="T:DevExpress.Pdf.PdfOrientedRectangle"/> value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfCharacter.Rectangle"/> property.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfCharacter.Font">
      <summary>
        <para>Returns the character&#39;s font settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfFont"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfCharacter.FontSize">
      <summary>
        <para>Returns the character&#39;s font size (in points).</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value, specifying the font size, in points.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfCharacter.Rectangle">
      <summary>
        <para>Returns a rectangle that surrounds the character.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOrientedRectangle"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfCharacter.UnicodeData">
      <summary>
        <para>Returns a Unicode representation of the character.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying a character&#39;s Unicode representation.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfCharacterMapping">
      <summary>
        <para>Stores a CMap table that maps character codes to Unicode values.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfCharacterMapping.Data">
      <summary>
        <para>Stores raw data of the CMap table.</para>
      </summary>
      <value>An array of <see cref="T:System.Byte"/> values.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfChoiceFormField">
      <summary>
        <para>Represents a choice field (a combo box, list box) on a PDF form.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfChoiceFormField.DefaultValues">
      <summary>
        <para>Gets default values of the choice form field.</para>
      </summary>
      <value>A collection of <see cref="T:System.String"/> objects that represent default values of the choice form field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfChoiceFormField.Options">
      <summary>
        <para>Gets the list of options in the choice field.</para>
      </summary>
      <value>A collection of the <see cref="T:DevExpress.Pdf.PdfOptionsFormFieldOption"/> objects that represent the list of options in the choice field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfChoiceFormField.SelectedIndices">
      <summary>
        <para>Gets a collection of integers, sorted in ascending order, representing the zero-based indices in the option list of the currently selected option items.</para>
      </summary>
      <value>A collection of integers representing the zero-based indices in the option list of the currently selected option items.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfChoiceFormField.SelectedValues">
      <summary>
        <para>Gets items currently selected in the choice field.</para>
      </summary>
      <value>A collection of  <see cref="T:System.String"/>  objects that represent currently selected items in the choice field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfChoiceFormField.TopIndex">
      <summary>
        <para>Gets the index of the first option visible in the option list.</para>
      </summary>
      <value>An integer value that represents the top index. Default value is 0.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfColor">
      <summary>
        <para>Represents a color used in the PDF document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfColor.#ctor(DevExpress.Pdf.PdfPattern,System.Double[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfColor"/> class with the specified settings.</para>
      </summary>
      <param name="pattern">A <see cref="T:DevExpress.Pdf.PdfPattern"/> object that is the color pattern.</param>
      <param name="components">An array of double values that represent the color components.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfColor.#ctor(System.Double[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfColor"/> class with the specified color components.</para>
      </summary>
      <param name="components">An array of double values representing the components in the color space in which the color is defined.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfColor.Components">
      <summary>
        <para>Provides access to an array of numbers in the range from 0.0 to 1.0, representing the components in the color space in which the color is defined.</para>
      </summary>
      <value>An array of double numbers in the range from 0.0 to 1.0, representing the components in the color space.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfColor.Equals(DevExpress.Pdf.PdfColor)">
      <summary>
        <para>Determines whether the specified object is equal to the current PdfColor object.</para>
      </summary>
      <param name="other">A PdfColor object to compare with the current object.</param>
      <returns>true if the specified object is equal to the current object; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfColor.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current PdfColor object.</para>
      </summary>
      <param name="obj">An object to compare with the current PdfColor object.</param>
      <returns>true if the specified object is equal to the current object; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfColor.GetHashCode">
      <summary>
        <para>Returns a number that identifies the object instance.</para>
      </summary>
      <returns>An integer that identifies the color instance.</returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfColor.Pattern">
      <summary>
        <para>Gets the color pattern.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPattern"/> object.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfColorSpace">
      <summary>
        <para>Represents a PDF color space.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfColorSpace.ComponentsCount">
      <summary>
        <para>Gets the number of color components in the color space.</para>
      </summary>
      <value>An integer value that is the components count.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfCommand">
      <summary>
        <para>Represents a command.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfCommandList">
      <summary>
        <para>Represents a list of <see cref="T:DevExpress.Pdf.PdfCommand"/> objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfCommandList.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfCommandList"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfCommandList.#ctor(System.Collections.Generic.IEnumerable{DevExpress.Pdf.PdfCommand})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfCommandList"/> class with the specified <see cref="T:DevExpress.Pdf.PdfCommand"/> object implementing the <see cref="T:System.Collections.IEnumerable"/> interface.</para>
      </summary>
      <param name="commands">A <see cref="T:DevExpress.Pdf.PdfCommand"/> object implementing the System.Collections.IEnumerable interface that represents the command list.</param>
    </member>
    <member name="T:DevExpress.Pdf.PdfCompatibility">
      <summary>
        <para>Lists the values specifying the compatibility mode of a document.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfCompatibility.Pdf">
      <summary>
        <para>The document supports the ISO 32000-1:2008 standard.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfCompatibility.PdfA1b">
      <summary>
        <para>The document supports the ISO 19005-1:2005 standard.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfCompatibility.PdfA2b">
      <summary>
        <para>The document supports the ISO 19005-2:2011 standard.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfCompatibility.PdfA3b">
      <summary>
        <para>The document supports the ISO 19005-3:2012 standard.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfContentHorizontalAlignment">
      <summary>
        <para>Lists values used to specify the horizontal alignment of the PDF page content.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfContentHorizontalAlignment.Center">
      <summary>
        <para>The content is center-aligned.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfContentHorizontalAlignment.Left">
      <summary>
        <para>The content is left-aligned.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfContentHorizontalAlignment.Right">
      <summary>
        <para>The content is right-aligned.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfContentVerticalAlignment">
      <summary>
        <para>Lists values used to specify the vertical alignment of the PDF page content.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfContentVerticalAlignment.Bottom">
      <summary>
        <para>The content is bottom-aligned.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfContentVerticalAlignment.Center">
      <summary>
        <para>The content is center-aligned.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfContentVerticalAlignment.Top">
      <summary>
        <para>The content is top-aligned.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfCreationOptions">
      <summary>
        <para>Represents document creation options.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfCreationOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfCreationOptions"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfCreationOptions.Compatibility">
      <summary>
        <para>Gets or sets the compatibility mode of a document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfCompatibility"/> enumeration value that specifies the compatibility mode of a document.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfCreationOptions.DisableEmbeddingAllFonts">
      <summary>
        <para>Gets or sets a value that specifies whether to prohibit embedding all fonts in a PDF document.</para>
      </summary>
      <value>true, to disable embedding all fonts in the PDF; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfCreationOptions.MergePdfADocuments">
      <summary>
        <para>Specifies whether to merge PDF/A documents.</para>
      </summary>
      <value>true, to merge document; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfCreationOptions.NotEmbeddedFontFamilies">
      <summary>
        <para>Gets or sets a list of font families that are not embedded in a document.</para>
      </summary>
      <value>A string list representing the collection of font families that are not embedded in a document.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfDeferredSignatureBuilder">
      <summary>
        <para>Allows you to build a signature for a document hash.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfDeferredSignatureBuilder.#ctor(DevExpress.Pdf.ExternalSignerInfo)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfDeferredSignatureBuilder"/> class with specified settings.</para>
      </summary>
      <param name="signerInfo">An object that contains information about the external signature.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfDeferredSignatureBuilder.#ctor(DevExpress.Pdf.ExternalSignerInfo,DevExpress.Pdf.PdfSignatureFieldInfo)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfDeferredSignatureBuilder"/> class with specified settings.</para>
      </summary>
      <param name="signerInfo">An object that contains information about the external signature.</param>
      <param name="info">An object that contains information about a signature field.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfDeferredSignatureBuilder.#ctor(DevExpress.Pdf.ExternalSignerInfo,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfDeferredSignatureBuilder"/> class with specified settings.</para>
      </summary>
      <param name="signerInfo">An object that contains information about the external signature.</param>
      <param name="formFieldName">The name of the signature form field to apply an external signature.</param>
    </member>
    <member name="T:DevExpress.Pdf.PdfDestination">
      <summary>
        <para>Represents a destination (a particular view of a document) to which a document target (e.g. a bookmark) is referred to.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfDestination.Page">
      <summary>
        <para>Gets the page of a document where the corresponding destination is located.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the page where the destination is located.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDestination.PageIndex">
      <summary>
        <para>Gets the zero-based index of a page where the destination is located.</para>
      </summary>
      <value>An integer value that is the zero-based index of the destination page.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfDocument">
      <summary>
        <para>A document contained in a PDF file.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.AcroForm">
      <summary>
        <para>Provides access to the document&#39;s interactive form (AcroForm) properties.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfInteractiveForm"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Actions">
      <summary>
        <para>Provides access to the <see cref="T:DevExpress.Pdf.PdfDocumentActions"/> class  that contains references to JavaScript actions which should be taken in response to some events.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfDocumentActions"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.AllowAccessibility">
      <summary>
        <para>Indicates whether copying or extracting text and graphics from the document is allowed (in support of accessibility to users with disabilities or for other purposes).</para>
      </summary>
      <value>true, if copying or extracting text and graphics from the document is allowed; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.AllowAnnotationsAndFormsModifying">
      <summary>
        <para>Indicates whether adding and modifying text annotations and interactive form fields is allowed.</para>
      </summary>
      <value>true, if adding and modifying text annotations and interactive form fields is allowed; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.AllowDataExtraction">
      <summary>
        <para>Indicates whether data extraction is allowed.</para>
      </summary>
      <value>true, if data extraction is allowed; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.AllowDocumentAssembling">
      <summary>
        <para>Indicates whether inserting, rotating, or deleting pages and creating navigation elements such as bookmarks is allowed.</para>
      </summary>
      <value>true, if  inserting, rotating, or deleting pages and creating navigation elements such as bookmarks is allowed; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.AllowFormsFilling">
      <summary>
        <para>Indicates whether interactive form fields filling is allowed.</para>
      </summary>
      <value>true, if  interactive form fields filling is allowed; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.AllowHighQualityPrinting">
      <summary>
        <para>Indicates whether document printing in high resolution is allowed.</para>
      </summary>
      <value>true, if  document printing in high resolution is allowed; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.AllowModifying">
      <summary>
        <para>Indicates whether document modification and assembling is allowed.</para>
      </summary>
      <value>true, if document modification and assembling is allowed; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.AllowPrinting">
      <summary>
        <para>Indicates whether document printing is allowed.</para>
      </summary>
      <value>true, if document printing is allowed; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Author">
      <summary>
        <para>Gets or sets  the name of the person who created the document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Bookmarks">
      <summary>
        <para>Gets or sets the bookmarks that are used to navigate from one part of a document to another.</para>
      </summary>
      <value>A list of <see cref="T:DevExpress.Pdf.PdfBookmark"/> objects that are the collection of bookmarks.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.CreationDate">
      <summary>
        <para>Gets the date and time when the document was created.</para>
      </summary>
      <value>A nullable <see cref="T:System.DateTimeOffset"/> structure that is a valid date-time offset.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Creator">
      <summary>
        <para>Gets or sets  the name of the conforming product that created the original document, if this document was converted to PDF from another format.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.CustomProperties">
      <summary>
        <para>Provides access to the document&#39;s custom properties.</para>
      </summary>
      <value>A dictionary object containing custom properties.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Destinations">
      <summary>
        <para>Gets named destinations for targets in the document.</para>
      </summary>
      <value>A dictionary, containing the <see cref="T:DevExpress.Pdf.PdfDestination"/> objects, along with their <see cref="T:System.String"/> key values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.FileAttachments">
      <summary>
        <para>Gets the file attachments from a document.</para>
      </summary>
      <value>An object implementing the <see cref="T:System.Collections.IEnumerable"/> interface that represents the collection of <see cref="T:DevExpress.Pdf.PdfFileAttachment"/>s.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Keywords">
      <summary>
        <para>Gets or sets the keywords associated with the document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.LanguageCulture">
      <summary>
        <para>Gets or sets the language identifier that specifies the natural language for al document text.</para>
      </summary>
      <value>A <see cref="T:System.Globalization.CultureInfo"/> object containing language identifier for all document text.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.LogicalStructure">
      <summary>
        <para>Provides access to the document&#39;s structure tree root dictionary.</para>
      </summary>
      <value>A DevExpress.Pdf.PdfLogicalStructure object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.MarkInfo">
      <summary>
        <para>Provides access to the mark information dictionary that contains information about the document&#39;s usage of Tagged PDF conventions.</para>
      </summary>
      <value>A DevExpress.Pdf.PdfMarkInfo object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Metadata">
      <summary>
        <para>Provides access to a stream that contains the document metadata.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfMetadata"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.ModDate">
      <summary>
        <para>Gets the date and time the document was modified.</para>
      </summary>
      <value>A nullable <see cref="T:System.DateTimeOffset"/> value that is the date and time of the file&#39;s modification.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Names">
      <summary>
        <para>Provides access to the document&#39;s name dictionary.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfNames"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.NeedsRendering">
      <summary>
        <para>Indicates whether or not to expedite the display of PDF documents containing XFA forms.</para>
      </summary>
      <value>true if the document shall be regenerated when the document is first opened; otherwise false. The default value is false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.OpenAction">
      <summary>
        <para>Indicates an action to be performed when a document is opened.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that represents an action to be performed when a document is opened.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.OpenDestination">
      <summary>
        <para>Indicates a destination that shall be displayed when the document is opened.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfDestination"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.OptionalContentProperties">
      <summary>
        <para>Provides access to the document&#39;s optional content properties.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOptionalContentProperties"/> object that represents the document&#39;s optional content properties.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Outlines">
      <summary>
        <para>Provides access to the outline dictionary that shall be the root of the document&#39;s outline hierarchy.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOutlines"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.OutputIntents">
      <summary>
        <para>Provides access to the array of output intent dictionaries that specify the color characteristics of output devices on which the document might be rendered.</para>
      </summary>
      <value>A collection of DevExpress.Pdf.PdfOutputIntent objects.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.PageLabels">
      <summary>
        <para>Provides access to the number tree defining the page labeling for the document.</para>
      </summary>
      <value>A dictionary, containing the DevExpress.Pdf.PdfPageLabel objects, along with their integer key values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.PageLayout">
      <summary>
        <para>Indicates the page layout of the opened document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPageLayout"/> enumeration value. The default value is <see cref="F:DevExpress.Pdf.PdfPageLayout.OneColumn"/>.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.PageMode">
      <summary>
        <para>Gets or sets the document&#39;s page mode that specifies how to display the opened document (for example, whether to show page thumbnails or outlines automatically for a document).</para>
      </summary>
      <value>A  <see cref="T:DevExpress.Pdf.PdfPageMode"/> enumeration value. The default value is UseNone.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Pages">
      <summary>
        <para>Provides access to the collection of document pages.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfPage"/> objects.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.PieceInfo">
      <summary>
        <para>Provides access to the page-piece dictionary associated with the document.</para>
      </summary>
      <value>A dictionary, containing the DevExpress.Pdf.PdfPieceInfoEntry objects, along with their <see cref="T:System.String"/> key values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Producer">
      <summary>
        <para>Gets or sets the name of the conforming product that converted the original document to PDF from another format.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Subject">
      <summary>
        <para>Gets or sets the subject of a document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Threads">
      <summary>
        <para>Provides access to the array of thread dictionaries that contains the document&#39;s article threads.</para>
      </summary>
      <value>A collection of DevExpress.Pdf.PdfArticleThread objects.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Title">
      <summary>
        <para>Gets or sets the document&#39;s title.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Trapped">
      <summary>
        <para>Gets or sets a value that specifies whether the document has been modified to include trapping information.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.Version">
      <summary>
        <para>Indicates the version of the PDF specification to which the document conforms if later than the version specified in the file&#39;s header.</para>
      </summary>
      <value>A DevExpress.Pdf.PdfFileVersion object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocument.ViewerPreferences">
      <summary>
        <para>Provides access to the viewer preferences dictionary specifying how to display the document on the screen.</para>
      </summary>
      <value>A DevExpress.Pdf.PdfViewerPreferences object.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfDocumentActions">
      <summary>
        <para>Represents an action that is performed with a document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfDocumentActions.#ctor(DevExpress.Pdf.PdfDocument)">
      <summary>
        <para>Initializes a new PdfDocumentActions object with the specified settings.</para>
      </summary>
      <param name="document">A target document.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocumentActions.DocumentClosing">
      <summary>
        <para>Gets an action performed when the document is closing.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfJavaScriptAction"/> object that represents the document closing action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocumentActions.DocumentPrinted">
      <summary>
        <para>Gets an action performed when the document is printed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfJavaScriptAction"/> object that represents the action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocumentActions.DocumentPrinting">
      <summary>
        <para>Gets an action that is performed when the document is printing.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfJavaScriptAction"/> object that represents the action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocumentActions.DocumentSaved">
      <summary>
        <para>Gets an action performed when the document is saved.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfJavaScriptAction"/> object that represents the action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocumentActions.DocumentSaving">
      <summary>
        <para>Gets an action that is performed when the document is saving.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfJavaScriptAction"/> object that represents the action.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfDocumentArea">
      <summary>
        <para>A document area.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfDocumentArea.#ctor(System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfDocumentArea"/> class with the specified settings.</para>
      </summary>
      <param name="pageNumber">An integer value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfDocumentArea.PageNumber"/> property.</param>
      <param name="area">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfDocumentArea.Area"/> property.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocumentArea.Area">
      <summary>
        <para>Returns a document area corresponding to the specified rectangle.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfDocumentArea.Create(DevExpress.Pdf.PdfDocumentPosition,DevExpress.Pdf.PdfDocumentPosition)">
      <summary>
        <para>Creates a document area based on the specified start and end positions.</para>
      </summary>
      <param name="position1">A <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> object.</param>
      <param name="position2">A <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> object.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfDocumentArea"/> object.</returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocumentArea.PageNumber">
      <summary>
        <para>Indicates the page number corresponding to the current document area.</para>
      </summary>
      <value>An integer value that is the page number.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfDocumentContent">
      <summary>
        <para>Provides information about the PDF content type at a specific client point.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfDocumentContent.#ctor(DevExpress.Pdf.PdfDocumentPosition,DevExpress.Pdf.PdfDocumentContentType,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfDocumentContent"/> class with the specified settings.</para>
      </summary>
      <param name="documentPosition">A <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> object. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfDocumentContent.DocumentPosition"/> property.</param>
      <param name="contentType">A <see cref="T:DevExpress.Pdf.PdfDocumentContentType"/> enumeration value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfDocumentContent.ContentType"/> property.</param>
      <param name="selected">true if the document content has been selected; otherwise false. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfDocumentContent.IsSelected"/> property.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocumentContent.ContentType">
      <summary>
        <para>Indicates the type of the PDF content corresponding to a specific document point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfDocumentContentType"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocumentContent.DocumentPosition">
      <summary>
        <para>Indicates the document position corresponding to the PDF content.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocumentContent.IsSelected">
      <summary>
        <para>Indicates whether or not the PDF content has been selected.</para>
      </summary>
      <value>true if the document content has been selected; otherwise false.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfDocumentContentType">
      <summary>
        <para>Lists the values indicating the type of the PDF content corresponding to a specific document point.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentContentType.Annotation">
      <summary>
        <para>The PDF content is an annotation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentContentType.Image">
      <summary>
        <para>The PDF content is an image.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentContentType.None">
      <summary>
        <para>The PDF content is not defined.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentContentType.Text">
      <summary>
        <para>The PDF content is text.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfDocumentDataExtractionPermissions">
      <summary>
        <para>Lists the values specifying permissions that are used to restrict or allow access to data extraction operations.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentDataExtractionPermissions.Accessibility">
      <summary>
        <para>Allow PDF Viewers to access document contents by using the Viewer&#39;s accessibility features.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentDataExtractionPermissions.Allowed">
      <summary>
        <para>Permit data extraction operations (copying or text and graphics extraction from the document) including access for the software that uses assistive technologies.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentDataExtractionPermissions.NotAllowed">
      <summary>
        <para>Prohibit data extraction operations (copying or text and graphics extraction from the document) including access for the software that uses assistive technologies.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfDocumentInteractivityPermissions">
      <summary>
        <para>Lists the values specifying permissions that are used to restrict or allow access to document interaction operations.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentInteractivityPermissions.Allowed">
      <summary>
        <para>Permit interactive operations (adding or modifying text annotations, filling in interactive form fields, and creating or modifying interactive form fields) in the PDF document.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentInteractivityPermissions.FormFillingAndSigning">
      <summary>
        <para>Prohibit interactive operations in the PDF document except filling existing form fields and document signing.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentInteractivityPermissions.NotAllowed">
      <summary>
        <para>Prohibit all interactive operations (adding or modifying text annotations, filling in interactive form fields, and creating or modifying interactive form fields) in the PDF document.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfDocumentModificationPermissions">
      <summary>
        <para>Lists the values specifying permissions that are used to restrict or allow access to document modification operations.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentModificationPermissions.Allowed">
      <summary>
        <para>Permit document modification and assembling.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentModificationPermissions.DocumentAssembling">
      <summary>
        <para>Allow only document assembling such as inserting, rotating or deleting pages, as well as bookmark creation on the navigation pane.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentModificationPermissions.NotAllowed">
      <summary>
        <para>Prohibit document modification and assembling.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfDocumentPosition">
      <summary>
        <para>Represents a point in the page coordinates of the PDF document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfDocumentPosition.#ctor(System.Int32,DevExpress.Pdf.PdfPoint)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> class with the specified settings.</para>
      </summary>
      <param name="pageNumber">An integer value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfDocumentPosition.PageNumber"/> property.</param>
      <param name="point">A <see cref="T:DevExpress.Pdf.PdfPoint"/> structure. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfDocumentPosition.Point"/> property.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocumentPosition.PageNumber">
      <summary>
        <para>Indicates the number of a page corresponding to the hit point.</para>
      </summary>
      <value>An integer value that is the page number.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfDocumentPosition.Point">
      <summary>
        <para>Returns the hit point coordinates in a PDF.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPoint"/> structure.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfDocumentPrintingPermissions">
      <summary>
        <para>Lists the values specifying permissions that are used to restrict or allow access to document printing operations.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentPrintingPermissions.Allowed">
      <summary>
        <para>Permit document printing.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentPrintingPermissions.LowQuality">
      <summary>
        <para>Prohibit document printing at the highest quality level.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfDocumentPrintingPermissions.NotAllowed">
      <summary>
        <para>Prohibit document printing.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfDocumentProcessorHelper">
      <summary>
        <para>This class supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfEncryptionAlgorithm">
      <summary>
        <para>Lists the available algorithms to encrypt a document.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfEncryptionAlgorithm.AES128">
      <summary>
        <para>Use the 128-bit AES (Advanced Encryption Standard) algorithm.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfEncryptionAlgorithm.AES256">
      <summary>
        <para>Use the 256-bit AES (Advanced Encryption Standard) algorithm.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfEncryptionAlgorithm.ARC4">
      <summary>
        <para>Use the 128-bit ARC4 algorithm.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfEncryptionOptions">
      <summary>
        <para>Contains settings to protect a PDF document with a password and user permissions.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfEncryptionOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfEncryptionOptions"/> class without encryption settings specified for a PDF document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfEncryptionOptions.Algorithm">
      <summary>
        <para>Specifies an algorithm to encrypt a PDF document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfEncryptionAlgorithm"/> enumeration value that specifies an encryption algorithm.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfEncryptionOptions.DataExtractionPermissions">
      <summary>
        <para>Specifies the permissions on data extraction operations.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfDocumentDataExtractionPermissions"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfEncryptionOptions.InteractivityPermissions">
      <summary>
        <para>Specifies the permissions on interaction operations.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfDocumentInteractivityPermissions"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfEncryptionOptions.ModificationPermissions">
      <summary>
        <para>Specifies the permissions on document modification operations.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfDocumentModificationPermissions"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfEncryptionOptions.OwnerPassword">
      <summary>
        <para>This property is now obsolete. To specify an owner password, use the <see cref="P:DevExpress.Pdf.PdfEncryptionOptions.OwnerPasswordString"/> property instead.</para>
      </summary>
      <value>A <see cref="T:System.Security.SecureString"/> object that is an owner password.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfEncryptionOptions.OwnerPasswordString">
      <summary>
        <para>Specifies an owner password that is used to allow full access to a document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that is an owner password.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfEncryptionOptions.PrintingPermissions">
      <summary>
        <para>Specifies the permissions on printing operations.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfDocumentPrintingPermissions"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfEncryptionOptions.UserPassword">
      <summary>
        <para>This property is now obsolete. To specify a user password, use the <see cref="P:DevExpress.Pdf.PdfEncryptionOptions.UserPasswordString"/> property instead.</para>
      </summary>
      <value>A <see cref="T:System.Security.SecureString"/> object that is a user password.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfEncryptionOptions.UserPasswordString">
      <summary>
        <para>Specifies a user password that is used to protect opening the document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that is a user password.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfFileAttachment">
      <summary>
        <para>Represents an attachment where a file can be embedded to a PDF document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfFileAttachment.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfFileAttachment"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileAttachment.CreationDate">
      <summary>
        <para>Gets or sets date when the file attachment was created in the document.</para>
      </summary>
      <value>A nullable <see cref="T:System.DateTimeOffset"/> structure that is a valid date-time offset.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileAttachment.Data">
      <summary>
        <para>Gets or sets the data of the attached file represented as a byte array.</para>
      </summary>
      <value>The file content data represented as a byte array.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileAttachment.Description">
      <summary>
        <para>Specifies the description for the attached file shown in the Attachments panel of a PDF Viewer.</para>
      </summary>
      <value>A string that is an attached file description.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileAttachment.FileName">
      <summary>
        <para>Gets or sets the attached file name.</para>
      </summary>
      <value>A string value that is the name of an attached file.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileAttachment.MimeType">
      <summary>
        <para>Gets or sets the MIME type (content type) of the attached file.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that represents the MIME type of the attached file.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileAttachment.ModificationDate">
      <summary>
        <para>Gets or sets the date and time of the attachment file&#39;s last modification.</para>
      </summary>
      <value>A nullable <see cref="T:System.DateTimeOffset"/> value that is the date and time of the attachment file&#39;s last modification.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileAttachment.Relationship">
      <summary>
        <para>Gets or sets the relationship between the document and the attachment file.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAssociatedFileRelationship"/> enumeration value that specifies the relationship between the document and the attachment file.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileAttachment.Size">
      <summary>
        <para>Gets the size of the file attachment.</para>
      </summary>
      <value>An integer value that is the file attachment size, in bytes.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfFileSpecification">
      <summary>
        <para>Represents the file specification that gives the name of the target file in a standard format, and can also contain information related to one or more specific file systems.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileSpecification.CreationDate">
      <summary>
        <para>Gets the date and time when the file was created.</para>
      </summary>
      <value>A nullable <see cref="T:System.DateTimeOffset"/> structure that is a valid date-time offset.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileSpecification.Description">
      <summary>
        <para>Gets the descriptive text associated with the file specification.</para>
      </summary>
      <value>A string that is a file specification description.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileSpecification.FileData">
      <summary>
        <para>Gets the data of the file specification represented as a byte array.</para>
      </summary>
      <value>The file content data represented as a byte array.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileSpecification.FileName">
      <summary>
        <para>Returns the file name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the file name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileSpecification.FileSystem">
      <summary>
        <para>Gets the name of the file system that is used to interpret this file specification.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the name of the file system.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileSpecification.Index">
      <summary>
        <para>Gets the zero-based index associated with the file specification.</para>
      </summary>
      <value>An integer value that is the zero-based index associated with the file specification.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileSpecification.MimeType">
      <summary>
        <para>Gets the MIME type (content type) of the file.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that represents the MIME type of the file.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileSpecification.ModificationDate">
      <summary>
        <para>Gets the date and time of the file&#39;s last modification.</para>
      </summary>
      <value>A nullable <see cref="T:System.DateTimeOffset"/> value that is the date and time of the file&#39;s last modification.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileSpecification.Relationship">
      <summary>
        <para>Gets the relationship between the document and the file.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAssociatedFileRelationship"/> enumeration value that specifies the relationship between the document and the file.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFileSpecification.Size">
      <summary>
        <para>Gets the size of the file attachment.</para>
      </summary>
      <value>An integer value that is the file attachment size, in bytes.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfFilter">
      <summary>
        <para>Represents an image filter.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfFitBBoxDestination">
      <summary>
        <para>Represents a destination that displays the page with its contents magnified just enough to fit its bounding box entirely within the window both horizontally and vertically.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfFitBBoxDestination.#ctor(DevExpress.Pdf.PdfPage)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfFitBBoxDestination"/> class with the page that contains the content magnified just enough to fit its bounding box entirely within the window.</para>
      </summary>
      <param name="page">A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the PDF page.</param>
    </member>
    <member name="T:DevExpress.Pdf.PdfFitBBoxHorizontallyDestination">
      <summary>
        <para>Represents a destination that displays the page with its vertical <see cref="P:DevExpress.Pdf.PdfFitBBoxHorizontallyDestination.Top"/> coordinate positioned at the top edge of the window and the contents magnified just enough to fit the entire width of its bounding box within the window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfFitBBoxHorizontallyDestination.#ctor(DevExpress.Pdf.PdfPage,System.Nullable{System.Double})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfFitBBoxHorizontallyDestination"/> class with the specified page and the top vertical coordinate positioned at the top edge of the window.</para>
      </summary>
      <param name="page">A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the PDF page.</param>
      <param name="top">A nullable double value that is the top vertical page coordinate.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfFitBBoxHorizontallyDestination.Top">
      <summary>
        <para>Gets the top vertical page coordinate positioned at the top edge of the window.</para>
      </summary>
      <value>A nullable double value that it the vertical page coordinate.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfFitBBoxVerticallyDestination">
      <summary>
        <para>Represents a destination that displays the page with the horizontal <see cref="P:DevExpress.Pdf.PdfFitBBoxVerticallyDestination.Left"/> coordinate positioned at the left edge of the window with its contents magnified just enough to fit the entire height of its bounding box within the window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfFitBBoxVerticallyDestination.#ctor(DevExpress.Pdf.PdfPage,System.Nullable{System.Double})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfFitBBoxVerticallyDestination"/> class with the specified page and the left horizontal coordinate positioned at the left edge of the window.</para>
      </summary>
      <param name="page">A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the PDF page.</param>
      <param name="left">A nullable double value that is the left horizontal page coordinate.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfFitBBoxVerticallyDestination.Left">
      <summary>
        <para>Gets the left horizontal coordinate positioned at the left edge of the window.</para>
      </summary>
      <value>A nullable double value that it the left horizontal coordinate.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfFitDestination">
      <summary>
        <para>Represents a destination that displays the page with its contents magnified just enough to fit the entire page within the window both horizontally and vertically.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfFitDestination.#ctor(DevExpress.Pdf.PdfPage)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfFitDestination"/> class with the page that contains the contents magnified just enough to fit the entire page within the window both horizontally and vertically.</para>
      </summary>
      <param name="page">A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the PDF page.</param>
    </member>
    <member name="T:DevExpress.Pdf.PdfFitHorizontallyDestination">
      <summary>
        <para>Represents a destination that displays the page with the <see cref="P:DevExpress.Pdf.PdfFitHorizontallyDestination.Top"/> vertical coordinate positioned at the top edge of the window and the contents magnified just enough to fit the entire width of the page within the window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfFitHorizontallyDestination.#ctor(DevExpress.Pdf.PdfPage,System.Nullable{System.Double})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfFitHorizontallyDestination"/> class with the specified page and the top vertical coordinate positioned at the top edge of the window.</para>
      </summary>
      <param name="page">A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the PDF page with contents.</param>
      <param name="top">A nullable double value that is the top vertical coordinate positioned at the top edge of the window.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfFitHorizontallyDestination.Top">
      <summary>
        <para>Gets the top vertical page coordinate positioned at the top edge of the window.</para>
      </summary>
      <value>A nullable double value that is the top vertical page coordinate.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfFitRectangleDestination">
      <summary>
        <para>Represents a destination that displays the page with its contents magnified just enough to fit the <see cref="P:DevExpress.Pdf.PdfFitRectangleDestination.Rectangle"/> specified by the <see cref="P:DevExpress.Pdf.PdfRectangle.Left"/>, <see cref="P:DevExpress.Pdf.PdfRectangle.Bottom"/>, <see cref="P:DevExpress.Pdf.PdfRectangle.Right"/>, <see cref="P:DevExpress.Pdf.PdfRectangle.Top"/> coordinates entirely within the window both horizontally and vertically.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfFitRectangleDestination.#ctor(DevExpress.Pdf.PdfPage,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfFitRectangleDestination"/> class with the specified page and rectangle specified by the coordinates left, bottom, right, and top.</para>
      </summary>
      <param name="page">A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the page with its contents.</param>
      <param name="rectangle">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> that is the rectangle specified by the coordinates left, bottom, right, and top.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfFitRectangleDestination.Rectangle">
      <summary>
        <para>Gets a rectangle specified by the coordinates left, bottom, right, and top to place the page entirely within the window both horizontally and vertically.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRectangle"/> that is a rectangle specified by the coordinates left, bottom, right, and top.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfFitVerticallyDestination">
      <summary>
        <para>Represents a destination that displays the page with the horizontal <see cref="P:DevExpress.Pdf.PdfFitVerticallyDestination.Left"/> coordinate positioned at the left edge of the window  and the contents magnified just enough to fit the entire height of the page within the window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfFitVerticallyDestination.#ctor(DevExpress.Pdf.PdfPage,System.Nullable{System.Double})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfFitVerticallyDestination"/> class with the specified page and the left horizontal coordinate positioned at the left edge of the window.</para>
      </summary>
      <param name="page">A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the PDF page with the contents.</param>
      <param name="left">A nullable double value that is the left horizontal page coordinate.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfFitVerticallyDestination.Left">
      <summary>
        <para>Gets the horizontal left coordinate positioned at the left edge of the window.</para>
      </summary>
      <value>A nullable double value that it the left horizontal coordinate.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfFont">
      <summary>
        <para>A font assigned to a document text.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfFont.BaseFont">
      <summary>
        <para>The PostScript name of the font.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFont.FontDescriptor">
      <summary>
        <para>Provides access to the PDF font options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfFontDescriptor"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFont.FontName">
      <summary>
        <para>The PostScript name of the font.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the font name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFont.SubsetName">
      <summary>
        <para>Specifies a font subset.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFont.ToUnicode">
      <summary>
        <para>Converts PDF character codes to Unicode values.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfCharacterMapping"/> object, providing a CMap table that maps character codes to Unicode values.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfFontDescriptor">
      <summary>
        <para>Provides the PDF font options.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.Ascent">
      <summary>
        <para>Indicates the maximum height above the baseline reached by glyphs in this font.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.AvgWidth">
      <summary>
        <para>Indicates the average width of glyphs in the font.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.CapHeight">
      <summary>
        <para>The vertical coordinate of the top of flat capital letters, measured from the baseline.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.CharSet">
      <summary>
        <para>Indicates the character names defined in a font subset.</para>
      </summary>
      <value>A list of <see cref="T:System.String"/> values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.CIDMapping">
      <summary>
        <para>Provides access to a dictionary identifying which CIDs are present in the CIDFont file.</para>
      </summary>
      <value>A dictionary, containing the System.Int16 objects, along with their <see cref="T:System.Int16"/> key values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.Descent">
      <summary>
        <para>Indicates the maximum depth below the baseline reached by glyphs in this font.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.Flags">
      <summary>
        <para>Provides access to a collection of flags defining various characteristics of the font.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfFontFlags"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.FontBBox">
      <summary>
        <para>Indicates a rectangle, expressed in the glyph coordinate system, that shall specify the font bounding box.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.FontFamily">
      <summary>
        <para>Indicates the preferred font family name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.FontName">
      <summary>
        <para>Indicates the PostScript name of the font.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.FontStretch">
      <summary>
        <para>Indicates the font stretch value.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfFontStretch"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.FontWeight">
      <summary>
        <para>Indicates the weight (thickness) component of the fully-qualified font name or font specifier.</para>
      </summary>
      <value>An integer value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.ItalicAngle">
      <summary>
        <para>The angle, expressed in degrees counterclockwise from the vertical, of the dominant vertical strokes of the font.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.Leading">
      <summary>
        <para>Indicates the spacing between baselines of consecutive lines of text.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.MaxWidth">
      <summary>
        <para>Indicates the maximum width of glyphs in the font.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.MissingWidth">
      <summary>
        <para>The width to use for character codes whose widths are not specified in a font dictionary&#39;s Widths array.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.StemH">
      <summary>
        <para>Indicates the thickness, measured vertically, of the dominant horizontal stems of glyphs in the font.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.StemV">
      <summary>
        <para>Indicates the thickness, measured horizontally, of the dominant vertical stems of glyphs in the font.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFontDescriptor.XHeight">
      <summary>
        <para>Indicates the font&#39;s X-height: the vertical coordinate of the top of flat nonascending lowercase letters (like the letter x), measured from the baseline, in fonts that have Latin characters.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfFontFlags">
      <summary>
        <para>Lists the values that correspond to flags defining various characteristics of the font.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontFlags.AllCap">
      <summary>
        <para>&quot;0x10000&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontFlags.FixedPitch">
      <summary>
        <para>&quot;0x00001&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontFlags.ForceBold">
      <summary>
        <para>&quot;0x40000&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontFlags.Italic">
      <summary>
        <para>&quot;0x00040&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontFlags.None">
      <summary>
        <para>&quot;0x00000&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontFlags.Nonsymbolic">
      <summary>
        <para>&quot;0x00020&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontFlags.Script">
      <summary>
        <para>&quot;0x00008&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontFlags.Serif">
      <summary>
        <para>&quot;0x00002&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontFlags.SmallCap">
      <summary>
        <para>&quot;0x20000&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontFlags.Symbolic">
      <summary>
        <para>&quot;0x00004&quot;</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfFontStretch">
      <summary>
        <para>Lists the font stretch values.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStretch.Condensed">
      <summary>
        <para>&quot;Condensed&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStretch.Expanded">
      <summary>
        <para>&quot;Expanded&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStretch.ExtraCondensed">
      <summary>
        <para>&quot;ExtraCondensed&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStretch.ExtraExpanded">
      <summary>
        <para>&quot;ExtraExpanded&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStretch.Normal">
      <summary>
        <para>&quot;Normal&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStretch.SemiCondensed">
      <summary>
        <para>&quot;SemiCondensed&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStretch.SemiExpanded">
      <summary>
        <para>&quot;SemiExpanded&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStretch.UltraCondensed">
      <summary>
        <para>&quot;UltraCondensed&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStretch.UltraExpanded">
      <summary>
        <para>&quot;UltraExpanded&quot;</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfFontStyle">
      <summary>
        <para>Lists the form field&#39;s font styles.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStyle.Bold">
      <summary>
        <para>Bold text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStyle.Italic">
      <summary>
        <para>Italic text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStyle.Regular">
      <summary>
        <para>Normal text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStyle.Strikeout">
      <summary>
        <para>Text with a line through the middle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFontStyle.Underline">
      <summary>
        <para>Underlined text.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfForm">
      <summary>
        <para>Represents a PDF document form.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfForm.BBox">
      <summary>
        <para>Gets a document rectangle in the form coordinate system of the form&#39;s bounding box.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that is the document rectangle.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfForm.Commands">
      <summary>
        <para>Returns the PDF form commands.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfCommandList"/> objects.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfForm.LastModified">
      <summary>
        <para>Gets the modification date to be sure which of the application data dictionaries it contains corresponds to the current content of the form.</para>
      </summary>
      <value>A nullable  <see cref="T:System.DateTimeOffset"/> structure that is a valid date-time offset.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfForm.Matrix">
      <summary>
        <para>Returns a transformation matrix which maps the form space into user space.</para>
      </summary>
      <value>A DevExpress.Pdf.PdfTransformationMatrix object that is the PDF transformation matrix.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfForm.PieceInfo">
      <summary>
        <para>Gets a page-piece dictionary which holds private product data.</para>
      </summary>
      <value>A dictionary, containing the DevExpress.Pdf.PdfPieceInfoEntry objects, along with their <see cref="T:System.String"/> key values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfForm.StructParents">
      <summary>
        <para>Gets an integer key of the form entry in the structural parents tree.</para>
      </summary>
      <value>A nullable integer value specifying the key of the form entry.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfFormData">
      <summary>
        <para>Contains data values stored in the PDF Form.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfFormData.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfFormData"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfFormData.#ctor(System.IO.Stream)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfFormData"/> class with the predefined stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> class descendant containing a file with the interactive form data (Fdf, Txt, Xfdf or Xml format).</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfFormData.#ctor(System.IO.Stream,DevExpress.Pdf.PdfFormDataFormat)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfFormData"/> class with the predefined stream and data format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> class descendant containing a file with the form data (Fdf, Txt, Xfdf or Xml format).</param>
      <param name="format">A <see cref="T:DevExpress.Pdf.PdfFormDataFormat"/> enumeration value specifying the format in which interactive form values are stored.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfFormData.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfFormData"/> class with the predefined file name.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> containing the path to a file with the interactive form data (Fdf, Txt, Xfdf or Xml format).</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfFormData.#ctor(System.String,DevExpress.Pdf.PdfFormDataFormat)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfFormData"/> class with the predefined file name and data format.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> containing the path to a file with the interactive form data (Fdf, Txt, Xfdf or Xml format).</param>
      <param name="format">A <see cref="T:DevExpress.Pdf.PdfFormDataFormat"/> enumeration value specifying the format in which interactive form values are stored.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfFormData.GetFieldNames">
      <summary>
        <para>Returns the name of all fields contained in this PDF form.</para>
      </summary>
      <returns>A list of <see cref="T:System.String"/> values.</returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfFormData.Item(System.String)">
      <summary>
        <para>Returns an individual field by its name on the PDF Form.</para>
      </summary>
      <param name="name">A string representing the field name.</param>
      <value>A <see cref="T:DevExpress.Pdf.PdfFormData"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfFormData.Name">
      <summary>
        <para>Returns the name of the field represented by this <see cref="T:DevExpress.Pdf.PdfFormData"/> object.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfFormData.Save(System.IO.Stream,DevExpress.Pdf.PdfFormDataFormat)">
      <summary>
        <para>Saves the interactive form data to a stream using form data format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> class descendant, specifying the stream into which the interactive form should be saved.</param>
      <param name="format">A <see cref="T:DevExpress.Pdf.PdfFormDataFormat"/> enumeration value, specifying into which format interactive form values should be saved.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfFormData.Save(System.String,DevExpress.Pdf.PdfFormDataFormat)">
      <summary>
        <para>Saves the interactive form data into a file using form data format.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> specifying the file path and file name.</param>
      <param name="format">A <see cref="T:DevExpress.Pdf.PdfFormDataFormat"/> enumeration value, specifying into which format interactive form values should be saved.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfFormData.Value">
      <summary>
        <para>Specifies a value to the interactive form field represented by this <see cref="T:DevExpress.Pdf.PdfFormData"/> object.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> class descendant.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfFormDataFormat">
      <summary>
        <para>Lists formats allowed for PDF Form data values.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFormDataFormat.Fdf">
      <summary>
        <para>Data is represented as FDF (Forms Data Format).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFormDataFormat.Txt">
      <summary>
        <para>Data is represented as text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFormDataFormat.Xfdf">
      <summary>
        <para>Data is represented as XFDF (XML Forms Data Format).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfFormDataFormat.Xml">
      <summary>
        <para>Data is represented as XML.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfGoToAction">
      <summary>
        <para>An action that changes the view to a specified destination (page, location, and magnification factor).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGoToAction.#ctor(DevExpress.Pdf.PdfDocument,DevExpress.Pdf.PdfDestination)">
      <summary>
        <para>Initializes a new PdfGoToAction object with specified settings.</para>
      </summary>
      <param name="document">A target document.</param>
      <param name="destination">A PdfDestination object that is a destination (a particular view of a document).</param>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphicsTextOrigin">
      <summary>
        <para>Contains values that specify how to interpret a point passed to one of the PdfGraphics.DrawString overload methods that take a PointF object as an argument.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfGraphicsTextOrigin.Baseline">
      <summary>
        <para>The point passed to one of the PdfGraphics.DrawString methods that take a PointF object is on the text baseline.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfGraphicsTextOrigin.TopLeftCorner">
      <summary>
        <para>The point passed to one the PdfGraphics.DrawString methods that take a PointF object is the top left corner of the text bounding rectangle.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfHotkeyPrefix">
      <summary>
        <para>Specifies the type of display for hot-key prefixes that relate to text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfHotkeyPrefix.Hide">
      <summary>
        <para>Do not display the hot-key prefix.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfHotkeyPrefix.None">
      <summary>
        <para>No hot-key prefix.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfIconFit">
      <summary>
        <para>Contains properties that define how to display the button&#39;s icon within the annotation rectangle of its widget annotation.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfIconFit.FitToAnnotationBounds">
      <summary>
        <para>Gets a value that indicates whether the button appearance is scaled to fit fully within the bounds of the annotation without taking into consideration the line width of the border.</para>
      </summary>
      <value>true, to fit fully within the bounds of the annotation otherwise, false. The default value is false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfIconFit.HorizontalPosition">
      <summary>
        <para>Gets the horizontal leftover of the icon within an annotation rectangle.</para>
      </summary>
      <value>A double value that is the horizontal leftover of the icon within an annotation rectangle.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfIconFit.ScalingCircumstances">
      <summary>
        <para>Gets the circumstances under which the icon shall be scaled inside the annotation rectangle.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfIconScalingCircumstances"/> enumeration value that lists the circumstances for scaling an icon.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfIconFit.ScalingType">
      <summary>
        <para>Get the type of icon scaling.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfIconScalingType"/> enumeration value that is the icon scaling type.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfIconFit.VerticalPosition">
      <summary>
        <para>Gets the vertical leftover of the icon within an annotation rectangle.</para>
      </summary>
      <value>A double value that is the vertical icon leftover within the annotation rectangle.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfIconScalingCircumstances">
      <summary>
        <para>Lists the circumstances under which the icon will be scaled inside the annotation rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfIconScalingCircumstances.Always">
      <summary>
        <para>Always scale.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfIconScalingCircumstances.BiggerThanAnnotationRectangle">
      <summary>
        <para>Scale only when the icon is bigger than the annotation rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfIconScalingCircumstances.Never">
      <summary>
        <para>Never scale.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfIconScalingCircumstances.SmallerThanAnnotationRectangle">
      <summary>
        <para>Scale only when the icon is smaller than the annotation rectangle.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfIconScalingType">
      <summary>
        <para>Lists the type of icon scaling within the annotation rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfIconScalingType.Anamorphic">
      <summary>
        <para>Scale the icon to fill the annotation rectangle exactly, without regard to its original aspect ratio.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfIconScalingType.Proportional">
      <summary>
        <para>Scale the icon to fit the width or height of the annotation rectangle while maintaining the icon&#39;s original aspect ratio.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfImage">
      <summary>
        <para>Represents an image in the PDF document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.BitsPerComponent">
      <summary>
        <para>Gets the number of bits used to represent each color component.</para>
      </summary>
      <value>An integer value that is the number of bits used to represent each color component.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.ColorKeyMask">
      <summary>
        <para>Gets a color key mask.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfRange"/> objects that represents a range of colors to be masked out.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.ColorSpace">
      <summary>
        <para>Gets the color space in which image samples shall be specified.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfColorSpace"/> object that is the color space required for an image.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.Data">
      <summary>
        <para>Gets the image data.</para>
      </summary>
      <value>The image data represented as a byte array.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.Decode">
      <summary>
        <para>Gets a collection of numbers describing how to map image samples into the range of values appropriate for the image&#39;s color space.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfRange"/> objects.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.Filters">
      <summary>
        <para>Gets image filters.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfFilter"/> objects.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.Height">
      <summary>
        <para>Gets the height of the image, in pixels.</para>
      </summary>
      <value>An integer value that is the image height.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.Intent">
      <summary>
        <para>Gets the name of a color rendering intent to be used in rendering the image.</para>
      </summary>
      <value>A nullable <see cref="T:DevExpress.Pdf.PdfRenderingIntent"/> enumeration value that is the name of a color rendering intent.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.Interpolate">
      <summary>
        <para>Gets a value that indicates whether image interpolation shall be performed.</para>
      </summary>
      <value>true, if image interpolation should be performed while rendering this image; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.IsMask">
      <summary>
        <para>Gets a value that determines whether an image is a stencil mask image.</para>
      </summary>
      <value>true, if an image is a stencil mask image; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.Mask">
      <summary>
        <para>An image XObject defining an image mask to be applied to this image.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfImage"/> that is an image mask to be applied to this image.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.Matte">
      <summary>
        <para>Gets a collection of component values specifying the matte color with which the image data in the parent image shall have been preblended.</para>
      </summary>
      <value>A collection of <see cref="T:System.Double"/> objects.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.SMask">
      <summary>
        <para>Gets a subsidiary image XObject defining a soft-mask image that shall be used as a source of the mask shape or mask opacity values in the transparent imaging model.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfImage"/> object that represents a subsidiary image XObject defining a soft-mask image.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfImage.Width">
      <summary>
        <para>Gets the width of the image, in pixels</para>
      </summary>
      <value>An integer value that is the image width.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfInteractiveForm">
      <summary>
        <para>An interactive form (AcroForm) that represents a collection of fields for gathering information interactively from the user.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveForm.DefaultAppearanceCommands">
      <summary>
        <para>Gets a collection of default appearance commands.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfCommand"/> objects.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveForm.DefaultTextJustification">
      <summary>
        <para>Gets the default justification that is used in displaying the annotation&#39;s text.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfTextJustification"/> enumeration value that represents the default text justification.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveForm.Fields">
      <summary>
        <para>Gets the document&#39;s root fields.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfInteractiveFormField"/> objects that contain the document&#39;s root fields.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveForm.NeedAppearances">
      <summary>
        <para>Gets a value that indicates whether to construct appearance streams and appearance dictionaries for all widget annotations in the document.</para>
      </summary>
      <value>true to construct appearance streams and appearance dictionaries for all widget annotations; otherwise false. Default value: false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveForm.SignatureFlags">
      <summary>
        <para>Gets flags specifying various document-level characteristics related to signature fields.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfSignatureFlags"/> enumeration values that represent the signature flags.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveForm.XFAForm">
      <summary>
        <para>Gets an XFA form.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfXFAForm"/> object that represents the XFA form.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfInteractiveFormField">
      <summary>
        <para>Represents interactive form field  data (e.g., text boxes, radio buttons, combo boxes) in a document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormField.Actions">
      <summary>
        <para>Provides access to actions that define the field&#39;s behavior in response to various trigger events.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfInteractiveFormFieldActions"/> object that contains interactive form fields actions.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormField.AlternateName">
      <summary>
        <para>Gets an alternate name specified for an interactive form field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the alternate name for an interactive form field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormField.AppearanceCommands">
      <summary>
        <para>Gets the appearance commands for an interactive form field.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfCommand"/> objects that represent appearance commands.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormField.DefaultStyle">
      <summary>
        <para>Gets the default style string.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that represents the name of the default style applied to the interactive form.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormField.Flags">
      <summary>
        <para>Gets or sets flags specifying various document-level characteristics related to interactive form fields.</para>
      </summary>
      <value><see cref="T:DevExpress.Pdf.PdfInteractiveFormFieldFlags"/> enumeration values that represent flags specifying various document-level characteristics.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormField.Form">
      <summary>
        <para>Provides access to the contents and properties of a document&#39;s interactive form.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfInteractiveForm"/> object that represents the document&#39;s interactive form.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormField.Kids">
      <summary>
        <para>Gets the collection of interactive form field children.</para>
      </summary>
      <value>A collection of the <see cref="T:DevExpress.Pdf.PdfInteractiveFormField"/> objects containing the immediate children of the interactive form field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormField.MappingName">
      <summary>
        <para>Gets the mapping name that is used when exporting interactive form field data from the document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that represents the mapping name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormField.Name">
      <summary>
        <para>Gets the partial field name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the partial field name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormField.Parent">
      <summary>
        <para>Gets the parent of the field in the interactive form field hierarchy.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfInteractiveFormField"/> object that represents the parent of the interactive form field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormField.RichTextData">
      <summary>
        <para>Gets the rich text string that contains formatting (style) information.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the rich text data.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormField.TextJustification">
      <summary>
        <para>Gets the form of quadding (justification) that is used in displaying the text.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfTextJustification"/> object that represents the form of text justification.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormField.Widget">
      <summary>
        <para>Gets the widget annotation associated with the interactive form field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfWidgetAnnotation"/> object that is the widget annotation.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfInteractiveFormFieldActions">
      <summary>
        <para>Represents an action that is performed with the interactive form fields.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormFieldActions.CharacterChanged">
      <summary>
        <para>Gets a JavaScript action that is executed when the user modifies a character in a text field or combo box or modifies the selection in a scrollable list box.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfJavaScriptAction"/> object that represents the JavaScript action that is executed when the user modifies a character.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormFieldActions.FieldFormatting">
      <summary>
        <para>Gets a JavaScript action that is executed before the field is formatted to display its value.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfJavaScriptAction"/> object that represents the JavaScript action that is executed before the field is formatted to display its value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormFieldActions.FieldValueRecalculating">
      <summary>
        <para>Gets a JavaScript action that is performed  to recalculate the value of this field when another field is changed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfJavaScriptAction"/> object that represents the JavaScript action performed to recalculate the value of this field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfInteractiveFormFieldActions.FiledValueChanged">
      <summary>
        <para>Gets a JavaScript action that is performed when the field&#39;s value is changed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfJavaScriptAction"/> object that represents the JavaScript action performed when the field&#39;s value is changed.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfInteractiveFormFieldFlags">
      <summary>
        <para>Represents flags specifying various document-level characteristics related to interactive form fields.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.Comb">
      <summary>
        <para>This flag is set only if the <see cref="P:DevExpress.Pdf.PdfTextFormField.MaxLen"/> entry is present in the text field dictionary and if the <see cref="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.Multiline"/>, <see cref="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.Password"/>, and <see cref="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.FileSelect"/> flags are clear. If this flag is active, the field shall be automatically divided into as many equally spaced positions, or combs, as the value of MaxLen, and the text is laid out into those combs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.Combo">
      <summary>
        <para>If this flag is active, the field is a combo box. Otherwise, the field is a list box.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.CommitOnSelChange">
      <summary>
        <para>If the flag is set, the new value shall be committed once a selection is made. Otherwise, the new value is not committed until the end user exits the field.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.DoNotScroll">
      <summary>
        <para>If this flag is set, the field shall not scroll to accommodate more text than fits within its annotation rectangle. Once the field is full, no further text shall be accepted for interactive form filling.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.DoNotSpellCheck">
      <summary>
        <para>If this flag is set, text entered in the field shall not be spell-checked. This flag shall not be used unless the <see cref="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.Combo"/> and <see cref="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.Edit"/> flags are both set.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.Edit">
      <summary>
        <para>If this flag is set, the combo box shall include an editable text box as well as a drop-down list. Otherwise, the combo box shall include only a drop-down list. This flag shall be used only if the <see cref="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.Combo"/> flag is set.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.FileSelect">
      <summary>
        <para>If this flag is set, the text entered in the field represents the pathname of a file whose contents shall be submitted as the value of the field.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.Multiline">
      <summary>
        <para>If this flag is set, the field may contain multiple lines of text. Otherwise, the field&#39;s text shall be restricted to a single line.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.MultiSelect">
      <summary>
        <para>If this flag is set, more than one of the field&#39;s option items may be selected simultaneously. Otherwise, only one item shall be selected.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.NoExport">
      <summary>
        <para>If this flag is set, the field shall not be exported by a submit-form action.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.None">
      <summary>
        <para>If active, deactivates all other options.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.NoToggleToOff">
      <summary>
        <para>This flag is used for radio buttons only. If active, exactly one radio button shall be selected at all times; selecting the currently selected button has no effect. Otherwise, clicking the selected button deselects it, leaving no button selected.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.Password">
      <summary>
        <para>If this flag is set, the field is intended for entering a secure password that should not be echoed visibly on the screen. Characters typed from the keyboard shall instead be echoed in an unreadable form, such as asterisks or bullet characters.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.PushButton">
      <summary>
        <para>If this flag is set, the field is a pushbutton that does not retain a permanent value.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.Radio">
      <summary>
        <para>If this flag is set, the field is a set of radio buttons. Otherwise, the field is a check box. This flag may be set only if the <see cref="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.PushButton"/> flag is clear.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.RadiosInUnison">
      <summary>
        <para>If this flag is set, a group of radio buttons within a radio button field that use the same value for the on state will turn on and off in unison; that is if one is checked, they are all checked. Otherwise, the buttons are mutually exclusive.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.ReadOnly">
      <summary>
        <para>If this flag is set, the end user may not change the value of the field. Any associated widget annotations will not interact with the end user. This flag is useful for fields whose values are computed or imported from a database.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.Required">
      <summary>
        <para>If this flag is set, the field shall have a value at the time it is exported by a submit-form action.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.RichText">
      <summary>
        <para>If this flag is set, the value of this field shall be a rich text string.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfInteractiveFormFieldFlags.Sort">
      <summary>
        <para>If the flag is set, the field&#39;s option items shall be sorted alphabetically.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfJavaScriptAction">
      <summary>
        <para>Represents a JavaScript action that executes a script that is written in the JavaScript programming language.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfJavaScriptAction.#ctor(System.String,DevExpress.Pdf.PdfDocument)">
      <summary>
        <para>Initializes a new PdfGoToAction object with specified settings.</para>
      </summary>
      <param name="javaScript">A string value that is the JavaScript code.</param>
      <param name="document">A target document.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfJavaScriptAction.JavaScript">
      <summary>
        <para>Gets a JavaScript script.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the JavaScript script.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfJumpAction">
      <summary>
        <para>Serves as the base for classes that allow jumping to a destination in the current document (<see cref="T:DevExpress.Pdf.PdfGoToAction"/>) or a destination in another PDF file (<see cref="T:DevExpress.Pdf.PdfRemoteGoToAction"/>).</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfJumpAction.Destination">
      <summary>
        <para>Gets the destination that will be displayed when the action is performed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfDestination"/> object that represents the destination.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfLineStyle">
      <summary>
        <para>Provides the style settings used to paint the lines in a document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfLineStyle.CreateDashed(System.Double,System.Double,System.Double)">
      <summary>
        <para>Creates a dashed guideline style using the dash length, gap length, and dash phase.</para>
      </summary>
      <param name="dashLength">A double value that is the dash length.</param>
      <param name="gapLength">A double value that is the gap length.</param>
      <param name="dashPhase">A double value that is the dash phase.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfLineStyle"/> object that is the dashed guideline style.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfLineStyle.CreateDashed(System.Double[],System.Double)">
      <summary>
        <para>Creates a dashed guideline style using the dash pattern and dash phase.</para>
      </summary>
      <param name="dashPattern">An array of double values that represents the dash pattern.</param>
      <param name="dashPhase">A double value that is the dash phase.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfLineStyle"/> object that is the dashed guideline style.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfLineStyle.CreateSolid">
      <summary>
        <para>Create the solid guideline style.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Pdf.PdfLineStyle"/> object that represents the created style.</returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfLineStyle.DashPattern">
      <summary>
        <para>Gets a line dash pattern specified by a dash array and a dash phase.</para>
      </summary>
      <value>An array of double values that represent the line dash pattern.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfLineStyle.DashPhase">
      <summary>
        <para>Gets the dash phase, which specifies the distance into the dash pattern at which to start the dash.</para>
      </summary>
      <value>A double value that is the dash phase.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfLineStyle.IsDashed">
      <summary>
        <para>Gets a value that indicates whether a line is dashed.</para>
      </summary>
      <value>true, if the line is dashed; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfLinkAnnotation">
      <summary>
        <para>Represents the link annotation (a hypertext link to a destination) in a document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfLinkAnnotation.Action">
      <summary>
        <para>Provides access to the link annotation action being executed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that is an action that is performed with the link annotation.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfLinkAnnotation.BorderStyle">
      <summary>
        <para>Gets the annotation border style specifying the line width and dash pattern to be used in drawing the annotation&#39;s border.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAnnotationBorderStyle"/> object that contains properties to specify the annotation border style.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfLinkAnnotation.Destination">
      <summary>
        <para>Gets a destination that will be displayed when the annotation is activated.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfDestination"/> object that is the link annotation destination.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfLinkAnnotation.HighlightingMode">
      <summary>
        <para>Gets the link annotation&#39;s highlighting mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAnnotationHighlightingMode"/> enumeration value that represents the visual effect that will be used when the mouse button is pressed or held down inside its active area.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfLinkAnnotation.Region">
      <summary>
        <para>Gets the region in which the annotation link should be activated.</para>
      </summary>
      <value>A list of <see cref="T:DevExpress.Pdf.PdfQuadrilateral"/> objects that represent the coordinates of quadrilaterals in the default user space that comprises the region.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfLinkAnnotation.UriAction">
      <summary>
        <para>Gets a URI action that is associated with the annotation.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfUriAction"/> object that is the URI action.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfMarkupAnnotation">
      <summary>
        <para>Represents a markup annotation that is used to mark up PDF documents.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotation.CreationDate">
      <summary>
        <para>Gets the date and time when the annotation was created in the document.</para>
      </summary>
      <value>A nullable <see cref="T:System.DateTimeOffset"/> structure that is a valid date-time offset.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotation.FilterType">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotation.InReplyTo">
      <summary>
        <para>Gets an annotation that requires a reply to another annotation.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAnnotation"/> object that represents an annotation that requires a reply to another annotation.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotation.Intent">
      <summary>
        <para>Gets the name that describes the intent of the markup annotation.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the name that describes the markup annotation intent.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotation.Opacity">
      <summary>
        <para>Gets the opacity value that is used in painting the annotation.</para>
      </summary>
      <value>A double value that is the annotation opacity.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotation.Popup">
      <summary>
        <para>Gets a pop-up annotation for entering or editing the text associated with this annotation.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPopupAnnotation"/> object that is the popup annotation.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotation.ReplyType">
      <summary>
        <para>Gets a name specifying the relationship (the reply type) between this annotation and the annotation obtained in the <see cref="P:DevExpress.Pdf.PdfMarkupAnnotation.InReplyTo"/> property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfMarkupAnnotationReplyType"/> enumeration value that represents the annotation reply type.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotation.RichTextData">
      <summary>
        <para>Gets a rich text that is displayed in the pop-up window when the annotation is opened.</para>
      </summary>
      <value>A string value that represents the rich text.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotation.Subject">
      <summary>
        <para>Gets a description of the subject being addressed by the annotation.</para>
      </summary>
      <value>A string value that is the text representing a short description of the subject.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotation.Title">
      <summary>
        <para>Gets the text displayed in the title bar of the annotation&#39;s pop-up window when the annotation is open and active.</para>
      </summary>
      <value>A string value that is the annotation title.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfMarkupAnnotationComment">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationComment.AddReply(System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="author"></param>
      <param name="contents"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationComment.AddReview(System.String,DevExpress.Pdf.PdfAnnotationReviewStatus)">
      <summary>
        <para></para>
      </summary>
      <param name="author"></param>
      <param name="status"></param>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationComment.AddReview(System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="author"></param>
      <param name="status"></param>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationComment.Author">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationComment.ClearReviews">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationComment.Contents">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationComment.Equals(System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="obj"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationComment.GetHashCode">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationComment.ModificationDate">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationComment.RemoveReply(DevExpress.Pdf.PdfMarkupAnnotationComment)">
      <summary>
        <para></para>
      </summary>
      <param name="reply"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationComment.Replies">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationComment.Reviews">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationComment.Subject">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Pdf.PdfMarkupAnnotationData">
      <summary>
        <para>Represents data common to all markup annotations.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationData.AddReply(System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="author"></param>
      <param name="contents"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationData.AddReview(System.String,DevExpress.Pdf.PdfAnnotationReviewStatus)">
      <summary>
        <para></para>
      </summary>
      <param name="author"></param>
      <param name="reviewStatus"></param>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationData.AddReview(System.String,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="author"></param>
      <param name="reviewStatus"></param>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationData.Author">
      <summary>
        <para>Specifies the author of a text markup annotation.</para>
      </summary>
      <value>A string that specifies the text markup annotation&#39;s author.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationData.ClearReviews">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationData.CreationDate">
      <summary>
        <para>Specifies the date and time when the markup annotation was created on the page.</para>
      </summary>
      <value>A nullable <see cref="T:System.DateTimeOffset"/> structure that is the date and time of the markup annotation&#39;s creation on the page.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationData.Opacity">
      <summary>
        <para>Specifies the opacity value that is used in painting the annotation.</para>
      </summary>
      <value>A double value that is the annotation opacity.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationData.RemoveReply(DevExpress.Pdf.PdfMarkupAnnotationComment)">
      <summary>
        <para></para>
      </summary>
      <param name="reply"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationData.Replies">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationData.Reviews">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationData.Subject">
      <summary>
        <para>Specifies a short description of the subject being addressed by the annotation.</para>
      </summary>
      <value>A string value that specifies the text representing a short description of the subject.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfMarkupAnnotationDataExtensions">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationDataExtensions.AsTextAnnotation(DevExpress.Pdf.PdfMarkupAnnotationData)">
      <summary>
        <para></para>
      </summary>
      <param name="data"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationDataExtensions.AsTextMarkupAnnotation(DevExpress.Pdf.PdfMarkupAnnotationData)">
      <summary>
        <para></para>
      </summary>
      <param name="data"></param>
      <returns></returns>
    </member>
    <member name="T:DevExpress.Pdf.PdfMarkupAnnotationReplyType">
      <summary>
        <para>List values that specify the relationship (the &quot;reply type&quot;) between one annotation and another.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfMarkupAnnotationReplyType.Group">
      <summary>
        <para>The annotation is grouped with the annotation obtained in the <see cref="P:DevExpress.Pdf.PdfMarkupAnnotation.InReplyTo"/> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfMarkupAnnotationReplyType.Reply">
      <summary>
        <para>The annotation is considered a reply to the annotation obtained in the <see cref="P:DevExpress.Pdf.PdfMarkupAnnotation.InReplyTo"/> property.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfMarkupAnnotationReview">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationReview.Author">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationReview.Equals(System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="obj"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfMarkupAnnotationReview.GetHashCode">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfMarkupAnnotationReview.Status">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Pdf.PdfMetadata">
      <summary>
        <para>Represents global information about the document such as the document&#39;s title, author, and creation and modification dates.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfMetadata.Data">
      <summary>
        <para>Gets the data for the document components.</para>
      </summary>
      <value>A string value that represents the data of document components.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfNamedAction">
      <summary>
        <para>Represents a named action.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfNamedAction.ActionName">
      <summary>
        <para>The name of the named action that should be performed.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the name of the action.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfNames">
      <summary>
        <para>Stores names of various PDF entities.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfNames.AnnotationAppearances">
      <summary>
        <para>Provides access to a dictionary of annotation appearances.</para>
      </summary>
      <value>A dictionary, containing the <see cref="T:DevExpress.Pdf.PdfAnnotationAppearances"/> objects, along with their <see cref="T:System.String"/> key values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfNames.EmbeddedFiles">
      <summary>
        <para>Gets embedded files in which the destination is located.</para>
      </summary>
      <value>A dictionary, containing the DevExpress.Pdf.PdfFileSpecification objects, along with their <see cref="T:System.String"/> key values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfNames.JavaScriptActions">
      <summary>
        <para>Gets Java Script actions found in the document.</para>
      </summary>
      <value>A dictionary, containing the  <see cref="T:DevExpress.Pdf.PdfJavaScriptAction"/> objects, along with their System.String key values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfNames.PageDestinations">
      <summary>
        <para>Gets page destinations for targets in the document.</para>
      </summary>
      <value>A dictionary, containing the <see cref="T:DevExpress.Pdf.PdfDestination"/> objects, along with their <see cref="T:System.String"/> key values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfNames.PageNames">
      <summary>
        <para>Provides access to a dictionary of page names.</para>
      </summary>
      <value>A dictionary, containing the <see cref="T:DevExpress.Pdf.PdfPage"/> objects, along with their <see cref="T:System.String"/> key values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfNames.WebCaptureContentSetsIds">
      <summary>
        <para>Gets a web capture content dictionary, which maps digital identifiers (IDs) to PDF objects such as pages and forms.</para>
      </summary>
      <value>A dictionary, containing the DevExpress.Pdf.PdfSpiderSet objects, along with their <see cref="T:System.String"/> key values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfNames.WebCaptureContentSetsUrls">
      <summary>
        <para>Gets a web capture content dictionary which maps URLs to PDF objects such as pages and forms.</para>
      </summary>
      <value>A dictionary, containing the DevExpress.Pdf.PdfSpiderSet objects, along with their <see cref="T:System.String"/> key values.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfOptionalContent">
      <summary>
        <para>Represents content in a PDF document that can be selectively viewed or hidden.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfOptionalContentConfiguration">
      <summary>
        <para>Represents different presentations of a document&#39;s optional content groups.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentConfiguration.BaseState">
      <summary>
        <para>Gets a state value that is used to initialize the states of all the optional content groups in a document when this configuration is applied.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOptionalContentState"/> enumeration value that initializes the states of all the optional content groups in a document when this configuration is applied.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentConfiguration.Creator">
      <summary>
        <para>Gets the name of the application or feature that created this content configuration.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the name of the application or feature.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentConfiguration.Intent">
      <summary>
        <para>Gets intent names that are used to determine which optional content groups&#39; states to consider and which to ignore in calculating the visibility of content.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOptionalContentIntent"/> enumeration values that list intent names.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentConfiguration.Locked">
      <summary>
        <para>Gets a collection of optional content groups that shall be locked when this configuration is applied.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfOptionalContentGroup"/> objects that represent optional content groups that shall be locked when this configuration is applied</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentConfiguration.Name">
      <summary>
        <para>Gets the name of the optional content configuration.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the configuration name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentConfiguration.Off">
      <summary>
        <para>Gets the collection of optional content groups whose state shall be set to Off when this configuration is applied.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfOptionalContentGroup"/> objects that represent optional content groups whose state shall be set to Off when this configuration is applied.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentConfiguration.On">
      <summary>
        <para>Gets the collection of optional content groups whose state shall be set to On when this configuration is applied.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfOptionalContentGroup"/> objects that represent optional content groups whose state shall be set to On when this configuration is applied.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentConfiguration.Order">
      <summary>
        <para>Gets the order for presentation of optional content groups.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOptionalContentOrder"/> object that is the order for presentation of optional content groups.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentConfiguration.OrderListMode">
      <summary>
        <para>Gets a name specifying which optional content groups in the <see cref="P:DevExpress.Pdf.PdfOptionalContentConfiguration.Order"/> property will be displayed to the user.</para>
      </summary>
      <value><see cref="T:DevExpress.Pdf.PdfOptionalContentOrderListMode"/> enumeration values that list the optional content order.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentConfiguration.RadioButtonGroups">
      <summary>
        <para>Gets a collection consisting of one or more collections, each of which represents a collection of optional content groups whose states shall be intended to follow a radio button paradigm.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfOptionalContentRadioButtonGroup"/> objects.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentConfiguration.UsageApplication">
      <summary>
        <para>Gets a collection of usage applications specifying which usage categories will be used to automatically manipulate the state of optional content groups.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfOptionalContentUsageApplication"/> objects.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfOptionalContentGroup">
      <summary>
        <para>Represents the optional content group that is used to control the visibility of graphic objects.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentGroup.Intent">
      <summary>
        <para>Gets a single intent name containing any combination of names.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOptionalContentIntent"/> enumeration values that list intent names.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentGroup.Name">
      <summary>
        <para>Gets the name of the optional content group.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the optional content group name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentGroup.Usage">
      <summary>
        <para>Gets the content usage describing the nature of the content controlled by the group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOptionalContentUsage"/> object that represents the content usage.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfOptionalContentIntent">
      <summary>
        <para>Lists names that are used to determine which optional content group states to consider and which to ignore in calculating the visibility of content.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfOptionalContentIntent.All">
      <summary>
        <para>Indicates the set of all intents, including those not yet defined.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfOptionalContentIntent.Design">
      <summary>
        <para>Indicates the Design intent name.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfOptionalContentIntent.View">
      <summary>
        <para>Indicates the View intent name.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfOptionalContentOrder">
      <summary>
        <para>Represents the order for presentation of optional content groups.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentOrder.Items">
      <summary>
        <para>Gets items of the optional content that will be displayed in the user interface.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfOptionalContent"/> objects that represent items of the optional content.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentOrder.Name">
      <summary>
        <para>Gets a name of the optional content group displayed in the user interface.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the name of the optional content group.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfOptionalContentOrderListMode">
      <summary>
        <para>Lists values that specify which optional content groups in the <see cref="P:DevExpress.Pdf.PdfOptionalContentConfiguration.Order"/> property will be displayed to the user.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfOptionalContentOrderListMode.AllPages">
      <summary>
        <para>Display all groups in the <see cref="P:DevExpress.Pdf.PdfOptionalContentConfiguration.Order"/> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfOptionalContentOrderListMode.VisiblePages">
      <summary>
        <para>Display only those groups in the <see cref="P:DevExpress.Pdf.PdfOptionalContentConfiguration.Order"/> property that are referenced by one or more visible pages.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfOptionalContentProperties">
      <summary>
        <para>Contains a list of all the optional content groups in the document, as well as information about the default and alternate configurations for optional content.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentProperties.Configurations">
      <summary>
        <para>Gets a collection of alternate optional content configurations.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfOptionalContentConfiguration"/> objects.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentProperties.DefaultConfiguration">
      <summary>
        <para>Gets the default viewing optional content configuration.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOptionalContentConfiguration"/> object that is the default optional content configuration.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentProperties.Groups">
      <summary>
        <para>Gets a collection of all the optional content groups in the document.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfOptionalContentGroup"/> objects.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfOptionalContentRadioButtonGroup">
      <summary>
        <para>Represents a collection of optional content groups whose states are intended to follow a radio button paradigm.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfOptionalContentRadioButtonGroup.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfOptionalContentRadioButtonGroup"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfOptionalContentState">
      <summary>
        <para>Lists the states of all the optional content groups in a document when the optional content configuration is applied.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfOptionalContentState.Off">
      <summary>
        <para>The states of all groups will be turned OFF.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfOptionalContentState.On">
      <summary>
        <para>The states of all groups will be turned ON.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfOptionalContentState.Unchanged">
      <summary>
        <para>The states of all groups will be left unchanged.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfOptionalContentUsage">
      <summary>
        <para>Represents an optional content group&#39;s usage that contains information describing the nature of the content controlled by the group.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentUsage.CreatorInfo">
      <summary>
        <para>Gets a value that is used by the creating application to store application-specific data associated with this optional content group.</para>
      </summary>
      <value>A PdfOptionalContentUsageCreatorInfo object that contains information that  is used by the creating application.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentUsage.ExportState">
      <summary>
        <para>Gets a value that indicates the recommended state for content in this group when the document is saved to a format that does not support optional content (for example, a raster image format).</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> enumeration member that indicates the recommended state for content in this group.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentUsage.IsLanguagePreferred">
      <summary>
        <para>Gets a value that indicates whether a partial match exists between the system language and the language strings in the entire usage.</para>
      </summary>
      <value>true, if the language is preferred; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentUsage.LanguageCulture">
      <summary>
        <para>Gets the language of the content controlled by this optional content group.</para>
      </summary>
      <value>A <see cref="T:System.Globalization.CultureInfo"/> object containing the language of the content.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentUsage.MaxZoom">
      <summary>
        <para>Gets the magnification factor below which the content in this optional content group is best viewed.</para>
      </summary>
      <value>A double value that is the magnification factor below which the group will be ON.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentUsage.MinZoom">
      <summary>
        <para>Gets the minimum magnification factor at which the content in this optional content group is best viewed.</para>
      </summary>
      <value>A double value that is the minimum magnification factor at which the group will be ON. The default value: 0.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentUsage.PageElement">
      <summary>
        <para>Gets a value indicating that the group contains a pagination artifact.</para>
      </summary>
      <value>A PdfPageElement enumeration value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentUsage.PrintContentKind">
      <summary>
        <para>Gets a name specifying the kind of content controlled by the group (e.g.,Trapping, PrintersMarks and Watermark).</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the name specifying the kind of content controlled by the group.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentUsage.PrintState">
      <summary>
        <para>Gets a value that indicates whether the group is set to either the ON or OFF state when the document is printed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> enumeration member that specifies whether the group is set to the state when the document is printed.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentUsage.ViewState">
      <summary>
        <para>Gets a value that indicates whether the group is set to either the ON or OFF state when the document is opened.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> enumeration member that specifies whether the group is set to the state when the document is opened.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfOptionalContentUsageApplication">
      <summary>
        <para>Represents the optional content usage application.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentUsageApplication.Category">
      <summary>
        <para>Gets names, each of which corresponds to a usage dictionary entry.</para>
      </summary>
      <value>A collection of strings that represent names, each of which corresponds to a usage dictionary entry.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentUsageApplication.Event">
      <summary>
        <para>Gets a name defining the situation in which this usage application should be used.</para>
      </summary>
      <value>A PdfOptionalContentUsageApplicationEvent enumeration value that defines the situation in which this usage application should be used.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionalContentUsageApplication.Groups">
      <summary>
        <para>Gets optional content groups that will have their states automatically managed based on information in their usage.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfOptionalContentGroup"/> objects that represent optional content groups.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfOptionsFormFieldOption">
      <summary>
        <para>Represents the list of options in the choice field, each of which shall be represented by a text string that shall be displayed on the screen.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionsFormFieldOption.ExportText">
      <summary>
        <para>Gets a text that represents the option&#39;s export value.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that represents the option&#39;s export value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOptionsFormFieldOption.Text">
      <summary>
        <para>Gets the text representing one of the available options in the choice field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that represents one of the available options in the choice field.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfOrientedRectangle">
      <summary>
        <para>A rectangle that delimits a page area around a specific document element.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfOrientedRectangle.#ctor(DevExpress.Pdf.PdfPoint,System.Double,System.Double)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfOrientedRectangle"/> class with the specified location and size.</para>
      </summary>
      <param name="topLeft">A <see cref="T:DevExpress.Pdf.PdfPoint"/> object that represents the top left corner of the rectangular region.</param>
      <param name="width">A <see cref="T:System.Double"/> value that represents the width of the rectangular region.</param>
      <param name="height">A <see cref="T:System.Double"/> value that represents the height of the rectangular region.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfOrientedRectangle.#ctor(DevExpress.Pdf.PdfPoint,System.Double,System.Double,System.Double)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfOrientedRectangle"/> class with the specified location, size, and angle.</para>
      </summary>
      <param name="topLeft">A <see cref="T:DevExpress.Pdf.PdfPoint"/> object that represents the top left corner of the rectangular region.</param>
      <param name="width">A <see cref="T:System.Double"/> value that specifies the width of the rectangular region.</param>
      <param name="height">A <see cref="T:System.Double"/> value that specifies the height of the rectangular region.</param>
      <param name="angle">A <see cref="T:System.Double"/> value that specifies the rotation angle of the rectangle (in radians). A positive value indicates counterclockwise rotation; a negative value indicates clockwise rotation.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfOrientedRectangle.Angle">
      <summary>
        <para>Gets the rotation angle of the rectangle.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that is the rotation angle of the rectangle in radians.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOrientedRectangle.BoundingRectangle">
      <summary>
        <para>Provides access to the PdfOrientedRectangle object&#39; s minimum bounding rectangle.</para>
      </summary>
      <value>An PdfRectangle object that is the minimum bounding rectangle.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfOrientedRectangle.Contains(DevExpress.Pdf.PdfOrientedRectangle)">
      <summary>
        <para>Determines whether the current PdfOrientedRectangle object contains the specified rectangle.</para>
      </summary>
      <param name="rectangle">Specifies the rectangle to test.</param>
      <returns>true, if the specified rectangle belongs to the current PdfOrientedRectangle object; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfOrientedRectangle.Contains(DevExpress.Pdf.PdfPoint)">
      <summary>
        <para>Determines whether the current PdfOrientedRectangle object contains the specified point.</para>
      </summary>
      <param name="point">Specifies the point to test.</param>
      <returns>true, if the specified point belongs to the current PdfOrientedRectangle object; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfOrientedRectangle.Height">
      <summary>
        <para>Gets the rectangle height.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that is the rectangle height.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOrientedRectangle.Left">
      <summary>
        <para>Gets the rectangle position in relation to the left edge of the page.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOrientedRectangle.Top">
      <summary>
        <para>Gets the rectangle position in relation to the top edge of the page.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOrientedRectangle.Vertices">
      <summary>
        <para>Provides access to the PdfOrientedRectangle object&#39;s vertices.</para>
      </summary>
      <value>A list of vertices (starting from top left).</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOrientedRectangle.Width">
      <summary>
        <para>Gets the rectangle width.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that is the rectangle width.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfOutline">
      <summary>
        <para>Represents a document outline that allows the user to navigate interactively from one part of the document to another.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfOutline.Action">
      <summary>
        <para>Provides access to the action that should be performed when the outline item is activated.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that is an action that is performed with outlines.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOutline.Color">
      <summary>
        <para>Gets the color of the outline entry&#39;s text in the navigation pane.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfColor"/> object that represents the color for an outline entry&#39;s  text.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOutline.Destination">
      <summary>
        <para>Gets a destination (a particular view of a document), which is displayed when an outline item is activated.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfDestination"/> object that is an outline destination.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOutline.IsBold">
      <summary>
        <para>Gets a value indicating whether the outline&#39;s text is formatted as bold.</para>
      </summary>
      <value>true, if the outline&#39;s text is bold; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOutline.IsItalic">
      <summary>
        <para>Gets the value indicating whether the outline&#39;s text is formatted as italic.</para>
      </summary>
      <value>true, if the outline&#39;s text is italic; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOutline.Next">
      <summary>
        <para>Gets the next item at the outline level.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOutline"/> object that is the next outline item.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOutline.Parent">
      <summary>
        <para>Gets the parent of the outline item in the outline hierarchy.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOutlineItem"/> object that represents the parent of the outline item.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOutline.Prev">
      <summary>
        <para>Gets the previous item at the outline level.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOutline"/> object that is the previous outline item.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOutline.Title">
      <summary>
        <para>Gets the text that is displayed for the outline item.</para>
      </summary>
      <value>A string that is displayed on the screen for the outline item.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfOutlineItem">
      <summary>
        <para>Represents an outline item in the tree-structured hierarchy of outlines.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfOutlineItem.Closed">
      <summary>
        <para>Gets or sets a value that indicates whether the outline item is closed when it is displayed on the screen.</para>
      </summary>
      <value>true, if the outline item is hidden (closed); otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOutlineItem.Count">
      <summary>
        <para>Gets the total number of visible outline items at all levels of the outline.</para>
      </summary>
      <value>An integer value that is the outline items count.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOutlineItem.First">
      <summary>
        <para>Gets the first top-level item in the outline.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOutline"/> object that is the first top-level item in the outline.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfOutlineItem.Last">
      <summary>
        <para>Gets the last top-level item in the outline.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOutline"/> object that is the last  top-level item in the outline.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfOutlines">
      <summary>
        <para>Represents the root of a document&#39;s outline hierarchy.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfPage">
      <summary>
        <para>An individual page in a document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.Actions">
      <summary>
        <para>Provides access to a dictionary of additional actions to be performed when the page is opened or closed.</para>
      </summary>
      <value>A DevExpress.Pdf.PdfPageActions object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.Annotations">
      <summary>
        <para>Provides access to the collection of annotation dictionaries that contain indirect references to all annotations associated with the page.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfAnnotation"/> objects.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.AnnotationTabOrder">
      <summary>
        <para>Indicates the tab order that shall be used for annotations on the page.</para>
      </summary>
      <value>A DevExpress.Pdf.PdfAnnotationTabOrder enumeration value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.ArtBox">
      <summary>
        <para>Specifies the art box defining the boundaries of the meaningful content (including potential white space) on a page as intended by the page&#39;s creator.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object, specifying the art box.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.ArticleBeads">
      <summary>
        <para>Provides access to the collection of indirect references to all article beads appearing on the page.</para>
      </summary>
      <value>A collection of DevExpress.Pdf.PdfBead objects.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.BleedBox">
      <summary>
        <para>Specifies the bleed box defining the region to which the contents of the page shall be clipped when output in a production environment.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object, specifying the bleed box.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.Commands">
      <summary>
        <para>Gets the PDF page commands.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfCommandList"/> object that represents the page commands.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.DisplayDuration">
      <summary>
        <para>Indicates the display duration of a page (also called its advance timing): the maximum length of time, in seconds, that the page shall be displayed during presentations before the viewer application shall automatically advance to the next page.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfPage.GetPageIndex">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.ID">
      <summary>
        <para>Indicates the digital identifier of the page&#39;s parent Web Capture content set.</para>
      </summary>
      <value>A <see cref="T:System.Byte"/> array.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.LastModified">
      <summary>
        <para>Specifies the date and time when the page&#39;s contents were most recently modified.</para>
      </summary>
      <value>A nullable <see cref="T:System.DateTimeOffset"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.Metadata">
      <summary>
        <para>Provides access to a metadata stream that contains metadata for the page.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfMetadata"/> object.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfPage.OffsetContent(System.Double,System.Double)">
      <summary>
        <para>Offsets the page content.</para>
      </summary>
      <param name="dx">The horizontal offset (in points).</param>
      <param name="dy">The vertical offset (in points).</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.PieceInfo">
      <summary>
        <para>Provides access to a page-piece dictionary associated with the page.</para>
      </summary>
      <value>A dictionary, containing the DevExpress.Pdf.PdfPieceInfoEntry objects, along with their <see cref="T:System.String"/> keys.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.PreferredZoomFactor">
      <summary>
        <para>Specifies the page&#39;s preferred zoom factor, i.e. the factor by which it shall be scaled to achieve the natural display magnification.</para>
      </summary>
      <value>A nullable <see cref="T:System.Double"/> value.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfPage.Resize(DevExpress.Pdf.PdfRectangle,DevExpress.Pdf.PdfContentHorizontalAlignment,DevExpress.Pdf.PdfContentVerticalAlignment)">
      <summary>
        <para>Resizes the page content.</para>
      </summary>
      <param name="mediaBox">New page size.</param>
      <param name="horizontalAlignment">The content&#39;s horizontal alignment.</param>
      <param name="verticalAlignment">The content&#39;s vertical alignment.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfPage.RotateContent(System.Double,System.Double,System.Double)">
      <summary>
        <para>Rotates the page content relative to the specified point. Document annotations are not rotated.</para>
      </summary>
      <param name="x">The point&#39;s X coordinate in the page coordinate system.</param>
      <param name="y">The point&#39;s Y coordinate in the page coordinate system..</param>
      <param name="degree">The degree of rotation (from 0 to 360).</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfPage.ScaleContent(System.Double,System.Double)">
      <summary>
        <para>Scales the page content.</para>
      </summary>
      <param name="scaleX">The horizontal scale factor (1 is 100%).</param>
      <param name="scaleY">The vertical scale factor (1 is 100%).</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.StructParents">
      <summary>
        <para>Indicates the key of the page&#39;s entry in the structural parent tree.</para>
      </summary>
      <value>A nullable integer value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.ThumbnailImage">
      <summary>
        <para>Provides access to a stream object that defines the page&#39;s thumbnail image.</para>
      </summary>
      <value>A DevExpress.Pdf.PdfImage object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.Trans">
      <summary>
        <para>Provides access to a transition dictionary describing the transition effect that shall be used when displaying the page during presentations.</para>
      </summary>
      <value>A DevExpress.Pdf.PdfPagePresentation object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.TransparencyGroup">
      <summary>
        <para>Specifies the group attributes dictionary that specifies the attributes of the page&#39;s page group for use in the transparent imaging model.</para>
      </summary>
      <value>A DevExpress.Pdf.PdfTransparencyGroup object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.TrimBox">
      <summary>
        <para>Specifies the trim box defining the intended dimensions of the finished page after trimming.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object, specifying the trim box.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPage.UserUnit">
      <summary>
        <para>Indicates the size of default user space units, in multiples of 1⁄72 inch.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value. The default value is 1.0 (user space unit is 1⁄72 inch).</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfPageLayout">
      <summary>
        <para>Lists the values that define how the page is positioned in the PDF Viewer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPageLayout.OneColumn">
      <summary>
        <para>The PDF Viewer displays pages in one column.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPageLayout.SinglePage">
      <summary>
        <para>The PDF Viewer displays one page at a time.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPageLayout.TwoColumnLeft">
      <summary>
        <para>The PDF Viewer displays the pages in two columns, with odd-numbered pages on the left.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPageLayout.TwoColumnRight">
      <summary>
        <para>The PDF Viewer displays pages in two columns, with odd-numbered pages on the right.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPageLayout.TwoPageLeft">
      <summary>
        <para>The PDF Viewer displays the pages two at a time, with odd-numbered pages on the left.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPageLayout.TwoPageRight">
      <summary>
        <para>The PDF Viewer displays the pages two at a time, with odd-numbered pages on the right.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfPageMode">
      <summary>
        <para>Lists values that specify how to display the opened document.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPageMode.FullScreen">
      <summary>
        <para>When a document is opened in the viewer, it shows pages in full screen mode (no menu bar, window controls, or any other window is visible).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPageMode.UseAttachments">
      <summary>
        <para>When a document is opened in the viewer, it shows pages and opens the Attachments panel to display attachments.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPageMode.UseNone">
      <summary>
        <para>When a document is opened in the viewer, it shows only pages (Bookmarks, Attachments, Page Thumbnails, and Layers panels are hidden).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPageMode.UseOC">
      <summary>
        <para>When a document is opened, for example, in the Adobe Acrobat Reader, the Reader shows pages and opens the optional content group panel (the Layers panel).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPageMode.UseOutlines">
      <summary>
        <para>When a document is opened in the viewer, it shows pages and opens the Bookmarks panel to display outlines (bookmarks).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPageMode.UseThumbs">
      <summary>
        <para>When a document is opened in the viewer, it shows pages and opens the Page Thumbnails panel to display thumbnails.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfPageTreeObject">
      <summary>
        <para>Provides settings that define page boundaries for specific content types.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfPageTreeObject.CropBox">
      <summary>
        <para>Specifies the crop box imposing the page boundaries displayed in a PDF Viewer.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object, specifying the crop box.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPageTreeObject.MediaBox">
      <summary>
        <para>Specifies the media box defining the boundaries of the physical medium on which the page is to be printed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object, specifying the media box.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPageTreeObject.Rotate">
      <summary>
        <para>Rotates the document page at the specified angle.</para>
      </summary>
      <value>An integer value, specifying the page rotation angle (in degrees).</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfPaperSize">
      <summary>
        <para>Contains the standard paper sizes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A2">
      <summary>
        <para>The PDF paper size for the A2 paper type is 420 x 594 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A3">
      <summary>
        <para>The PDF paper size for the A3 paper type is 297 x 420 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A3Extra">
      <summary>
        <para>The PDF paper size for the A3 Extra paper type is 322 x 445 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A3ExtraTransverse">
      <summary>
        <para>The PDF paper size for the A3 Extra Transverse paper type is 322 x 445 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A3Rotated">
      <summary>
        <para>The PDF paper size for the A3 Rotated paper type is 420 x 297 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A3Transverse">
      <summary>
        <para>The PDF paper size for the A3 Transverse paper type is 297 x 420 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A4">
      <summary>
        <para>The PDF paper size for the A4 paper type is 210 x 297 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A4Extra">
      <summary>
        <para>The PDF paper size for the A4  Extra paper type is 235 x 322 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A4Plus">
      <summary>
        <para>The PDF paper size for the A4 Plus paper type is 210 x 330 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A4Rotated">
      <summary>
        <para>The PDF paper size for the A4 Rotated paper type is 297 x 210 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A4Small">
      <summary>
        <para>The PDF paper size for the A4 Small paper type is 210 x 297 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A4Transverse">
      <summary>
        <para>The PDF paper size for the A4 Transverse paper type is 210 x 297 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A5">
      <summary>
        <para>The PDF paper size for the A5 paper type is 148 x 210 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A5Extra">
      <summary>
        <para>The PDF paper size for the A5 Extra paper type is 174 x 235 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A5Rotated">
      <summary>
        <para>The PDF paper size for the A5 Rotated paper type is 210 x 148 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A5Transverse">
      <summary>
        <para>The PDF paper size for the A5 Transverse paper type is 148 x 210 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A6">
      <summary>
        <para>The PDF paper size for the A6 paper type is 105 x 148 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.A6Rotated">
      <summary>
        <para>The PDF paper size for the A6 Rotated paper type is 148 x 105 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.APlus">
      <summary>
        <para>The PDF paper size for the APlus paper type is 227 x 356 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.B4">
      <summary>
        <para>The PDF paper size for the B4 paper type is 250 x 353 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.B4Envelope">
      <summary>
        <para>The PDF paper size for the B4  Envelope paper type is 250 x 353 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.B4JisRotated">
      <summary>
        <para>The PDF paper size for the B4 (JIS) Rotated paper type is 364 x 257 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.B5">
      <summary>
        <para>The PDF paper size for the B5 paper type is 176 x 250 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.B5Envelope">
      <summary>
        <para>The PDF paper size for the B5 Envelope paper type is 176 x 250 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.B5Extra">
      <summary>
        <para>The PDF paper size for the B5 Extra paper type is 201 x 276 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.B5JisRotated">
      <summary>
        <para>The PDF paper size for the B5 (JIS) Rotated paper type is 257 x 182 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.B5Transverse">
      <summary>
        <para>The PDF paper size for the B5 Transverse paper type is 182 x 257 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.B6Envelope">
      <summary>
        <para>The PDF paper size for the B6 Envelope paper type is 176 x 125 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.B6Jis">
      <summary>
        <para>The PDF paper size for the B6 (JIS) paper type is 128 x 182 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.B6JisRotated">
      <summary>
        <para>The PDF paper size for the B6 (JIS) Rotated paper type is 182 x 128 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.BPlus">
      <summary>
        <para>The PDF paper size for the BPlus paper type is 305 x 487 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.C3Envelope">
      <summary>
        <para>The PDF paper size for the C3 Envelope paper type is 324 x 458 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.C4Envelope">
      <summary>
        <para>The PDF paper size for the C4 Envelope paper type is 229 x 324 mm (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.C5Envelope">
      <summary>
        <para>The PDF paper size for the C5 Envelope paper type is 162 x 229 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.C65Envelope">
      <summary>
        <para>The PDF paper size for the C65 paper type is 114 x 229 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.C6Envelope">
      <summary>
        <para>The PDF paper size for the C6 Envelope paper type is 114 x 162 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.CSheet">
      <summary>
        <para>The PDF paper size for the C paper type is 17 x 22 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.DLEnvelope">
      <summary>
        <para>The PDF paper size for the DL Envelope is 110 x 220 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.DSheet">
      <summary>
        <para>The PDF paper size for the D paper type is 22 x 34 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.ESheet">
      <summary>
        <para>The PDF paper size for the E paper type is 34 x 44 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Executive">
      <summary>
        <para>The PDF paper size for the Executive paper type is 7.25 x 10.5 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Folio">
      <summary>
        <para>The PDF paper size for the Folio paper type is 8.5 x 13 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.GermanLegalFanfold">
      <summary>
        <para>The PDF paper size for the German legal fanfold paper type is 8.5 x 13 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.GermanStandardFanfold">
      <summary>
        <para>The PDF paper size for the German standard fanfold paper type is 8.5 x 12 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.InviteEnvelope">
      <summary>
        <para>The PDF paper size for the Invitation envelope paper type is 220 x 220 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.IsoB4">
      <summary>
        <para>The PDF paper size for the ISO B4 paper type is 250 x 353 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.ItalyEnvelope">
      <summary>
        <para>The PDF paper size for the Italy envelope paper type is 110 x 230 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.JapaneseDoublePostcard">
      <summary>
        <para>The PDF paper size for the Japanese double postcard paper type is 200 x 148 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.JapaneseDoublePostcardRotated">
      <summary>
        <para>The PDF paper size for the Japanese double postcard rotated paper type is 148 x 200 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.JapanesePostcard">
      <summary>
        <para>The PDF paper size for the Japanese postcard paper type is 100 x 148 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.JapanesePostcardRotated">
      <summary>
        <para>The PDF paper size for the Japanese postcard rotated paper type is 148 x 100 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Ledger">
      <summary>
        <para>The PDF paper size for the Ledger paper type is 17 x 11 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Legal">
      <summary>
        <para>The PDF paper size for the Legal paper type is 8.5 x 14 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.LegalExtra">
      <summary>
        <para>The PDF paper size for the Legal Extra paper type is 9.275 x 15 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Letter">
      <summary>
        <para>The PDF paper size for the Letter paper type is 8.5 x 11 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.LetterExtra">
      <summary>
        <para>The PDF paper size for the Letter Extra paper type is 9.275 x 12 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.LetterExtraTransverse">
      <summary>
        <para>The PDF paper size for the Letter extra transverse paper type is 9.275 x 12 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.LetterPlus">
      <summary>
        <para>The PDF paper size for the Letter plus paper type is 8.5 x 12.69 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.LetterRotated">
      <summary>
        <para>The PDF paper size for the Letter rotated paper type is 11 x 8.5 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.LetterSmall">
      <summary>
        <para>The PDF paper size for the Letter small paper type is 8.5 x 11 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.LetterTransverse">
      <summary>
        <para>The PDF paper size for the Letter transverse paper type is 8.275 x 11 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.MonarchEnvelope">
      <summary>
        <para>The PDF paper size for the Monarch envelope paper type is 3.875 x 7.5 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Note">
      <summary>
        <para>The PDF paper size for the Note paper type is 8.5 x 11 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Number10Envelope">
      <summary>
        <para>The PDF paper size for the Number 10 envelope paper type is 4.125 x 9.5 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Number11Envelope">
      <summary>
        <para>The PDF paper size for the Number 11 envelope paper type is 4.5 x 10.375 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Number12Envelope">
      <summary>
        <para>The PDF paper size for the Number 12 envelope paper type is 4.75 x 11 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Number9Envelope">
      <summary>
        <para>The PDF paper size for the Number 9 envelope paper type is 3.875 x 8.875 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PersonalEnvelope">
      <summary>
        <para>The PDF paper size for the Personal envelope paper type is 3.625 x 6.5 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Prc16K">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China 16K paper type is 146 x 215 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Prc16KRotated">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China 16K rotated paper type is 146 x 215 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Prc32K">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China 32K paper type is 97 x 151 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Prc32KBig">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China 32K big paper type is 97 x 151 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Prc32KBigRotated">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China 32K big rotated paper type is 97 x 151 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Prc32KRotated">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China 32K rotated paper type is 97 x 151 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber1">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 1 envelope paper type is 102 x 165 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber10">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 10 envelope paper type is 324 x 458 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber10Rotated">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 10 rotated envelope paper type is 458 x 324 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber1Rotated">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 1 rotated envelope paper type is 165 x 102 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber2">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 2 envelope paper type is 102 x 176 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber2Rotated">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 2 rotated envelope paper type is 176 x 102 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber3">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 3 envelope paper type is 125 x 176 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber3Rotated">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 3 rotated envelope paper type is 176 x 125 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber4">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 4 envelope paper type is 110 x 208 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber4Rotated">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 4 rotated envelope paper type is 208 x 110 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber5">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 5 envelope paper type is 110 x 220 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber5Rotated">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China Envelope number 5 rotated envelope paper type is 220 x 110 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber6">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 6 envelope paper type is 120 x 230 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber6Rotated">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 6 rotated envelope paper type is 230 x 120 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber7">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 7 envelope paper type is 160 x 230 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber7Rotated">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 7 rotated envelope paper type is 230 x 160 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber8">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 8 envelope paper type is 120 x 309 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber8Rotated">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 8 rotated envelope paper type is 309 x 120 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber9">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 9 envelope paper type is 229 x 324 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.PrcEnvelopeNumber9Rotated">
      <summary>
        <para>The PDF paper size for the People&#39;s Republic of China number 9 rotated envelope paper type is 324 x 229 (millimeters). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Quarto">
      <summary>
        <para>The PDF paper size for the Quarto paper type is 215 x 275 (millimeters).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Standard10x11">
      <summary>
        <para>The PDF paper size for the Standard paper type is 10 x 11 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Standard10x14">
      <summary>
        <para>The PDF paper size for the Standard paper type is 10 x 14 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Standard11x17">
      <summary>
        <para>The PDF paper size for the Standard paper type is 11 x 17 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Standard12x11">
      <summary>
        <para>The PDF paper size for the Standard paper type is 12 x 11 (inches). Requires Windows 98, Windows NT 4.0, or later.</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Standard15x11">
      <summary>
        <para>The PDF paper size for the Standard paper type is 15 x 11 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Statement">
      <summary>
        <para>The PDF paper size for the Statement paper type is 5.5 x 8.5 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.Tabloid">
      <summary>
        <para>The PDF paper size for the Tabloid paper type is 11 x 17 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.TabloidExtra">
      <summary>
        <para>The PDF paper size for the Tabloid extra paper type is 11.69 x 18 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfPaperSize.USStandardFanfold">
      <summary>
        <para>The PDF paper size for the US standard fanfold is 14.875 x 11 (inches).</para>
      </summary>
      <value>A page rectangle.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfPasswordRequestedEventArgs">
      <summary>
        <para>Provides the security password to open a protected PDF file in the PDF Viewer and PDF Document Processor.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfPasswordRequestedEventArgs.FileName">
      <summary>
        <para>Returns the file name of a document that is requested to be opened in the PDF Viewer or PDF Document Processor.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the file name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPasswordRequestedEventArgs.FilePath">
      <summary>
        <para>Returns the path of the file requested to be opened in the PDF Viewer or PDF Document Processor.</para>
      </summary>
      <value>The file path.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPasswordRequestedEventArgs.Password">
      <summary>
        <para>This property is now obsolete. Use the <see cref="P:DevExpress.Pdf.PdfPasswordRequestedEventArgs.PasswordString"/> property instead.</para>
      </summary>
      <value>A <see cref="T:System.Security.SecureString"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPasswordRequestedEventArgs.PasswordRequestsCount">
      <summary>
        <para>Returns the current number of password request attempts.</para>
      </summary>
      <value>An integer value that is the current number of password requests.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPasswordRequestedEventArgs.PasswordString">
      <summary>
        <para>Specifies the security password to open a PDF file.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfPasswordRequestedEventHandler">
      <summary>
        <para>This event handler is used to programmatically provide a document opening password in a PDF Viewer and PDF Document Processor to open a document without end-user interaction.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">A <see cref="T:DevExpress.Pdf.PdfPasswordRequestedEventArgs"/> object that is used to provide the security password to open a protected PDF file in the PDF Viewer and PDF Document Processor.</param>
    </member>
    <member name="T:DevExpress.Pdf.PdfPattern">
      <summary>
        <para>Represents a color pattern.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfPattern.Matrix">
      <summary>
        <para>Gets a pattern matrix that specifies the transformation from pattern space to user space.</para>
      </summary>
      <value>A PdfTransformationMatrix object that represents the pattern matrix.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfPoint">
      <summary>
        <para>Specifies the coordinates of a PDF point.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfPoint.#ctor(System.Double,System.Double)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfPoint"/> class with the specified coordinates.</para>
      </summary>
      <param name="x">A <see cref="T:System.Double"/> value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfPoint.X"/> property.</param>
      <param name="y">A <see cref="T:System.Double"/> value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfPoint.Y"/> property.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfPoint.op_Addition(DevExpress.Pdf.PdfPoint,DevExpress.Pdf.PdfPoint)">
      <summary>
        <para>Adds a PdfPoint object to the PdfPoint object and returns the result as the PdfPoint object.</para>
      </summary>
      <param name="left">A PdfPoint object to add.</param>
      <param name="right">A PdfPoint object to add.</param>
      <returns>The sum of two PdfPoint objects.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfPoint.op_Division(DevExpress.Pdf.PdfPoint,DevExpress.Pdf.PdfPoint)">
      <summary>
        <para>Divides two <see cref="T:DevExpress.Pdf.PdfPoint"/> values.</para>
      </summary>
      <param name="left">A <see cref="T:DevExpress.Pdf.PdfPoint"/> value that is the dividend.</param>
      <param name="right">A <see cref="T:DevExpress.Pdf.PdfPoint"/> value that is the divisor.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfPoint"/> value that is the result of division.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfPoint.op_Division(DevExpress.Pdf.PdfPoint,System.Double)">
      <summary>
        <para>Divides a PdfPoint value to the <see cref="T:System.Double"/> value.</para>
      </summary>
      <param name="value1">A PdfPoint value that is the dividend.</param>
      <param name="value2">A <see cref="T:System.Double"/> value that is the divisor.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfPoint"/> object that is the result of dividing.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfPoint.op_Multiply(DevExpress.Pdf.PdfPoint,DevExpress.Pdf.PdfPoint)">
      <summary>
        <para>Multiplies two <see cref="T:DevExpress.Pdf.PdfPoint"/> values.</para>
      </summary>
      <param name="left">A PdfPoint value to multiply.</param>
      <param name="right">A PdfPoint value to multiply.</param>
      <returns>The result of multiplication.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfPoint.op_Multiply(DevExpress.Pdf.PdfPoint,System.Double)">
      <summary>
        <para>Multiplies the PdfPoint value by the <see cref="T:System.Double"/> value.</para>
      </summary>
      <param name="left">A PdfPoint value to multiply.</param>
      <param name="right">A <see cref="T:System.Double"/> value to multiply.</param>
      <returns>The result of multiplication.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfPoint.op_Multiply(System.Double,DevExpress.Pdf.PdfPoint)">
      <summary>
        <para>Multiplies the <see cref="T:System.Double"/> value by the PdfPoint value.</para>
      </summary>
      <param name="left">A <see cref="T:System.Double"/> value to multiply.</param>
      <param name="right">A PdfPoint value to multiply.</param>
      <returns>The result of multiplication.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfPoint.op_Subtraction(DevExpress.Pdf.PdfPoint,DevExpress.Pdf.PdfPoint)">
      <summary>
        <para>Subtracts the <see cref="T:DevExpress.Pdf.PdfPoint"/> value from the <see cref="T:DevExpress.Pdf.PdfPoint"/> value.</para>
      </summary>
      <param name="left">The minuend.</param>
      <param name="right">The subtrahend.</param>
      <returns>The result of subtraction.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfPoint.op_UnaryNegation(DevExpress.Pdf.PdfPoint)">
      <summary>
        <para>Negates the value of the specified <see cref="T:DevExpress.Pdf.PdfPoint"/> operand.</para>
      </summary>
      <param name="value">The value to negate.</param>
      <returns>The result of the value multiplied by negative one (-1).</returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfPoint.X">
      <summary>
        <para>Gets the X coordinate of the <see cref="T:DevExpress.Pdf.PdfPoint"/>.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that is the PDF point&#39;s X coordinate.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPoint.Y">
      <summary>
        <para>Gets the Y coordinate of the <see cref="T:DevExpress.Pdf.PdfPoint"/>.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that is the PDF point&#39;s Y coordinate.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfPopupAnnotation">
      <summary>
        <para>Represents a pop-up annotation that displays text in a pop-up window for editing the annotation text.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfPopupAnnotation.Open">
      <summary>
        <para>Gets the value that indicates whether the popup annotation is opened on the page.</para>
      </summary>
      <value>true if the popup annotation is opened on the page; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPopupAnnotation.Parent">
      <summary>
        <para>Gets the parent of the popup annotation item in the annotation hierarchy.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAnnotation"/> object that represents the popup annotation parent.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfQuadrilateral">
      <summary>
        <para>Represents a quadrilateral that comprises the annotation.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfQuadrilateral.#ctor(DevExpress.Pdf.PdfPoint,DevExpress.Pdf.PdfPoint,DevExpress.Pdf.PdfPoint,DevExpress.Pdf.PdfPoint)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfQuadrilateral"/> class with the specified settings.</para>
      </summary>
      <param name="p1">A <see cref="T:DevExpress.Pdf.PdfPoint"/> object that represents the coordinates of the first quadrilateral point.</param>
      <param name="p2">A <see cref="T:DevExpress.Pdf.PdfPoint"/> object that represents the coordinates of the second quadrilateral point.</param>
      <param name="p3">A <see cref="T:DevExpress.Pdf.PdfPoint"/> object that represents the coordinates of the third quadrilateral point.</param>
      <param name="p4">A <see cref="T:DevExpress.Pdf.PdfPoint"/> object that represents the coordinates of the fourth quadrilateral point.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfQuadrilateral.P1">
      <summary>
        <para>Gets the first quadrilateral point in the default user space.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPoint"/> object that is the first quadrilateral point in the default user space.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfQuadrilateral.P2">
      <summary>
        <para>Gets the second quadrilateral point in the default user space.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPoint"/> object that represents the second quadrilateral point in the default user space.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfQuadrilateral.P3">
      <summary>
        <para>Gets the third quadrilateral point in the default user space.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPoint"/> object that is the third quadrilateral point in the default user space.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfQuadrilateral.P4">
      <summary>
        <para>Gets the fourth quadrilateral point in the default user space.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPoint"/> object that is the fourth quadrilateral point in the default user space.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfRange">
      <summary>
        <para>Represents a range of colors.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfRange.#ctor(System.Double,System.Double)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfRange"/> class with the specified settings.</para>
      </summary>
      <param name="min">A <see cref="T:System.Double"/> value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfRange.Min"/> property.</param>
      <param name="max">A <see cref="T:System.Double"/> value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfRange.Max"/> property.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfRange.Max">
      <summary>
        <para>Gets the upper value to which the corresponding sample value in the image is mapped.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that is the upper value to which the corresponding sample value in the image is mapped.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfRange.Min">
      <summary>
        <para>Gets the lower value to which the corresponding sample value in the image is mapped.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that is the lower value to which the corresponding sample value in the image is mapped.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfRectangle">
      <summary>
        <para>A rectangle used to describe locations on a page and bounding boxes for various PDF objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfRectangle.#ctor(System.Double,System.Double,System.Double,System.Double)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfRectangle"/> class with the specified settings.</para>
      </summary>
      <param name="left">Specifies the lower left x-coordinate.</param>
      <param name="bottom">Specifies the lower left y-coordinate.</param>
      <param name="right">Specifies the upper right x-coordinate.</param>
      <param name="top">Specifies the upper right y-coordinate.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfRectangle.Bottom">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Pdf.PdfRectangle"/>&#39;s bottom coordinate.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value, representing the bottom coordinate of the PDF rectangle.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfRectangle.BottomLeft">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Pdf.PdfRectangle"/>&#39;s bottom left coordinate.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPoint"/> structure, representing the bottom left coordinate of the PDF rectangle.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfRectangle.BottomRight">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Pdf.PdfRectangle"/>&#39;s bottom right coordinate.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPoint"/> structure, representing the bottom right coordinate of the PDF rectangle.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfRectangle.Contains(DevExpress.Pdf.PdfPoint)">
      <summary>
        <para>Determines whether a point is inside the current rectangle.</para>
      </summary>
      <param name="point">A <see cref="T:DevExpress.Pdf.PdfPoint"/> structure that represents the point in a two-dimensional coordinate space.</param>
      <returns>true  if a point is inside the current rectangle; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfRectangle.Equals(System.Object)">
      <summary>
        <para>Determines whether or not the specified object is equal to the current <see cref="T:DevExpress.Pdf.PdfRectangle"/> instance.</para>
      </summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>true if the specified object is equal to the current <see cref="T:DevExpress.Pdf.PdfRectangle"/> instance; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfRectangle.GetHashCode">
      <summary>
        <para>Serves as the default hash function.</para>
      </summary>
      <returns>An integer value, specifying the hash code for the current object.</returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfRectangle.Height">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Pdf.PdfRectangle"/>&#39;s height.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that is the height of the PDF rectangle, in points (1/72 of an inch).</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfRectangle.Left">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Pdf.PdfRectangle"/>&#39;s left coordinate.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value, representing the left coordinate of the PDF rectangle.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfRectangle.Right">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Pdf.PdfRectangle"/>&#39;s right coordinate.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value, representing the right coordinate of the PDF rectangle.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfRectangle.Top">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Pdf.PdfRectangle"/>&#39;s top coordinate.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value, representing the top coordinate of the PDF rectangle.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfRectangle.TopLeft">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Pdf.PdfRectangle"/>&#39;s top left coordinate.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPoint"/> structure, representing the top left coordinate of the PDF rectangle.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfRectangle.TopRight">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Pdf.PdfRectangle"/>&#39;s top right coordinate.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPoint"/> structure, representing the top right coordinate of the PDF rectangle.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfRectangle.Width">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Pdf.PdfRectangle"/>&#39;s width.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that is the width of the PDF rectangle, in points (1/72 of an inch).</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfRemoteGoToAction">
      <summary>
        <para>An action that jumps to a destination in another PDF file instead of the current file.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfRemoteGoToAction.FileSpecification">
      <summary>
        <para>Gets the file in which the destination will be located.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfFileSpecification"/> object that represents the file in which the destination will be located.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfRemoteGoToAction.OpenInNewWindow">
      <summary>
        <para>Gets a value that indicates whether to open the destination document in a new window.</para>
      </summary>
      <value>true to open the destination document in a new window; otherwise false.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfRenderingIntent">
      <summary>
        <para>Lists rendering intents that determine how a color management system handles color conversion from one color space to another.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfRenderingIntent.AbsoluteColorimetric">
      <summary>
        <para>Colors shall be represented solely with respect to the light source; no correction shall be made for the output medium&#39;s white point (such as the color of unprinted paper).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfRenderingIntent.Perceptual">
      <summary>
        <para>Colors shall be represented in a manner that provides a pleasing perceptual appearance.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfRenderingIntent.RelativeColorimetric">
      <summary>
        <para>Colors shall be represented with respect to the combination of the light source and the output medium&#39;s white point (such as the color of unprinted paper).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfRenderingIntent.Saturation">
      <summary>
        <para>Colors shall be represented in a manner that preserves or emphasizes saturation.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfRenderingSettings">
      <summary>
        <para>Provides PDF rendering settings to optimize JPEG image decoding.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfRenderingSettings.ExternalDctDecoderModulePath">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Pdf.PdfRenderingSettings.UseExternalDctDecoder">
      <summary>
        <para>Specifies whether the external Windows Imaging Component (WIC) decoder can be used to optimize JPEG image decoding.</para>
      </summary>
      <value>true, to allow using the external DCT decoder to optimize JPEG image decoding; false, the GDI+ decoder is used for JPEG image decoding. The default value is true.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfResetFormAction">
      <summary>
        <para>An action that resets selected interactive form fields to their default values.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfResetFormAction.ExcludeFields">
      <summary>
        <para>Gets a value that indicates whether to exclude fields obtained in the <see cref="P:DevExpress.Pdf.PdfResetFormAction.Fields"/> property from resetting.</para>
      </summary>
      <value>if true all fields in the document&#39;s interactive form will be reset except those listed in the <see cref="P:DevExpress.Pdf.PdfResetFormAction.Fields"/> property; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfResetFormAction.Fields">
      <summary>
        <para>Gets a collection of fields that indicate which fields to reset or which to exclude from resetting, depending on the value of the <see cref="P:DevExpress.Pdf.PdfResetFormAction.ExcludeFields"/> property.</para>
      </summary>
      <value>A collection of the <see cref="T:DevExpress.Pdf.PdfInteractiveFormField"/> objects that represent fields.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfRGBColor">
      <summary>
        <para>Represents the red, green, and blue color components in the range of 0 to 1.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfRGBColor.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfRGBColor"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfRGBColor.#ctor(System.Double,System.Double,System.Double)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfRGBColor"/> class with the specified red, green, and blue color components.</para>
      </summary>
      <param name="r">A <see cref="T:System.Double"/> value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfRGBColor.R"/> property. This value must be between 0 and 1.</param>
      <param name="g">A <see cref="T:System.Double"/> value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfRGBColor.G"/> property. This value must be between 0 and 1.</param>
      <param name="b">A <see cref="T:System.Double"/> value. This value is assigned to the <see cref="P:DevExpress.Pdf.PdfRGBColor.B"/> property. This value must be between 0 and 1.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfRGBColor.B">
      <summary>
        <para>Gets or sets the blue color component value.</para>
      </summary>
      <value>A double value that is the blue color component value in the range of 0 to 1.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfRGBColor.G">
      <summary>
        <para>Gets or sets the green color component value.</para>
      </summary>
      <value>A double value that is the green color component value in a range of 0 to 1.</value>
    </member>
    <member name="E:DevExpress.Pdf.PdfRGBColor.PropertyChanged">
      <summary>
        <para>Occurs every time any of the <see cref="T:DevExpress.Pdf.PdfRGBColor"/> class properties has changed its value.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfRGBColor.R">
      <summary>
        <para>Gets or sets the red color component value.</para>
      </summary>
      <value>A double value that is the red color component value in a range of 0 to 1.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfSaveOptions">
      <summary>
        <para>A class that provides document encryption options and document signature to save the document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfSaveOptions.EncryptionOptions">
      <summary>
        <para>Specifies encryption options of a PDF document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfEncryptionOptions"/> object that represents encryption options of a PDF document.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSaveOptions.Signature">
      <summary>
        <para>Gets or sets the signature, which can contain signing info, reason and location, to be used to save the signed document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfSignature"/> object that represents the PDF signature.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfSignature">
      <summary>
        <para>An electronic signature used to sign a PDF file.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignature.#ctor(DevExpress.Pdf.IExternalSigner)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignature"/> class with specified settings.</para>
      </summary>
      <param name="signer"></param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignature.#ctor(DevExpress.Pdf.IExternalSigner,System.Byte[],System.Int32,DevExpress.Pdf.PdfOrientedRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignature"/> class with specified settings.</para>
      </summary>
      <param name="signer"></param>
      <param name="imageData"></param>
      <param name="pageNumber"></param>
      <param name="signatureBounds"></param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignature.#ctor(DevExpress.Pdf.IExternalSigner,System.Byte[],System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignature"/> class with specified settings.</para>
      </summary>
      <param name="signer"></param>
      <param name="imageData"></param>
      <param name="pageNumber"></param>
      <param name="signatureBounds"></param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignature.#ctor(DevExpress.Pdf.IExternalSigner,System.IO.Stream,System.Int32,DevExpress.Pdf.PdfOrientedRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignature"/> class with specified settings.</para>
      </summary>
      <param name="signer"></param>
      <param name="imageData"></param>
      <param name="pageNumber"></param>
      <param name="signatureBounds"></param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignature.#ctor(DevExpress.Pdf.IExternalSigner,System.IO.Stream,System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignature"/> class with specified settings.</para>
      </summary>
      <param name="signer"></param>
      <param name="imageData"></param>
      <param name="pageNumber"></param>
      <param name="signatureBounds"></param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignature.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignature"/> class with the specified certificate.</para>
      </summary>
      <param name="certificate">A <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> object that is an X.509 certificate.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignature.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,System.Byte[],System.Int32,DevExpress.Pdf.PdfOrientedRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignature"/> class with the specified certificate, image data represented by a byte array, page number, signature bounds and signature rotation angle represented by a <see cref="T:DevExpress.Pdf.PdfOrientedRectangle"/> object.</para>
      </summary>
      <param name="certificate">A <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> object that is an X.509 certificate.</param>
      <param name="imageData">An array of bytes containing an image.</param>
      <param name="pageNumber">An integer value that specifies the page number where the signature should be added.</param>
      <param name="signatureBounds">A <see cref="T:DevExpress.Pdf.PdfOrientedRectangle"/> object that represents the signature bounds in the default user space and signature rotation angle (in radians).  A positive angle means counterclockwise rotation; a negative angle means clockwise rotation.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignature.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,System.Byte[],System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignature"/> class with the specified certificate, image data represented by a byte array, page number and the signature bounds represented by a <see cref="T:DevExpress.Pdf.PdfRectangle"/> object.</para>
      </summary>
      <param name="certificate">A <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> object that is an X.509 certificate.</param>
      <param name="imageData">An array of bytes containing an image.</param>
      <param name="pageNumber">An integer value that specifies the page number where the signature should be added.</param>
      <param name="signatureBounds">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents the signature bounds in the default user space.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignature.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,System.IO.Stream,System.Int32,DevExpress.Pdf.PdfOrientedRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignature"/> class with the specified certificate, image data represented by a stream, page number, signature bounds and signature rotation angle represented by a <see cref="T:DevExpress.Pdf.PdfOrientedRectangle"/> object.</para>
      </summary>
      <param name="certificate">A <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> object that is an X.509 certificate.</param>
      <param name="imageData">A System.IO.Stream class descendant, specifying the stream with the image data.</param>
      <param name="pageNumber">An integer value that specifies the page number where the signature should be added.</param>
      <param name="signatureBounds">A <see cref="T:DevExpress.Pdf.PdfOrientedRectangle"/> object that represents the signature bounds in the default user space and signature rotation angle (in radians). A positive angle means counterclockwise rotation; a negative angle means clockwise rotation.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignature.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,System.IO.Stream,System.Int32,DevExpress.Pdf.PdfRectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignature"/> class with the specified certificate, image data represented by a stream, page number and the signature bounds represented by a <see cref="T:DevExpress.Pdf.PdfRectangle"/> object.</para>
      </summary>
      <param name="certificate">A <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> object that is an X.509 certificate.</param>
      <param name="imageData">A System.IO.Stream class descendant, specifying the stream with the image data.</param>
      <param name="pageNumber">An integer value that specifies the page number where the signature should be added.</param>
      <param name="signatureBounds">A <see cref="T:DevExpress.Pdf.PdfRectangle"/> object that represents the signature bounds in the default user space.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignature.AnnotationFlags">
      <summary>
        <para>Gets or sets the signature&#39;s characteristics.</para>
      </summary>
      <value>A set of annotation flags.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignature.ContactInfo">
      <summary>
        <para>Specifies the contact information which helps a recipient to verify the signature provided by the signer.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that is the contact information provided by the signer to a recipient.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignature.Location">
      <summary>
        <para>Gets or sets the signing location.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that is the location associated with the identity.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignature.Name">
      <summary>
        <para>Gets or sets the name of the person or authority signing the document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that is the person or authority name signing the document.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignature.Reason">
      <summary>
        <para>Gets or sets the reason for a document signature.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that is the  reason for a document signature.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignature.SigningTime">
      <summary>
        <para>Gets the time the document was signed.</para>
      </summary>
      <value>A nullable <see cref="T:System.DateTimeOffset"/> structure that is the date and time the document was signed.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfSignatureBuilder">
      <summary>
        <para>Allows you to build a signature.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignatureBuilder.#ctor(DevExpress.Pdf.IExternalSigner)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignatureBuilder"/> class with specified settings.</para>
      </summary>
      <param name="signer">An object used to generate a signature.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignatureBuilder.#ctor(DevExpress.Pdf.IExternalSigner,DevExpress.Pdf.PdfSignatureFieldInfo)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignatureBuilder"/> class with specified settings.</para>
      </summary>
      <param name="signer">An object used to generate a signature.</param>
      <param name="info">An object that contains information about a signature field.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignatureBuilder.#ctor(DevExpress.Pdf.IExternalSigner,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignatureBuilder"/> class with specified settings.</para>
      </summary>
      <param name="signer">An object used to generate a signature.</param>
      <param name="formFieldName">The name of the signature form field to sign.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureBuilder.ApplicationName">
      <summary>
        <para>Specifies the name of the software module used to create the signature.</para>
      </summary>
      <value>The software module name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureBuilder.CertificationLevel">
      <summary>
        <para>Specifies the signature certification level.</para>
      </summary>
      <value>One of the enumeration values specifying the certification level.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureBuilder.ContactInfo">
      <summary>
        <para>Gets or sets the signer&#39;s contact information.</para>
      </summary>
      <value>A contact information.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureBuilder.Location">
      <summary>
        <para>Gets or sets the signing location.</para>
      </summary>
      <value>The location associated with the signer&#39;s identity.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureBuilder.Name">
      <summary>
        <para>Gets or sets a signer&#39;s name.</para>
      </summary>
      <value>A signer&#39;s name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureBuilder.Reason">
      <summary>
        <para>Gets or sets the reason to apply a signature.</para>
      </summary>
      <value>A reason for a signature (for example, &quot;I Agree&quot;).</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignatureBuilder.SetImageData(System.Byte[])">
      <summary>
        <para>Sets the signature&#39;s image data.</para>
      </summary>
      <param name="imageData">A byte array that represents image data.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignatureBuilder.SetImageData(System.IO.Stream)">
      <summary>
        <para>Sets the signature&#39;s image data.</para>
      </summary>
      <param name="imageData">A stream used to load image data.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignatureBuilder.SetImageData(System.String)">
      <summary>
        <para>Sets the signature&#39;s image data.</para>
      </summary>
      <param name="fileName">A path to the image file.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureBuilder.SigningTime">
      <summary>
        <para>Specifies the date and time when the signature is applied.</para>
      </summary>
      <value>The date and time when the signature is applied.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfSignatureFieldInfo">
      <summary>
        <para>Contains information about the signature field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignatureFieldInfo.#ctor(System.Collections.Generic.IList{System.Int32})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignatureFieldInfo"/> class with specified settings.</para>
      </summary>
      <param name="pageNumbers"></param>
    </member>
    <member name="M:DevExpress.Pdf.PdfSignatureFieldInfo.#ctor(System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfSignatureFieldInfo"/> class with specified settings.</para>
      </summary>
      <param name="pageNumber">A number of the page to place the signature field.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureFieldInfo.Flags">
      <summary>
        <para>Gets or sets the signature field&#39;s annotation flags.</para>
      </summary>
      <value>One of the enumeration values indicating signature field&#39;s parameters</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureFieldInfo.Name">
      <summary>
        <para>Gets or sets the signature field&#39;s name.</para>
      </summary>
      <value>A field name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureFieldInfo.RotationAngle">
      <summary>
        <para>Gets or sets the signature field&#39;s rotation angle.</para>
      </summary>
      <value>The degree of rotation (from 0 to 360).</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureFieldInfo.SignatureBounds">
      <summary>
        <para>Gets or sets the signature field&#39;s bounds on the page (measured in the page coordinate system).</para>
      </summary>
      <value>A rectangle that defines the signature&#39;s location.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfSignatureFlags">
      <summary>
        <para>Lists values that specify various document-level characteristics related to signature fields.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfSignatureFlags.AppendOnly">
      <summary>
        <para>The document contains signatures that may be invalidated if the file is saved (written) in a way that alters its previous contents.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfSignatureFlags.None">
      <summary>
        <para>No signature is applied to the document.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfSignatureFlags.SignaturesExist">
      <summary>
        <para>The document contains at least one signature field.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfSignatureInfo">
      <summary>
        <para>Contains signature information.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureInfo.CertificationLevel">
      <summary>
        <para>Gets the signature&#39;s certification level.</para>
      </summary>
      <value>An enumeration value that indicates the certification level.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureInfo.ContactInfo">
      <summary>
        <para>Gets the signer&#39;s contact information.</para>
      </summary>
      <value>A contact information.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureInfo.FieldName">
      <summary>
        <para>Gets the name of a signature form field.</para>
      </summary>
      <value>The form field&#39;s name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureInfo.Filter">
      <summary>
        <para>Retrieves the name of the preferred signature handler.</para>
      </summary>
      <value>Preferred signature handler&#39;s name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureInfo.Location">
      <summary>
        <para>Gets the place where the signature was applied (for example, the company address).</para>
      </summary>
      <value>The location associated with the signer&#39;s identity.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureInfo.Reason">
      <summary>
        <para>Gets the reason to apply a signature.</para>
      </summary>
      <value>A reason for a signature (for example, &quot;I Agree&quot;).</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureInfo.SignerName">
      <summary>
        <para>Gets a signer&#39;s name.</para>
      </summary>
      <value>A signer&#39;s name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureInfo.SigningTime">
      <summary>
        <para>Gets the date and time when the signature is applied.</para>
      </summary>
      <value>The date and time when the signature is applied.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfSignatureInfo.SubFilter">
      <summary>
        <para>Gets information about the signature&#39;s format.</para>
      </summary>
      <value>The signature&#39;s encoding and key information.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfSignatureProfile">
      <summary>
        <para>Lists values used to specify the signature profile type.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfSignatureProfile.PAdES_BES">
      <summary>
        <para>PAdES (PDF Advanced Electronic Signature) baseline profile.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfSignatureProfile.Pdf">
      <summary>
        <para>Standard PDF profile for PKCS#7 signatures.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfSignatureType">
      <summary>
        <para>Lists values used to specify the signature type.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfSignatureType.PAdES">
      <summary>
        <para>A signature with a PAdES baseline profile.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfSignatureType.Pkcs7">
      <summary>
        <para>A PKCS#7 signature.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfSignatureType.Timestamp">
      <summary>
        <para>A document-level timestamp.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfStringAlignment">
      <summary>
        <para>Specifies the alignment of a text string relative to its layout rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfStringAlignment.Center">
      <summary>
        <para>The text is aligned in the center of the layout rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfStringAlignment.Far">
      <summary>
        <para>The text is aligned far from the original position of the layout rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfStringAlignment.Near">
      <summary>
        <para>The text is aligned near the layout.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfStringFormat">
      <summary>
        <para>Contains settings to format strings in a document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfStringFormat.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfStringFormat"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfStringFormat.#ctor(DevExpress.Pdf.PdfStringFormat)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfStringFormat"/> class with the specified string format.</para>
      </summary>
      <param name="format">A <see cref="T:DevExpress.Pdf.PdfStringFormat"/> object.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfStringFormat.#ctor(DevExpress.Pdf.PdfStringFormatFlags)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfStringFormat"/> class with the specified string format flags.</para>
      </summary>
      <param name="formatFlags">A <see cref="T:DevExpress.Pdf.PdfStringFormatFlags"/> enumeration value.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfStringFormat.Alignment">
      <summary>
        <para>Gets or sets the horizontal alignment of the string.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfStringAlignment"/> enumeration that specifies the horizontal alignment of the string.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfStringFormat.Clone">
      <summary>
        <para>Creates a copy of this <see cref="T:DevExpress.Pdf.PdfStringFormat"/> object.</para>
      </summary>
      <returns>The <see cref="T:DevExpress.Pdf.PdfStringFormat"/> object this method creates.</returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfStringFormat.FormatFlags">
      <summary>
        <para>Gets or sets a <see cref="T:DevExpress.Pdf.PdfStringFormatFlags"/> enumeration that contains format information.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfStringFormatFlags"/> enumeration that contains format information.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfStringFormat.GenericDefault">
      <summary>
        <para>Gets a generic default <see cref="T:DevExpress.Pdf.PdfStringFormat"/> object.</para>
      </summary>
      <value>The generic default <see cref="T:DevExpress.Pdf.PdfStringFormat"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfStringFormat.GenericTypographic">
      <summary>
        <para>Gets a generic typographic <see cref="T:DevExpress.Pdf.PdfStringFormat"/> object.</para>
      </summary>
      <value>A generic typographic <see cref="T:DevExpress.Pdf.PdfStringFormat"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfStringFormat.HotkeyPrefix">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.Pdf.PdfHotkeyPrefix"/> object for this <see cref="T:DevExpress.Pdf.PdfStringFormat"/> object.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Pdf.PdfHotkeyPrefix"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfStringFormat.LeadingMarginFactor">
      <summary>
        <para>Gets or sets a value that is the fraction of the font em-size used as both horizontal and vertical leading string margins.</para>
      </summary>
      <value>A double value that is the fraction of the font em-size used as both horizontal and vertical leading string margins.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfStringFormat.LineAlignment">
      <summary>
        <para>Specifies the vertical alignment of the string.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfStringAlignment"/> enumeration that represents the vertical alignment of the string.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfStringFormat.TrailingMarginFactor">
      <summary>
        <para>Gets or sets a value that is the fraction of the font em-size used as the horizontal trailing string margin.</para>
      </summary>
      <value>A double value that is the fraction of the font em-size used as the horizontal trailing string margin.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfStringFormat.Trimming">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.Pdf.PdfStringTrimming"/> enumeration for this <see cref="T:DevExpress.Pdf.PdfStringFormat"/> object.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfStringTrimming"/> enumeration that indicates how text is trimmed when it exceeds the edges of the layout rectangle.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfStringFormatFlags">
      <summary>
        <para>Specifies the display and layout information for text strings.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfStringFormatFlags.LineLimit">
      <summary>
        <para>Only entire lines are laid out in the formatting rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfStringFormatFlags.MeasureTrailingSpaces">
      <summary>
        <para>Includes the trailing space at the end of each line.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfStringFormatFlags.NoClip">
      <summary>
        <para>Overhanging parts of glyphs, and unwrapped text reaching outside the formatting rectangle are allowed to be displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfStringFormatFlags.NoWrap">
      <summary>
        <para>Text wrapping between lines when formatting within a rectangle is disabled. This flag is implied when a point is passed instead of a rectangle, or when the specified rectangle has a zero line length.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfStringTrimming">
      <summary>
        <para>Specifies how to trim characters from a string that does not completely fit into a layout shape.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfStringTrimming.Character">
      <summary>
        <para>The text is trimmed to the nearest character.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfStringTrimming.EllipsisCharacter">
      <summary>
        <para>The text is trimmed to the nearest character, and an ellipsis is inserted at the end of a trimmed line.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfStringTrimming.EllipsisWord">
      <summary>
        <para>The text is trimmed to the nearest word, and an ellipsis is inserted at the end of a trimmed line.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfStringTrimming.None">
      <summary>
        <para>Specifies no trimming.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfStringTrimming.Word">
      <summary>
        <para>The text is trimmed to the nearest word.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfTextAnnotation">
      <summary>
        <para>Represents a text annotation (a &quot;sticky note&quot; attached to a point in a document).</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextAnnotation.FilterType">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextAnnotation.IconName">
      <summary>
        <para>Gets the name of an icon that will be used in displaying the annotation.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the name of a text annotation icon.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextAnnotation.IsOpened">
      <summary>
        <para>Gets a value that indicates whether the annotation is opened on the page.</para>
      </summary>
      <value>true if the annotation is opened on the page; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextAnnotation.State">
      <summary>
        <para>Gets the annotation state.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that represents the annotation state.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextAnnotation.StateModel">
      <summary>
        <para>Gets a model corresponding to the specific annotation  state.</para>
      </summary>
      <value>A string that represents the state model of the annotation.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfTextAnnotationData">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextAnnotationData.IconName">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Pdf.PdfTextAnnotationIconName">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.Check">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.Circle">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.Comment">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.Cross">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.CrossHairs">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.Help">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.Insert">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.Key">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.NewParagraph">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.Note">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.Paragraph">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.RightArrow">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.RightPointer">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.Star">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.UpArrow">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextAnnotationIconName.UpLeftArrow">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Pdf.PdfTextExtractionOptions">
      <summary>
        <para>Contains text extraction options.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfTextExtractionOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfTextExtractionOptions"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextExtractionOptions.ClipToCropBox">
      <summary>
        <para>Gets or sets whether to clip extracted content to the crop box.</para>
      </summary>
      <value>true, to clip content to the crop box; otherwise false.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfTextFormField">
      <summary>
        <para>Represents a text field on a PDF form.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextFormField.DefaultText">
      <summary>
        <para>Gets the default text of the text form field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that represents the field&#39;s default text.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextFormField.MaxLen">
      <summary>
        <para>Gets the maximum length of the field&#39;s text, in characters.</para>
      </summary>
      <value>A nullable integer value specifying the maximum length of the field&#39;s text.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextFormField.Text">
      <summary>
        <para>Gets the text form field&#39;s text.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that represents the field&#39;s text.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfTextJustification">
      <summary>
        <para>Lists the form of justification that is used in laying out the overlay text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextJustification.Centered">
      <summary>
        <para>The centered form of justification is used in displaying the text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextJustification.LeftJustified">
      <summary>
        <para>The left justified form of justification is used in displaying the text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextJustification.RightJustified">
      <summary>
        <para>The right justified form of justification is used in displaying the text.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfTextMarkupAnnotation">
      <summary>
        <para>Represents a text markup annotation.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextMarkupAnnotation.FilterType">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextMarkupAnnotation.Quads">
      <summary>
        <para>Gets a collection of quadrilaterial points that encompass the areas of this annotation.</para>
      </summary>
      <value>An array of <see cref="T:DevExpress.Pdf.PdfQuadrilateral"/> objects that represent the quadrilateral points.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextMarkupAnnotation.Type">
      <summary>
        <para>Gets the type of a text markup annotation.</para>
      </summary>
      <value>One of the <see cref="T:DevExpress.Pdf.PdfTextMarkupAnnotationType"/> enumeration values that represents the text markup annotation type.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfTextMarkupAnnotationData">
      <summary>
        <para>Represents text markup annotation data.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextMarkupAnnotationData.MarkupType">
      <summary>
        <para>Specifies the text markup annotation type.</para>
      </summary>
      <value>One of the <see cref="T:DevExpress.Pdf.PdfTextMarkupAnnotationType"/> enumeration values that specifies the text markup annotation type.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextMarkupAnnotationData.Quads">
      <summary>
        <para>Gets a collection of quadrilaterals that encompass the text markup annotation drawing area.</para>
      </summary>
      <value>An array of <see cref="T:DevExpress.Pdf.PdfQuadrilateral"/> objects that represent the quadrilaterals points.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfTextMarkupAnnotationType">
      <summary>
        <para>Specifies the type of a text markup annotation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextMarkupAnnotationType.Highlight">
      <summary>
        <para>Highlight text markup annotation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextMarkupAnnotationType.Squiggly">
      <summary>
        <para>Squiggly underline text markup annotation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextMarkupAnnotationType.StrikeOut">
      <summary>
        <para>Strikeout text markup annotation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextMarkupAnnotationType.Underline">
      <summary>
        <para>Underline text markup annotation.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfTextSearchDirection">
      <summary>
        <para>Lists the available text search direction modes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextSearchDirection.Backward">
      <summary>
        <para>Specifies a backward search in the document.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextSearchDirection.Forward">
      <summary>
        <para>Specifies a forward search in the document.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfTextSearchParameters">
      <summary>
        <para>Specifies the options applied to a text search.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfTextSearchParameters.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfTextSearchParameters"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextSearchParameters.CaseSensitive">
      <summary>
        <para>Specifies whether or not to ignore the letter case when searching text in a PDF.</para>
      </summary>
      <value>true, to take into account the letter case; otherwise false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextSearchParameters.Direction">
      <summary>
        <para>Specifies the direction of text search.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfTextSearchDirection"/> enumeration value, specifying the search direction.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextSearchParameters.WholeWords">
      <summary>
        <para>Specifies whether or not to take into account only whole words when searching text in a PDF.</para>
      </summary>
      <value>true, if whole words should match the search criteria; otherwise false.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfTextSearchResults">
      <summary>
        <para>Provides the information related to a PDF text search result.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextSearchResults.Page">
      <summary>
        <para>Provides access to the page containing the text found.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPage"/> object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextSearchResults.PageIndex">
      <summary>
        <para>Indicates the zero-based index of a page that contains the found text.</para>
      </summary>
      <value>An integer value, specifying the zero-based page index.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextSearchResults.PageNumber">
      <summary>
        <para>Indicates the number of a page that contains the text that was found.</para>
      </summary>
      <value>An integer value, specifying the page number.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextSearchResults.Rectangles">
      <summary>
        <para>Provides access to the document area containing the text found.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfOrientedRectangle"/> objects.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextSearchResults.Status">
      <summary>
        <para>Indicates the current text search operation status.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfTextSearchStatus"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTextSearchResults.Words">
      <summary>
        <para>Returns the collection of words corresponding to the text that was found.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfWord"/> objects.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfTextSearchStatus">
      <summary>
        <para>Lists the values specifying the status of a PDF text search operation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextSearchStatus.Finished">
      <summary>
        <para>The text search has finished.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextSearchStatus.Found">
      <summary>
        <para>The text, which is being searched for, has been found.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfTextSearchStatus.NotFound">
      <summary>
        <para>The text, which is being searched for, has not been found.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfTimeStamp">
      <summary>
        <para>Allows you to generate document-level timestamps.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfTimeStamp.#ctor(DevExpress.Office.Tsp.ITsaClient)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfTimeStamp"/> class with specified settings.</para>
      </summary>
      <param name="tsaClient">An object used to generate a timestamp.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfTimeStamp.BuildSignature(System.Byte[],System.String)">
      <summary>
        <para>Builds the signature and returns its contents.</para>
      </summary>
      <param name="digest">The document&#39;s hash value.</param>
      <param name="digestAlgorithmOID">The hash algorithm&#39;s object identifier.</param>
      <returns>The signature contents.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfTimeStamp.BuildSignature(System.IO.Stream)">
      <summary>
        <para>Builds the signature for the specified document.</para>
      </summary>
      <param name="stream">A stream that contains the document to sign.</param>
      <returns>The document-level timestamp signature.</returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfTimeStamp.Filter">
      <summary>
        <para>Retrieves the name of the preferred signature handler.</para>
      </summary>
      <value>Preferred signature handler&#39;s name.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfTimeStamp.GetSignatureSize">
      <summary>
        <para>Returns the signature size (in bytes).</para>
      </summary>
      <returns>A signature&#39;s size (in bytes).</returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfTimeStamp.SubFilter">
      <summary>
        <para>Returns information about a timestamp signature&#39;s format.</para>
      </summary>
      <value>The signature&#39;s encoding and key information.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfTimeStamp.Type">
      <summary>
        <para>Returns the signature type.</para>
      </summary>
      <value>The signature type.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfUriAction">
      <summary>
        <para>Represents a URI (uniform resource identifier) action associated with the annotation.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfUriAction.IsMap">
      <summary>
        <para>Gets a value that determines whether to track the mouse position when the URI is resolved.</para>
      </summary>
      <value>true to track the mouse position when the URI is resolved; otherwise false. Default value: false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfUriAction.Uri">
      <summary>
        <para>Returns the URI (uniform resource identifier) action that causes a URI to be resolved.</para>
      </summary>
      <value>A string that identifies a resource on the Internet.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfWidgetAnnotation">
      <summary>
        <para>Represents the widget annotation for the interactive forms in a document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAnnotation.Action">
      <summary>
        <para>Gets an action that will be performed when the widget annotation is active.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAction"/> object that is the widget annotation action.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAnnotation.Actions">
      <summary>
        <para>Gets actions that will be performed when the annotation is activated.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAnnotationActions"/> object that represents actions that will be performed when the annotation is activated.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAnnotation.AppearanceCharacteristics">
      <summary>
        <para>Gets the appearance characteristics specifying the widget annotation&#39;s visual presentation on the page.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfWidgetAppearanceCharacteristics"/> object that represents the appearance characteristics of the widget annotation.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAnnotation.BorderStyle">
      <summary>
        <para>Gets a value specifying the width and line style that shall be used in drawing the widget annotation&#39;s border.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAnnotationBorderStyle"/> object that represents the widget annotation border style.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAnnotation.HighlightingMode">
      <summary>
        <para>Gets the widget annotation&#39;s highlighting mode - the visual effect that shall be used when the mouse button is pressed or held down inside its active area.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAnnotationHighlightingMode"/> enumeration value that represents the widget annotation&#39;s highlighting mode.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAnnotation.InteractiveFormField">
      <summary>
        <para>Gets an interactive form field that the widget annotation used to represent the appearance of fields and to manage user interactions.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfInteractiveFormField"/> object that is the interactive form field for the widget annotation.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfWidgetAnnotationTextPosition">
      <summary>
        <para>Lists values that specify the text position of the widget annotation&#39;s caption relative to its icon.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfWidgetAnnotationTextPosition.CaptionAboveTheIcon">
      <summary>
        <para>Caption above the icon.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfWidgetAnnotationTextPosition.CaptionBelowTheIcon">
      <summary>
        <para>Caption below the icon.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfWidgetAnnotationTextPosition.CaptionOverlaidDirectlyOnTheIcon">
      <summary>
        <para>Caption overlaid directly on the icon.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfWidgetAnnotationTextPosition.CaptionToTheLeftOfTheIcon">
      <summary>
        <para>Caption to the left of the icon.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfWidgetAnnotationTextPosition.CaptionToTheRightOfTheIcon">
      <summary>
        <para>Caption to the right of the icon</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfWidgetAnnotationTextPosition.NoCaption">
      <summary>
        <para>No caption, icon only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfWidgetAnnotationTextPosition.NoIcon">
      <summary>
        <para>No icon, caption only.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfWidgetAppearanceCharacteristics">
      <summary>
        <para>Contains appearance characteristics for constructing the annotation&#39;s appearance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAppearanceCharacteristics.AlternateCaption">
      <summary>
        <para>Gets the widget annotation&#39;s alternate (down) caption, which shall be displayed when the mouse button is pressed within its active area.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the widget annotation&#39;s alternate (down) caption.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAppearanceCharacteristics.AlternateIcon">
      <summary>
        <para>Gets the widget annotation&#39;s alternate (down) icon, which will be displayed when the mouse button is pressed within its active area.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfXObject"/> object that is a form XObject defining the widget annotation&#39;s alternate (down) icon.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAppearanceCharacteristics.BackgroundColor">
      <summary>
        <para>Gets a value specifying the color of the widget annotation&#39;s background.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfColor"/> object that is the color of the widget annotation&#39;s background.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAppearanceCharacteristics.BorderColor">
      <summary>
        <para>Gets the value specifying the color of the widget annotation&#39;s border.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfColor"/> object that is the color of the widget annotation&#39;s border.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAppearanceCharacteristics.Caption">
      <summary>
        <para>Gets the widget annotation&#39;s normal caption, which will be displayed when it is not interacting with the user.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the  widget annotation&#39;s normal caption.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAppearanceCharacteristics.IconFit">
      <summary>
        <para>Gets the value specifying how the widget annotation&#39;s icon will be displayed within its annotation rectangle.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfIconFit"/> enumeration value that specifies how the widget annotation&#39;s icon will be displayed within its annotation rectangle.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAppearanceCharacteristics.NormalIcon">
      <summary>
        <para>Gets the widget annotation&#39;s normal icon, which will be displayed when it is not interacting with the user.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfXObject"/> object that is the widget annotation&#39;s normal icon.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAppearanceCharacteristics.RolloverCaption">
      <summary>
        <para>Gets the widget annotation&#39;s rollover caption, which will be displayed when the user rolls the cursor into its active area without pressing the mouse button.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that is the widget annotation&#39;s rollover caption.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAppearanceCharacteristics.RolloverIcon">
      <summary>
        <para>Gets the widget annotation&#39;s rollover icon, which will be displayed when the user rolls the cursor into its active area without pressing the mouse button.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfXObject"/> object that is the form XObject defining the widget annotation&#39;s rollover icon.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAppearanceCharacteristics.RotationAngle">
      <summary>
        <para>Gets the number of degrees by which the widget annotation shall be rotated counterclockwise relative to the page.</para>
      </summary>
      <value>An integer value that is the widget annotation rotation angle.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWidgetAppearanceCharacteristics.TextPosition">
      <summary>
        <para>Gets the text position of the widget annotation&#39;s caption relative to its icon.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfWidgetAnnotationTextPosition"/> enumeration value that is the text position of the widget annotation.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfWord">
      <summary>
        <para>An individual word in a document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfWord.Characters">
      <summary>
        <para>Returns a list of the word&#39;s characters.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfCharacter"/> values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWord.Rectangles">
      <summary>
        <para>Returns rectangles surrounding the word.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfOrientedRectangle"/> values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWord.Segments">
      <summary>
        <para>Returns a list of the word&#39;s segments.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfWordSegment"/> values that represent the word&#39;s segments.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWord.Text">
      <summary>
        <para>Returns a Unicode representation of the word&#39;s characters.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfWordSegment">
      <summary>
        <para>Represents a word segment in a document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfWordSegment.Characters">
      <summary>
        <para>Returns a list of the word segment&#39;s characters.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Pdf.PdfCharacter"/> values.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWordSegment.Rectangle">
      <summary>
        <para>Returns a rectangle surrounding the word&#39;s segment.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOrientedRectangle"/> object that is the rectangle surrounding the word&#39;s segment.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfWordSegment.Text">
      <summary>
        <para>Returns a Unicode representation of the word&#39;s segments.</para>
      </summary>
      <value>A System.String value.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfXFAForm">
      <summary>
        <para>Represents an interactive form based on the Adobe XML Forms Architecture (XFA).</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfXFAForm.Content">
      <summary>
        <para>Gets the content of a XFA form.</para>
      </summary>
      <value>A <see cref="T:System.String"/> object that represents the content of a XFA form.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfXObject">
      <summary>
        <para>Represents a form XObject in a PDF document content.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfXObject.Metadata">
      <summary>
        <para>Provides access to a stream that contains the form X-Object metadata.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfMetadata"/> object that represents the metadata stream of the form X-Object.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfXObject.OpenPrepressInterface">
      <summary>
        <para>Gets an Open Prepress Interface (OPI) dictionary for the form XObject.</para>
      </summary>
      <value>A PdfOpenPrepressInterface object that is the OPI dictionary.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfXObject.OptionalContent">
      <summary>
        <para>Gets the value specifying the optional content properties for the form XObject.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfOptionalContent"/> object that contains the optional content properties for the form XObject.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfXObject.StructParent">
      <summary>
        <para>Gets the integer key of the form XObject&#39;s entry in the structural parent tree.</para>
      </summary>
      <value>A nullable integer value specifying the key of the form XObject&#39;s entry</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfXYZDestination">
      <summary>
        <para>Represents a destination that displays the PDF page with the <see cref="P:DevExpress.Pdf.PdfXYZDestination.Left"/>, <see cref="P:DevExpress.Pdf.PdfXYZDestination.Top"/> coordinates positioned at the upper-left corner of the window and the contents of the page magnified by the factor zoom (the <see cref="P:DevExpress.Pdf.PdfXYZDestination.Zoom"/> property).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfXYZDestination.#ctor(DevExpress.Pdf.PdfPage,System.Nullable{System.Double},System.Nullable{System.Double},System.Nullable{System.Double})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfXYZDestination"/> class with the specified page, top and left coordinates that will be shown on the upper-left corner of the window, and zoom level to show the page.</para>
      </summary>
      <param name="page">A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the PDF page with contents.</param>
      <param name="left">A nullable double value that is the left page coordinate.</param>
      <param name="top">A nullable double value that is the top page coordinate.</param>
      <param name="zoom">A nullable double value that is the zoom factor by which the page is magnified.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfXYZDestination.Left">
      <summary>
        <para>Gets the left horizontal coordinate positioned at the left edge of the window.</para>
      </summary>
      <value>A nullable double value that it the left horizontal coordinate.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfXYZDestination.Top">
      <summary>
        <para>Gets the top vertical page coordinate positioned at the top edge of the window.</para>
      </summary>
      <value>A nullable double value that is the vertical page coordinate.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfXYZDestination.Zoom">
      <summary>
        <para>Gets the contents of the page magnified by the zoom factor.</para>
      </summary>
      <value>A nullable double value that is the zoom factor.</value>
    </member>
    <member name="T:DevExpress.Pdf.Pkcs7Signer">
      <summary>
        <para>Allows you to create PKCS#7 signatures.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7Signer.#ctor(System.IO.Stream,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Pkcs7Signer"/> class with specified settings.</para>
      </summary>
      <param name="stream">A stream containing signature certificate (.pfx file format).</param>
      <param name="password">A password for a certificate.</param>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7Signer.#ctor(System.IO.Stream,System.String,DevExpress.Office.DigitalSignatures.HashAlgorithmType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Pkcs7Signer"/> class with specified settings.</para>
      </summary>
      <param name="stream">A stream that contains signature certificate (.pfx file format).</param>
      <param name="password">A password for a certificate.</param>
      <param name="hashAlgorithm">Signature&#39;s hash algorithm.</param>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7Signer.#ctor(System.IO.Stream,System.String,DevExpress.Office.DigitalSignatures.HashAlgorithmType,DevExpress.Office.Tsp.ITsaClient)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Pkcs7Signer"/> class with specified settings.</para>
      </summary>
      <param name="stream">A stream that contains signature certificate (.pfx file format).</param>
      <param name="password">A password for a certificate.</param>
      <param name="hashAlgorithm">Signature&#39;s hash algorithm.</param>
      <param name="tsaClient">An object that provides a timestamp.</param>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7Signer.#ctor(System.IO.Stream,System.String,DevExpress.Office.DigitalSignatures.HashAlgorithmType,DevExpress.Office.Tsp.ITsaClient,DevExpress.Office.DigitalSignatures.IOcspClient,DevExpress.Office.DigitalSignatures.ICrlClient,DevExpress.Pdf.PdfSignatureProfile)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Pkcs7Signer"/> class with specified settings.</para>
      </summary>
      <param name="stream">A stream that contains signature certificate (.pfx file format).</param>
      <param name="password">A password for a certificate.</param>
      <param name="hashAlgorithm">Signature&#39;s hash algorithm.</param>
      <param name="tsaClient">An object that provides a timestamp.</param>
      <param name="ocspClient">An object that performs Online Certificate Status Protocol (OCSP) request for a certificate.</param>
      <param name="crlClient">An object that performs a Certificate Revocation List (CRL) request for a certificate.</param>
      <param name="profile">An enumeration value type that specifies the signature&#39;s baseline profile type.</param>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7Signer.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Pkcs7Signer"/> class with specified settings.</para>
      </summary>
      <param name="certificate">An X.509 signature certificate.</param>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7Signer.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,DevExpress.Office.DigitalSignatures.HashAlgorithmType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Pkcs7Signer"/> class with specified settings.</para>
      </summary>
      <param name="certificate">An X.509 signature certificate.</param>
      <param name="hashAlgorithm">A signature&#39;s hash algorithm.</param>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7Signer.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,DevExpress.Office.DigitalSignatures.HashAlgorithmType,DevExpress.Office.Tsp.ITsaClient)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Pkcs7Signer"/> class with specified settings.</para>
      </summary>
      <param name="certificate">An X.509 signature certificate.</param>
      <param name="hashAlgorithm">Signature&#39;s hash algorithm.</param>
      <param name="tsaClient">An object that provides a timestamp.</param>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7Signer.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,DevExpress.Office.DigitalSignatures.HashAlgorithmType,DevExpress.Office.Tsp.ITsaClient,DevExpress.Office.DigitalSignatures.IOcspClient,DevExpress.Office.DigitalSignatures.ICrlClient,DevExpress.Pdf.PdfSignatureProfile)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Pkcs7Signer"/> class with specified settings.</para>
      </summary>
      <param name="certificate">An X.509 signature certificate.</param>
      <param name="hashAlgorithm">Signature&#39;s hash algorithm.</param>
      <param name="tsaClient">An object that provides a timestamp.</param>
      <param name="ocspClient">An object that performs Online Certificate Status Protocol (OCSP) request for a certificate.</param>
      <param name="crlClient">An object that performs a Certificate Revocation List (CRL) request for a certificate.</param>
      <param name="profile">An enumeration value type that specifies the signature&#39;s baseline profile type.</param>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7Signer.#ctor(System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Pkcs7Signer"/> class with specified settings.</para>
      </summary>
      <param name="fileName">A path to a signature certificate file (.pfx file format).</param>
      <param name="password">A password for a certificate.</param>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7Signer.#ctor(System.String,System.String,DevExpress.Office.DigitalSignatures.HashAlgorithmType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Pkcs7Signer"/> class with specified settings.</para>
      </summary>
      <param name="fileName">A path to a signature certificate file (.pfx file format).</param>
      <param name="password">A password for a certificate.</param>
      <param name="hashAlgorithm">A signature&#39;s hash algorithm.</param>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7Signer.#ctor(System.String,System.String,DevExpress.Office.DigitalSignatures.HashAlgorithmType,DevExpress.Office.Tsp.ITsaClient)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Pkcs7Signer"/> class with specified settings.</para>
      </summary>
      <param name="fileName">A path to a signature certificate file (.pfx file format).</param>
      <param name="password">A password for a certificate.</param>
      <param name="hashAlgorithm">Signature&#39;s hash algorithm.</param>
      <param name="tsaClient">An object that provides a timestamp.</param>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7Signer.#ctor(System.String,System.String,DevExpress.Office.DigitalSignatures.HashAlgorithmType,DevExpress.Office.Tsp.ITsaClient,DevExpress.Office.DigitalSignatures.IOcspClient,DevExpress.Office.DigitalSignatures.ICrlClient,DevExpress.Pdf.PdfSignatureProfile)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.Pkcs7Signer"/> class with specified settings.</para>
      </summary>
      <param name="fileName">A path to a signature certificate file (.pfx file format).</param>
      <param name="password">A password for a certificate.</param>
      <param name="hashAlgorithm">Signature&#39;s hash algorithm.</param>
      <param name="tsaClient">An object that provides a timestamp.</param>
      <param name="ocspClient">An object that performs Online Certificate Status Protocol (OCSP) request for a certificate.</param>
      <param name="crlClient">An object that performs a Certificate Revocation List (CRL) request for a certificate.</param>
      <param name="profile">An enumeration value type that specifies the signature&#39;s baseline profile type.</param>
    </member>
    <member name="T:DevExpress.Pdf.Pkcs7SignerBase">
      <summary>
        <para>A base class used to create PKCS#7 signatures.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7SignerBase.BuildSignature(System.Byte[],System.String)">
      <summary>
        <para>Builds the signature and returns its contents.</para>
      </summary>
      <param name="digest">The document&#39;s hash value.</param>
      <param name="digestAlgorithmOID">The hash algorithm&#39;s object identifier.</param>
      <returns>The signature contents.</returns>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7SignerBase.BuildSignature(System.IO.Stream)">
      <summary>
        <para>Builds the signature for the specified document.</para>
      </summary>
      <param name="stream">A stream that contains the document to sign.</param>
      <returns>The PKCS#7 signature.</returns>
    </member>
    <member name="P:DevExpress.Pdf.Pkcs7SignerBase.Filter">
      <summary>
        <para>Retrieves the name of the preferred signature handler.</para>
      </summary>
      <value>Preferred signature handler&#39;s name.</value>
    </member>
    <member name="M:DevExpress.Pdf.Pkcs7SignerBase.GetSignatureSize">
      <summary>
        <para>Returns the signature size (in bytes).</para>
      </summary>
      <returns>A signature&#39;s size (in bytes).</returns>
    </member>
    <member name="P:DevExpress.Pdf.Pkcs7SignerBase.SubFilter">
      <summary>
        <para>Returns information about a signature&#39;s format.</para>
      </summary>
      <value>The signature&#39;s encoding and key information.</value>
    </member>
    <member name="P:DevExpress.Pdf.Pkcs7SignerBase.Type">
      <summary>
        <para>Returns the signature type.</para>
      </summary>
      <value>null (Nothing in Visual Basic)</value>
    </member>
  </members>
</doc>