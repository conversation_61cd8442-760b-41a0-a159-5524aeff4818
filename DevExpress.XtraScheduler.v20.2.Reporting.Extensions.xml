<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.XtraScheduler.v20.2.Reporting.Extensions</name>
  </assembly>
  <members>
    <member name="N:DevExpress.XtraScheduler.Reporting.UI">
      <summary>
        <para>Contains a class providing access to the End-User Designer for Scheduler Reporting.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraScheduler.Reporting.UI.SchedulerReportDesignTool">
      <summary>
        <para>Enables access to the End-User Designer form which allows a Scheduler report to be edited by end-users.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraScheduler.Reporting.UI.SchedulerReportDesignTool.#ctor(DevExpress.XtraScheduler.Reporting.XtraSchedulerReport)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraScheduler.Reporting.UI.SchedulerReportDesignTool"/> class for the specified report.</para>
      </summary>
      <param name="report">A <see cref="T:DevExpress.XtraScheduler.Reporting.XtraSchedulerReport"/> instance representing the report for which the End-User Designer should be invoked.</param>
    </member>
  </members>
</doc>