<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.ExpressApp.Xpo.v20.2</name>
  </assembly>
  <members>
    <member name="N:DevExpress.ExpressApp.DC">
      <summary>
        <para>Contains classes and interfaces specific to the domain components functionality.</para>
      </summary>
    </member>
    <member name="N:DevExpress.ExpressApp.DC.ClassGeneration">
      <summary>
        <para>Contains classes and interfaces used in code generation for Domain Components.</para>
      </summary>
    </member>
    <member name="T:DevExpress.ExpressApp.DC.ClassGeneration.DCIntermediateObject">
      <summary>
        <para>The base class for intermediate classes that define many-to-many associations between Domain Components.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.DC.ClassGeneration.DCIntermediateObject.#ctor(DevExpress.Xpo.Session)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.DC.ClassGeneration.DCIntermediateObject"/> class in a particular <see cref="T:DevExpress.Xpo.Session"/>.</para>
      </summary>
      <param name="session">A DevExpress.Xpo.Session object which represents a persistent object&#39;s cache where the business object will be instantiated.</param>
    </member>
    <member name="P:DevExpress.ExpressApp.DC.ClassGeneration.DCIntermediateObject.LeftObject">
      <summary>
        <para>Gets the left object of the association.</para>
      </summary>
      <value>The left object of the association.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.DC.ClassGeneration.DCIntermediateObject.LeftObjectType">
      <summary>
        <para>Gets the type of the <see cref="P:DevExpress.ExpressApp.DC.ClassGeneration.DCIntermediateObject.LeftObject"/>.</para>
      </summary>
      <value>The type of the left object.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.DC.ClassGeneration.DCIntermediateObject.RightObject">
      <summary>
        <para>Gets the right object of the association.</para>
      </summary>
      <value>The right object of the association.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.DC.ClassGeneration.DCIntermediateObject.RightObjectType">
      <summary>
        <para>Gets the type of the <see cref="P:DevExpress.ExpressApp.DC.ClassGeneration.IDCIntermediateObject.RightObject"/>.</para>
      </summary>
      <value>The type of the right object.</value>
    </member>
    <member name="T:DevExpress.ExpressApp.DC.ClassGeneration.DCIntermediateObjectSettings">
      <summary>
        <para>Specifies settings for intermediate classes that define many-to-many associations between Domain Components.</para>
      </summary>
    </member>
    <member name="P:DevExpress.ExpressApp.DC.ClassGeneration.DCIntermediateObjectSettings.BaseType">
      <summary>
        <para>Specifies the base type for intermediate classes that define many-to-many associations between Domain Components.</para>
      </summary>
      <value>The System.Type object that is the base type for intermediate classes that define many-to-many associations. The default type is <see cref="T:DevExpress.ExpressApp.DC.ClassGeneration.DCIntermediateObject"/>.</value>
    </member>
    <member name="T:DevExpress.ExpressApp.DC.ClassGeneration.IDCIntermediateObject">
      <summary>
        <para>Declares members of the class that can be a base for intermediate classes defining many-to-many associations between Domain Components.</para>
      </summary>
    </member>
    <member name="P:DevExpress.ExpressApp.DC.ClassGeneration.IDCIntermediateObject.LeftObject">
      <summary>
        <para>Gets the left object of the association.</para>
      </summary>
      <value>The left object of the association.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.DC.ClassGeneration.IDCIntermediateObject.LeftObjectType">
      <summary>
        <para>Gets the type of the <see cref="P:DevExpress.ExpressApp.DC.ClassGeneration.IDCIntermediateObject.LeftObject"/>.</para>
      </summary>
      <value>The type of the left object.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.DC.ClassGeneration.IDCIntermediateObject.RightObject">
      <summary>
        <para>Gets the right object of the association.</para>
      </summary>
      <value>The right object of the association.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.DC.ClassGeneration.IDCIntermediateObject.RightObjectType">
      <summary>
        <para>Gets the type of the <see cref="P:DevExpress.ExpressApp.DC.ClassGeneration.IDCIntermediateObject.RightObject"/>.</para>
      </summary>
      <value>The type of the right object.</value>
    </member>
    <member name="T:DevExpress.ExpressApp.DC.DCBaseObject">
      <summary>
        <para>The default base persistent class for classes that are generated for Domain Components (DC).</para>
      </summary>
    </member>
    <member name="P:DevExpress.ExpressApp.DC.DCBaseObject.Oid">
      <summary>
        <para>Specifies the persistent object&#39;s identifier.</para>
      </summary>
      <value>A globally unique identifier which represents the persistent object&#39;s identifier.</value>
    </member>
    <member name="N:DevExpress.ExpressApp.MiddleTier">
      <summary>
        <para>Contains interfaces and classes used in the eXpressApp Framework middle tier application server.</para>
      </summary>
    </member>
    <member name="T:DevExpress.ExpressApp.MiddleTier.ServerApplication">
      <summary>
        <para>Manages an XAF Application Server.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.MiddleTier.ServerApplication.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.MiddleTier.ServerApplication"/> class.</para>
      </summary>
    </member>
    <member name="N:DevExpress.ExpressApp.Xpo">
      <summary>
        <para>Contains interfaces and classes that implement XPO integration.</para>
      </summary>
    </member>
    <member name="T:DevExpress.ExpressApp.Xpo.CalculatedPersistentAliasAttribute">
      <summary>
        <para>Applied to a business class. Allows you to dynamically configure a persistent alias for the target business class&#39; property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.CalculatedPersistentAliasAttribute.#ctor(System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.CalculatedPersistentAliasAttribute"/> class.</para>
      </summary>
      <param name="propertyName">A string holding the property name for which you would like to set up a persistent alias. This parameter&#39;s value is assigned to the <see cref="P:DevExpress.ExpressApp.Xpo.CalculatedPersistentAliasAttribute.PropertyName"/> property.</param>
      <param name="calculatedAliasPropertyName">A string holding the name of a public static property which returns the alias&#39; expression. This parameter&#39;s value is assigned to the <see cref="P:DevExpress.ExpressApp.Xpo.CalculatedPersistentAliasAttribute.CalculatedAliasPropertyName"/> property.</param>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.CalculatedPersistentAliasAttribute.CalculatedAliasPropertyName">
      <summary>
        <para>Specifies the name of a property which returns the persistent alias&#39; expression.</para>
      </summary>
      <value>A string holding the name of the public static property which returns the alias&#39; expression.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.CalculatedPersistentAliasAttribute.PropertyName">
      <summary>
        <para>Specifies the property name for which you would like to set up a persistent alias.</para>
      </summary>
      <value>A string holding the property name for which a persistent alias should be configured.</value>
    </member>
    <member name="N:DevExpress.ExpressApp.Xpo.Utils">
      <summary>
        <para>Contains utility classes used by XAF applications with XPO.</para>
      </summary>
    </member>
    <member name="T:DevExpress.ExpressApp.Xpo.Utils.StructTypeConverter`1">
      <summary>
        <para>Provides the capability to enable some built-in <see cref="T:DevExpress.Web.ASPxGridView"/> functions, such as the inplace editing and the selection column in an ASP.NET application when a composite key is used.</para>
      </summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.Utils.StructTypeConverter`1.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.Utils.StructTypeConverter`1"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.Utils.StructTypeConverter`1.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>
        <para>Indicates whether or not this converter can convert an object of the specified type to the type of this converter.</para>
      </summary>
      <param name="context">An ITypeDescriptorContext object that provides a format context.</param>
      <param name="sourceType">A Type object that is the type to be converted from.</param>
      <returns>true, if the certain type can be convert to the string; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.Utils.StructTypeConverter`1.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>
        <para>Converts the specified object to the object of this converter type subject to context and culture information.</para>
      </summary>
      <param name="context">An ITypeDescriptorContext object that provides a format context.</param>
      <param name="culture">An <see cref="T:System.Globalization.CultureInfo"/> object that specifies the current culture.</param>
      <param name="value">An <see cref="T:System.Object"/> to be converted.</param>
      <returns>An <see cref="T:System.Object"/> that is a converted object.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.Utils.StructTypeConverter`1.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>
        <para>Converts the specified object to the object of given type subject to context and culture information.</para>
      </summary>
      <param name="context">An ITypeDescriptorContext object that provides a format context.</param>
      <param name="culture">An <see cref="T:System.Globalization.CultureInfo"/> object that specifies the current culture.</param>
      <param name="value">An <see cref="T:System.Object"/> to be converted.</param>
      <param name="destinationType">A <see cref="T:System.Type"/> object that is the type to be converted to.</param>
      <returns>An <see cref="T:System.Object"/> that is a converted object.</returns>
    </member>
    <member name="T:DevExpress.ExpressApp.Xpo.XPInstantFeedbackSourceMappingMode">
      <summary>
        <para>Contains values specifying properties mapping modes in InstantFeedback or InstantFeedbackView mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.ExpressApp.Xpo.XPInstantFeedbackSourceMappingMode.AllProperties">
      <summary>
        <para>All properties should be mapped on a grid.</para>
      </summary>
    </member>
    <member name="F:DevExpress.ExpressApp.Xpo.XPInstantFeedbackSourceMappingMode.RequiredProperties">
      <summary>
        <para>Only visible properties should be mapped on a grid.</para>
      </summary>
    </member>
    <member name="T:DevExpress.ExpressApp.Xpo.XPObjectSpace">
      <summary>
        <para>An Object Space which is used for data manipulation via the DevExpress ORM Tool (XPO).</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.#ctor(DevExpress.ExpressApp.DC.ITypesInfo,DevExpress.ExpressApp.DC.Xpo.XpoTypeInfoSource,DevExpress.ExpressApp.Xpo.CreateUnitOfWorkHandler)">
      <summary>
        <para>Creates a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpace"/> class.</para>
      </summary>
      <param name="typesInfo">An <see cref="T:DevExpress.ExpressApp.DC.ITypesInfo"/> object providing access to XAF-related information on business classes.</param>
      <param name="xpoTypeInfoSource">An XpoTypeInfoSource object that is a source of XPO-related information on business classes.</param>
      <param name="createUnitOfWorkDelegate">A CreateUnitOfWorkHandler object that is a delegate that encapsulates a method for creating a <see cref="T:DevExpress.Xpo.UnitOfWork"/> for the Object Space&#39;s <see cref="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.Session"/>.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.#ctor(DevExpress.ExpressApp.DC.ITypesInfo,DevExpress.ExpressApp.DC.Xpo.XpoTypeInfoSource,System.UInt16,DevExpress.ExpressApp.Xpo.CreateUnitOfWorkHandler)">
      <summary>
        <para>Creates a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpace"/> class.</para>
      </summary>
      <param name="typesInfo">An <see cref="T:DevExpress.ExpressApp.DC.ITypesInfo"/> object that provides access to XAF-related information on business classes.</param>
      <param name="xpoTypeInfoSource">An XpoTypeInfoSource object that is a source of XPO-related information on business classes.</param>
      <param name="hostParametersMaxNumber">A UInt16 value that specifies the maximum number of hosted parameters.</param>
      <param name="createUnitOfWorkDelegate">A CreateUnitOfWorkHandler object that is a delegate that encapsulates a method for creating a <see cref="T:DevExpress.Xpo.UnitOfWork"/> for the Object Space&#39;s <see cref="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.Session"/>.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.#ctor(DevExpress.ExpressApp.DC.ITypesInfo,DevExpress.ExpressApp.DC.Xpo.XpoTypeInfoSource,System.UInt16,DevExpress.Xpo.UnitOfWork)">
      <summary>
        <para>Creates a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpace"/> class.</para>
      </summary>
      <param name="typesInfo">An <see cref="T:DevExpress.ExpressApp.DC.ITypesInfo"/> object providing access to XAF-related information on business classes.</param>
      <param name="xpoTypeInfoSource">An XpoTypeInfoSource object that is a source of XPO-related information on business classes.</param>
      <param name="hostParametersMaxNumber">A UInt16 value that specifies the maximum number of hosted parameters.</param>
      <param name="session">A <see cref="T:DevExpress.Xpo.UnitOfWork"/> object which is used by the current Object Space to load and save persistent objects.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.ApplyCriteria(System.Object,DevExpress.Data.Filtering.CriteriaOperator)">
      <summary>
        <para>Filters a particular collection on the server side.</para>
      </summary>
      <param name="collection">An Object representing the collection to be filtered.</param>
      <param name="criteria">A DevExpress.Data.Filtering.CriteriaOperator object that specifies the criteria used to filter objects on the server side.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.ApplyFilter(System.Object,DevExpress.Data.Filtering.CriteriaOperator)">
      <summary>
        <para>Filters a particular collection on the client side.</para>
      </summary>
      <param name="collection">A collection to be filtered.</param>
      <param name="filter">A DevExpress.Data.Filtering.CriteriaOperator object that specifies the criteria used to filter objects on the client side.</param>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.AsyncServerModeSourceDismissSession">
      <summary>
        <para>This property is obsolete (use the &#39;InstantFeedbackSourceDismissSession&#39; field instead).</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.AsyncServerModeSourceResolveSession">
      <summary>
        <para>This property is obsolete (use the &#39;InstantFeedbackSourceResolveSession&#39; field instead).</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CanApplyCriteria(System.Type)">
      <summary>
        <para>Indicates whether collections of a particular type can be filtered on the server side.</para>
      </summary>
      <param name="collectionType">A <see cref="T:System.Type"/> object specifying the type of collections whose server-side filtering capability must be determined.</param>
      <returns>true, if collections of the specified type can be filtered on the server side; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CanApplyFilter(System.Object)">
      <summary>
        <para>Indicates whether a particular collection can be filtered on the client side.</para>
      </summary>
      <param name="collection">A collection whose client-side filtering capability must be determined.</param>
      <returns>true, if the specified collection can be filtered on the client side; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CanInstantiate(System.Type)">
      <summary>
        <para>Indicates whether instances of a particular type can be created.</para>
      </summary>
      <param name="type">An object type for which it must be determined whether its instances can be created.</param>
      <returns>true, if instances of the specified type can be created; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CombineCriteria(DevExpress.Data.Filtering.CriteriaOperator[])">
      <summary>
        <para>Combines criteria operators passed as parameters.</para>
      </summary>
      <param name="criteriaOperators">A comma-separated list of criteria operators to be combined.</param>
      <returns>A CriteriaOperator object that is a group criteria operator combining the criteria operators passed as parameters.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CommitChangesAsync(System.Threading.CancellationToken)">
      <summary>
        <para>Asynchronously saves all the changes made to the persistent objects belonging to the current Object Space to the database.</para>
      </summary>
      <param name="cancellationToken">A CancellationToken object that delivers a cancellation notice to the running operation.</param>
      <returns>A Task object.</returns>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.Connection">
      <summary>
        <para>Gets the connection to the underlying data source.</para>
      </summary>
      <value>An IDbConnection object that is the connection to the underlying data source.</value>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.Contains(System.Object)">
      <summary>
        <para>Indicates whether a specified object belongs to the current Object Space.</para>
      </summary>
      <param name="obj">An object that represents the persistent object to be tested.</param>
      <returns>true if the specified persistent object belongs to the current Object Space; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CreateInstantFeedbackCollection(System.Type,DevExpress.Data.Filtering.CriteriaOperator)">
      <summary>
        <para>Creates an InstantFeedback mode collection that contains the specified type objects filtered according to the criteria.</para>
      </summary>
      <param name="objectType">The type of objects to be added to the collection.</param>
      <param name="criteria">The criteria used to filter objects to be added to the collection.</param>
      <returns>An InstantFeedback mode collection that contains the specified type objects filtered according to the criteria.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CreateInstantFeedbackView(System.Type,DevExpress.Data.Filtering.CriteriaOperator)">
      <summary>
        <para></para>
      </summary>
      <param name="objectType"></param>
      <param name="criteria"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CreateInstantFeedbackView(System.Type,DevExpress.Xpo.ServerViewProperty[],DevExpress.Data.Filtering.CriteriaOperator)">
      <summary>
        <para></para>
      </summary>
      <param name="objectType"></param>
      <param name="properties"></param>
      <param name="criteria"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CreateNestedObjectSpace">
      <summary>
        <para>Creates a nested Object Space.</para>
      </summary>
      <returns>An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> object that is a created nested Object Space.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CreateParseCriteriaScope">
      <summary>
        <para>Used when parsing a CriteriaOperator represented by a string and containing persistent objects.</para>
      </summary>
      <returns>An IDisposable object used to restore persistent objects from a serialized string.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CreateServerCollection(System.Type,DevExpress.Data.Filtering.CriteriaOperator)">
      <summary>
        <para>Creates and initializes a new instance of the <see cref="T:DevExpress.Xpo.XPServerCollectionSource"/> class with the current Object Space&#39;s <see cref="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.Session"/> and criteria-specific options.</para>
      </summary>
      <param name="objectType">The type of persistent objects to include into the collection.</param>
      <param name="criteria">The DevExpress.Data.Filtering.CriteriaOperator that specifies the criteria for object selection in a data store.</param>
      <returns>A server collection that includes the persistent objects of the specified type. In addition, only objects that satisfy the specified criteria will be retrieved to this collection.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CreateServerModeView(System.Type,DevExpress.Xpo.ServerViewProperty[],DevExpress.Data.Filtering.CriteriaOperator)">
      <summary>
        <para></para>
      </summary>
      <param name="objectType"></param>
      <param name="properties"></param>
      <param name="criteria"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CreateServerView(System.Type,DevExpress.Data.Filtering.CriteriaOperator)">
      <summary>
        <para></para>
      </summary>
      <param name="objectType"></param>
      <param name="criteria"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.CreateSortingCollection(System.Collections.Generic.IList{DevExpress.Xpo.SortProperty})">
      <summary>
        <para></para>
      </summary>
      <param name="sorting"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.Database">
      <summary>
        <para>Gets the name of the database used when a connection associated with the current Object Space&#39;s <see cref="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.Session"/> is opened.</para>
      </summary>
      <value>A string that is the name of the database used when a connection is opened.</value>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.Dispose">
      <summary>
        <para>Releases all resources used by an <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpace"/> object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.EnableObjectDeletionOnRemove(System.Object,System.Boolean)">
      <summary>
        <para>Enables/disables the deletion of persistent objects from the data source when they are removed from the specified collection.</para>
      </summary>
      <param name="collection">A collection of persistent objects that are requested to be removed from the database along with their removal from the collection.</param>
      <param name="enable">true, to enable the deletion from the database along with the removal from the collection; false, to disable it.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.Evaluate(System.Type,DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.CriteriaOperator)">
      <summary>
        <para>Evaluates the specified criteria for business objects of the given type.</para>
      </summary>
      <param name="objectType">A <see cref="T:System.Type"/> object that identifies the type of objects against which the expression will be evaluated.</param>
      <param name="expression">A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that specifies the expression to evaluate.</param>
      <param name="criteria">A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that specifies the filter criteria. The objects that match this criteria will be used to evaluate the expression.</param>
      <returns>The value evaluated.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.EvaluateAsync(System.Type,DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.CriteriaOperator,System.Threading.CancellationToken)">
      <summary>
        <para>Asynchronously evaluates the specified criteria for business objects of the given type.</para>
      </summary>
      <param name="objectType">A <see cref="T:System.Type"/> object that identifies the type of objects against which the expression will be evaluated.</param>
      <param name="expression">A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that specifies the expression to evaluate.</param>
      <param name="criteria">A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that specifies the filter criteria. The objects that match this criteria will be used to evaluate the expression.</param>
      <param name="cancellationToken">A CancellationToken object that delivers a cancellation notice to the running operation.</param>
      <returns>A Task that returns an object. This object represents the evaluated value. null (Nothing in Visual Basic) if no persistent object is found that matches the criteria.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.FindObject(System.Type,DevExpress.Data.Filtering.CriteriaOperator,System.Boolean)">
      <summary>
        <para>Searches for the first object which matches the specified criteria.</para>
      </summary>
      <param name="objectType">A <see cref="T:System.Type"/> object which is the type of objects to search for.</param>
      <param name="criteria">A DevExpress.Data.Filtering.CriteriaOperator descendant which is the criteria for matching persistent objects.</param>
      <param name="inTransaction">true if all objects (in the database and retrieved) are processed by the specified criteria; otherwise, false.</param>
      <returns>An object which is the first persistent object which matches the specified criteria. null (Nothing in Visual Basic) if there is no persistent object which matches the criteria.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.FindObjectAsync(System.Type,DevExpress.Data.Filtering.CriteriaOperator,System.Boolean,System.Threading.CancellationToken)">
      <summary>
        <para>Asynchronously searches for an object that matches the specified criteria.</para>
      </summary>
      <param name="objectType">A <see cref="T:System.Type"/> object which is the type of objects to search for.</param>
      <param name="criteria">A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the criteria the persistent object must match.</param>
      <param name="inTransaction">true, to enable the InTransaction mode; otherwise, false.</param>
      <param name="cancellationToken">A CancellationToken object that delivers a cancellation notice to the running operation.</param>
      <returns>A Task that returns an object. This object represents a persistent object that matches the specified criteria. null (Nothing in Visual Basic) if no persistent object is found that matches the criteria.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.FindObjectAsync``1(DevExpress.Data.Filtering.CriteriaOperator,System.Boolean,System.Threading.CancellationToken)">
      <summary>
        <para>Asynchronously searches for an object that matches the specified criteria. The specified generic parameter determines the object&#39;s type.</para>
      </summary>
      <param name="criteria">A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the criteria the persistent object must match.</param>
      <param name="inTransaction">true, to enable the InTransaction mode; otherwise, false.</param>
      <param name="cancellationToken">A CancellationToken object that delivers a cancellation notice to the running operation.</param>
      <typeparam name="ObjectType">The type of objects to search for.</typeparam>
      <returns>A Task that returns an object. This object represents a persistent object that matches the specified criteria. null (Nothing in Visual Basic) if no persistent object is found that matches the criteria.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.FindObjectAsync``1(DevExpress.Data.Filtering.CriteriaOperator,System.Threading.CancellationToken)">
      <summary>
        <para>Asynchronously searches for an object that matches the specified criteria. This object&#39;s type is designated by the specified generic parameter.</para>
      </summary>
      <param name="criteria">A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the criteria the persistent object must match.</param>
      <param name="cancellationToken">A CancellationToken object that delivers a cancellation notice to the running operation.</param>
      <typeparam name="ObjectType">A type of objects to search for.</typeparam>
      <returns>A Task that returns an object. This object represents a persistent object that matches the specified criteria. null (Nothing in Visual Basic) if no persistent object is found that matches the criteria.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.FindObjectSpaceByObject(System.Object)">
      <summary>
        <para>Determines the Object Space used to load and save a specified persistent object.</para>
      </summary>
      <param name="obj">The object whose Object Space must be determined.</param>
      <returns>An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> object which represents the Object Space used to load and save the specified persistent object. null (Nothing in VB.NET) if the specified object belongs to an XPO Session not managed by the ObjectSpace class.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.FirstOrDefault``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Boolean)">
      <summary>
        <para>Searches for the first object that matches the specified lambda expression. The generic parameter determines the object&#39;s type. This method takes uncommitted changes into account.</para>
      </summary>
      <param name="criteriaExpression">A lambda expression to search for an object.</param>
      <param name="inTransaction">true if the method takes unsaved changes into account; otherwise, false.</param>
      <typeparam name="ObjectType">The <see cref="T:System.Type"/> of an object to be returned.</typeparam>
      <returns>The first object that matches the specified lambda expression.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetCollectionObjectType(System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="collection"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetCollectionSorting(System.Object)">
      <summary>
        <para>Returns the sort settings for a particular collection.</para>
      </summary>
      <param name="collection">An Object that is the collection whose sort settings are requested.</param>
      <returns>A list of SortProperty objects specifying the sort order for the collection.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetCriteria(System.Object)">
      <summary>
        <para>Returns the criteria used to filter a particular collection on the server side.</para>
      </summary>
      <param name="collection">A collection whose server-side filter must be retrieved.</param>
      <returns>A DevExpress.Data.Filtering.CriteriaOperator object that specifies the criteria used to filter objects on the server side.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetDisplayableProperties(System.Object)">
      <summary>
        <para>Gets the properties considered visible by the specified collection.</para>
      </summary>
      <param name="collection">A collection whose visible properties will be retrieved.</param>
      <returns>A semicolon-separated list of property names considered visible by a particular collection.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetEvaluatorContextDescriptor(System.Type)">
      <summary>
        <para>Creates an instance of the EvaluatorContextDescriptor that is used to supply metadata on the specified type to the ExpressionEvaluator objects.</para>
      </summary>
      <param name="type">A type for which an instance of the EvaluatorContextDescriptor class must be created.</param>
      <returns>An EvaluatorContextDescriptor object initialized for the specified type.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetFilter(System.Object)">
      <summary>
        <para>Returns the criteria used to filter a particular collection on the client side.</para>
      </summary>
      <param name="collection">An object representing the collection whose client-side filter must be retrieved.</param>
      <returns>A DevExpress.Data.Filtering.CriteriaOperator object that specifies the criteria used to filter objects on the client side.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetIntermediateObjectReferences(System.Object,System.Object@,System.Object@)">
      <summary>
        <para></para>
      </summary>
      <param name="obj"></param>
      <param name="left"></param>
      <param name="right"></param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetKeyValue(System.Object)">
      <summary>
        <para>Returns the key property&#39;s value of the specified persistent object.</para>
      </summary>
      <param name="obj">An object whose key property&#39;s value is requested.</param>
      <returns>An object which is the value of the specified object&#39;s key property.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetKeyValueAsString(System.Object)">
      <summary>
        <para>Returns the key property&#39;s value of the specified object, converted to a string representation.</para>
      </summary>
      <param name="obj">An object whose key property value is requested.</param>
      <returns>A string which is the value of the specified object&#39;s key property.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetListServer(System.Object)">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="collection"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetObject(System.Object)">
      <summary>
        <para>Retrieves an object from another Object Space to the current Object Space.</para>
      </summary>
      <param name="obj">An object that represents a template object from another Object Space.</param>
      <returns>An object retrieved from the database via the current Object Space.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetObjectAsync(System.Object,System.Threading.CancellationToken)">
      <summary>
        <para>Asynchronously retrieves an object that corresponds to an <see cref="T:DevExpress.ExpressApp.IObjectRecord"/> wrapper or object from another Object Space.</para>
      </summary>
      <param name="obj">An object that represents a template object from another Object Space.</param>
      <param name="cancellationToken">A CancellationToken object that delivers a cancellation notice to the running operation.</param>
      <returns>A Task that returns an object. This object represents the evaluated value.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetObjectByKey(System.Type,System.Object)">
      <summary>
        <para>Returns the persistent object that has the specified value for its key property.</para>
      </summary>
      <param name="objectType">A <see cref="T:System.Type"/> object which is the type of the object to search for.</param>
      <param name="key">An object that is the persistent object&#39;s key property value.</param>
      <returns>A persistent object with the specified value for its key property.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetObjectByKeyAsync(System.Type,System.Object,System.Threading.CancellationToken)">
      <summary>
        <para>Returns the persistent object that has the specified value for its key property.</para>
      </summary>
      <param name="objectType">A <see cref="T:System.Type"/> object which is the type of objects to search for.</param>
      <param name="key">An object that is the persistent object&#39;s key property value.</param>
      <param name="cancellationToken">A CancellationToken object that delivers a cancellation notice to the running operation.</param>
      <returns>A Task that returns the searched object. null (Nothing in Visual Basic) if no persistent object is found with the specified key.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetObjectByKeyAsync``1(System.Object,System.Threading.CancellationToken)">
      <summary>
        <para>Returns the persistent object that has the specified value for its key property.</para>
      </summary>
      <param name="key">An object that is the persistent object&#39;s key property value.</param>
      <param name="cancellationToken">A CancellationToken object that delivers a cancellation notice to the running operation.</param>
      <typeparam name="ObjectType">The type of an object to search for.</typeparam>
      <returns>A Task that returns the searched object. null (Nothing in Visual Basic) if no persistent object is found with the specified key.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetObjectKey(System.Type,System.String)">
      <summary>
        <para>Converts the key property value string representation into its actual type.</para>
      </summary>
      <param name="objectType">A <see cref="T:System.Type"/> object which is the type of the object whose key property value is to be converted.</param>
      <param name="objectKeyString">A string that is the key property value to be converted.</param>
      <returns>An object that is the value of the specified type&#39;s key property.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetObjectsQuery``1(System.Boolean)">
      <summary>
        <para>Gets a queryable data structure that provides functionality to evaluate queries against a specific business object type.</para>
      </summary>
      <param name="inTransaction">true, if querying a data store for objects includes all in-memory changes into query results; otherwise, false.</param>
      <typeparam name="T"></typeparam>
      <returns>An <see cref="T:System.Linq.IQueryable`1"/> object that provides functionality to evaluate queries against a specific business object type.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetObjectsToDelete(System.Boolean)">
      <summary>
        <para>Returns a collection of persistent objects that will be deleted when the current transaction is committed, including objects that will be deleted in the parent transaction(s), optionally.</para>
      </summary>
      <param name="includeParent">true, to include persistent objects that will be deleted in the parent transaction(s); otherwise, false.</param>
      <returns>The collection of persistent objects that will be deleted when the current transaction is committed.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetObjectsToSave(System.Boolean)">
      <summary>
        <para>Returns a collection of persistent objects that will be saved when the current transaction is committed, including objects that will be saved in the parent transaction(s), optionally.</para>
      </summary>
      <param name="includeParent">true, to include persistent objects that will be saved in the parent transaction(s); otherwise, false.</param>
      <returns>The collection of persistent objects that will be saved when the current transaction is committed.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.GetTopReturnedObjectsCount(System.Object)">
      <summary>
        <para>Returns the maximum number of objects to be retrieved by the specified collection from a data store.</para>
      </summary>
      <param name="collection">A collection that is the subject for determining the number of objects that can be retrieved by it.</param>
      <returns>An integer value specifying the maximum number of objects that can be retrieved by the specified collection from the database. 0 indicates that all objects will be retrieved.</returns>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.InstantFeedbackMappingMode">
      <summary>
        <para>Specifies what properties should be mapped on a grid in the InstantFeedback or InstantFeedbackView mode.</para>
      </summary>
      <value>The <see cref="T:DevExpress.ExpressApp.Xpo.XPInstantFeedbackSourceMappingMode"/> enumeration value that specifies a properties mapping mode in the InstantFeedback or InstantFeedbackView mode.</value>
    </member>
    <member name="F:DevExpress.ExpressApp.Xpo.XPObjectSpace.InstantFeedbackSourceDismissSession">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.ExpressApp.Xpo.XPObjectSpace.InstantFeedbackSourceResolveSession">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.IsAsyncOperationInProgress">
      <summary>
        <para>Specifies whether an asynchronous operation is in progress.</para>
      </summary>
      <value>true if the operation is in progress; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.IsCollectionLoaded(System.Object)">
      <summary>
        <para>Indicates whether a particular collection is loaded with objects from the database.</para>
      </summary>
      <param name="collection">An object representing the collection for which it must be determined whether it is loaded with objects from the database.</param>
      <returns>true, if the specified collection is loaded with objects from the database; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.IsConnected">
      <summary>
        <para>Indicates whether the <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpace"/>&#39;s Session is connected to the database.</para>
      </summary>
      <value>true, if the <see cref="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.Session"/> is connected to the database; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.IsDeletedObject(System.Object)">
      <summary>
        <para>Indicates whether the specified persistent object is deleted and this is committed to the database.</para>
      </summary>
      <param name="obj">The object to test.</param>
      <returns>true if the specified object is marked as deleted; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.IsDeletionDeferredType(System.Type)">
      <summary>
        <para>Returns a value that indicates if the deferred deletion is enabled for persistent objects of a given type.</para>
      </summary>
      <param name="type">A <see cref="T:System.Type"/> object that is a type of persistent object.</param>
      <returns>true, if the deferred deletion is enabled; otherwise - false.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.IsDisposedObject(System.Object)">
      <summary>
        <para>Determines whether an object has been disposed of.</para>
      </summary>
      <param name="obj">An object to test.</param>
      <returns>true, if the specified object has been disposed of; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.IsIntermediateObject(System.Object)">
      <summary>
        <para></para>
      </summary>
      <param name="obj"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.IsNewObject(System.Object)">
      <summary>
        <para>Indicates whether a specified object has been created but has not been saved to the database.</para>
      </summary>
      <param name="obj">A object to be tested.</param>
      <returns>true if the specified object has not been yet saved to the database; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.IsObjectDeletionOnRemoveEnabled(System.Object)">
      <summary>
        <para>Indicates whether the deletion of persistent objects from the data source when they are removed from the specified collection is enabled.</para>
      </summary>
      <param name="collection">A collection of persistent objects that are requested to be removed from the database along with their removal from the collection.</param>
      <returns>true, if the deletion from the database along with removal from the collection is enabled; false, if disabled.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.IsObjectToDelete(System.Object)">
      <summary>
        <para>Indicates whether the specified object has been deleted but not committed in the transaction currently in progress.</para>
      </summary>
      <param name="obj">The persistent object to test.</param>
      <returns>true, if the specified object is marked as deleted in the transaction currently in progress; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.IsObjectToSave(System.Object)">
      <summary>
        <para>Indicates whether the specified object has been added, deleted or modified, but not committed in the transaction currently in progress.</para>
      </summary>
      <param name="obj">An object for which it is requested whether it should be saved.</param>
      <returns>true, if the specified object has been added, deleted or modified, but not committed; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.LoadAsync(System.Object,System.Threading.CancellationToken)">
      <summary>
        <para>Asynchronously loads the specified <see cref="T:DevExpress.Xpo.XPCollection"/> collection.</para>
      </summary>
      <param name="collection">An <see cref="T:DevExpress.Xpo.XPCollection"/> collection to be loaded.</param>
      <param name="cancellationToken">A CancellationToken object that delivers a cancellation notice to the running operation.</param>
      <returns>A <see cref="T:System.Threading.Tasks.Task"/> object.</returns>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.ModifiedObjects">
      <summary>
        <para>Returns a collection of objects that have been created, modified or deleted after they were retrieved or committed.</para>
      </summary>
      <value>An IList collection that contains the modified objects belonging to the current Object Space.</value>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.ParseCriteria(System.String)">
      <summary>
        <para>Parses the specified criteria expression in the <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpace"/>&#39;s <see cref="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.Session"/>.</para>
      </summary>
      <param name="criteria">A string representation of the criteria.</param>
      <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that specifies an expression.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.PreFetchAsync(System.Object,System.String[],System.Threading.CancellationToken)">
      <summary>
        <para>Asynchronously forces associated collection data loading and delayed property loading for specified parent objects.</para>
      </summary>
      <param name="collection">A collection of parent objects.</param>
      <param name="propertyNames">An array of strings which are the names of the associated collection properties or delayed properties.</param>
      <param name="cancellationToken">A CancellationToken object that delivers a cancellation notice to the running operation.</param>
      <returns>A Task object.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.RaiseObjectPropertyChanged(System.Object,System.String)">
      <summary>
        <para>This method is intended for internal use.</para>
      </summary>
      <param name="obj">An object whose property has been changed.</param>
      <param name="propertyName">A string that is the name of the property that has been changed.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.ReloadCollection(System.Object)">
      <summary>
        <para>Clears a collection, and marks it to be reloaded with data from the database.</para>
      </summary>
      <param name="collection">An Object that is the collection to be reloaded.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.ReloadObject(System.Object)">
      <summary>
        <para>Reloads the state of the specified persistent object and its aggregated objects from the data store.</para>
      </summary>
      <param name="obj">An object which represents the persistent object whose state needs to be reloaded.</param>
      <returns>The object specified by the obj parameter after it has been reloaded.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.ReloadObjectAsync(System.Object,System.Threading.CancellationToken)">
      <summary>
        <para>Asynchronously reloads the state of the specified persistent object and its aggregated objects from the data store.</para>
      </summary>
      <param name="obj">An object which represents the persistent object whose state needs to be reloaded.</param>
      <param name="cancellationToken">A CancellationToken object that delivers a cancellation notice to the running operation.</param>
      <returns>A Task that returns an object. This object is the object specified by the obj parameter after it has been reloaded.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.RemoveFromModifiedObjects(System.Object)">
      <summary>
        <para>Removes the specified object from the list of objects to be committed.</para>
      </summary>
      <param name="obj">An object to be removed from the list of modified objects.</param>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.Session">
      <summary>
        <para>Provides access to a Session that is used to load and save persistent objects.</para>
      </summary>
      <value>A DevExpress.Xpo.Session object which is used by the current Object Space to load and save persistent objects.</value>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.SetCollectionSorting(System.Object,System.Collections.Generic.IList{DevExpress.Xpo.SortProperty})">
      <summary>
        <para>Applies the specified sorting to a given collection.</para>
      </summary>
      <param name="collection">An object that is a collection to be sorted.</param>
      <param name="sorting">An IList&lt;<see cref="T:DevExpress.Xpo.SortProperty"/>&gt; object that specifies the sorting to be applied.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.SetDisplayableProperties(System.Object,System.String)">
      <summary>
        <para>Sets the properties to be visible by the specified collection.</para>
      </summary>
      <param name="collection">A collection whose visible properties are set.</param>
      <param name="displayableProperties">A semicolon-separated list of property names to be visible by a particular collection.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.SetPrefetchPropertyNames(System.Object,System.String[])">
      <summary>
        <para></para>
      </summary>
      <param name="collection"></param>
      <param name="propertyNames"></param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.SetTopReturnedObjectsCount(System.Object,System.Int32)">
      <summary>
        <para>Sets the maximum number of objects that can be retrieved from the specified collection in a data store.</para>
      </summary>
      <param name="collection">A collection from which a number of objects will be retrieved.</param>
      <param name="topReturnedObjectsCount">An integer value specifying the maximum number of objects that can be retrieved by the specified collection from the database. 0 indicates that all objects will be retrieved.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.ToListAsync``1(System.Object,System.Threading.CancellationToken)">
      <summary>
        <para>Asynchronously enumerates all elements in a collection and saves them to a list.</para>
      </summary>
      <param name="collection">A collection to be enumerated.</param>
      <param name="cancellationToken">A CancellationToken object that delivers a cancellation notice to the running operation.</param>
      <typeparam name="T">The type of elements in the specified collection.</typeparam>
      <returns>A Task that returns a list of objects.</returns>
    </member>
    <member name="F:DevExpress.ExpressApp.Xpo.XPObjectSpace.TrackPropertyModifications">
      <summary>
        <para>Specifies whether or not the current <see cref="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.Session"/> tracks persistent object property modifications.</para>
      </summary>
      <value>true, if the <see cref="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.Session"/> tracks persistent object property modifications; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpace.TryGetObjectHandle(System.Object,System.String@)">
      <summary>
        <para>Creates a handle for the specified object if this object is persistent and is not a new one.</para>
      </summary>
      <param name="theObject">An object for which a handle is created.</param>
      <param name="handle">A string that is the handle created for the specified object.</param>
      <returns>true if a handle has been created successfully for the specified object; otherwise false.</returns>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpace.XpoTypeInfoSource">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider">
      <summary>
        <para>Provides Object Space in XPO-based XAF applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.#ctor(DevExpress.ExpressApp.Xpo.IXpoDataStoreProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider"/> class.</para>
      </summary>
      <param name="dataStoreProvider">An IXpoDataStoreProvider object.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.#ctor(DevExpress.ExpressApp.Xpo.IXpoDataStoreProvider,DevExpress.ExpressApp.DC.ITypesInfo,DevExpress.ExpressApp.DC.Xpo.XpoTypeInfoSource)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider"/> class.</para>
      </summary>
      <param name="dataStoreProvider">An IXpoDataStoreProvider object.</param>
      <param name="typesInfo">An <see cref="T:DevExpress.ExpressApp.DC.ITypesInfo"/> object that supplies metadata on types used in the XAF application.</param>
      <param name="xpoTypeInfoSource">An XpoTypeInfoSource object that is a source of XPO-related information on business classes.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.#ctor(DevExpress.ExpressApp.Xpo.IXpoDataStoreProvider,DevExpress.ExpressApp.DC.ITypesInfo,DevExpress.ExpressApp.DC.Xpo.XpoTypeInfoSource,System.Boolean,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider"/> class.</para>
      </summary>
      <param name="dataStoreProvider">An IXpoDataStoreProvider object.</param>
      <param name="typesInfo">An <see cref="T:DevExpress.ExpressApp.DC.ITypesInfo"/> object that supplies metadata on types used in the XAF application.</param>
      <param name="xpoTypeInfoSource">An XpoTypeInfoSource object that is a source of XPO-related information on business classes.</param>
      <param name="threadSafe">true, if the <see cref="T:DevExpress.Xpo.ThreadSafeDataLayer"/> Data Access Layer should be used; otherwise, false.</param>
      <param name="useSeparateDataLayers">true, if a separate <see cref="T:DevExpress.Xpo.SimpleDataLayer"/> Data Access Layer should be created for each Object Space when the threadSafe parameter is set to false; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.#ctor(DevExpress.ExpressApp.Xpo.IXpoDataStoreProvider,System.Boolean,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider"/> class.</para>
      </summary>
      <param name="dataStoreProvider">An IXpoDataStoreProvider object.</param>
      <param name="threadSafe">true, if the <see cref="T:DevExpress.Xpo.ThreadSafeDataLayer"/> Data Access Layer should be used; otherwise, false.</param>
      <param name="useSeparateDataLayers">true, if a separate <see cref="T:DevExpress.Xpo.SimpleDataLayer"/> Data Access Layer should be created for each Object Space when the threadSafe parameter is set to false; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.#ctor(System.Data.IDbConnection)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider"/> class.</para>
      </summary>
      <param name="connection">An IDbConnection object that specifies the database connection.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider"/> class.</para>
      </summary>
      <param name="connectionString">A string value that is the application&#39;s connection string.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.#ctor(System.String,System.Data.IDbConnection)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider"/> class.</para>
      </summary>
      <param name="connectionString">A string value that is the application&#39;s connection string.</param>
      <param name="connection">An IDbConnection object that specifies the database connection.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.#ctor(System.String,System.Data.IDbConnection,System.Boolean,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider"/> class.</para>
      </summary>
      <param name="connectionString">A string value that is the application&#39;s connection string.</param>
      <param name="connection">An IDbConnection object that specifies the database connection.</param>
      <param name="threadSafe">true, if the <see cref="T:DevExpress.Xpo.ThreadSafeDataLayer"/> Data Access Layer should be used; otherwise, false.</param>
      <param name="useSeparateDataLayers">true, if a separate <see cref="T:DevExpress.Xpo.SimpleDataLayer"/> Data Access Layer should be created for each Object Space when the threadSafe parameter is set to false; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.CheckCompatibilityType">
      <summary>
        <para>Specifies how the database and application compatibility is checked.</para>
      </summary>
      <value>A Nullable&lt;<see cref="T:DevExpress.ExpressApp.CheckCompatibilityType"/>&gt; enumeration value specifying how the database and application compatibility is checked.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.ConnectionString">
      <summary>
        <para>Specifies the connection string used by the Object Space Provider&#39;s data layer.</para>
      </summary>
      <value>A connection string used by the Object Space Provider&#39;s data layer.</value>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.CreateObjectSpace">
      <summary>
        <para>Instantiates an Object Space.</para>
      </summary>
      <returns>An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> object that is the instantiated Object Space.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.CreateUpdatingObjectSpace(System.Boolean)">
      <summary>
        <para>Instantiates an Object Space to be used to update the database.</para>
      </summary>
      <param name="allowUpdateSchema">true, to allow schema updates; otherwise, false.</param>
      <returns>An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> object that is the instantiated Object Space that can be used to update the database.</returns>
    </member>
    <member name="E:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.CustomizeTableName">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.DataLayer">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.Dispose">
      <summary>
        <para>Releases all resources used by an <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider"/> object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.GetDataStoreProvider(System.String,System.Data.IDbConnection)">
      <summary>
        <para>Returns the data store provider.</para>
      </summary>
      <param name="connectionString">A string containing the database connection settings.</param>
      <param name="connection">An IDbConnection object specifying the database connection.</param>
      <returns>An IXpoDataStoreProvider object.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.GetDataStoreProvider(System.String,System.Data.IDbConnection,System.Boolean)">
      <summary>
        <para>Returns the data store provider.</para>
      </summary>
      <param name="connectionString">A string containing the database connection settings.</param>
      <param name="connection">An IDbConnection object specifying the database connection.</param>
      <param name="enablePoolingInConnectionString">true, if the connection pooling is enabled; otherwise, false.</param>
      <returns>An IXpoDataStoreProvider object.</returns>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.HostParametersMaxNumber">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.InstantFeedbackMappingMode">
      <summary>
        <para>Specifies what properties should be mapped on a grid in the InstantFeedback or InstantFeedbackView mode for all Object Spaces.</para>
      </summary>
      <value>The <see cref="T:DevExpress.ExpressApp.Xpo.XPInstantFeedbackSourceMappingMode"/> enumeration value that specifies a properties mapping mode in the InstantFeedback or InstantFeedbackView mode.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.IsDisposed">
      <summary>
        <para>Indicates whether an Object Space Provider has been disposed of.</para>
      </summary>
      <value>true, if the current Object Space Provider has been disposed of; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.ModuleInfoType">
      <summary>
        <para>Gets the type of the class whose objects are persisted to the ModuleInfo table in the database.</para>
      </summary>
      <value>A type of the class corresponding to the ModuleInfo database table.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.SchemaUpdateMode">
      <summary>
        <para>Specifies how to handle compatibility checking for the database associated with the current <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.ExpressApp.SchemaUpdateMode"/> enumeration value that specifies how to handle database compatibility checking.</value>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.SetDataStoreProvider(DevExpress.ExpressApp.Xpo.IXpoDataStoreProvider)">
      <summary>
        <para></para>
      </summary>
      <param name="provider"></param>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.ThreadSafe">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.TypesInfo">
      <summary>
        <para>Supplies metadata on types used in an XAF application.</para>
      </summary>
      <value>An <see cref="T:DevExpress.ExpressApp.DC.ITypesInfo"/> object which supplies metadata on types used in an XAF application.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.XPDictionary">
      <summary>
        <para>Returns a base class for metadata providers.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Xpo.Metadata.XPDictionary"/> object that is a base class for metadata providers.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XPObjectSpaceProvider.XpoTypeInfoSource">
      <summary>
        <para>Returns a source of XPO-related information on business classes.</para>
      </summary>
      <value>An XpoTypeInfoSource object that is a source of XPO-related information on business classes.</value>
    </member>
    <member name="T:DevExpress.ExpressApp.Xpo.XpoDataView">
      <summary>
        <para>A lightweight read-only list of data records (a data view) retrieved from a database without loading complete XPO objects. Can be queried much more quickly than a real objects collection.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XpoDataView.#ctor(DevExpress.ExpressApp.Xpo.XPObjectSpace,System.Type,System.Collections.Generic.IList{DevExpress.ExpressApp.Utils.DataViewExpression},DevExpress.Data.Filtering.CriteriaOperator,System.Collections.Generic.IList{DevExpress.Xpo.SortProperty})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XpoDataView"/> class with the specified settings. Data view columns are specified using the list of <see cref="T:DevExpress.ExpressApp.Utils.DataViewExpression"/> objects. For internal use only.</para>
      </summary>
      <param name="objectSpace">An <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpace"/> object used to access data in XAF applications based on the XPO data model.</param>
      <param name="objectType">The Type of requested persistent objects.</param>
      <param name="expressions">An IList&lt;<see cref="T:DevExpress.ExpressApp.Utils.DataViewExpression"/>&gt; list that specifies data view column names and expressions used to compute column values. These column names can be used for sorting the data view using the sorting parameters.</param>
      <param name="criteria">A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that specifies criteria associated with the data view.</param>
      <param name="sorting">An IList&lt;<see cref="T:DevExpress.Xpo.SortProperty"/>&gt; collection whose elements identify the sorted columns within the data view.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XpoDataView.#ctor(DevExpress.ExpressApp.Xpo.XPObjectSpace,System.Type,System.String,DevExpress.Data.Filtering.CriteriaOperator,System.Collections.Generic.IList{DevExpress.Xpo.SortProperty})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XpoDataView"/> class with the specified settings. Data view columns are specified using the semicolon-separated list of expression strings. For internal use only.</para>
      </summary>
      <param name="objectSpace">An <see cref="T:DevExpress.ExpressApp.Xpo.XPObjectSpace"/> object used to access data in XAF applications based on XPO data model.</param>
      <param name="objectType">The Type of requested persistent objects.</param>
      <param name="expressions">A string that contains a semicolon separated list of expressions that specify data view column values.</param>
      <param name="criteria">A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that specifies criteria associated with the data view.</param>
      <param name="sorting">An IList&lt;<see cref="T:DevExpress.Xpo.SortProperty"/>&gt; collection whose elements identify the sorted columns within the data view.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XpoDataView.Dispose">
      <summary>
        <para>Releases all resources used by this <see cref="T:DevExpress.ExpressApp.Xpo.XpoDataView"/> object.</para>
      </summary>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XpoDataView.Filter">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.ExpressApp.Xpo.XpoDataViewRecord">
      <summary>
        <para>A XPO-oriented class that represents a lightweight read-only data record (a data view) retrieved from a database without loading a complete business object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XpoDataViewRecord.#ctor(DevExpress.ExpressApp.Xpo.XpoDataView,DevExpress.Xpo.ViewRecord)">
      <summary>
        <para>Creates a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XpoDataViewRecord"/> class.</para>
      </summary>
      <param name="dataView">A <see cref="T:DevExpress.ExpressApp.Xpo.XpoDataView"/> collection in which a new ViewRecord object is placed.</param>
      <param name="viewRecord">A ViewRecord object that is a record to be placed in the XpoDataView collection.</param>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XpoDataViewRecord.Item(System.Int32)">
      <summary>
        <para>Gets the <see cref="T:DevExpress.ExpressApp.Xpo.XpoDataViewRecord"/> object with the specified index from the <see cref="T:DevExpress.ExpressApp.Xpo.XpoDataView"/> collection.</para>
      </summary>
      <param name="index">An integer specifying the index of the <see cref="T:DevExpress.ExpressApp.Xpo.XpoDataViewRecord"/> object to be returned.</param>
      <value>The <see cref="T:DevExpress.ExpressApp.Xpo.XpoDataViewRecord"/> object with the specified index.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.Xpo.XpoDataViewRecord.Item(System.String)">
      <summary>
        <para>Gets the <see cref="T:DevExpress.ExpressApp.Xpo.XpoDataViewRecord"/> object with the specified name from the <see cref="T:DevExpress.ExpressApp.Xpo.XpoDataView"/> collection.</para>
      </summary>
      <param name="name">The string specifying the name of the <see cref="T:DevExpress.ExpressApp.Xpo.XpoDataViewRecord"/> object to be returned.</param>
      <value>The <see cref="T:DevExpress.ExpressApp.Xpo.XpoDataViewRecord"/> object with the specified name.</value>
    </member>
    <member name="T:DevExpress.ExpressApp.Xpo.XpoTypesInfoHelper">
      <summary>
        <para>Provides helper methods used to manage the metadata information on XPO business classes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XpoTypesInfoHelper.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.ExpressApp.Xpo.XpoTypesInfoHelper"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XpoTypesInfoHelper.ForceInitialize">
      <summary>
        <para>Forces the initialization of the metadata information on XPO business classes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XpoTypesInfoHelper.GetTypesInfo">
      <summary>
        <para>Returns metadata information on types used in an XAF application.</para>
      </summary>
      <returns>An <see cref="T:DevExpress.ExpressApp.DC.ITypesInfo"/> object supplying metadata on types used in an XAF application.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XpoTypesInfoHelper.GetXpoTypeInfoSource">
      <summary>
        <para>Returns an object providing access to the <see cref="T:DevExpress.Xpo.Metadata.XPDictionary"/>.</para>
      </summary>
      <returns>An XpoTypeInfoSource object providing access to the XPDictionary.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.Xpo.XpoTypesInfoHelper.Reset">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
  </members>
</doc>