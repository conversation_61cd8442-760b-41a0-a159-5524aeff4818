<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.XtraGantt.v20.2</name>
  </assembly>
  <members>
    <member name="N:DevExpress.XtraGantt">
      <summary>
        <para>Contains types related to the Gantt control.</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraGantt.Base.Scheduling">
      <summary>
        <para>Contains base types that specify a workweek schedule in the Gantt control.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.Base.Scheduling.BaseRule">
      <summary>
        <para>Represents a base rule for exceptions to the regular workweek schedule.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Base.Scheduling.BaseRule.Includes(System.DateTime)">
      <summary>
        <para>Returns whether the rules includes the specified date.</para>
      </summary>
      <param name="date">A date to check whether the rule includes it.</param>
      <returns>true, if the rule includes the specified date; otherwise, false.</returns>
    </member>
    <member name="T:DevExpress.XtraGantt.Base.Scheduling.ExceptionRule">
      <summary>
        <para>Represents a rule for exceptions to the regular workweek schedule.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Base.Scheduling.ExceptionRule.#ctor">
      <summary>
        <para>Initializes a new ExceptionRule class instance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.Base.Scheduling.ExceptionRule.EndDate">
      <summary>
        <para>Gets or sets the date when a rule stops applying.</para>
      </summary>
      <value>A <see cref="T:System.DateTime"/> structure that specifies the date when a rule stops applying.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.Base.Scheduling.ExceptionRule.Includes(System.Int64)">
      <summary>
        <para>Returns whether the rules includes the specified date.</para>
      </summary>
      <param name="day">An <see cref="T:System.Int64"/> value that specifies the date in ticks to check whether the rule includes it.</param>
      <returns>true, if the rule includes the specified date; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraGantt.Base.Scheduling.ExceptionRule.Interval">
      <summary>
        <para>Gets or sets the rule recurrence interval.</para>
      </summary>
      <value>An integer value that specifies the recurrence interval.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Base.Scheduling.ExceptionRule.Occurrences">
      <summary>
        <para>Gets or sets a value that specifies how many times the rule is applied.</para>
      </summary>
      <value>An integer value that specifies how many times the rule is applied.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Base.Scheduling.ExceptionRule.StartDate">
      <summary>
        <para>Gets or sets the date when a rule is set to apply.</para>
      </summary>
      <value>A <see cref="T:System.DateTime"/> structure that specifies the date when a rule is set to apply.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.Base.Scheduling.ScheduleDay">
      <summary>
        <para>Represents a day with a schedule.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Base.Scheduling.ScheduleDay.#ctor">
      <summary>
        <para>Initializes a new ScheduleDay class instance.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Base.Scheduling.ScheduleDay.#ctor(DevExpress.XtraGantt.Scheduling.WorkTime[])">
      <summary>
        <para>Initializes a new ScheduleDay class instance.</para>
      </summary>
      <param name="workTimes">An array of WorkTime objects that specify work intervals in a day&#39;s schedule.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.Base.Scheduling.ScheduleDay.#ctor(System.Collections.Generic.IEnumerable{DevExpress.XtraGantt.Scheduling.WorkTime})">
      <summary>
        <para>Initializes a new ScheduleDay class instance.</para>
      </summary>
      <param name="workTimes">An collection of WorkTime objects that specify work intervals in a day&#39;s schedule.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.Base.Scheduling.ScheduleDay.IsNonWorkDay">
      <summary>
        <para>Gets whether the day is a non-work day.</para>
      </summary>
      <value>true, if the day is a non-work day; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Base.Scheduling.ScheduleDay.Tag">
      <summary>
        <para>Gets or sets a custom tag assigned to the day.</para>
      </summary>
      <value>An <see cref="T:System.Object"/> that specifies the tag.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Base.Scheduling.ScheduleDay.WorkTimes">
      <summary>
        <para>Provides access to work intervals in the day&#39;s schedule (for example, 9:00 to 12:00 and 13:00 to 18:00).</para>
      </summary>
      <value>A collection of WorkTime objects that specify work intervals in the day&#39;s schedule.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.ChangeType">
      <summary>
        <para>Enumerates values that specify whether a dependency is created, modified, or removed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.ChangeType.Create">
      <summary>
        <para>The dependency is created.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.ChangeType.Modify">
      <summary>
        <para>The dependency is modified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.ChangeType.Remove">
      <summary>
        <para>The dependency is removed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.CriticalPathHighlightMode">
      <summary>
        <para>Enumerates values that specify whether the control highlights single, multiple, or no critical paths.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.CriticalPathHighlightMode.Multiple">
      <summary>
        <para>The control highlights all sequences of dependent tasks that affect the project&#39;s start or finish date.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.CriticalPathHighlightMode.None">
      <summary>
        <para>The control does not highlight critical paths.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.CriticalPathHighlightMode.Single">
      <summary>
        <para>The control highlights the longest sequence of dependent tasks that affect the project&#39;s start or finish date.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.CustomCalcTaskDependency"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs.ArrowBounds">
      <summary>
        <para>Gets or sets the dependency arrow&#39;s bounds.</para>
      </summary>
      <value>A structure that specifies a rectangle.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs.DependencyType">
      <summary>
        <para>Gets the dependency type (finish-to-start, finish-to-finish, etc.).</para>
      </summary>
      <value>A value that specifies a dependency type.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs.Handled">
      <summary>
        <para>Gets or sets whether the event is handled. Suppresses the default calculation.</para>
      </summary>
      <value>true to suppress the default calculation; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs.Lines">
      <summary>
        <para>Gets the list of lines that specify the dependency line.</para>
      </summary>
      <value>A collection of rectangles that specify a dependency line.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs.PredecessorDuration">
      <summary>
        <para>Gets the predecessor task&#39;s duration.</para>
      </summary>
      <value>A structure that specifies a time interval.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs.PredecessorFinishDate">
      <summary>
        <para>Gets the predecessor task&#39;s finish date.</para>
      </summary>
      <value>A structure that specifies a date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs.PredecessorStartDate">
      <summary>
        <para>Gets the predecessor task&#39;s start date.</para>
      </summary>
      <value>A structure that specifies a date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs.RotateArrow">
      <summary>
        <para>Gets or sets how much the arrow is rotated and the axis used to flip the arrow.</para>
      </summary>
      <value>A value that specifies how much an image is rotated and the axis used to flip the image.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs.SuccessorDuration">
      <summary>
        <para>Gets the successor task&#39;s duration.</para>
      </summary>
      <value>A structure that specifies a time interval.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs.SuccessorFinishDate">
      <summary>
        <para>Gets the successor task&#39;s finish date.</para>
      </summary>
      <value>A structure that specifies a date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomCalcTaskDependencyEventArgs.SuccessorStartDate">
      <summary>
        <para>Gets the successor task&#39;s start date.</para>
      </summary>
      <value>A structure that specifies a date.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.CustomDrawTaskDependencyEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.CustomDrawTaskDependency"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTaskDependencyEventArgs.Appearance">
      <summary>
        <para>Provides access to appearance settings that specify the dependency line background color.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTaskDependencyEventArgs.DefaultDraw">
      <summary>
        <para>Draws the task dependency line.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTaskDependencyEventArgs.Lines">
      <summary>
        <para>Provides access to the collection of <see cref="T:System.Drawing.RectangleF"/> structures that specify the processed dependency line.</para>
      </summary>
      <value>A collection of <see cref="T:System.Drawing.RectangleF"/> structures.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTaskDependencyEventArgs.PredecessorNode">
      <summary>
        <para>Gets the predecessor node.</para>
      </summary>
      <value>An object that specifies the predecessor node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTaskDependencyEventArgs.State">
      <summary>
        <para>Gets the object state: normal, hot tracked, pressed, disabled, or selected.</para>
      </summary>
      <value>A value that specifies the object state: normal, hot tracked, pressed, disabled, or selected.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTaskDependencyEventArgs.SuccessorNode">
      <summary>
        <para>Gets the successor node.</para>
      </summary>
      <value>An object that specifies the successor node.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.CustomDrawTaskDependencyEventHandler">
      <summary>
        <para>Represents a reference to a method that handles the <see cref="E:DevExpress.XtraGantt.GanttControl.CustomDrawTaskDependency"/> event.</para>
      </summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">An object that contains event arguments.</param>
    </member>
    <member name="T:DevExpress.XtraGantt.CustomDrawTaskEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.CustomDrawTask"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTaskEventArgs.Appearance">
      <summary>
        <para>Provides access to the task&#39;s appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTaskEventArgs.DrawBaseline">
      <summary>
        <para>Draws the baseline.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTaskEventArgs.DrawBaseline(System.Drawing.RectangleF)">
      <summary>
        <para>Draws the baseline in the specified bounds.</para>
      </summary>
      <param name="bounds">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the bounds where the baseline should be drawn.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTaskEventArgs.DrawInsideText">
      <summary>
        <para>Draws the text inside the bar.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTaskEventArgs.DrawLeftText">
      <summary>
        <para>Draws the text to the left of the bar.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTaskEventArgs.DrawLeftText(System.Drawing.RectangleF)">
      <summary>
        <para>Draws the text to the left of the bar within the specified bounds.</para>
      </summary>
      <param name="bounds">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the bounds where the text should be drawn.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTaskEventArgs.DrawRightText">
      <summary>
        <para>Draws the text to the right of the bar.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTaskEventArgs.DrawRightText(System.Drawing.RectangleF)">
      <summary>
        <para>Draws the text to the left of the bar within the specified bounds.</para>
      </summary>
      <param name="bounds">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the bounds where the text should be drawn.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTaskEventArgs.DrawShape">
      <summary>
        <para>Draws the task shape: bar, summary bar, or milestone.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTaskEventArgs.DrawShape(System.DateTime,System.DateTime)">
      <summary>
        <para>Draws the task shape (bar, summary bar, or milestone) with an interruption.</para>
      </summary>
      <param name="interruptionStart">The interruption&#39;s start date.</param>
      <param name="interruptionFinish">The interruption&#39;s end date.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTaskEventArgs.DrawShape(System.Drawing.RectangleF,System.Single)">
      <summary>
        <para>Draws the task shape: bar, summary bar, or milestone.</para>
      </summary>
      <param name="bounds">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the bounds where the shape should be drawn.</param>
      <param name="progress">A value that specifies the task progress.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTaskEventArgs.Duration">
      <summary>
        <para>Gets the task duration.</para>
      </summary>
      <value>A <see cref="T:System.TimeSpan"/> structure that specifies the task duration.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTaskEventArgs.FinishDate">
      <summary>
        <para>Gets the task finish date.</para>
      </summary>
      <value>A <see cref="T:System.DateTime"/> structure that specifies the task finish date.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTaskEventArgs.GetPosition(System.DateTime)">
      <summary>
        <para></para>
      </summary>
      <param name="date"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTaskEventArgs.Info">
      <summary>
        <para>Provides access to view information about the task.</para>
      </summary>
      <value>An object that contains view information about the task.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTaskEventArgs.Node">
      <summary>
        <para>Gets the processed node.</para>
      </summary>
      <value>An object that specifies the processed node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTaskEventArgs.Progress">
      <summary>
        <para>Gets the task progress.</para>
      </summary>
      <value>A value that specifies the task progress.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTaskEventArgs.StartDate">
      <summary>
        <para>Gets the task start date.</para>
      </summary>
      <value>A <see cref="T:System.DateTime"/> structure that specifies the task start date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTaskEventArgs.State">
      <summary>
        <para>Gets the object state: normal, hot tracked, pressed, disabled, or selected.</para>
      </summary>
      <value>A value that specifies the object state: normal, hot tracked, pressed, disabled, or selected.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.CustomDrawTaskEventHandler">
      <summary>
        <para>Represents a reference to a method that handles the <see cref="E:DevExpress.XtraGantt.GanttControl.CustomDrawTask"/> event.</para>
      </summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">An object that contains event arguments.</param>
    </member>
    <member name="T:DevExpress.XtraGantt.CustomDrawTimescaleColumnEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.CustomDrawTimescaleColumn"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomDrawTimescaleColumnEventArgs.Column">
      <summary>
        <para>Gets the processed column.</para>
      </summary>
      <value>An object that specifies the processed column.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTimescaleColumnEventArgs.DrawBackground">
      <summary>
        <para>Draws the background.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTimescaleColumnEventArgs.DrawHeader">
      <summary>
        <para>Draws the header.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomDrawTimescaleColumnEventArgs.GetPosition(System.DateTime)">
      <summary>
        <para>Returns the position of the column that corresponds to the specified date.</para>
      </summary>
      <param name="date">A <see cref="T:System.DateTime"/> structure that specifies the date whose column position should be returned.</param>
      <returns>A <see cref="T:System.Double"/> value that specifies the column position.</returns>
    </member>
    <member name="T:DevExpress.XtraGantt.CustomDrawTimescaleColumnEventHandler">
      <summary>
        <para>Represents a reference to a method that handles the <see cref="E:DevExpress.XtraGantt.GanttControl.CustomDrawTimescaleColumn"/> event.</para>
      </summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">An object that contains event arguments.</param>
    </member>
    <member name="T:DevExpress.XtraGantt.CustomTaskDisplayTextEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.CustomTaskDisplayText"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomTaskDisplayTextEventArgs.InsideText">
      <summary>
        <para>Gets or sets the text displayed inside the task&#39;s bar. This property is not in effect for summary tasks and milestones.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the text displayed inside the task&#39;s bar.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomTaskDisplayTextEventArgs.LeftText">
      <summary>
        <para>Gets or sets the text displayed to the left of the task&#39;s bar.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the text displayed to the left of the task&#39;s bar.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomTaskDisplayTextEventArgs.Node">
      <summary>
        <para>Gets the processed node.</para>
      </summary>
      <value>An object that specifies the processed node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomTaskDisplayTextEventArgs.RightText">
      <summary>
        <para>Gets or sets the text displayed to the right of the task&#39;s bar.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the text displayed to the right of the task&#39;s bar.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomTaskDisplayTextEventArgs.TaskInfo">
      <summary>
        <para>Provides access to view information about the task.</para>
      </summary>
      <value>An object that contains view information about the task.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.CustomTaskDisplayTextEventHandler">
      <summary>
        <para>Represents a reference to a method that handles the <see cref="E:DevExpress.XtraGantt.GanttControl.CustomTaskDisplayText"/> event.</para>
      </summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">An object that contains event arguments.</param>
    </member>
    <member name="T:DevExpress.XtraGantt.CustomTaskProgressEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.CustomTaskProgress"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomTaskProgressEventArgs.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraGantt.CustomTaskProgressEventArgs"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomTaskProgressEventArgs.Node">
      <summary>
        <para>Gets or sets the processed node.</para>
      </summary>
      <value>An object that specifies the processed node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomTaskProgressEventArgs.Progress">
      <summary>
        <para>Gets or sets the task&#39;s progress (a value from 0 to 1).</para>
      </summary>
      <value>A value the specifies the task&#39;s progress.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.CustomTaskProgressEventHandler">
      <summary>
        <para>Represents a reference to a method that handles the <see cref="E:DevExpress.XtraGantt.GanttControl.CustomTaskProgress"/> event.</para>
      </summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">An object that contains event arguments.</param>
    </member>
    <member name="T:DevExpress.XtraGantt.CustomTaskSchedulingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.CustomTaskScheduling"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.CustomTaskSchedulingEventArgs.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraGantt.CustomTaskSchedulingEventArgs"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomTaskSchedulingEventArgs.Cancel">
      <summary>
        <para>Gets or sets whether to cancel rescheduling the processed task and all dependent tasks.</para>
      </summary>
      <value>true, to cancel rescheduling the processed task and all dependent tasks; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomTaskSchedulingEventArgs.Duration">
      <summary>
        <para>Gets or sets the task&#39;s duration.</para>
      </summary>
      <value>A structure that specifies the task&#39;s duration.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomTaskSchedulingEventArgs.FinishDate">
      <summary>
        <para>Gets or sets the task&#39;s finish date.</para>
      </summary>
      <value>A structure that specifies the finish date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomTaskSchedulingEventArgs.Node">
      <summary>
        <para>Gets the processed node.</para>
      </summary>
      <value>An object that specifies the processed node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomTaskSchedulingEventArgs.Row">
      <summary>
        <para>Gets an object that specifies the processed data row.</para>
      </summary>
      <value>An object that specifies the processed data row.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.CustomTaskSchedulingEventArgs.StartDate">
      <summary>
        <para>Gets or sets the task&#39;s start date.</para>
      </summary>
      <value>A structure that specifies the start date.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.CustomTaskSchedulingEventHandler">
      <summary>
        <para>Represents a reference to a method that handles the <see cref="E:DevExpress.XtraGantt.GanttControl.CustomTaskScheduling"/> event.</para>
      </summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">An object that contains event arguments.</param>
    </member>
    <member name="T:DevExpress.XtraGantt.DependencyType">
      <summary>
        <para>Enumerates values that specify types of dependencies between tasks.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.DependencyType.FinishToFinish">
      <summary>
        <para>The successor finishes after the predecessor finishes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.DependencyType.FinishToStart">
      <summary>
        <para>The successor starts after the predecessor finishes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.DependencyType.StartToFinish">
      <summary>
        <para>The successor finishes after the predecessor starts.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.DependencyType.StartToStart">
      <summary>
        <para>The successor starts after the predecessor starts.</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraGantt.Exceptions">
      <summary>
        <para>Contains types that specify exceptions to the regular workweek schedule in the Gantt control.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.Exceptions.DailyExceptionRule">
      <summary>
        <para>Represents a rule that specifies an exception that reoccurs every day.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.DailyExceptionRule.#ctor">
      <summary>
        <para>Initializes a new DailyExceptionRule class instance.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.Exceptions.DaysExceptionRule">
      <summary>
        <para>Represents a rule that specifies an exception that reoccurs by days.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.DaysExceptionRule.#ctor">
      <summary>
        <para>Initializes a new DaysExceptionRule class instance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.Exceptions.DaysExceptionRule.Days">
      <summary>
        <para>Provides access to a collection of <see cref="T:DevExpress.XtraGantt.Exceptions.ExceptionDay"/> objects that specify exception days.</para>
      </summary>
      <value>An object that contains <see cref="T:DevExpress.XtraGantt.Exceptions.ExceptionDay"/> objects.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.Exceptions.ExceptionDay">
      <summary>
        <para>Represents a day with a schedule that is an exception to the regular workweek.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.ExceptionDay.#ctor">
      <summary>
        <para>Initializes a new ExceptionDay class instance.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.ExceptionDay.#ctor(DevExpress.XtraGantt.Scheduling.WorkTime[])">
      <summary>
        <para>Initializes a new ExceptionDay class instance.</para>
      </summary>
      <param name="workTimes"></param>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.ExceptionDay.#ctor(System.Collections.Generic.IEnumerable{DevExpress.XtraGantt.Scheduling.WorkTime})">
      <summary>
        <para>Initializes a new ExceptionDay class instance.</para>
      </summary>
      <param name="workTimes"></param>
    </member>
    <member name="T:DevExpress.XtraGantt.Exceptions.ExceptionRuleCollection">
      <summary>
        <para>Represents a collection of rules that specify exceptions to the regular workweek.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.ExceptionRuleCollection.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraGantt.Exceptions.ExceptionRuleCollection"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.ExceptionRuleCollection.#ctor(DevExpress.XtraGantt.GanttControl)">
      <summary>
        <para>Initializes a new ExceptionRuleCollection class instance.</para>
      </summary>
      <param name="ganttControl"></param>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.ExceptionRuleCollection.Insert(System.Int32,DevExpress.XtraGantt.Base.Scheduling.ExceptionRule)">
      <summary>
        <para>Inserts the specified exception rule to the collection at the specified index.</para>
      </summary>
      <param name="index">An <see cref="T:System.Int32"/> value that specifies the index at which to insert the item.</param>
      <param name="item">An object that specifies the exception rule to insert to the collection.</param>
      <returns>true, if the item is successfully inserted; otherwise, false.</returns>
    </member>
    <member name="T:DevExpress.XtraGantt.Exceptions.MonthlyDayOfWeekExceptionRule">
      <summary>
        <para>Represents a rule that specifies an exception that reoccurs every month in a particular week of the month and day of the week.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.MonthlyDayOfWeekExceptionRule.#ctor">
      <summary>
        <para>Initializes a new MonthlyDayOfWeekExceptionRule class instance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.Exceptions.MonthlyDayOfWeekExceptionRule.DayOfWeek">
      <summary>
        <para>Gets or sets the day of week when the rule applies.</para>
      </summary>
      <value>A <see cref="T:System.DayOfWeek"/> value that specifies the day of week when the rule applies.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Exceptions.MonthlyDayOfWeekExceptionRule.WeekOfMonth">
      <summary>
        <para>Gets or sets the week of months when the rule applies.</para>
      </summary>
      <value>A value that specifies the week of the month when the rule applies.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.Exceptions.MonthlyExceptionRule">
      <summary>
        <para>Represents a rule that specifies an exception that reoccurs every month on a particular day of the month.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.MonthlyExceptionRule.#ctor">
      <summary>
        <para>Initializes a new MonthlyExceptionRule class instance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.Exceptions.MonthlyExceptionRule.DayOfMonth">
      <summary>
        <para>Gets or sets the day of the month when the rule applies.</para>
      </summary>
      <value>A <see cref="T:System.Int32"/> value that specifies the day of the month when the rule applies.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.Exceptions.WeeklyExceptionRule">
      <summary>
        <para>Represents a rule that specifies an exception that reoccurs every week on a particular day of the week.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.WeeklyExceptionRule.#ctor">
      <summary>
        <para>Initializes a new WeeklyExceptionRule class instance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.Exceptions.WeeklyExceptionRule.DayOfWeek">
      <summary>
        <para>Gets the day of the week when the rule applies.</para>
      </summary>
      <value>A <see cref="T:System.DayOfWeek"/> value that specifies the day of the week when the rule applies.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.Exceptions.WorkTimesExceptionRule">
      <summary>
        <para>Represents a base rule that specifies an exception to the regular workweek schedule.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.WorkTimesExceptionRule.#ctor">
      <summary>
        <para>Initializes a new WorkTimesExceptionRule class instance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.Exceptions.WorkTimesExceptionRule.WorkTimes">
      <summary>
        <para>Provides access to work intervals in the day&#39;s schedule (for example, 9:00 to 12:00 and 13:00 to 18:00).</para>
      </summary>
      <value>A collection of WorkTime objects that specify work intervals in the day&#39;s schedule.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.Exceptions.YearlyDayOfWeekExceptionRule">
      <summary>
        <para>Represents a rule that specifies an exception that reoccurs every year in a particular month, week of the month, and day of the week.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.YearlyDayOfWeekExceptionRule.#ctor">
      <summary>
        <para>Initializes a new YearlyDayOfWeekExceptionRule class instance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.Exceptions.YearlyDayOfWeekExceptionRule.DayOfWeek">
      <summary>
        <para>Gets or sets the day of the week when the rule applies.</para>
      </summary>
      <value>A value that specifies the day of the week when the rule applies.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Exceptions.YearlyDayOfWeekExceptionRule.Month">
      <summary>
        <para>Gets or sets the month when the rule applies.</para>
      </summary>
      <value>A value that specifies the month when the rule applies.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Exceptions.YearlyDayOfWeekExceptionRule.WeekOfMonth">
      <summary>
        <para>Gets the week of the month when the rule applies.</para>
      </summary>
      <value>A value that specifies the week of the month when the rule applies.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.Exceptions.YearlyDayOfYearExceptionRule">
      <summary>
        <para>Represents a rule that specifies an exception that reoccurs every year on a particular day of the year.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.YearlyDayOfYearExceptionRule.#ctor">
      <summary>
        <para>Initializes a new YearlyDayOfYearExceptionRule class instance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.Exceptions.YearlyDayOfYearExceptionRule.DayOfYear">
      <summary>
        <para>Gets or sets the day of the year when the rule applies.</para>
      </summary>
      <value>An <see cref="T:System.Int32"/> value that specifies the day of the year when the rule applies.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.Exceptions.YearlyExceptionRule">
      <summary>
        <para>Represents a rule that specifies an exception that reoccurs every year in a particular month and day of the month.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Exceptions.YearlyExceptionRule.#ctor">
      <summary>
        <para>Initializes a new YearlyExceptionRule class instance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.Exceptions.YearlyExceptionRule.DayOfMonth">
      <summary>
        <para>Gets the day of the month when the rule applies.</para>
      </summary>
      <value>A <see cref="T:System.Int32"/> value that specifies the day of the month when the rule applies.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Exceptions.YearlyExceptionRule.Month">
      <summary>
        <para>Gets or sets the month when the rule applies.</para>
      </summary>
      <value>A value that specifies when the rule applies.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttChartMappings">
      <summary>
        <para>Provides access to options specified based on data source field names: task captions, task start and finish dates, task progress, etc.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttChartMappings.#ctor(DevExpress.XtraGantt.GanttControl)">
      <summary>
        <para>Initializes a new GanttChartMappings class instance.</para>
      </summary>
      <param name="ganttControl">A control to which the options belong.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttChartMappings.BaselineDurationFieldName">
      <summary>
        <para>Gets or sets the data source field (of the <see cref="T:System.TimeSpan"/> type) that specifies a task&#39;s baseline duration.</para>
      </summary>
      <value>The data source field that specifies a task&#39;s baseline duration.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttChartMappings.BaselineFinishDateFieldName">
      <summary>
        <para>Gets or sets the data source field (of the <see cref="T:System.DateTime"/> type) that specifies a task&#39;s baseline finish date.</para>
      </summary>
      <value>The data source field that specifies a task&#39;s baseline finish date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttChartMappings.BaselineStartDateFieldName">
      <summary>
        <para>Gets or sets the data source field (of the <see cref="T:System.DateTime"/> type) that specifies a task&#39;s baseline start date.</para>
      </summary>
      <value>The data source field that specifies a task&#39;s baseline start date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttChartMappings.DurationFieldName">
      <summary>
        <para>Gets or sets the data source field (of the <see cref="T:System.TimeSpan"/> type) that specifies a task&#39;s duration (only work hours).</para>
      </summary>
      <value>The data source field that specifies a task&#39;s duration.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttChartMappings.FinishDateFieldName">
      <summary>
        <para>Gets or sets the data source field (of the <see cref="T:System.DateTime"/> type) that specifies a task&#39;s finish date.</para>
      </summary>
      <value>The data source field that specifies a task&#39;s finish date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttChartMappings.InteractionTooltipTextFieldName">
      <summary>
        <para>Gets or sets the data source field (of the <see cref="T:System.String"/> type) that specifies a task&#39;s text in interaction tooltips.</para>
      </summary>
      <value>The data source field that specifies a task&#39;s text in interaction tooltips.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttChartMappings.PredecessorsFieldName">
      <summary>
        <para>Gets or sets the data source field that specifies a task&#39;s predecessors. The data field should contain an <see cref="T:System.Collections.IList"/> object or a <see cref="T:System.String"/> value with keys separated by space, comma, or semicolon (&#39; &#39;, &#39;,&#39; &#39;;&#39;).</para>
      </summary>
      <value>The data source field that specifies a task&#39;s predecessors.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttChartMappings.ProgressFieldName">
      <summary>
        <para>Gets or sets the data source field (of the <see cref="T:System.Single"/> type) that specifies a task&#39;s progress (from 0 to 100 percent).</para>
      </summary>
      <value>The data source field that specifies a task&#39;s progress.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttChartMappings.StartDateFieldName">
      <summary>
        <para>Gets or sets the data source field (of the <see cref="T:System.DateTime"/> type) that specifies a task&#39;s start date.</para>
      </summary>
      <value>The data source field that specifies a task&#39;s start date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttChartMappings.TextFieldName">
      <summary>
        <para>Gets or sets the data source field (of the <see cref="T:System.String"/> type) that specifies the text displayed next to a task in the chart.</para>
      </summary>
      <value>The data source field that specifies a task&#39;s text.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttControl">
      <summary>
        <para>Represents a bar chart that illustrates a project schedule. The control displays project tasks organized in a tree list. The chart displays a bar against each task that shows the task start and finish dates, progress, and dependencies.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.#ctor">
      <summary>
        <para>Initializes a new GanttControl class instance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.Appearance">
      <summary>
        <para>Provides access to appearance settings.</para>
      </summary>
      <value>An object that specifies an appearance settings collection.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.AppearancePrint">
      <summary>
        <para>Provides access to appearance settings applied when the control is printed.</para>
      </summary>
      <value>An object that specifies an appearance settings collection.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.BeginUpdate">
      <summary>
        <para>Does not allow the framework to redraw the control until the CancelUpdate or EndUpdate method is called.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.CalcHitInfo(System.Drawing.Point)">
      <summary>
        <para>Returns information about the visual element under the specified hit point.</para>
      </summary>
      <param name="pt">A Point structure that specifies the hit point relative to the control&#39;s upper-left corner.</param>
      <returns>A GanttControlHitInfo object that contains information about the visual element under the specified hit point.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.CancelUpdate">
      <summary>
        <para>Allows the framework to redraw the control after the BeginUpdate method is called. Does not redraw the control immediately.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.ChartFinishDate">
      <summary>
        <para>Gets or sets the timescale ruler finish date.</para>
      </summary>
      <value>A <see cref="T:System.DateTime"/> structure that specifies the timescale ruler finish date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.ChartMappings">
      <summary>
        <para>Provides access to settings specified based on data source field names: task captions, task start and finish dates, task progress, etc.</para>
      </summary>
      <value>A GanttChartMappings object that contains mappings between data source fields and task properties.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.ChartStartDate">
      <summary>
        <para>Gets or sets the timescale ruler start date.</para>
      </summary>
      <value>A <see cref="T:System.DateTime"/> structure that specifies the timescale ruler start date.</value>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.CustomCalcTaskDependency">
      <summary>
        <para>Fires before the control calculates a dependency line. Allows you to specify the dependency line.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.CustomDrawTask">
      <summary>
        <para>Fires before a task in the chart area is displayed. Provides access to a drawing surface and allows you to draw the task manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.CustomDrawTaskDependency">
      <summary>
        <para>Fires before a task dependency line in the chart area is displayed. Provides access to a drawing surface and allows you to draw the task manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.CustomDrawTimescaleColumn">
      <summary>
        <para>Fires before a column in the chart area is displayed. Provides access to a drawing surface and allows you to draw the column manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.CustomTaskDisplayText">
      <summary>
        <para>Allows you to specify the text displayed inside, to the left, and to the right of the task.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.CustomTaskProgress">
      <summary>
        <para>Fires when a task&#39;s progress is recalculated and allows you to specify a custom value.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.CustomTaskScheduling">
      <summary>
        <para>Fires during the automatic rescheduling process when the control calculates new start and finish dates for a task. Allows you to cancel rescheduling the current task and stop the rescheduling process for its successors.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.DependencyMappings">
      <summary>
        <para>Provides access to data source field names that specify dependencies between tasks (dependency type, time lag, predecessor, and successor).</para>
      </summary>
      <value>An object that maps data source fields to task properties.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.DependencySource">
      <summary>
        <para>Gets or sets the data source that contains values that specify dependencies between tasks.</para>
      </summary>
      <value>An object that specifies a data source.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.EndUpdate">
      <summary>
        <para>Allows the framework to redraw the control after the BeginUpdate method is called. Redraws the control immediately.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.Exceptions">
      <summary>
        <para>Provides access to rules that specify exceptions to the regular workweek schedule.</para>
      </summary>
      <value>An object that contains exceptions to the regular workweek schedule.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetBaselineDuration(DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Returns the specified task&#39;s baseline duration.</para>
      </summary>
      <param name="node">An object that specifies the task for which to return the baseline duration.</param>
      <returns>A <see cref="T:System.TimeSpan"/> structure that specifies the task&#39;s baseline duration.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetBaselineDuration(System.Int32)">
      <summary>
        <para>Returns the specified task&#39;s baseline duration.</para>
      </summary>
      <param name="id">An integer value that specifies the Id of the task for which to return the baseline duration.</param>
      <returns>A <see cref="T:System.TimeSpan"/> structure that specifies the task&#39;s baseline duration.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetBaselineFinishDate(DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Returns the specified task&#39;s baseline finish date.</para>
      </summary>
      <param name="node">An object that specifies the task for which to return the baseline finish date.</param>
      <returns>A <see cref="T:System.DateTime"/> structure that specifies the task&#39;s baseline finish date.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetBaselineFinishDate(System.Int32)">
      <summary>
        <para>Returns the specified task&#39;s baseline finish date.</para>
      </summary>
      <param name="id">An integer value that specifies the Id of the task for which to return the baseline finish date.</param>
      <returns>A <see cref="T:System.DateTime"/> structure that specifies the task&#39;s baseline finish date.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetBaselineStartDate(DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Returns the specified task&#39;s baseline start date.</para>
      </summary>
      <param name="node">An object that specifies the task for which to return the baseline start date.</param>
      <returns>A <see cref="T:System.DateTime"/> structure that specifies the task&#39;s baseline start date.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetBaselineStartDate(System.Int32)">
      <summary>
        <para>Returns the specified task&#39;s baseline start date.</para>
      </summary>
      <param name="id">An integer value that specifies the Id of the task for which to return the baseline start date.</param>
      <returns>A <see cref="T:System.DateTime"/> structure that specifies the task&#39;s baseline start date.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetChartVisibleFinishDate">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetChartVisibleStartDate">
      <summary>
        <para></para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetDuration(DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Returns the specified task&#39;s duration.</para>
      </summary>
      <param name="node">An object that specifies the task for which to return the start baseline start date.</param>
      <returns>A <see cref="T:System.TimeSpan"/> structure that specifies the task&#39;s duration.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetDuration(System.Int32)">
      <summary>
        <para>Returns the specified task&#39;s duration.</para>
      </summary>
      <param name="id">An integer value that specifies the Id of the task for which to return the duration.</param>
      <returns>A <see cref="T:System.TimeSpan"/> structure that specifies the task&#39;s duration.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetFinishDate(DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Returns the specified task&#39;s finish date.</para>
      </summary>
      <param name="node">An object that specifies the task for which to return the baseline start date.</param>
      <returns>A <see cref="T:System.DateTime"/> structure that specifies the task&#39;s finish date.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetFinishDate(System.Int32)">
      <summary>
        <para>Returns the specified task&#39;s finish date.</para>
      </summary>
      <param name="id">An integer value that specifies the Id of the task for which to return the finish date.</param>
      <returns>A <see cref="T:System.DateTime"/> structure that specifies the task&#39;s finish date.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetName(DevExpress.XtraTreeList.Nodes.TreeListNode)">
      <summary>
        <para>Returns the specified task&#39;s text.</para>
      </summary>
      <param name="node">An integer value that specifies the Id of the task for which to return the text.</param>
      <returns>A <see cref="T:System.String"/> value that specifies the task&#39;s text.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetPredecessors(DevExpress.XtraTreeList.Nodes.TreeListNode)">
      <summary>
        <para>Returns the specified task&#39;s predecessors.</para>
      </summary>
      <param name="node">An object that specifies the task for which to return the predecessors.</param>
      <returns>An object that specifies the task&#39;s predecessors.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetPredecessors(System.Int32)">
      <summary>
        <para>Returns the specified task&#39;s predecessors.</para>
      </summary>
      <param name="id">An integer value that specifies the Id of the task for which to return the predecessors.</param>
      <returns>An object that specifies the task&#39;s predecessors.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetProgress(DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Returns the specified task&#39;s progress.</para>
      </summary>
      <param name="node">An object that specifies the task for which to return the progress.</param>
      <returns>An object that specifies the task&#39;s progress.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetProgress(System.Int32)">
      <summary>
        <para>Returns the specified task&#39;s progress.</para>
      </summary>
      <param name="id">An integer value that specifies the Id of the task for which to return the progress.</param>
      <returns>An object that specifies the task&#39;s progress.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetStartDate(DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Returns the specified task&#39;s start day.</para>
      </summary>
      <param name="node">An object that specifies the task for which to return the start date.</param>
      <returns>A <see cref="T:System.DateTime"/> structure that specifies the task&#39;s start date.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetStartDate(System.Int32)">
      <summary>
        <para>Returns the specified task&#39;s start day.</para>
      </summary>
      <param name="id">An integer value that specifies the Id of the task for which to return the start date.</param>
      <returns>A <see cref="T:System.DateTime"/> structure that specifies the task&#39;s start date.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetTaskSplits(System.Int32)">
      <summary>
        <para>Returns the collection of time spans that specify the task splits.</para>
      </summary>
      <param name="nodeId">A task&#39;s <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Id"/> property value.</param>
      <returns>The collection of time spans that specify the task splits.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetText(DevExpress.XtraTreeList.Nodes.TreeListNode)">
      <summary>
        <para>Returns the specified task&#39;s text.</para>
      </summary>
      <param name="node">An object that specifies the task for which to return the text.</param>
      <returns>A <see cref="T:System.String"/> value that specifies the task&#39;s text.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetText(System.Int32)">
      <summary>
        <para>Returns the specified task&#39;s text.</para>
      </summary>
      <param name="id">An integer value that specifies the Id of the task for which to return the text.</param>
      <returns>A <see cref="T:System.String"/> value that specifies the task&#39;s text.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.GetTooltipText(System.Int32)">
      <summary>
        <para>Returns the text (caption) in an interactive tooltip for the node with the specified Id.</para>
      </summary>
      <param name="id">An integer value that specifies an Id.</param>
      <returns>A string value that specifes the text in an interactive tooltip.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.InvalidateRow(DevExpress.XtraTreeList.ViewInfo.RowInfo)">
      <summary>
        <para>Invalidates the node that corresponds to the specified view information.</para>
      </summary>
      <param name="rowInfo">An object that contains view information about a tree list node.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.Nodes">
      <summary>
        <para>Provides access to the collection of root nodes.</para>
      </summary>
      <value>The collection of root nodes.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.OptionsBehavior">
      <summary>
        <para>Provides access to a set of options that specify the behavior of the control.</para>
      </summary>
      <value>An object that contains a set of options.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.OptionsCustomization">
      <summary>
        <para>Provides access to a set of options that specify the customization facilities of the control.</para>
      </summary>
      <value>An object that contains a set of options.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.OptionsMainTimeRuler">
      <summary>
        <para>Provides access to options that specify the bottom timescale ruler: measure unit, maximum and minimum scale, division marks, etc.</para>
      </summary>
      <value>An object that contains options that specify the main time ruler.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.OptionsSplitter">
      <summary>
        <para>Provides access to options of the splitter that separates the task list and chart.</para>
      </summary>
      <value>A GanttControlOptionsSplitter object that specifies the splitter&#39;s options.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.OptionsView">
      <summary>
        <para>Provides access to options that specify the view: visibility of particular visual elements, animations, etc.</para>
      </summary>
      <value>An object that contains options that specify the view.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.RangeControlClient">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.RequestTimescaleRulers">
      <summary>
        <para>Fires when the chart is zoomed in / zoomed out. Allows you to customize the timescale rulers.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.ScheduleFromStartDate(System.DateTime)">
      <summary>
        <para>Reschedules all tasks so that the project starts on the specified date.</para>
      </summary>
      <param name="startDate">A structure that specifies a date and time.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.ScheduleToFinishDate(System.DateTime)">
      <summary>
        <para>Reschedules all tasks so that the project ends on the specified date.</para>
      </summary>
      <param name="finishDate">A structure that specifies a date and time.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.ScrollChartToDate(System.DateTime)">
      <summary>
        <para>Scrolls the chart to the specified date.</para>
      </summary>
      <param name="date">A <see cref="T:System.DateTime"/> structure that specifies the date to which scroll the chart.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.ScrollChartToTask(DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Scrolls the chart to the specified task.</para>
      </summary>
      <param name="node">A <see cref="T:System.DateTime"/> structure that specifies the date to which scroll the chart.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.SetChartVisibleRange(System.DateTime,System.DateTime)">
      <summary>
        <para>Sets the chart&#39;s visible range.</para>
      </summary>
      <param name="startDate">A <see cref="T:System.DateTime"/> structure that specifies the vilible range&#39;s start date.</param>
      <param name="finishDate">A <see cref="T:System.DateTime"/> structure that specifies the vilible range&#39;s finish date.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.SetTaskSplits(System.Int32,System.Collections.Generic.List{System.TimeSpan})">
      <summary>
        <para>Sets the collection of time spans that specify the task&#39;s body and splits one after another.</para>
      </summary>
      <param name="nodeId">A task&#39;s <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Id"/> property value.</param>
      <param name="splits">The collection of time spans that specify a task&#39;s body and splits one after another.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.SplitTask(System.Int32,System.DateTime,System.TimeSpan)">
      <summary>
        <para>Splits the specified task.</para>
      </summary>
      <param name="nodeId">A task&#39;s <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Id"/> property value.</param>
      <param name="splitStart">The split start date.</param>
      <param name="splitDuration">The split duration.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.SplitTaskMappings">
      <summary>
        <para>Contains data source field names that specify task splits (task, split start date, and split duration).</para>
      </summary>
      <value>An object that contains mappings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.SplitTaskSource">
      <summary>
        <para>Gets or sets the data source that contains records that specify task splits.</para>
      </summary>
      <value>An object that specifies a data source.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.SplitterPosition">
      <summary>
        <para>Gets or sets the position of the splitter that separates the task list and chart.</para>
      </summary>
      <value>An integer value that specifies the splitter&#39;s position.</value>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.SplitterPositionChanged">
      <summary>
        <para>Fires after the splitter changes its position.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.SplitterPositionChanging">
      <summary>
        <para>Fires before the splitter changes its position. Allows you to cancel the action.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskDependencyModification">
      <summary>
        <para>Repeatedly fires when a user modifies a task&#39;s dependency. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskDependencyModificationCanceled">
      <summary>
        <para>Fires when a user presses Esc to cancel modifying a task&#39;s dependency. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskDependencyModificationCompleted">
      <summary>
        <para>Fires when a user finishes modifying a dependency. Allows you to cancel the operation.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskDependencyModified">
      <summary>
        <para>Fires when a task&#39;s dependency is successfully modified. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskFinishDateModification">
      <summary>
        <para>Repeatedly fires when a user modifies a task&#39;s finish date. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskFinishDateModificationCanceled">
      <summary>
        <para>Fires when a user presses Esc to cancel modifying a task&#39;s finish date. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskFinishDateModificationCompleted">
      <summary>
        <para>Fires when a user finishes modifying a finish date. Allows you to cancel the operation.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskFinishDateModificationStarted">
      <summary>
        <para>Fires when a user starts to modify a task&#39;s finish date. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskFinishDateModified">
      <summary>
        <para>Fires when a task&#39;s finish date is successfully modified. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskMoveCanceled">
      <summary>
        <para>Fires when a user presses Esc to cancel moving a task. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskMoveCompleted">
      <summary>
        <para>Fires when a user finishes moving a task. Allows you to cancel the operation.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskMoved">
      <summary>
        <para>Fires when a task is successfully moved. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskMoveStarted">
      <summary>
        <para>Fires when a user starts to move a task. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskMoving">
      <summary>
        <para>Repeatedly fires when a user moves a task. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskPopupMenuShowing">
      <summary>
        <para>Fires when a context menu for a task is about to be shown.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskProgressModification">
      <summary>
        <para>Repeatedly fires when a user modifies a task&#39;s progress. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskProgressModificationCanceled">
      <summary>
        <para>Fires when a user presses Esc to cancel modifying a task&#39;s progress. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskProgressModificationCompleted">
      <summary>
        <para>Fires when a user finishes modifying progress. Allows you to cancel the operation.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskProgressModificationStarted">
      <summary>
        <para>Fires when a user starts to modify a task&#39;s progress. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskProgressModified">
      <summary>
        <para>Fires when a task&#39;s progress is successfully modified. This event only fires when a user modifies a task in the chart. When the user modifies a task in the task list, the event does not fire.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TaskToolTipShowing">
      <summary>
        <para>Fires when a tooltip for a task is about to be shown.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.TimescaleColumnSize">
      <summary>
        <para>Gets or sets the timescale unit width as a percentage of the default width.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that specifies the unit width as a percentage of the default width.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.TimescaleLeftIndent">
      <summary>
        <para>Gets or sets the indentation of the content from the chart&#39;s left edge.</para>
      </summary>
      <value>An integer value that specifies the indentation of the content from the chart&#39;s left edge.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.TimescaleRightIndent">
      <summary>
        <para>Gets or sets the indentation of the content from the chart&#39;s right edge.</para>
      </summary>
      <value>An integer value that specifies the indentation of the content from the chart&#39;s right edge.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.TimescaleRulerCount">
      <summary>
        <para>Gets or sets the number of rulers in the timescale.</para>
      </summary>
      <value>An <see cref="T:System.Int32"/> value that specifies the number of rulers in the timescale.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.TimescaleRulerHeight">
      <summary>
        <para>Gets or sets the timescale ruler height.</para>
      </summary>
      <value>An integer value that specifies the timescale ruler height.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.TreeListMappings">
      <summary>
        <para>Provides access to options specified based on data source field names and tree list column object.</para>
      </summary>
      <value>An object that contains options specified based on data source field names and tree list column object.&#39;</value>
    </member>
    <member name="E:DevExpress.XtraGantt.GanttControl.TreeListPopupMenuShowing">
      <summary>
        <para>Allows you to customize the default menus for column headers, summary footers, and nodes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.UpdateSummaryProgress(DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Updates the progress for all summary tasks in a tree branch that starts from the specified task.</para>
      </summary>
      <param name="node">The summary task that is the root task in the branch that should be updated. If not specified, the method updates all the tasks in the project.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.ViewInfo">
      <summary>
        <para>Provides access to view information about the control.</para>
      </summary>
      <value>A GanttControlViewInfo object that contains view information about the control.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.WorkWeek">
      <summary>
        <para>Provides access to a rule that specifies work hours for a workweek.</para>
      </summary>
      <value>An object that specifies work hours for a workweek.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.ZoomIn">
      <summary>
        <para>Increases the time ruler scale.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControl.ZoomMode">
      <summary>
        <para>Gets or sets how the time ruler scale changes: smoothly or by predefined intervals (week, month, quarter, etc.).</para>
      </summary>
      <value>A GanttZoomMode enumeration value that specifies how the time ruler scale changes.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControl.ZoomOut">
      <summary>
        <para>Decreases the time ruler scale.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttControlAppearanceCollection">
      <summary>
        <para>Provides access to appearance settings applied to the control&#39;s visual elements.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlAppearanceCollection.#ctor(DevExpress.XtraGantt.GanttControl)">
      <summary>
        <para>Initializes a new GanttControlAppearanceCollection object instance.</para>
      </summary>
      <param name="owner">A GanttControl to which this collection belongs.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlAppearanceCollection.ChartHorzLine">
      <summary>
        <para>Provides access to chart horizontal line appearance settings.</para>
      </summary>
      <value>An AppearanceObject object that specifies chart horizontal line appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlAppearanceCollection.ChartNonWorkTime">
      <summary>
        <para>Provides access to chart non-work time appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlAppearanceCollection.ChartTimescaleRuler">
      <summary>
        <para>Provides access to timescale ruler appearance settings.</para>
      </summary>
      <value>An AppearanceObject object that specifies chart time ruler appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlAppearanceCollection.ChartVertLine">
      <summary>
        <para>Provides access to chart vertical line appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlAppearanceCollection.ChartWorkTime">
      <summary>
        <para>Provides access to chart work time appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlAppearanceCollection.CriticalPathDependency">
      <summary>
        <para>Provides access to appearance settings for dependency lines in critical paths.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlAppearanceCollection.CriticalPathTask">
      <summary>
        <para>Provides access to appearance settings for tasks in critical paths.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlAppearanceCollection.Dependency">
      <summary>
        <para>Provides access to dependency line appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlAppearanceCollection.Milestone">
      <summary>
        <para>Provides access to milestone appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlAppearanceCollection.Splitter">
      <summary>
        <para>Provides access to splitter appearance settings.</para>
      </summary>
      <value>An AppearanceObject object that specifies splitter appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlAppearanceCollection.SummaryTask">
      <summary>
        <para>Provides access to summary task appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlAppearanceCollection.Task">
      <summary>
        <para>Provides access to task appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttControlHitInfo">
      <summary>
        <para>Represents information about the control&#39;s visual elements under the hit point.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlHitInfo.#ctor">
      <summary>
        <para>Initializes a new GanttControlHitInfo class instance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlHitInfo.ChartHitTest">
      <summary>
        <para>Gets or sets information about the chart&#39;s visual element under the hit point.</para>
      </summary>
      <value>A GanttSplitterHitTest object that contains information about the chart&#39;s visual element under the hit point.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlHitInfo.InChart">
      <summary>
        <para>Gets whether the hit point belongs to the chart.</para>
      </summary>
      <value>true, if the hit point belongs to the chart; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlHitInfo.InSplitter">
      <summary>
        <para>Gets whether the hit point belongs to the splitter.</para>
      </summary>
      <value>true, if the hit point belongs to the splitter; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlHitInfo.InTreeList">
      <summary>
        <para>Gets whether the hit point belongs to the task list.</para>
      </summary>
      <value>true, if the hit point belongs to the task list; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlHitInfo.SplitterHitTest">
      <summary>
        <para>Gets or sets information about the splitter&#39;s visual element under the hit point.</para>
      </summary>
      <value>A GanttSplitterHitTest object that contains information about the splitter&#39;s visual element under the hit point.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlHitInfo.TreeListHitTest">
      <summary>
        <para>Gets or sets information about the task list&#39;s visual element under the hit point.</para>
      </summary>
      <value>A TreeListHitTest object that contains information about the task list&#39;s visual element under the hit point.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttControlNode">
      <summary>
        <para>Specifies a node in a <see cref="T:DevExpress.XtraGantt.GanttControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNode.#ctor">
      <summary>
        <para>Initializes a new GanttControlNode class instance.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNode.Assign(DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Assigns the specified object&#39;s property values to the current object&#39;s corresponding properties.</para>
      </summary>
      <param name="node">The object whose property values to assign to the current object&#39;s corresponding properties.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNode.FirstNode">
      <summary>
        <para>Gets the first child node.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the first child node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNode.GanttControl">
      <summary>
        <para>Gets the control to which this node belongs.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControl"/> object that specifies the control to which this node belongs.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNode.GetBaselineDuration">
      <summary>
        <para>Returns the task baseline duration.</para>
      </summary>
      <returns>A <see cref="T:System.TimeSpan"/> structure that specifies the task baseline duration.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNode.GetBaselineFinishDate">
      <summary>
        <para>Gets the task&#39;s baseline finish date.</para>
      </summary>
      <returns>A <see cref="T:System.DateTime"/> structure that specifies the task&#39;s baseline finish date.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNode.GetBaselineStartDate">
      <summary>
        <para>Gets the task&#39;s baseline start date.</para>
      </summary>
      <returns>A <see cref="T:System.DateTime"/> structure that specifies the task&#39;s baseline start date.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNode.GetDuration">
      <summary>
        <para>Returns the task&#39;s duration.</para>
      </summary>
      <returns>A <see cref="T:System.TimeSpan"/> structure that specifies the task&#39;s duration.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNode.GetFinishDate">
      <summary>
        <para>Returns the task&#39;s finish date.</para>
      </summary>
      <returns>A <see cref="T:System.DateTime"/> structure that specifies the task&#39;s finish date.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNode.GetPredecessors">
      <summary>
        <para>Returns the task&#39;s predecessors.</para>
      </summary>
      <returns>An object that specifies the task&#39;s predecessors.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNode.GetProgress">
      <summary>
        <para>Returns the task&#39;&#39;s progress.</para>
      </summary>
      <returns>A <see cref="T:System.Single"/> value that specifies the task&#39;&#39;s progress.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNode.GetStartDate">
      <summary>
        <para>Returns the task&#39;s start date.</para>
      </summary>
      <returns>A <see cref="T:System.DateTime"/> structure that specifies the task&#39;s start date.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNode.GetText">
      <summary>
        <para>Returns the task&#39;s text.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value that specifies the task&#39;s text.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNode.HasAsParent(DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Returns whether the specified node is a parent node.</para>
      </summary>
      <param name="node">A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the node to check if it is a parent node.</param>
      <returns>true, if the specified node is a parent node; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNode.LastNode">
      <summary>
        <para>Gets the last child node.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the last child node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNode.NextNode">
      <summary>
        <para>Returns the next node at the same level.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the next node at the same level.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNode.NextVisibleNode">
      <summary>
        <para>Returns the next visible node.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the next visible node. null (Nothing in Visual Basic) if the current node is the last visible node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNode.Nodes">
      <summary>
        <para>Provides access to a collection of child nodes.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControlNodes"/> object that specifies the child node collection.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNode.ParentNode">
      <summary>
        <para>Gets the node that is the parent node for the current node.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the parent node for the current node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNode.PrevNode">
      <summary>
        <para>Gets the previous node at the same level.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the previous node at the same level.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNode.PrevVisibleNode">
      <summary>
        <para>Returns the previous visible node.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the previous visible node. null (Nothing in Visual Basic) if the current node is the first visible node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNode.RootNode">
      <summary>
        <para>Returns the parent node at the root level.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the parent node at the root level.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttControlNodes">
      <summary>
        <para>Provides a collection of <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNodes.#ctor(DevExpress.XtraTreeList.TreeList)">
      <summary>
        <para>Initializes a new GanttControlNodes class instance.</para>
      </summary>
      <param name="treeList">A <see cref="T:DevExpress.XtraTreeList.TreeList"/> object that specifies the control to which this collection belongs.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNodes.#ctor(DevExpress.XtraTreeList.TreeList,DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Initializes a new GanttControlNodes class instance.</para>
      </summary>
      <param name="treeList">A <see cref="T:DevExpress.XtraTreeList.TreeList"/> object that specifies the control to which this collection belongs.</param>
      <param name="parentNode">A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the parent node for nodes in this collection.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNodes.Add(System.Object)">
      <summary>
        <para>Appends a new node to the end of the collection.</para>
      </summary>
      <param name="nodeData">An array of objects, or a <see cref="T:System.Data.DataRow"/> object that specifies node values.</param>
      <returns>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the appended node.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNodes.Add(System.Object[])">
      <summary>
        <para>Appends a new node to the end of the collection.</para>
      </summary>
      <param name="nodeData">An array of objects, or a comma-separated list of parameters, or a <see cref="T:System.Data.DataRow"/> object that specifies node values.</param>
      <returns>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the appended node.</returns>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNodes.FirstNode">
      <summary>
        <para>Gets the first element in the collection.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that is the first node in the collection. null (Nothing in Visual Basic) if the collection is empty.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNodes.GanttControl">
      <summary>
        <para>Gets the control to which this collection belongs.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControl"/> object that specifies the control to which this collection belongs.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNodes.IndexOf(DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Returns the index of the specified node in this collection.</para>
      </summary>
      <param name="node">A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the node whose index should be returned.</param>
      <returns>An integer value that specifies the index of the specified node in this collection.</returns>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNodes.Item(System.Int32)">
      <summary>
        <para>Gets the element at the specified index.</para>
      </summary>
      <param name="index">An integer value that specifies the index of the element.</param>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that is the node at the specified index.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNodes.LastNode">
      <summary>
        <para>Gets the last element in the collection.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that is the last node in the collection. null (Nothing in Visual Basic) if the collection is empty.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlNodes.ParentNode">
      <summary>
        <para>Gets the parent node for nodes in this collection.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the parent node for nodes in this collection.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlNodes.Remove(DevExpress.XtraGantt.GanttControlNode)">
      <summary>
        <para>Removes the specified node from the collection.</para>
      </summary>
      <param name="node">A <see cref="T:DevExpress.XtraGantt.GanttControlNode"/> object that specifies the node that should be removed.</param>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttControlOptionsMainTimeRuler">
      <summary>
        <para>Specifies the timescale&#39;s bottom ruler settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlOptionsMainTimeRuler.#ctor(DevExpress.XtraGantt.GanttControl)">
      <summary>
        <para>Initializes a new GanttControlOptionsMainTimeRuler class instance. For internal use.</para>
      </summary>
      <param name="owner">A control to which the options belongs.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsMainTimeRuler.Count">
      <summary>
        <para>Gets or sets the number of measure units in a single division on the scale.</para>
      </summary>
      <value>A value that specifies the number of measure units in a single division on the scale. The value cannot be less than 1.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsMainTimeRuler.MaxUnit">
      <summary>
        <para>Gets or sets the maximum time interval that corresponds to a single unit on the timescale.</para>
      </summary>
      <value>A value that specifies the maximum time interval that corresponds to a single unit on the timescale.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsMainTimeRuler.MinUnit">
      <summary>
        <para>Gets or sets the minimum time interval that corresponds to a single unit on the timescale.</para>
      </summary>
      <value>A value that specifies the minimum time interval that corresponds to a single unit on the timescale.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsMainTimeRuler.Unit">
      <summary>
        <para>Gets or sets the minimum time interval that corresponds to a single unit on the timescale.</para>
      </summary>
      <value>A value that specifies the minimum time interval that corresponds to a single unit on the timescale.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttControlOptionsSplitter">
      <summary>
        <para>Specifies splitter options.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlOptionsSplitter.#ctor(DevExpress.XtraGantt.GanttControl)">
      <summary>
        <para>Initializes a new GanttControlOptionsSplitter class instance. For internal use.</para>
      </summary>
      <param name="ganttControl">A GanttControl object that specifes the control to which the options belongs.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsSplitter.AllowResize">
      <summary>
        <para>Gets or sets whether users can change the splitter position.</para>
      </summary>
      <value>true, if users can change the splitter position; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsSplitter.FixedPanel">
      <summary>
        <para>Gets or sets whether the task list or chart width is fixed when the user resizes the control, or their widths change proportionally.</para>
      </summary>
      <value>A GanttFixedPanel enumeration value that specifies the area whose width is fixed.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsSplitter.OverlayResizeZoneThickness">
      <summary>
        <para>Gets or sets the splitter grip zone width.</para>
      </summary>
      <value>An integer value that specifies the splitter grip zone width.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsSplitter.PanelVisibility">
      <summary>
        <para>Gets or sets whether the task list, chart or both are visible.</para>
      </summary>
      <value>A GanttPanelVisibility enumeration value that specifies whether the task list, chart or both are visible.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsSplitter.SplitterThickness">
      <summary>
        <para>Gets or sets the splitter width.</para>
      </summary>
      <value>An integer value that specifies the splitter width.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttControlOptionsView">
      <summary>
        <para>Specifies view options.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlOptionsView.#ctor(DevExpress.XtraGantt.GanttControl)">
      <summary>
        <para>Initializes a new GanttControlOptionsView class instance. For internal use.</para>
      </summary>
      <param name="gantt">A GanttControl object that specifies the control to which the options belong.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlOptionsView.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Assigns the specified object&#39;s property values to the current object&#39;s corresponding properties.</para>
      </summary>
      <param name="options">The object whose property values to assign to the current object&#39;s corresponding properties.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsView.CriticalPathHighlightMode">
      <summary>
        <para>Gets or sets whether the control highlights single, multiple, or no critical paths.</para>
      </summary>
      <value>A value that specifies whether the control highlights single, multiple, or no critical paths.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsView.DependencyDisplayMode">
      <summary>
        <para>Gets or sets whether dependency lines are displayed behind the text or above it.</para>
      </summary>
      <value>A value that specifies whether dependency lines are displayed behind the text or above it.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsView.InteractionTooltipLocation">
      <summary>
        <para>Gets or sets whether interaction tooltips are displayed and specifies their location.</para>
      </summary>
      <value>A value that specifies the tooltip location.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsView.ShowBaselines">
      <summary>
        <para>Gets or sets whether to show task baselines in the chart area.</para>
      </summary>
      <value>true to show task baselines in the chart area; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsView.ShowFirstLines">
      <summary>
        <para>Gets or sets whether the control displays the nearest (leftmost) vertical gridlines in the column that displays the node expand buttons.</para>
      </summary>
      <value>true to display the nearest vertical gridlines; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsView.ShowHorzLines">
      <summary>
        <para>Gets or sets whether to show horizontal lines in the task list.</para>
      </summary>
      <value>true, if the horizontal lines are shown; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlOptionsView.ShowIndicator">
      <summary>
        <para>Gets or sets whether the node indicator panel is displayed in the task list.</para>
      </summary>
      <value>true, if the node indicator panel is displayed; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection">
      <summary>
        <para>Provides access to appearance settings applied when the control is printed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection.#ctor(DevExpress.XtraGantt.GanttControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection"/> class with specified settings.</para>
      </summary>
      <param name="owner"></param>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection.ChartHorzLine">
      <summary>
        <para>Provides access to chart horizontal line appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection.ChartNonWorkTime">
      <summary>
        <para>Provides access to non-work time appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection.ChartTimescaleRuler">
      <summary>
        <para>Provides access to timescale ruler appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection.ChartVertLine">
      <summary>
        <para>Provides access to chart vertical line appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection.ChartWorkTime">
      <summary>
        <para>Provides access to work time appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection.CriticalPathDependency">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection.CriticalPathTask">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection.Dependency">
      <summary>
        <para>Provides access to dependency line appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection.Milestone">
      <summary>
        <para>Provides access to milestone appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection.SummaryTask">
      <summary>
        <para>Provides access to summary task appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttControlPrintAppearanceCollection.Task">
      <summary>
        <para>Provides access to task appearance settings.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttDependencyMappings">
      <summary>
        <para>Provides access to options specified based on data source field names (dependency type, time lag, predecessor and successor tasks).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttDependencyMappings.#ctor(DevExpress.XtraGantt.GanttControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraGantt.GanttDependencyMappings"/> class with specified settings.</para>
      </summary>
      <param name="ganttControl">A control to which the options belong.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttDependencyMappings.DataSource">
      <summary>
        <para>Gets or sets the data source that contains information about dependencies between tasks.</para>
      </summary>
      <value>An object that specifies a data source.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttDependencyMappings.LagFieldName">
      <summary>
        <para>Gets or sets the data source field (of the <see cref="T:System.TimeSpan"/> type) that specifies a task&#39;s time delay relative to its predecessor.</para>
      </summary>
      <value>The data source field that specifies a task&#39;s time delay relative to its predecessor.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttDependencyMappings.PredecessorFieldName">
      <summary>
        <para>Gets or sets the data source field that specifies a task&#39;s predecessor. Use a task&#39;s key to specify the predecessor.</para>
      </summary>
      <value>The data source field that specifies a task&#39;s predecessor.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttDependencyMappings.SuccessorFieldName">
      <summary>
        <para>Gets or sets the data source field that specifies a task&#39;s successor. Use a task&#39;s key to specify the successor.</para>
      </summary>
      <value>The data source field that specifies a task&#39;s successor.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttDependencyMappings.TypeFieldName">
      <summary>
        <para>Gets or sets the data source field (of the <see cref="T:DevExpress.XtraGantt.DependencyType"/> type) that specifies a task&#39;s dependency type.</para>
      </summary>
      <value>The data source field that specifies a task&#39;s dependency type.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttFixedPanel">
      <summary>
        <para>Enumerates modes that specify whether the task list or chart width is fixed when the user resizes the control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttFixedPanel.Chart">
      <summary>
        <para>The chart width is fixed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttFixedPanel.Default">
      <summary>
        <para>The mode is not specified explicitly. Enables the None mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttFixedPanel.None">
      <summary>
        <para>None width is fixed. The task list and diagram change their widths proportionally.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttFixedPanel.Tree">
      <summary>
        <para>The task list width is fixed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttPanelVisibility">
      <summary>
        <para>Enumerates modes that specify whether the task list, chart, or both are visible.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttPanelVisibility.Both">
      <summary>
        <para>The task list and chart are visible.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttPanelVisibility.Chart">
      <summary>
        <para>The chart is visible. The task list is hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttPanelVisibility.Default">
      <summary>
        <para>The mode is not specified explicitly. Enables the Both mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttPanelVisibility.Tree">
      <summary>
        <para>The task list is visible. The chart is hidden.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttTaskPopupMenuShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskPopupMenuShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttTaskPopupMenuShowingEventArgs.#ctor(DevExpress.XtraGantt.Chart.Item.Task.GanttChartTaskInfo,DevExpress.XtraGantt.Handler.GanttPopupMenu)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraGantt.GanttTaskPopupMenuShowingEventArgs"/> class with specified settings.</para>
      </summary>
      <param name="taskInfo">An object that contains view information about the task.</param>
      <param name="popupMenu">An object that specifies the context menu.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttTaskPopupMenuShowingEventArgs.Info">
      <summary>
        <para>Provides access to view information about the task.</para>
      </summary>
      <value>An object that contains information about the task.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttTaskPopupMenuShowingEventArgs.Items">
      <summary>
        <para>Provides access to menu items.</para>
      </summary>
      <value>An object that specifies the collection that contains menu items.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttTaskPopupMenuShowingEventArgs.Node">
      <summary>
        <para>Gets the node in the tree area for which the menu is about to be shown.</para>
      </summary>
      <value>An object that specifies the node for which the menu is about to be shown.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttTaskPopupMenuShowingEventHandler">
      <summary>
        <para>Represents a reference to a method that handles the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskPopupMenuShowing"/> event.</para>
      </summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">An object that contains event arguments.</param>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttTaskToolTipShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskToolTipShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.GanttTaskToolTipShowingEventArgs.#ctor(DevExpress.XtraGantt.Chart.Item.Task.GanttChartTaskInfo)">
      <summary>
        <para>Initializes a new GanttTaskToolTipShowingEventArgs class instance. For internal use.</para>
      </summary>
      <param name="taskInfo">An object that contains view information about the task.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttTaskToolTipShowingEventArgs.Info">
      <summary>
        <para>Provides view information about the processed task.</para>
      </summary>
      <value>An object that contains view information about the processed task.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttTaskToolTipShowingEventArgs.Node">
      <summary>
        <para>Gets the processed node.</para>
      </summary>
      <value>An object that specifies the processed node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttTaskToolTipShowingEventArgs.SuperTip">
      <summary>
        <para>Gets or sets the super tooltip.</para>
      </summary>
      <value>An object that specifies the super tooltip.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.GanttTaskToolTipShowingEventArgs.Text">
      <summary>
        <para>Gets or sets the text for the regular tooltip.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the regular tooltip&#39;s text.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttTaskToolTipShowingEventHandler">
      <summary>
        <para>Represents a reference to a method that handles the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskToolTipShowing"/> event.</para>
      </summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">An object that contains event arguments.</param>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttTimescaleUnit">
      <summary>
        <para>Enumerates values that specify time intervals that correspond to a single unit on the time scale.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttTimescaleUnit.Day">
      <summary>
        <para>A single unit corresponds to a day.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttTimescaleUnit.HalfYear">
      <summary>
        <para>A single unit corresponds to a half of the year.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttTimescaleUnit.Hour">
      <summary>
        <para>A single unit corresponds to an hour.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttTimescaleUnit.Millisecond">
      <summary>
        <para>A single unit corresponds to a millisecond.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttTimescaleUnit.Minute">
      <summary>
        <para>A single unit corresponds to a minute.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttTimescaleUnit.Month">
      <summary>
        <para>A single unit corresponds to a month.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttTimescaleUnit.Quarter">
      <summary>
        <para>A single unit corresponds to a quarter of the year.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttTimescaleUnit.Second">
      <summary>
        <para>A single unit corresponds to a second.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttTimescaleUnit.Week">
      <summary>
        <para>A single unit corresponds to a week.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttTimescaleUnit.Year">
      <summary>
        <para>A single unit corresponds to a year.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.GanttZoomMode">
      <summary>
        <para>Enumerates values that specify how the scale changes when a user scrolls the mouse wheel.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttZoomMode.Default">
      <summary>
        <para>The actual mode is not explicitly specified. Enables the Smooth mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttZoomMode.FixedIntervals">
      <summary>
        <para>Switches to the next measure unit (week, month, quarter, etc.) by each wheel step.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.GanttZoomMode.Smooth">
      <summary>
        <para>First decreases the size of a single division to a certain limit and then switches to the next measure unit (week, month, quarter, etc.).</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraGantt.Localization">
      <summary>
        <para>Contains types related to the Gantt control localization.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.Localization.GanttLocalizer">
      <summary>
        <para>Provides captions and date-time formats for the <see cref="T:DevExpress.XtraGantt.GanttControl"/>. Allows you to provide custom caption and date-time formats.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Localization.GanttLocalizer.#ctor">
      <summary>
        <para>Initializes a new GanttLocalizer class instance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.Localization.GanttLocalizer.Active">
      <summary>
        <para>Gets or sets an object used to localize the user interface. This is a static (Shared in VB) property.</para>
      </summary>
      <value>An object that is used to localize the user interface.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.Localization.GanttLocalizer.CreateResXLocalizer">
      <summary>
        <para>Returns an object that provides resources based on the regional settings (culture). For internal use.</para>
      </summary>
      <returns>An object that provides resources based on the regional settings (culture).</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.Localization.GanttLocalizer.GetString(DevExpress.XtraGantt.Localization.GanttStringId)">
      <summary>
        <para>Returns a caption or a date-time format for the specified visual element.</para>
      </summary>
      <param name="id">A value that specifies a visual element for which to return a caption or date-time format.</param>
      <returns>A <see cref="T:System.String"/> value that specifies a caption or date-time format.</returns>
    </member>
    <member name="T:DevExpress.XtraGantt.Localization.GanttStringId">
      <summary>
        <para>Enumerates captions and date-time formats for the <see cref="T:DevExpress.XtraGantt.GanttControl"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.BothPanelsVisibilityCaption">
      <summary>
        <para>&quot;Both&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.BothPanelsVisibilityDescription">
      <summary>
        <para>&quot;Set Both Panels Visible&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ChangeAllowResizeCaption">
      <summary>
        <para>&quot;Allow Resize&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ChangeAllowResizeDescription">
      <summary>
        <para>&quot;Change Allow Resize&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ChartFixedPanelCaption">
      <summary>
        <para>&quot;Chart&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ChartFixedPanelDescription">
      <summary>
        <para>&quot;Set Chart Fixed Panel&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ChartPanelVisibilityCaption">
      <summary>
        <para>&quot;Chart&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ChartPanelVisibilityDescription">
      <summary>
        <para>&quot;Set Chart Panel Visible&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ChartToolsRibbonPageCategoryCaption">
      <summary>
        <para>&quot;Gantt Chart Tools&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ChooseFixedPanelCaption">
      <summary>
        <para>&quot;Fixed Panel&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ChooseFixedPanelDescription">
      <summary>
        <para>&quot;Choose Fixed Panel&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ChoosePanelVisibilityCaption">
      <summary>
        <para>&quot;Panel Visibility&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ChoosePanelVisibilityDescription">
      <summary>
        <para>&quot;Choose Visible Panels&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ColumnsRibbonPageGroupCaption">
      <summary>
        <para>&quot;Columns&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.DayFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.DefaultFixedPanelCaption">
      <summary>
        <para>&quot;Default&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.DefaultFixedPanelDescription">
      <summary>
        <para>&quot;Set Default Fixed Panel State&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.DefaultPanelVisibilityCaption">
      <summary>
        <para>&quot;Default&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.DefaultPanelVisibilityDescription">
      <summary>
        <para>&quot;Set Default Panel Visibility State&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.FormatRibbonPageCaption">
      <summary>
        <para>&quot;Format&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.HalfYearFormat">
      <summary>
        <para>&quot;H{0}&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.HourFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.InteractionTooltipFinishCaption">
      <summary>
        <para>TASK FINISH</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.InteractionTooltipPredecessorCaption">
      <summary>
        <para>PREDECESSOR</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.InteractionTooltipProgressCaption">
      <summary>
        <para>PROGRESS</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.InteractionTooltipStartCaption">
      <summary>
        <para>TASK START</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.InteractionTooltipSuccessorCaption">
      <summary>
        <para>SUCCESSOR</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.LongDayFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.LongHalfYearFormat">
      <summary>
        <para>&quot;Half {0}, {1}&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.LongHourFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.LongMillisecondFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.LongMinuteFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.LongMonthFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.LongQuarterFormat">
      <summary>
        <para>&quot;Qtr {0}, {1}&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.LongSecondFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.MillisecondFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.MinuteFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.MonthFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.MonthYearFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.NoneFixedPanelCaption">
      <summary>
        <para>&quot;None&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.NoneFixedPanelDescription">
      <summary>
        <para>&quot;Set None Fixed Panels&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.PrintPreviewBestFit">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.PrintPreviewClosePreviewButton">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.PrintPreviewGroupClose">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.PrintPreviewGroupPrint">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.PrintPreviewGroupZoom">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.PrintPreviewInfoLabel">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.PrintPreviewPageSetupButton">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.PrintPreviewPrintButton">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.PrintPreviewPrintQuickButton">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.PrintPreviewStopOperationButton">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.PrintPreviewZoom">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.PrintPreviewZoomInButton">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.PrintPreviewZoomOutButton">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.QuarterFormat">
      <summary>
        <para>&quot;Q{0}&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ScheduleRibbonPageGroupCaption">
      <summary>
        <para>&quot;Schedule&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.SecondFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ShortMonthFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ShowHorzLinesCaption">
      <summary>
        <para>&quot;Show Horizontal Lines&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ShowHorzLinesDescription">
      <summary>
        <para>&quot;Show Horizontal Lines&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.SplitViewRibbonPageGroupCaption">
      <summary>
        <para>&quot;Split View&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.TaskRibbonPageCaption">
      <summary>
        <para>&quot;Task&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.TreeFixedPanelCaption">
      <summary>
        <para>&quot;Tree&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.TreeFixedPanelDescription">
      <summary>
        <para>&quot;Set Tree Fixed Panel&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.TreePanelVisibilityCaption">
      <summary>
        <para>&quot;Tree&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.TreePanelVisibilityDescription">
      <summary>
        <para>&quot;Set Tree Panel Visibile&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.ViewRibbonPageCaption">
      <summary>
        <para>&quot;View&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Localization.GanttStringId.YearFormat">
      <summary>
        <para>Not specified.</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraGantt.Options">
      <summary>
        <para>Contains types that specify options applied to  <see cref="T:DevExpress.XtraGantt.GanttControl"/>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.Options.GanttControlOptionsBehavior">
      <summary>
        <para>Contains options that specify a <see cref="T:DevExpress.XtraGantt.GanttControl"/>&#39;s behavior.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Options.GanttControlOptionsBehavior.#ctor(DevExpress.XtraGantt.GanttControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraGantt.Options.GanttControlOptionsBehavior"/> class with specified settings.</para>
      </summary>
      <param name="gantt">The control whose behavior the <see cref="T:DevExpress.XtraGantt.Options.GanttControlOptionsBehavior"/> object being created specifies.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.Options.GanttControlOptionsBehavior.AllowSplitTasks">
      <summary>
        <para>Gets or sets whether the control automatically splits tasks and whether users can manually split tasks.</para>
      </summary>
      <value><see cref="F:DevExpress.Utils.DefaultBoolean.Default"/> or <see cref="F:DevExpress.Utils.DefaultBoolean.True"/> if the control splits tasks; otherwise, <see cref="F:DevExpress.Utils.DefaultBoolean.False"/>.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Options.GanttControlOptionsBehavior.LegacyScheduleMode">
      <summary>
        <para>Gets or sets whether the control reschedules the task being modified with respect to the workweek schedule.</para>
      </summary>
      <value>true, if the control reschedules tasks with respect to the workweek schedule; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Options.GanttControlOptionsBehavior.ScheduleMode">
      <summary>
        <para>Gets or sets whether the control automatically reschedules all dependent tasks when a user modifies a particular task. <see cref="F:DevExpress.XtraGantt.Options.ScheduleMode.Default"/> is equivalent to <see cref="F:DevExpress.XtraGantt.Options.ScheduleMode.Auto"/>.</para>
      </summary>
      <value>A value that specifies whether the control automatically updates tasks.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Options.GanttControlOptionsBehavior.TaskDateChangeMode">
      <summary>
        <para>Gets or sets whether the control shifts a task or extends/reduces its duration when a user changes the task&#39;s start or finish date.</para>
      </summary>
      <value>A value that specifies whether the control shifts a task or extends/reduces its duration when a user changes the task&#39;s start or finish date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Options.GanttControlOptionsBehavior.UpdateDependentTaskProgress">
      <summary>
        <para>Gets or sets whether the control updates the progress of dependent tasks (summaries and sub-tasks) when a user modifies a task&#39;s progress.</para>
      </summary>
      <value>true to update the progress of dependent tasks when changes are made; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.Options.GanttControlOptionsCustomization">
      <summary>
        <para>Contains options that specify what users can customize in a <see cref="T:DevExpress.XtraGantt.GanttControl"/>&#39;s chart.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Options.GanttControlOptionsCustomization.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraGantt.Options.GanttControlOptionsCustomization"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.Options.GanttControlOptionsCustomization.AllowModifyDependencies">
      <summary>
        <para>Gets or sets whether users can modify dependencies between tasks in the chart: change a predecessor or successor of a task, create new dependencies.</para>
      </summary>
      <value><see cref="F:DevExpress.Utils.DefaultBoolean.True"/>, if users can modify dependencies; otherwise, <see cref="F:DevExpress.Utils.DefaultBoolean.Default"/> or <see cref="F:DevExpress.Utils.DefaultBoolean.False"/>.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Options.GanttControlOptionsCustomization.AllowModifyProgress">
      <summary>
        <para>Gets or sets whether users can modify a task&#39;s progress in the chart.</para>
      </summary>
      <value><see cref="F:DevExpress.Utils.DefaultBoolean.True"/>, if users can modify a task&#39;s progress; otherwise, <see cref="F:DevExpress.Utils.DefaultBoolean.Default"/> or <see cref="F:DevExpress.Utils.DefaultBoolean.False"/>.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.Options.GanttControlOptionsCustomization.AllowModifyTasks">
      <summary>
        <para>Gets or sets whether users can modify a task&#39;s start and finish dates in the chart: resize its bounds or move it to a new time slot.</para>
      </summary>
      <value><see cref="F:DevExpress.Utils.DefaultBoolean.True"/>, if users can modify a task&#39;s start and finish dates; otherwise, <see cref="F:DevExpress.Utils.DefaultBoolean.Default"/> or <see cref="F:DevExpress.Utils.DefaultBoolean.False"/>.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.Options.ScheduleMode">
      <summary>
        <para>Enumerates values that specify whether the control automatically reschedules all successor tasks when a user moves a particular task to a new time slot.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Options.ScheduleMode.Auto">
      <summary>
        <para>When a user modifies a task, the control automatically reschedules the processed task and all dependent tasks.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Options.ScheduleMode.Default">
      <summary>
        <para>The mode is not specified explicitly. Enables <see cref="F:DevExpress.XtraGantt.Options.ScheduleMode.Auto"/> mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Options.ScheduleMode.Manual">
      <summary>
        <para>When a user modifies a task, the control does not reschedule the processed task and dependent tasks.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.Options.TaskDateChangeMode">
      <summary>
        <para>Enumerates values that specify whether the control shifts a task or extends/reduces its duration when a user changes the task&#39;s start or finish date.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Options.TaskDateChangeMode.Default">
      <summary>
        <para>The mode is not specified explicitly. Enables MoveTask mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Options.TaskDateChangeMode.MoveTask">
      <summary>
        <para>The control shifts a task when a user changes the task&#39;s start or finish date.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Options.TaskDateChangeMode.UpdateDuration">
      <summary>
        <para>The control extends/reduces a task&#39;s duration when a user changes the task&#39;s start or finish date.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.RequestTimescaleRulersEventArgs">
      <summary>
        <para>Contains data for the <see cref="E:DevExpress.XtraGantt.GanttControl.RequestTimescaleRulers"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.RequestTimescaleRulersEventArgs.#ctor(DevExpress.XtraGantt.ViewInfo.GanttChartViewInfo)">
      <summary>
        <para>Initializes a new RequestTimescaleRulersEventArgs class instance. For internal use.</para>
      </summary>
      <param name="chartInfo">An object that contains view information about the chart.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.RequestTimescaleRulersEventArgs.GetTimescaleUnitWidth(DevExpress.XtraGantt.GanttTimescaleUnit)">
      <summary>
        <para>Returns the width of the specified measure unit on the timescale.</para>
      </summary>
      <param name="unit">A value that specifies the timescale measure unit.</param>
      <returns>A value that specifies the width of the specified measure unit on the timescale.</returns>
    </member>
    <member name="P:DevExpress.XtraGantt.RequestTimescaleRulersEventArgs.TimescaleRulers">
      <summary>
        <para>Provides access to the chart&#39;s timescale rulers.</para>
      </summary>
      <value>An object that contains timescale rulers.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.RequestTimescaleRulersEventHandler">
      <summary>
        <para>Represents a reference to a method that handles the <see cref="E:DevExpress.XtraGantt.GanttControl.RequestTimescaleRulers"/> event.</para>
      </summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">An object that contains event arguments.</param>
    </member>
    <member name="N:DevExpress.XtraGantt.Scheduling">
      <summary>
        <para>Contains types that specify a workweek schedule in the Gantt control.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.Scheduling.GanttControlTask">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Scheduling.GanttControlTask.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraGantt.Scheduling.GanttControlTask"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.GanttControlTask.Node">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.GanttControlTask.Progress">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.GanttControlTask.ScheduledBaselineDuration">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.GanttControlTask.ScheduledBaselineFinishDate">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.GanttControlTask.ScheduledBaselineStartDate">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.GanttControlTask.ScheduledDuration">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.GanttControlTask.ScheduledFinishDate">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.GanttControlTask.ScheduledStartDate">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.GanttControlTask.Text">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.GanttControlTask.TooltipText">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.GanttControlTask.Type">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.GanttControlTask.ValidationResult">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraGantt.Scheduling.Month">
      <summary>
        <para>Enumerates months.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.Month.April">
      <summary>
        <para>April</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.Month.August">
      <summary>
        <para>August</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.Month.December">
      <summary>
        <para>December</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.Month.February">
      <summary>
        <para>February</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.Month.January">
      <summary>
        <para>January</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.Month.July">
      <summary>
        <para>July</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.Month.June">
      <summary>
        <para>June</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.Month.March">
      <summary>
        <para>March</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.Month.May">
      <summary>
        <para>May</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.Month.November">
      <summary>
        <para>November</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.Month.October">
      <summary>
        <para>October</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.Month.September">
      <summary>
        <para>September</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.Scheduling.WeekOfMonth">
      <summary>
        <para>Enumerates week numbers in a month.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.WeekOfMonth.First">
      <summary>
        <para>The first week.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.WeekOfMonth.Forth">
      <summary>
        <para>The forth week.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.WeekOfMonth.Last">
      <summary>
        <para>The last week.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.WeekOfMonth.Second">
      <summary>
        <para>The second week.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraGantt.Scheduling.WeekOfMonth.Third">
      <summary>
        <para>The third week.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.Scheduling.WorkTime">
      <summary>
        <para>Represents a single time interval in a day&#39;s schedule (for example, 9:00 to 12:00).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Scheduling.WorkTime.#ctor(System.Int32,System.Int32)">
      <summary>
        <para>Initializes a new WorkTime class instance.</para>
      </summary>
      <param name="fromHours">An <see cref="T:System.Int64"/> value that specifies the start time, in ticks, of the interval.</param>
      <param name="toHours">An <see cref="T:System.Int64"/> value that specifies the end time, in ticks, of the interval.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.Scheduling.WorkTime.#ctor(System.TimeSpan,System.TimeSpan)">
      <summary>
        <para>Initializes a new WorkTime class instance.</para>
      </summary>
      <param name="fromTime">A <see cref="T:System.TimeSpan"/> structure that specifies the start time of the interval.</param>
      <param name="toTime">An <see cref="T:System.Int64"/> value that specifies the end time of the interval.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.WorkTime.Duration">
      <summary>
        <para>Gets the duration of the work time interval.</para>
      </summary>
      <value>An <see cref="T:System.Int64"/> value that specifies the duration of the work time interval.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.Scheduling.WorkTime.Equals(DevExpress.XtraGantt.Scheduling.WorkTime)">
      <summary>
        <para>Returns whether the objects specify the same time interval.</para>
      </summary>
      <param name="other">An object that specifies a time interval.</param>
      <returns>true, if the objects specify the same interval; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.WorkTime.FromTime">
      <summary>
        <para>Gets the start time of the interval.</para>
      </summary>
      <value>An <see cref="T:System.Int64"/> value that specifies the start time, in ticks, of the interval.</value>
    </member>
    <member name="M:DevExpress.XtraGantt.Scheduling.WorkTime.Includes(System.TimeSpan)">
      <summary>
        <para>Returns whether the interval includes the specified time.</para>
      </summary>
      <param name="time">A <see cref="T:System.TimeSpan"/> structure that specifies the time to check whether the interval includes it.</param>
      <returns>true, if whether the time interval includes specified time; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.WorkTime.ToTime">
      <summary>
        <para>Gets the end time of the interval.</para>
      </summary>
      <value>An <see cref="T:System.Int64"/> value that specifies the end time, in ticks, of the interval.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.Scheduling.WorkTimeCollection">
      <summary>
        <para>Represents a collection of WortTime objects that specify a day&#39;s schedule (for example, 9:00 to 12:00 and 13:00 to 18:00).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Scheduling.WorkTimeCollection.#ctor">
      <summary>
        <para>Initializes a new WorkTimeCollection class instance.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.Scheduling.WorkTimeCollection.Add(System.TimeSpan,System.TimeSpan)">
      <summary>
        <para>Adds a time interval with the specified start and end time to the collection.</para>
      </summary>
      <param name="fromTime">A <see cref="T:System.TimeSpan"/> structure that specifies the start time of the interval.</param>
      <param name="toTime">A <see cref="T:System.TimeSpan"/> structure that specifies the end time of the interval.</param>
      <returns>An object that represents a time interval with the specified start and end time.</returns>
    </member>
    <member name="P:DevExpress.XtraGantt.Scheduling.WorkTimeCollection.IsNonWorkDay">
      <summary>
        <para>Gets whether this collection is empty.</para>
      </summary>
      <value>true, if the collection is empty; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.SplitTaskMappings">
      <summary>
        <para>Specifies names of data source fields that contain information about task splits.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.SplitTaskMappings.#ctor(DevExpress.XtraGantt.GanttControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraGantt.SplitTaskMappings"/> class with specified settings.</para>
      </summary>
      <param name="ganttControl"></param>
    </member>
    <member name="P:DevExpress.XtraGantt.SplitTaskMappings.DurationFieldName">
      <summary>
        <para>Gets or sets the name of the field that contains a task split&#39;s duration.</para>
      </summary>
      <value>The name of the field that contains a task split&#39;s duration.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.SplitTaskMappings.KeyFieldName">
      <summary>
        <para>Gets or sets the name of the field that contains a task&#39;s identifier.</para>
      </summary>
      <value>The name of the field that contains a task&#39;s identifier.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.SplitTaskMappings.StartDateFieldName">
      <summary>
        <para>Gets or sets the name of the field that contains a task split&#39;s start date.</para>
      </summary>
      <value>The name of the field that contains a task split&#39;s start date.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.SplitterPositionChangingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.SplitterPositionChanging"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.SplitterPositionChangingEventArgs.#ctor(System.Int32,System.Int32)">
      <summary>
        <para>Initializes a new SplitterPositionChangingEventArgs object instance. For internal use.</para>
      </summary>
      <param name="oldValue">An integer value that specifies the current position.</param>
      <param name="newValue">An integer value that specifies the position that is about to be set.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.SplitterPositionChangingEventArgs.#ctor(System.Int32,System.Int32,System.Boolean)">
      <summary>
        <para>Initializes a new SplitterPositionChangingEventArgs object instance. For internal use.</para>
      </summary>
      <param name="oldValue">An integer value that specifies the current position.</param>
      <param name="newValue">An integer value that specifies the position that is about to be set.</param>
      <param name="cancel">true to cancel the action; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.SplitterPositionChangingEventArgs.NewValue">
      <summary>
        <para>Gets the position that is about to be set.</para>
      </summary>
      <value>An object that specifies the position that is about to be set.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.SplitterPositionChangingEventArgs.OldValue">
      <summary>
        <para>Gets the current position.</para>
      </summary>
      <value>An integer value that specifies the splitter&#39;s current position.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.SplitterPositionChangingEventHandler">
      <summary>
        <para>Represents a reference to a method that handles the <see cref="E:DevExpress.XtraGantt.GanttControl.SplitterPositionChanging"/> event.</para>
      </summary>
      <param name="sender">The object that raised the event.</param>
      <param name="e">An object that contains event arguments.</param>
    </member>
    <member name="T:DevExpress.XtraGantt.TaskDependencyModificationCancelEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskDependencyModification"/> and <see cref="E:DevExpress.XtraGantt.GanttControl.TaskDependencyModificationCompleted"/> events.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskDependencyModificationCancelEventArgs.Allow">
      <summary>
        <para>Gets or sets whether users are allowed to modify the dependency.</para>
      </summary>
      <value>true, users are allowed to modify the dependency; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskDependencyModificationCancelEventArgs.Cancel">
      <summary>
        <para>Allows you to cancel the operation.</para>
      </summary>
      <value>true, to cancel the operation; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.TaskDependencyModificationEventArgs">
      <summary>
        <para>Provides data for <see cref="E:DevExpress.XtraGantt.GanttControl.TaskDependencyModificationCanceled"/> and <see cref="E:DevExpress.XtraGantt.GanttControl.TaskDependencyModified"/> events.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskDependencyModificationEventArgs.ChangeType">
      <summary>
        <para>Gets the type of the operation.</para>
      </summary>
      <value>A value that specifies the type of the operation.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskDependencyModificationEventArgs.OriginalSuccessorNode">
      <summary>
        <para>Gets the processed dependency&#39;s original (before the operation) successor node.</para>
      </summary>
      <value>An object that specifies a node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskDependencyModificationEventArgs.OriginalSuccessorTask">
      <summary>
        <para>Gets the processed dependency&#39;s original (before the operation) successor task.</para>
      </summary>
      <value>An object that specifies a task.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskDependencyModificationEventArgs.PredecessorNode">
      <summary>
        <para>Gets the processed dependency&#39;s current (during the operation) predecessor node.</para>
      </summary>
      <value>An object that specifies a node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskDependencyModificationEventArgs.PredecessorTask">
      <summary>
        <para>Gets the processed dependency&#39;s current (during the operation) predecessor task.</para>
      </summary>
      <value>An object that specifies a task.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskDependencyModificationEventArgs.SuccessorNode">
      <summary>
        <para>Gets the processed dependency&#39;s current (during the operation) successor node.</para>
      </summary>
      <value>An object that specifies a node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskDependencyModificationEventArgs.SuccessorTask">
      <summary>
        <para>Gets the processed dependency&#39;s current (during the operation) successor task.</para>
      </summary>
      <value>An object that specifies a task.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskDependencyModificationEventArgs.Type">
      <summary>
        <para>Gets the type of the processed dependency.</para>
      </summary>
      <value>A value that specifies the dependency type.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.TaskFinishModificationCanceledEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskFinishDateModificationCanceled"/> event.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.TaskFinishModificationEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskFinishDateModificationStarted"/>, <see cref="E:DevExpress.XtraGantt.GanttControl.TaskFinishDateModification"/>, and <see cref="E:DevExpress.XtraGantt.GanttControl.TaskFinishDateModificationCompleted"/> events.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskFinishModificationEventArgs.CurrentTaskFinish">
      <summary>
        <para>Gets or sets the processed task&#39;s current (during the operation) finish date.</para>
      </summary>
      <value>A structure that specifies a date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskFinishModificationEventArgs.OriginalTaskFinish">
      <summary>
        <para>Gets the processed task&#39;s original (before the operation) finish date.</para>
      </summary>
      <value>A structure that specifies a date.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.TaskFinishModifiedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskFinishDateModified"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskFinishModifiedEventArgs.OriginalTaskFinish">
      <summary>
        <para>Gets the processed task&#39;s original (before the operation) finish date.</para>
      </summary>
      <value>A structure that specifies a date.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.TaskModificationEventArgs">
      <summary>
        <para>Provides data for events raised when a user modifies a task.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskModificationEventArgs.ProcessedNode">
      <summary>
        <para>Gets the modified node.</para>
      </summary>
      <value>An object that specifies a node.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskModificationEventArgs.ProcessedTask">
      <summary>
        <para>Gets the modified task.</para>
      </summary>
      <value>An object that specifies a task.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskModificationEventArgs.TaskSplitInfo">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraGantt.TaskMoveCanceledEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskMoveCanceled"/> event.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.TaskMovedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskMoved"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskMovedEventArgs.OriginalTaskFinish">
      <summary>
        <para>Gets the processed task&#39;s original (before the operation) finish date.</para>
      </summary>
      <value>A structure that specifies a date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskMovedEventArgs.OriginalTaskStart">
      <summary>
        <para>Gets the processed task&#39;s original (before the operation) start date.</para>
      </summary>
      <value>A structure that specifies a date.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.TaskMovingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskMoveStarted"/>, <see cref="E:DevExpress.XtraGantt.GanttControl.TaskMoving"/>, <see cref="E:DevExpress.XtraGantt.GanttControl.TaskMoveCanceled"/>, and <see cref="E:DevExpress.XtraGantt.GanttControl.TaskMoved"/> events.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskMovingEventArgs.CurrentTaskFinish">
      <summary>
        <para>Gets or sets the processed task&#39;s current (during the operation) finish date.</para>
      </summary>
      <value>A structure that specifies a date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskMovingEventArgs.CurrentTaskStart">
      <summary>
        <para>Gets or sets the processed task&#39;s current (during the operation) start date.</para>
      </summary>
      <value>A structure that specifies a date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskMovingEventArgs.OriginalTaskFinish">
      <summary>
        <para>Gets the processed task&#39;s original (before the operation) finish date.</para>
      </summary>
      <value>A structure that specifies a date.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskMovingEventArgs.OriginalTaskStart">
      <summary>
        <para>Gets the processed task&#39;s original (before the operation) start date.</para>
      </summary>
      <value>A structure that specifies a date.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.TaskProgressModificationCanceledEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskProgressModificationCanceled"/> event.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraGantt.TaskProgressModificationEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskProgressModificationStarted"/>, <see cref="E:DevExpress.XtraGantt.GanttControl.TaskProgressModification"/>, and <see cref="E:DevExpress.XtraGantt.GanttControl.TaskProgressModificationCompleted"/> events.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskProgressModificationEventArgs.CurrentProgress">
      <summary>
        <para>Gets or sets the processed task&#39;s current (during the operation) progress.</para>
      </summary>
      <value>A value that specifies progress.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskProgressModificationEventArgs.OriginalProgress">
      <summary>
        <para>Gets the processed task&#39;s original (before the operation) progress.</para>
      </summary>
      <value>A value that specifies progress.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.TaskProgressModifiedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraGantt.GanttControl.TaskProgressModified"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskProgressModifiedEventArgs.CurrentProgress">
      <summary>
        <para>Gets the processed task&#39;s current (after the operation) progress.</para>
      </summary>
      <value>A value that specifies progress.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TaskProgressModifiedEventArgs.OriginalProgress">
      <summary>
        <para>Gets the processed task&#39;s original (before the operation) progress.</para>
      </summary>
      <value>A value that specifies progress.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.TreeListMappings">
      <summary>
        <para>Provides access to options specified based on data source field names and tree list column objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.TreeListMappings.#ctor(DevExpress.XtraGantt.GanttControl)">
      <summary>
        <para>Initializes a new TreeListMappings class instance.</para>
      </summary>
      <param name="ganttControl">A control to which the options belong.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.TreeListMappings.AutoFillColumn">
      <summary>
        <para>Gets or sets a column that automatically stretches to occupy all available empty space when the column auto-width functionality is disabled.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object that occupies free <see cref="T:DevExpress.XtraTreeList.TreeList"/> space.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TreeListMappings.AutoFillFieldName">
      <summary>
        <para>Gets or sets a data source field whose associated Tree List column should automatically resize to occupy all available <see cref="T:DevExpress.XtraTreeList.TreeList"/> space.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies a data source field whose associated Tree List column should automatically resize to occupy all available <see cref="T:DevExpress.XtraTreeList.TreeList"/> space.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TreeListMappings.CheckBoxFieldName">
      <summary>
        <para>Gets or sets the name of the field whose values define the node check states.</para>
      </summary>
      <value>The name of the field to bind to the node check states.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TreeListMappings.ChildListFieldName">
      <summary>
        <para>Gets or sets the name of the collection property that returns child items in a bound business object.</para>
      </summary>
      <value>The name of the collection property that returns child items in a bound business object.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TreeListMappings.DataMember">
      <summary>
        <para>Gets or sets a specific list in a data source whose data is displayed by the TreeList control.</para>
      </summary>
      <value>A string value specifying a list in a data source.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TreeListMappings.HierarchyColumn">
      <summary>
        <para>Gets or sets the column in which tree list nodes display their expand/collapse buttons.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object specifying the column in which tree list nodes display their expand/collapse buttons.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TreeListMappings.HierarchyFieldName">
      <summary>
        <para>Gets the name of a field in the underlying data source whose tree list column displays the node expand/collapse buttons.</para>
      </summary>
      <value>A string value specifying the name of a field in the underlying data source whose tree list column displays the node expand/collapse buttons.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TreeListMappings.ImageIndexFieldName">
      <summary>
        <para>Gets or sets the name of the field whose values represent select image indexes for corresponding nodes.</para>
      </summary>
      <value>A string value that specifies the name of the data field that stores image indexes.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TreeListMappings.KeyFieldName">
      <summary>
        <para>Gets or sets the name of the key field that uniquely identifies records in the data source.</para>
      </summary>
      <value>The key identifier field name.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TreeListMappings.ParentFieldName">
      <summary>
        <para>Gets or sets the data source field that identifies each record&#39;s parent.</para>
      </summary>
      <value>The parent identifier field name.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TreeListMappings.PreviewFieldName">
      <summary>
        <para>Gets or sets the name of the field that automatically provides data for preview sections.</para>
      </summary>
      <value>The name of the field whose values are displayed in preview sections.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TreeListMappings.TreeViewColumn">
      <summary>
        <para>Gets or sets the only column whose values are displayed by the control in TreeView visual style.</para>
      </summary>
      <value>The only column whose values are displayed by the control in TreeView visual style.</value>
    </member>
    <member name="P:DevExpress.XtraGantt.TreeListMappings.TreeViewFieldName">
      <summary>
        <para>Gets or sets the only field whose values are displayed by the control in TreeView visual style.</para>
      </summary>
      <value>The name of the field whose values are displayed by the control in TreeView visual style.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.WorkDayOfWeek">
      <summary>
        <para>Represents a work day of the week.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.WorkDayOfWeek.#ctor(System.DayOfWeek,DevExpress.XtraGantt.Scheduling.WorkTime[])">
      <summary>
        <para>Initializes a new WorkDayOfWeek class instance.</para>
      </summary>
      <param name="dayOfWeek">A value that specifies the day of the week.</param>
      <param name="workTimes">An array of <see cref="T:DevExpress.XtraGantt.Scheduling.WorkTime"/> objects that specify work time intervals.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.WorkDayOfWeek.#ctor(System.DayOfWeek,System.Boolean)">
      <summary>
        <para>Initializes a new WorkDayOfWeek class instance.</para>
      </summary>
      <param name="dayOfWeek">A value that specifies the day of the week.</param>
      <param name="nonWorkDay">true, if the day is a non-work day; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.WorkDayOfWeek.#ctor(System.DayOfWeek,System.Collections.Generic.IEnumerable{DevExpress.XtraGantt.Scheduling.WorkTime})">
      <summary>
        <para>Initializes a new WorkDayOfWeek class instance.</para>
      </summary>
      <param name="dayOfWeek">A value that specifies the day of the week.</param>
      <param name="workTimes">A collection of <see cref="T:DevExpress.XtraGantt.Scheduling.WorkTime"/> objects that specify work time intervals.</param>
    </member>
    <member name="P:DevExpress.XtraGantt.WorkDayOfWeek.DayOfWeek">
      <summary>
        <para>Gets the day of the week.</para>
      </summary>
      <value>A value that specifies the day of the week.</value>
    </member>
    <member name="T:DevExpress.XtraGantt.WorkWeekRule">
      <summary>
        <para>Represents a regular workweek schedule.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.WorkWeekRule.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraGantt.WorkWeekRule"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraGantt.WorkWeekRule.#ctor(DevExpress.XtraGantt.GanttControl)">
      <summary>
        <para>Initializes a new WorkWeekRule class instance.</para>
      </summary>
      <param name="ganttControl">A control to which the collection belongs.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.WorkWeekRule.Add(DevExpress.XtraGantt.WorkDayOfWeek)">
      <summary>
        <para>Adds the specified day of week to the workweek.&#39;</para>
      </summary>
      <param name="day">A value that specifies the day of the week.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.WorkWeekRule.Add(System.DayOfWeek,DevExpress.XtraGantt.Scheduling.WorkTime[])">
      <summary>
        <para>Adds the specified day of week to the workweek.</para>
      </summary>
      <param name="dayOfWeek">A value that specifies the day of the week.</param>
      <param name="workTimes">An array of objects that specify work intervals.</param>
      <returns>An object that specifies a work day of the week.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.WorkWeekRule.Add(System.DayOfWeek,System.Collections.Generic.IEnumerable{DevExpress.XtraGantt.Scheduling.WorkTime})">
      <summary>
        <para>Adds the specified day of week to the workweek.</para>
      </summary>
      <param name="dayOfWeek">A value that specifies the day of the week.</param>
      <param name="workTimes">A collection of objects that specify work intervals.</param>
      <returns>An object that specifies a work day of the week.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.WorkWeekRule.AddNonWorkDay(System.DayOfWeek)">
      <summary>
        <para>Adds the specified day of the week to the workweek schedule as a non-work day.</para>
      </summary>
      <param name="dayOfWeek">A value that specifies the day of the week.</param>
      <returns>A <see cref="T:DevExpress.XtraGantt.WorkDayOfWeek"/> object that specifies the work day schedule.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.WorkWeekRule.AddRange(DevExpress.XtraGantt.WorkDayOfWeek[])">
      <summary>
        <para>Adds an array to the collection.</para>
      </summary>
      <param name="days">An array to add to the collection.</param>
    </member>
    <member name="M:DevExpress.XtraGantt.WorkWeekRule.AddWorkDay(System.DayOfWeek)">
      <summary>
        <para>Adds the specified day of the week to the workweek schedule.</para>
      </summary>
      <param name="dayOfWeek">A value that specifies the day of the week.</param>
      <returns>A <see cref="T:DevExpress.XtraGantt.WorkDayOfWeek"/> object that specifies the work day schedule.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.WorkWeekRule.GetDefaultWorkTime(System.DayOfWeek)">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="index">For internal use.</param>
      <returns>For internal use.</returns>
    </member>
    <member name="M:DevExpress.XtraGantt.WorkWeekRule.Includes(System.Int64)">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="day">For internal use.</param>
      <returns>For internal use.</returns>
    </member>
    <member name="P:DevExpress.XtraGantt.WorkWeekRule.Item(System.DayOfWeek)">
      <summary>
        <para>Returns a <see cref="T:DevExpress.XtraGantt.WorkDayOfWeek"/> object that specifies the work day schedule for the specified day of the week.</para>
      </summary>
      <param name="index">A value that specifies the day of the week for which to return a work day schedule.</param>
      <value>A <see cref="T:DevExpress.XtraGantt.WorkDayOfWeek"/> object that specifies the work day schedule.</value>
    </member>
  </members>
</doc>