<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.Xpf.Docking.v20.2</name>
  </assembly>
  <members>
    <member name="N:DevExpress.Xpf.Docking">
      <summary>
        <para>Contains classes that implement the main functionality of the DXDocking for WPF suite. To use these classes in XAML code, add the xmlns:dxdo=&quot;&quot; namespace reference.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.ActivateOnFocusing">
      <summary>
        <para>Lists the values that specify how the content item is activated on focusing.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ActivateOnFocusing.Keyboard">
      <summary>
        <para>The content item is activated on getting keyboard focus.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ActivateOnFocusing.Logical">
      <summary>
        <para>The content item is activated on getting logical focus.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ActivateOnFocusing.None">
      <summary>
        <para>The content item is not activated.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.AutoHideGroup">
      <summary>
        <para>A container for auto-hidden dock panels at a specific side of the DockLayoutManager.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideSize">
      <summary>
        <para>Gets or sets the size of panels belonging to the current <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/>, in pixels.
This is a dependency property.</para>
      </summary>
      <value>A Size structure that specifies the size of panels belonging to the current <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/>, in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideSizeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideSize"/> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideSpeed">
      <summary>
        <para>Gets or sets the time, in milliseconds, required to open/close an auto-hidden panel belonging to the current group.
This is a dependency property.</para>
      </summary>
      <value>An integer value that specifies the time, in milliseconds, required to open/close an auto-hidden panel belonging to the current group.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideSpeedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideSpeed"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideTypeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType"/> attached property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.AutoHideGroup.DockType">
      <summary>
        <para>Gets or sets the side of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> at which the current <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> object is docked.
This is a dependency property.</para>
      </summary>
      <value>A Dock value that specifies the side of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> at which the current <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> object is docked.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.AutoHideGroup.DockTypeChanged">
      <summary>
        <para>Fires when the value of the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.DockType"/> property is changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.AutoHideGroup.DockTypeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.DockType"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.GetAutoHideSize(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the auto-hide size for a specific object.</para>
      </summary>
      <param name="target">A DependencyObject whose auto-size is to be obtained.</param>
      <returns>A System.Windows.Size object that is the object&#39;s auto-hide size.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.GetAutoHideType(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType"/> property for the specified object.</para>
      </summary>
      <param name="obj">An object whose <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType"/> property&#39;s value is to be returned.</param>
      <returns>The value of the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType"/> property for the specified object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.GetIsAutoHideCenter(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.IsAutoHideCenter"/> attached property for a specified <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/>.</para>
      </summary>
      <param name="d">The element from which the property value is read.</param>
      <returns>The <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.IsAutoHideCenter"/> property value for the element.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.GetSizeToContent(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the current auto-size behavior of the target Layout Panel.</para>
      </summary>
      <param name="target">A DependencyObject that is a Layout Panel whose auto-hide behavior is to be obtained.</param>
      <returns>A SizeToContent enumerator value that specifies the current auto-hide behavior of this Layout Panel.</returns>
    </member>
    <member name="F:DevExpress.Xpf.Docking.AutoHideGroup.IsAutoHideCenterProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.IsAutoHideCenter"/> attached property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.SetAutoHideSize(System.Windows.DependencyObject,System.Windows.Size)">
      <summary>
        <para>Sets the auto-hide size for a specific object.</para>
      </summary>
      <param name="target">A DependencyObject whose auto-size is to be set.</param>
      <param name="value">A System.Windows.Size object that is the new object&#39;s auto-hide size.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.SetAutoHideType(System.Windows.DependencyObject,DevExpress.Xpf.Docking.AutoHideType)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType"/> property for the specified object.</para>
      </summary>
      <param name="obj">An object whose <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType"/> property is to be set.</param>
      <param name="value">A new value of the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType"/> property for the specified object.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.SetIsAutoHideCenter(System.Windows.DependencyObject,System.Boolean)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.IsAutoHideCenter"/> attached property to a specified <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/>.</para>
      </summary>
      <param name="d">The element to which the attached property is written.</param>
      <param name="value">The required <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.IsAutoHideCenter"/> value.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.SetSizeToContent(System.Windows.DependencyObject,System.Windows.SizeToContent)">
      <summary>
        <para>Sets the auto-size behavior of the target Layout Panel.</para>
      </summary>
      <param name="target">A DependencyObject that is the Layout Panel whose auto-hide behavior is to be set.</param>
      <param name="value">A SizeToContent enumerator value that is the new auto-hide behavior of this Layout Panel.</param>
    </member>
    <member name="F:DevExpress.Xpf.Docking.AutoHideGroup.SizeToContentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.SizeToContent"/> dependency property.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.AutoHideGroupCollection">
      <summary>
        <para>Represents a collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.AutoHideGroupCollection.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.AutoHideGroupCollection"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.AutoHideGroupCollection.AddRange(DevExpress.Xpf.Docking.AutoHideGroup[])">
      <summary>
        <para>Adds an array of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects to the current collection.</para>
      </summary>
      <param name="items">An array of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects to be added to the current collection.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.AutoHideGroupCollection.BottomItems">
      <summary>
        <para>Provides access to the collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects displayed along the bottom edge of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Data.CompositeCollection"/> object that is the collection of the <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.AutoHideGroupCollection.Dispose">
      <summary>
        <para>Disposes of all the items in the collection and releases all the allocated resources.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.AutoHideGroupCollection.Item(System.String)">
      <summary>
        <para>Provides access to items in the <see cref="T:DevExpress.Xpf.Docking.AutoHideGroupCollection"/> by name.</para>
      </summary>
      <param name="name">A string value that specifies the name of the item to be located.</param>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object with the specified name.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.AutoHideGroupCollection.LeftItems">
      <summary>
        <para>Provides access to the collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects displayed along the left edge of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Data.CompositeCollection"/> object that is the collection of the <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.AutoHideGroupCollection.RightItems">
      <summary>
        <para>Provides access to the collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects displayed along the right edge of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Data.CompositeCollection"/> object that is the collection of the <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.AutoHideGroupCollection.ToArray">
      <summary>
        <para>Returns the elements of the current collection as an array object.</para>
      </summary>
      <returns>An array of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.AutoHideGroupCollection.TopItems">
      <summary>
        <para>Provides access to the collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects displayed along the top edge of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Data.CompositeCollection"/> object that is the collection of the <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.AutoHideType">
      <summary>
        <para>Contains values that identify possible auto-hide positions for dock panels.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.AutoHideType.Bottom">
      <summary>
        <para>A dock panel is auto-hidden at the bottom edge of the container.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.AutoHideType.Default">
      <summary>
        <para>Identifies the default location where a dock panel is auto-hidden. The default auto-hide location is calculated automatically, based on the panel&#39;s location and state.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.AutoHideType.Left">
      <summary>
        <para>A dock panel is auto-hidden at the left edge of the container.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.AutoHideType.Right">
      <summary>
        <para>A dock panel is auto-hidden at the right edge of the container.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.AutoHideType.Top">
      <summary>
        <para>A dock panel is auto-hidden at the top edge of the container.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.AutoScrollOnOverflow">
      <summary>
        <para>Lists the values that specify how the tab headers are scrolled while selecting tabs when tab headers could not fit into the header panel.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.AutoScrollOnOverflow.AnyItem">
      <summary>
        <para>The tab headers are scrolled when an end-user selects any item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.AutoScrollOnOverflow.PartiallyVisibleItem">
      <summary>
        <para>The tab headers are scrolled when an end-user selects a partially visible item.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Xpf.Docking.Base">
      <summary>
        <para>Contains classes that implement the base functionality of the DXDocking for WPF suite.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.AutoHideExpandMode">
      <summary>
        <para>Contains values that specify how auto-hidden panels are expanded.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.Base.AutoHideExpandMode.Default">
      <summary>
        <para>The same as the <see cref="F:DevExpress.Xpf.Docking.Base.AutoHideExpandMode.MouseHover"/> option.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.Base.AutoHideExpandMode.MouseDown">
      <summary>
        <para>An auto-hidden panel is expanded when clicked.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.Base.AutoHideExpandMode.MouseHover">
      <summary>
        <para>An auto-hidden panel is expanded when hovered over by the mouse.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility">
      <summary>
        <para>Contains values that specify the visibility state of the Closed Panels bar, used to access closed panels.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility.Auto">
      <summary>
        <para>The Closed Panels bar is made visible if any closed panel exists. It&#39;s possible to hide and then restore the bar via a context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility.Default">
      <summary>
        <para>The same as the <see cref="F:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility.Manual"/> option.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility.Manual">
      <summary>
        <para>The Closed Panels bar is visible if an end-user enabled it via a context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility.Never">
      <summary>
        <para>The Closed Panels bar is always hidden and cannot be made visible via a context menu.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockingLocalizer">
      <summary>
        <para>A base class that provides necessary functionality for custom localizers of the Dock Windows library.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.DockingLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.DockingLocalizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.DockingLocalizer.CreateDefaultLocalizer">
      <summary>
        <para>Returns a localizer object, which provides resources based on the thread&#39;s language and regional settings (culture).</para>
      </summary>
      <returns>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object representing resources based on the thread&#39;s culture.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.DockingLocalizer.CreateResXLocalizer">
      <summary>
        <para>Returns a localizer object, which provides resources based on the thread&#39;s language and regional settings (culture).</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object, which provides resources based on the thread&#39;s culture.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.DockingLocalizer.GetString(DevExpress.Xpf.Docking.Base.DockingStringId)">
      <summary>
        <para>Returns a localized string for the given string identifier.</para>
      </summary>
      <param name="id">A DevExpress.Xpf.Docking.Base.DockingStringId enumeration value identifying the string to localize.</param>
      <returns>A <see cref="T:System.String"/> corresponding to the specified identifier.</returns>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockItemActivatedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivated"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.DockItemActivatedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.DockItemActivatedEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.</param>
      <param name="oldItem">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the previously activated item.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.DockItemActivatedEventArgs.Item">
      <summary>
        <para>Gets the activated dock item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the activated dock item.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.DockItemActivatedEventArgs.OldItem">
      <summary>
        <para>Gets the previously activated dock item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the previously activated dock item.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockItemActivatedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivated"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.</param>
      <param name="ea">A <see cref="T:DevExpress.Xpf.Docking.Base.DockItemActivatedEventArgs"/> object that represents data for the event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockItemCollapsedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemCollapsed"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.DockItemCollapsedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.DockItemCollapsedEventArgs"/> class with the specified item.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being processed.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockItemCollapsedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemCollapsed"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.DockItemCollapsedEventArgs"/> object that represents data for the event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDocking"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,System.Windows.Point,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.DockType,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being docked.</param>
      <param name="pt">A Point at which the item is being docked.</param>
      <param name="target">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to which the current item is being docked.</param>
      <param name="type">A <see cref="T:DevExpress.Xpf.Layout.Core.DockType"/> value that specifies how the item is docked to another item.</param>
      <param name="isHiding">A Boolean value that specifies whether the item is being set to the auto-hide state.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs.#ctor(System.Boolean,DevExpress.Xpf.Docking.BaseLayoutItem,System.Windows.Point,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.DockType,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="cancel">A Boolean value that specifies whether the current event must be canceled.</param>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being docked.</param>
      <param name="pt">A Point at which the item is being docked.</param>
      <param name="target">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to which the current item is being docked.</param>
      <param name="type">A <see cref="T:DevExpress.Xpf.Layout.Core.DockType"/> value that specifies how the item is docked to another item.</param>
      <param name="isHiding">A Boolean value that specifies whether the item is being set to the auto-hide state.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs.DockTarget">
      <summary>
        <para>Gets the item to which the current item is being docked.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to which the current item is being docked.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs.DockType">
      <summary>
        <para>Gets or sets how an item is being docked to another item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Layout.Core.DockType"/> value that specifies how an item is being docked to another item.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs.DragPoint">
      <summary>
        <para>Gets the point at which the item is being docked. The point is relative to the top left corner of the target item&#39;s root parent.</para>
      </summary>
      <value>A Point structure that specifies the point at which the item is being docked.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs.IsHiding">
      <summary>
        <para>Gets whether the item is being docked over a zone used to set the item to the auto-hide state.</para>
      </summary>
      <value>true if the item is being docked over a zone used to set the item to the auto-hide state; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockItemDockingEventHandler">
      <summary>
        <para>Represents a method that will handle the  <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDocking"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs"/> object that represents data for the event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockItemDraggingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDragging"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.DockItemDraggingEventArgs.#ctor(System.Windows.Point,DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.DockItemDraggingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="screenPoint">A Point at which the item is being dragged.</param>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being processed.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.DockItemDraggingEventArgs.ScreenPoint">
      <summary>
        <para>Gets or sets the current point at which the item is being dragged.</para>
      </summary>
      <value>A Point structure that specifies the point where the item is being dragged.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockItemDraggingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDragging"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.DockItemDraggingEventArgs"/> object that represents data for the event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockItemExpandedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemExpanded"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.DockItemExpandedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.DockItemExpandedEventArgs"/> class.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being processed.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockItemExpandedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemExpanded"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.DockItemExpandedEventArgs"/> object that represents data for the event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockOperationCompletedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.DockOperationCompletedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.DockOperation)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.DockOperationCompletedEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to which a dock operation was applied.</param>
      <param name="dockOperation">A <see cref="T:DevExpress.Xpf.Docking.DockOperation"/> enumerator value that specifies the current operation type.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.DockOperationCompletedEventArgs.DockOperation">
      <summary>
        <para>Gets the type of an operation that is processed within the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> event.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.DockOperation"/> enumerator value that specifies the type of an operation that is processed within the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> event.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockOperationCompletedEventHandler">
      <summary>
        <para>The method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> event.</para>
      </summary>
      <param name="sender">An Object that raised the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.DockOperationCompletedEventArgs"/> object that provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.DockOperation)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> that raised the event.</param>
      <param name="dockTarget">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to which an item is to be docked.</param>
      <param name="dockOperation">A <see cref="T:DevExpress.Xpf.Docking.DockOperation"/> enumerator value that specifies the current operation type.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.DockOperation)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> that raised the event.</param>
      <param name="dockOperation">A <see cref="T:DevExpress.Xpf.Docking.DockOperation"/> enumerator value that specifies the current operation type.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs.DockOperation">
      <summary>
        <para>Gets the type of an operation that is processed within the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting"/> event.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.DockOperation"/> enumerator value that specifies the docking operation type.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs.DockTarget">
      <summary>
        <para>Gets a Dock Item to which the current Dock Item is to be docked.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to which the current Dock Item is to be docked.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.DockOperationStartingEventHandler">
      <summary>
        <para>The method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting"/> event.</para>
      </summary>
      <param name="sender">An Object that raised the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting"/> event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs"/> object that provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting"/> event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.IsCustomizationChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.IsCustomizationChangedEventArgs.#ctor(System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.IsCustomizationChangedEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="newValue">A new value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomization"/> property.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.IsCustomizationChangedEventArgs.Value">
      <summary>
        <para>Gets the new value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomization"/> property.</para>
      </summary>
      <value>A Boolean value that specifies the new value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomization"/> property.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.IsCustomizationChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationChanged"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.IsCustomizationChangedEventArgs"/> object that represents data for the event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.ItemCancelEventArgs">
      <summary>
        <para>Provides data for the events that can be handled to prevent specific docking operations.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.ItemCancelEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.ItemCancelEventArgs"/> class with the specified item.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being processed. This value is assigned to the <see cref="P:DevExpress.Xpf.Docking.Base.ItemEventArgs.Item"/> property.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.ItemCancelEventArgs.#ctor(System.Boolean,DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.ItemCancelEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="cancel">A Boolean value used to initialize the <see cref="P:DevExpress.Xpf.Docking.Base.ItemCancelEventArgs.Cancel"/> property.</param>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being processed. This value is used to initialize the <see cref="P:DevExpress.Xpf.Docking.Base.ItemEventArgs.Item"/> property.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.ItemCancelEventArgs.Cancel">
      <summary>
        <para>Gets or sets whether the current operation must be canceled.</para>
      </summary>
      <value>true if the current operation must be canceled; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.ItemEventArgs">
      <summary>
        <para>Provides data for the events used to process specific docking operations on items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.ItemEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.ItemEventArgs"/> class with the specified item.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being processed. This value is used to initialize the <see cref="P:DevExpress.Xpf.Docking.Base.ItemEventArgs.Item"/> property.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.ItemEventArgs.Item">
      <summary>
        <para>Gets the currently processed item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being currently processed.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivated"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.</param>
      <param name="oldItem">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the previously activated item.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventArgs.Item">
      <summary>
        <para>Gets the activated layout item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the activated layout item.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventArgs.OldItem">
      <summary>
        <para>Gets the previously activated layout item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the previously activated layout item.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivated"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.</param>
      <param name="ea">A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventArgs"/> object that represents data for the event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemHiddenEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemHidden"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemHiddenEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemHiddenEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemHiddenEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemHidden"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemHiddenEventArgs"/> object that represents data for the event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemMoved"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.MoveType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the currently processed item.</param>
      <param name="target">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the item next to which the current item is inserted.</param>
      <param name="type">A MoveType value that specifies how the current item is moved.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventArgs.Target">
      <summary>
        <para>Gets the item next to which the current item (<see cref="P:DevExpress.Xpf.Docking.Base.ItemEventArgs.Item"/>) is inserted.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that identifies the item next to which the current item (<see cref="P:DevExpress.Xpf.Docking.Base.ItemEventArgs.Item"/>) is inserted.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventArgs.Type">
      <summary>
        <para>Gets how the current item is inserted next to the target item.</para>
      </summary>
      <value>A MoveType value that specifies how the current item is inserted next to the target item.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemMoved"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventArgs"/> object that represents data for the event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemRestoredEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemRestored"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemRestoredEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemRestoredEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemRestoredEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemRestored"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemRestoredEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangedEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.</param>
      <param name="selected">A Boolean value that specifies whether the item is selected.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangedEventArgs.Selected">
      <summary>
        <para>Gets whether the item is selected.</para>
      </summary>
      <value>true if the item is selected; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanged"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangedEventArgs"/> object that represents data for the event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanging"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangingEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.</param>
      <param name="selected">A Boolean value that specifies whether the item is selected.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangingEventArgs.Selected">
      <summary>
        <para>Gets whether the item is selected.</para>
      </summary>
      <value>true if the item is selected; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanging"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangingEventArgs"/> object that represents data for the event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSizeChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,System.Boolean,System.Windows.GridLength,System.Windows.GridLength)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.</param>
      <param name="isWidth">A Boolean value that specifies whether the item&#39;s width has been changed.</param>
      <param name="value">A new value of the item&#39;s width/height.</param>
      <param name="prevValue">The previous value of the item&#39;s width/height.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs.HeightChanged">
      <summary>
        <para>Gets whether the item&#39;s height has been changed.</para>
      </summary>
      <value>true if the item&#39;s height has been changed; false if the item&#39;s width has been changed.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs.PrevValue">
      <summary>
        <para>Gets the previous value of the item&#39;s width/height.</para>
      </summary>
      <value>A GridLength value that specifies the item&#39;s previous width/height.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs.Value">
      <summary>
        <para>Gets the current value of the item&#39;s width/height.</para>
      </summary>
      <value>A GridLength value that specifies the item&#39;s new width/height.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs.WidthChanged">
      <summary>
        <para>Gets whether the item&#39;s width has been changed.</para>
      </summary>
      <value>true if the item&#39;s width has been changed; false if the item&#39;s height has been changed.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSizeChanged"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs"/> object that represents data for the event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.ShowingMenuEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowingMenu"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.Base.ShowingMenuEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutElementMenu)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.Base.ShowingMenuEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="menu">A BaseLayoutElementMenu object that represents the menu to be displayed.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.Base.ShowingMenuEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowingMenu"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Docking.Base.ShowingMenuEventArgs"/> object that represents data for the event.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.BaseLayoutItem">
      <summary>
        <para>Represents the base class for dock panels and groups.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.BaseLayoutItem.Accept(DevExpress.Xpf.Layout.Core.IVisitor{DevExpress.Xpf.Docking.BaseLayoutItem})">
      <summary>
        <para>Invokes the Visit method of the specified visitor for each item that belongs to the current <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object.</para>
      </summary>
      <param name="visitor">An object implementing the DevExpress.Xpf.Layout.Core.IVisitor interface.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.BaseLayoutItem.Accept(DevExpress.Xpf.Layout.Core.VisitDelegate{DevExpress.Xpf.Docking.BaseLayoutItem})">
      <summary>
        <para>Invokes the specified delegate for each item that belongs to the current item.</para>
      </summary>
      <param name="visit">A DevExpress.Xpf.Layout.Core.VisitDelegate delegate that will be invoked for the group&#39;s items.</param>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualAppearanceObjectProperty">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualAppearanceProperty">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualCaption">
      <summary>
        <para>Gets the item&#39;s actual caption, taking into account the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionFormat"/>.
This is a dependency property.</para>
      </summary>
      <value>A string that specifies the item&#39;s actual caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualCaptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualCaption"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualCaptionWidth">
      <summary>
        <para>Gets the actual width of the item&#39;s caption.
This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the actual width of the item&#39;s caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualCaptionWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualCaptionWidth"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMargin">
      <summary>
        <para>Gets the actual margins (outer indents) for the current item.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> value that contains the actual outer indents of the layout item&#39;s borders.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMarginProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMargin"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMaxSize">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMaxSizeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMaxSize"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMinSize">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMinSizeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMinSize"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualPadding">
      <summary>
        <para>Gets the actual padding (inner indents) for the current item.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> value that specifies the actual padding for the current item.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualPaddingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualPadding"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualTabCaption">
      <summary>
        <para>Gets the actual text displayed in the tab (when the current item is represented as a tab page).</para>
      </summary>
      <value>A string that specifies the text displayed in a corresponding tab.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualTabCaptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualTabCaption"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowActivate">
      <summary>
        <para>Gets or sets whether the item can be activated.
This is a dependency property.</para>
      </summary>
      <value>true if the item can be activated; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowActivateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowActivate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowClose">
      <summary>
        <para>Gets or sets whether the current dock item can be closed.
This is a dependency property.</para>
      </summary>
      <value>true if the current dock item can be closed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowCloseProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowClose"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowContextMenu">
      <summary>
        <para>Gets or sets whether a context menu is enabled for the current layout item.
This is a dependency property.</para>
      </summary>
      <value>true if a context menu is enabled for the current layout item; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowContextMenuProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowContextMenu"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowDock">
      <summary>
        <para>Gets or sets whether the dock item can be docked to another item (panel or group).
This is a dependency property.</para>
      </summary>
      <value>true if the dock item can be docked to another item; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowDockProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowDock"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowDockToCurrentItem">
      <summary>
        <para>Gets or sets whether a panel can be merged as a tab into the current item. This is a dependency property.</para>
      </summary>
      <value>true, if a panel can be merged as a tab into the current item; otherwise, false. The default is true.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowDockToCurrentItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowDockToCurrentItem"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowDrag">
      <summary>
        <para>Gets or sets whether the layout item may be dragged.
This is a dependency property.</para>
      </summary>
      <value>true if the layout item may be dragged; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowDragProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowDrag"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowFloat">
      <summary>
        <para>Gets or sets whether the dock item can float.
This is a dependency property.</para>
      </summary>
      <value>true if the dock item can float; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowFloatProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowFloat"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowHide">
      <summary>
        <para>Gets or sets whether the item can be hidden (auto-hidden, for dock items).
This is a dependency property.</para>
      </summary>
      <value>true if the item can be hidden (auto-hidden, for dock items); otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowHideProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowHide"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMaximize">
      <summary>
        <para>Gets or sets whether the current item can be maximized. This property is supported for floating <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> and <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> objects.</para>
      </summary>
      <value>true, if the current item can be maximized; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMaximizeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMaximize"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMinimize">
      <summary>
        <para>Gets or sets whether the current item can be minimized. This property is supported for floating <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> objects.</para>
      </summary>
      <value>true if the current item can be minimized; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMinimizeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMinimize"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMove">
      <summary>
        <para>Gets or sets whether the item is allowed to be moved.
This is a dependency property.</para>
      </summary>
      <value>true if the item is allowed to be moved; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMoveProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMove"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowRename">
      <summary>
        <para>Allows you to prevent an item&#39;s caption from being renamed when the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowLayoutItemRename"/> option is enabled.
This is a dependency property.</para>
      </summary>
      <value>true if the item&#39;s caption can be renamed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowRenameProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowRename"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowRestore">
      <summary>
        <para>Gets or sets whether the item can be restored from the hidden state.
This is a dependency property.</para>
      </summary>
      <value>true if the item can be restored from the hidden state; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowRestoreProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowRestore"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowSelection">
      <summary>
        <para>Gets or sets whether the current layout item can be selected in Customization Mode.
This is a dependency property.</para>
      </summary>
      <value>true if the current layout item can be selected in Customization Mode; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowSelectionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowSelection"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowSizing">
      <summary>
        <para>Gets or sets if the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> resizing at runtime is enabled.</para>
      </summary>
      <value>true if the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> resizing at runtime is enabled; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowSizingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowSizing"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Appearance">
      <summary>
        <para>Gets or sets the object that provides appearance settings for the item&#39;s captions.
This is a dependency property.</para>
      </summary>
      <value>A DevExpress.Xpf.Docking.Appearance object that specifies the appearance settings for the item&#39;s caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AppearanceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Appearance"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Background">
      <summary>
        <para>Gets or sets the layout item&#39;s background color.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.Brush"/> object, set as a <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/>&#39;s background color.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.BaseLayoutItem.BeginInit">
      <summary>
        <para>Starts the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/>&#39;s initialization. Initialization occurs at runtime.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.BindableName">
      <summary>
        <para>Gets or sets a value passed to the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> Name property. This is a dependency property.</para>
      </summary>
      <value>A String value that specifies the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> Name value.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.BindableNameProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.BindableName"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption">
      <summary>
        <para>Gets or sets the layout item&#39;s caption.</para>
      </summary>
      <value>A string value that specifies the layout item&#39;s caption.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionAlignMode">
      <summary>
        <para>Gets or sets the alignment settings of a control(s) displayed by a <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> object(s).
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.CaptionAlignMode"/> value that specifies the caption alignment settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionAlignModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionAlignMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionFormat">
      <summary>
        <para>Gets or sets the format string used to format the layout item&#39;s caption.
This is a dependency property.</para>
      </summary>
      <value>A string that specifies the caption format.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionFormatProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionFormat"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionHorizontalAlignment">
      <summary>
        <para>Gets or sets the horizontal alignment of the item&#39;s caption.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.HorizontalAlignment"/> value that specifies the caption&#39;s horizontal alignment.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionHorizontalAlignmentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionHorizontalAlignment"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImage">
      <summary>
        <para>Gets or sets the image displayed within the item&#39;s caption.
This is a dependency property.</para>
      </summary>
      <value>An ImageSource object that specifies the image displayed within the item&#39;s caption.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImageLocation">
      <summary>
        <para>Gets or sets the relative position of an image within the item&#39;s caption. 
This is a dependency property.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Xpf.Docking.ImageLocation"/> value that specifies the relative position of an image within the item&#39;s caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImageLocationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImageLocation"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImageProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImage"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImageStyle">
      <summary>
        <para>Gets or sets the caption image style. This is a dependency property.</para>
      </summary>
      <value>The style of the caption image.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImageStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImageStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionLocation">
      <summary>
        <para>Gets or sets the position of the item&#39;s caption.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.CaptionLocation"/> value that specifies the position where the caption is displayed.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionLocationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionLocation"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionTemplate">
      <summary>
        <para>Gets or sets the template used to visualize the current item&#39;s <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption"/>.</para>
      </summary>
      <value>A DataTemplate object that visualizes the current item&#39;s <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template applied to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionTemplate"/> property. This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionVerticalAlignment">
      <summary>
        <para>Gets or sets the vertical alignment of the item&#39;s caption.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.VerticalAlignment"/> value that specifies the caption&#39;s vertical alignment.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionVerticalAlignmentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionVerticalAlignment"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionWidth">
      <summary>
        <para>Gets or sets the width of the item&#39;s caption, which is in effect when the CaptionAlignMode property is set to Custom.
This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the width of the item&#39;s caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionWidth"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CloseCommand">
      <summary>
        <para>Gets or sets a command executed when the current item&#39;s close button (&#39;x&#39;) is clicked. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Input.ICommand"/> object executed when the current item&#39;s close button (&#39;x&#39;) is clicked.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CloseCommandParameter">
      <summary>
        <para>Gets or sets the parameter to pass to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CloseCommand"/>. This is a dependency property.</para>
      </summary>
      <value>The Object specifying the parameter to pass to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CloseCommand"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CloseCommandParameterProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CloseCommandParameter"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CloseCommandProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CloseCommand"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Closed">
      <summary>
        <para>Gets or sets whether a Dock Item is closed.
This is a dependency property.</para>
      </summary>
      <value>true if a dock item is closed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ClosedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Closed"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ClosingBehavior">
      <summary>
        <para>Gets or sets the way the current item acts if closed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.ClosingBehavior"/> enumeration value.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ClosingBehaviorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ClosingBehavior"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ContextMenuCustomizations">
      <summary>
        <para>Allows you to customize the layout item&#39;s context menu by adding new menu items or removing existing items.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Xpf.Bars.IControllerAction"/> objects.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ContextMenuCustomizationsTemplate">
      <summary>
        <para>Gets or sets a template used to customize menu items in all the affected dock layout items. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> used to customize menu items.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ContextMenuCustomizationsTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ContextMenuCustomizationsTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContent">
      <summary>
        <para>Gets or sets the content of the control box region.
This is a dependency property.</para>
      </summary>
      <value>An object that represents the content of the control box region.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContent"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContentTemplate">
      <summary>
        <para>Gets or sets the template that defines how the object assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContent"/> property is represented onscreen. 
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the control box region&#39;s presentation.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContentTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContentTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CustomizationCaption">
      <summary>
        <para>Gets or sets the caption that represents the current item within the Customization Window.
This is a dependency property.</para>
      </summary>
      <value>A string that specifies the caption that represents the current item within the Customization Window</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CustomizationCaptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CustomizationCaption"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Description">
      <summary>
        <para>Gets or sets the item&#39;s description displayed within the header of the Document Selector window.
This is a dependency property.</para>
      </summary>
      <value>A string that represents the item&#39;s description.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.DescriptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Description"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.DesiredCaptionWidth">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.DesiredCaptionWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.DesiredCaptionWidth"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.DockingViewStyle">
      <summary>
        <para>Gets a value that specifies how the current layout item displays its borders.</para>
      </summary>
      <value>A DockingViewStyle enumeration value.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.DockingViewStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.DockingViewStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.BaseLayoutItem.EndInit">
      <summary>
        <para>Ends the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/>&#39;s initialization.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.FloatOnDoubleClick">
      <summary>
        <para>Gets or sets whether an end-user can double-click the item&#39;s caption to float it.
This is a dependency property.</para>
      </summary>
      <value>true if an end-user can double-click the item&#39;s caption to float it; otherwise false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.FloatOnDoubleClickProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.FloatOnDoubleClick"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.FloatSize">
      <summary>
        <para>Gets or sets the size of the item when it is floating.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure representing the item&#39;s size, in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.FloatSizeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.FloatSize"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.FooterDescription">
      <summary>
        <para>Gets or sets the item&#39;s description displayed within the footer of the Document Selector window.
This is a dependency property.</para>
      </summary>
      <value>A string that represents the item&#39;s description.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.FooterDescriptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.FooterDescription"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasCaption">
      <summary>
        <para>Gets whether a non-empty caption is assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption"/> property.
This is a dependency property.</para>
      </summary>
      <value>true if a non-empty caption is assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption"/> property; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.HasCaptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasCaption"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasCaptionTemplate">
      <summary>
        <para>Gets whether a DataTemplate is used to render the caption. This is a dependency property.</para>
      </summary>
      <value>true, if a DataTemplate is used to render the caption; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.HasCaptionTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasCaptionTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasDesiredCaptionWidth">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.HasDesiredCaptionWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasDesiredCaptionWidth"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasImage">
      <summary>
        <para>Gets whether a caption image is assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImage"/> property.
This is a dependency property.</para>
      </summary>
      <value>true if a caption image is assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImage"/> property; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.HasImageProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasImage"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasTabCaption">
      <summary>
        <para>Gets if the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object has a non-empty <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaption"/> property value.</para>
      </summary>
      <value>true if the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object has a non-empty <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaption"/> property value; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.HasTabCaptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasTabCaption"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.HeaderBarContainerControlAllowDrop">
      <summary>
        <para>Gets or sets whether a bar can be dropped onto the bar container displayed in the current panel.
This is a dependency property.</para>
      </summary>
      <value>A Nullable Boolean value that specifies whether a bar can be dropped onto the bar container displayed in the current panel.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.HeaderBarContainerControlAllowDropProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.HeaderBarContainerControlAllowDrop"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.HeaderBarContainerControlName">
      <summary>
        <para>Gets or sets the name of the bar container, used to embed bars from the DXBars library.
This is a dependency property.</para>
      </summary>
      <value>A string that specifies the name of the bar container.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.HeaderBarContainerControlNameProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.HeaderBarContainerControlName"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ImageToTextDistance">
      <summary>
        <para>Gets or sets the distance between the item&#39;s caption and image. 
This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the distance between the item&#39;s caption and image, in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ImageToTextDistanceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ImageToTextDistance"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsActive">
      <summary>
        <para>Gets or sets whether the item is active.
This is a dependency property.</para>
      </summary>
      <value>A Boolean value that specifies whether the item is active.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsActiveProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsActive"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsAutoHidden">
      <summary>
        <para>Gets whether the item is auto-hidden.</para>
      </summary>
      <value>A Boolean value that specifies whether the item is auto-hidden.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsCaptionImageVisible">
      <summary>
        <para>Gets whether the caption image (<see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImage"/>) is visible.
This is a dependency property.</para>
      </summary>
      <value>true if the caption image is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsCaptionImageVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsCaptionImageVisible"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsCaptionVisible">
      <summary>
        <para>Gets whether the item&#39;s caption is visible.
This is a dependency property.</para>
      </summary>
      <value>true if the item&#39;s caption is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsCaptionVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsCaptionVisible"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsCloseButtonVisible">
      <summary>
        <para>Gets whether the Close (&#39;x&#39;) button is visible for the current item.
This is a dependency property.</para>
      </summary>
      <value>true if the Close (&#39;x&#39;) button is visible for the current item; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsCloseButtonVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsCloseButtonVisible"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsClosed">
      <summary>
        <para>Gets whether the item is closed.
This is a dependency property.</para>
      </summary>
      <value>A Boolean value that specifies whether the item is closed.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsClosedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsClosed"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsControlBoxVisible">
      <summary>
        <para>Gets whether the control box region is visible.
This is a dependency property.</para>
      </summary>
      <value>true if the control box region is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsControlBoxVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsControlBoxVisible"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsControlItemsHost">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
This is a dependency property.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsControlItemsHostProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsControlItemsHost"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsDropDownButtonVisibleProperty">
      <summary>
        <para>Identifies the IsDropDownButtonVisible dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsFloating">
      <summary>
        <para>Gets whether the item floats.</para>
      </summary>
      <value>A Boolean value that specifies whether the item floats.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsFloatingRootItem">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>A Boolean value.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsFloatingRootItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsFloatingRootItem"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsHidden">
      <summary>
        <para>Gets whether the current Layout Item is hidden.
This is a dependency property.</para>
      </summary>
      <value>true if the current layout item is hidden; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsHiddenProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsHidden"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsInitializing">
      <summary>
        <para>Gets whether the item is being initialized.</para>
      </summary>
      <value>A Boolean value that specifies whether the item is being initialized.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsMaximizeButtonVisibleProperty">
      <summary>
        <para>Identifies the IsMaximizeButtonVisible dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsMinimizeButtonVisibleProperty">
      <summary>
        <para>Identifies the IsMinimizeButtonVisible dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsPinButtonVisibleProperty">
      <summary>
        <para>Identifies the IsPinButtonVisible dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsRestoreButtonVisibleProperty">
      <summary>
        <para>Identifies the IsRestoreButtonVisible dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsScrollNextButtonVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.IsScrollNextButtonVisible"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsScrollPrevButtonVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.IsScrollPrevButtonVisible"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsSelected">
      <summary>
        <para>Gets whether the item is selected in Customization Mode.
This is a dependency property.</para>
      </summary>
      <value>true if the item is selected in Customization Mode; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsSelectedItem">
      <summary>
        <para>Gets or sets whether the current item is selected within any LayoutGroup. This is a dependency property.</para>
      </summary>
      <value>true, if the current item is selected within any LayoutGroup; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsSelectedItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsSelectedItem"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsSelectedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsSelected"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsTabPage">
      <summary>
        <para>Gets whether the current item is represented as a tab page.
This is a dependency property.</para>
      </summary>
      <value>true if the current item is represented as a tab page; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsTabPageProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsTabPage"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ItemHeight">
      <summary>
        <para>Gets or sets the height for the specified <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.GridLength"/> object, which is the height of the specified <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ItemHeightProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ItemHeight"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ItemType">
      <summary>
        <para>Gets the current item&#39;s type.</para>
      </summary>
      <value>A LayoutItemType enumeration value that specifies the item&#39;s type.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ItemWidth">
      <summary>
        <para>Gets or sets the width for the specified <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.GridLength"/> object, that is the width of the specified <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ItemWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ItemWidth"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.LayoutSize">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.LayoutSizeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.LayoutSize"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.LogicalParent">
      <summary>
        <para>Gets the logical parent element of this element.</para>
      </summary>
      <value>This element&#39;s logical parent.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Margin">
      <summary>
        <para>Gets or sets the outer indents of the item&#39;s borders. 
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> value that contains the outer indents of the layout item&#39;s borders.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.MarginProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Margin"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.BaseLayoutItem.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Padding">
      <summary>
        <para>Gets or sets the amount of space between the item&#39;s borders and its contents.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> value that specifies the amount of space between the item&#39;s borders and its contents.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.PaddingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Padding"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Parent">
      <summary>
        <para>Gets or sets the item&#39;s parent group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> object which is the item&#39;s parent.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ParentCollectionName">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>A string value.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ParentName">
      <summary>
        <para>Gets or sets the name of the item&#39;s parent. This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>A string value that specifies the name of the item&#39;s parent.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.SerializableDockSituation">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.SerializationInfo">
      <summary>
        <para>For internal use only.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCaption">
      <summary>
        <para>Gets or sets whether the item&#39;s caption is visible.
This is a dependency property.</para>
      </summary>
      <value>true if the item&#39;s caption is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCaptionImage">
      <summary>
        <para>Gets or sets whether the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImage"/> is visible.
This is a dependency property.</para>
      </summary>
      <value>true if the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImage"/> is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCaptionImageProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCaptionImage"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCaptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCaption"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCloseButton">
      <summary>
        <para>Allows you to hide the Close (&#39;x&#39;) button for the current item. This property is only supported for <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/>, <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> and <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> objects.
This is a dependency property.</para>
      </summary>
      <value>true, if the Close (&#39;x&#39;) button is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCloseButtonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCloseButton"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowControlBox">
      <summary>
        <para>Gets or sets whether the object assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContent"/> property is visible.
This is a dependency property.</para>
      </summary>
      <value>true if the object assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContent"/> property is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowControlBoxProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowControlBox"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowDropDownButtonProperty">
      <summary>
        <para>Identifies the ShowDropDownButton dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowMaximizeButtonProperty">
      <summary>
        <para>Identifies the ShowMaximizeButton dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowMinimizeButtonProperty">
      <summary>
        <para>Identifies the ShowMinimizeButton dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowPinButtonProperty">
      <summary>
        <para>Identifies the ShowPinButton dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowRestoreButtonProperty">
      <summary>
        <para>Identifies the ShowRestoreButton dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowScrollNextButtonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ShowScrollNextButton"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowScrollPrevButtonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ShowScrollPrevButton"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowTabCaptionImage">
      <summary>
        <para>Get or sets whether to show an image within a tab caption of the current layout item. This is a dependency property.</para>
      </summary>
      <value>true to display an image within a tab header; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowTabCaptionImageProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowTabCaptionImage"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaption">
      <summary>
        <para>Gets or sets the layout item&#39;s tab caption.</para>
      </summary>
      <value>A string value that specifies the layout item&#39;s tab caption.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionFormat">
      <summary>
        <para>Gets or sets the format string used to format the layout item&#39;s tab caption.
This is a dependency property.</para>
      </summary>
      <value>A string that specifies the tab caption format.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionFormatProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionFormat"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionHorizontalAlignment">
      <summary>
        <para>Gets or sets the horizontal alignment of the item&#39;s tab caption. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.HorizontalAlignment"/> value that specifies the tab caption&#39;s horizontal alignment.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionHorizontalAlignmentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionHorizontalAlignment"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionImage">
      <summary>
        <para>Gets or sets an image displayed within the layout item caption in a tabbed layout. This is a dependency property.</para>
      </summary>
      <value>An ImageSource object that specifies the image displayed within the layout item caption in a tabbed layout.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionImageProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionImage"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaption"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionTemplate">
      <summary>
        <para>Gets or sets a template that defines the tab caption presentation. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> object that presents the layout item&#39;s tab caption in a custom manner.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a tab caption template based on custom logic. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionVerticalAlignment">
      <summary>
        <para>Gets or sets the vertical alignment of the item&#39;s tab caption. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.VerticalAlignment"/> value that specifies the tab caption&#39;s vertical alignment.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionVerticalAlignmentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionVerticalAlignment"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionWidth">
      <summary>
        <para>Gets or sets the width of the corresponding tab. This property is in effect when the current object represents items as tabs or when it represents one of the tabs.
This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the tab&#39;s width.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionWidth"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TextTrimming">
      <summary>
        <para>Gets or sets text trimming options applied to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption"/>.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.TextTrimming"/> object.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TextTrimmingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TextTrimming"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TextWrapping">
      <summary>
        <para>Gets or sets text wrapping options applied to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption"/>.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.TextWrapping"/> object.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TextWrappingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TextWrapping"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ToolTip">
      <summary>
        <para>Gets or sets a tool tip, displayed at runtime when hovering a <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/>&#39;s caption or tab caption. This is a dependency property.</para>
      </summary>
      <value>A System.Object specifying a tool tip, displayed at runtime when hovering a <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/>&#39;s caption or tab caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ToolTipProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ToolTip"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TypeName">
      <summary>
        <para>Gets the name of the item&#39;s type.</para>
      </summary>
      <value>A string that specifies the name of the item&#39;s type.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.BaseLayoutItemCollection">
      <summary>
        <para>Represents a collection of <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.BaseLayoutItemCollection.#ctor(DevExpress.Xpf.Docking.LayoutGroup)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItemCollection"/> class with the specified owner.</para>
      </summary>
      <param name="owner">A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> object which specifies the owner for the created collection.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.BaseLayoutItemCollection.BeginUpdate">
      <summary>
        <para>For internal use only.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.BaseLayoutItemCollection.EndUpdate">
      <summary>
        <para>For internal use only.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.BaseLayoutItemCollection.FindItem(System.Collections.Generic.IList{DevExpress.Xpf.Docking.BaseLayoutItem},System.String)">
      <summary>
        <para>Locates an item with the specified name in the specified list.</para>
      </summary>
      <param name="items">A list of items to be searched through.</param>
      <param name="name">A string that specifies the item&#39;s name.</param>
      <returns>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object with the specified name. null if no item with the specified name is found.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItemCollection.IsLockUpdate">
      <summary>
        <para>For internal use only.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.BaseLayoutItemCollection.Item(System.String)">
      <summary>
        <para>Provides access to items by names.</para>
      </summary>
      <param name="name">A string that specifies the item&#39;s name.</param>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendant which represents an item with the specified name. null if the item with this name is not found.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.CaptionAlignMode">
      <summary>
        <para>Enumerates the options that specify how the controls and their captions are aligned within the layout items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.CaptionAlignMode.AlignInGroup">
      <summary>
        <para>Controls displayed by means of <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> objects are auto-aligned across a layout group and its nested groups that have the CaptionAlignMode property set to Default.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.CaptionAlignMode.AutoSize">
      <summary>
        <para>The auto-size feature is enabled. The captions of <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/>s are automatically resized to the minimum width that allows to display text without wrapping.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.CaptionAlignMode.Custom">
      <summary>
        <para>Enables custom size mode, in which the size of a <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/>&#39;s caption must be specified manually via the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionWidth"/> property</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.CaptionAlignMode.Default">
      <summary>
        <para>For nested layout items, this setting means that the alignment is controlled by the parent&#39;s CaptionAlignMode property.
For a root group, this setting means that controls of child <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/>s are auto-aligned across the root group, and nested groups that have the CaptionAlignMode property set to Default.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.CaptionLocation">
      <summary>
        <para>Contains values that specify the position of an item&#39;s caption.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.CaptionLocation.Bottom">
      <summary>
        <para>An item&#39;s caption is displayed at the bottom.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.CaptionLocation.Default">
      <summary>
        <para>An item&#39;s caption is displayed at the default position, which is different for different item types.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.CaptionLocation.Left">
      <summary>
        <para>An item&#39;s caption is displayed on the left.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.CaptionLocation.Right">
      <summary>
        <para>An item&#39;s caption is displayed on the right.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.CaptionLocation.Top">
      <summary>
        <para>An item&#39;s caption is displayed at the top.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.ClosedPanelCollection">
      <summary>
        <para>Represents a collection of closed (hidden) panels.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.ClosedPanelCollection.#ctor(DevExpress.Xpf.Docking.DockLayoutManager)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.ClosedPanelCollection"/> class with the specified owner.</para>
      </summary>
      <param name="owner">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object which specifies the owner for the created collection.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.ClosedPanelCollection.AddRange(DevExpress.Xpf.Docking.LayoutPanel[])">
      <summary>
        <para>Adds an array of LayoutPanel objects to the current collection.</para>
      </summary>
      <param name="panels">An array of LayoutPanels to be added to the collection.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.ClosedPanelCollection.Dispose">
      <summary>
        <para>Disposes of all the items in the collection and releases all the allocated resources.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.ClosedPanelCollection.EndUpdate">
      <summary>
        <para>Unlocks the ClosedPanelCollection object after a call to the BeginUpdate method and causes an immediate visual update.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.ClosedPanelCollection.Item(System.String)">
      <summary>
        <para>Provides access to panels in the collection by name.</para>
      </summary>
      <param name="name">A string that specifies the name of the panel to be returned. 
This value matches the value of the panel&#39;s Name property.</param>
      <value>A <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> object with the specified name.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.ClosedPanelCollection.ToArray">
      <summary>
        <para>Returns the elements of the current collection as an array object.</para>
      </summary>
      <returns>An array of <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> objects.</returns>
    </member>
    <member name="T:DevExpress.Xpf.Docking.ClosePageButtonShowMode">
      <summary>
        <para>Enumerates values that specify whether Close buttons are displayed in individual tab pages, the tab control&#39;s header, or in both.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.Default">
      <summary>
        <para>The same as the <see cref="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.InTabControlHeader"/> option.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.InActiveTabPageAndTabControlHeader">
      <summary>
        <para>Close buttons are displayed in the tab container&#39;s header, and within the active page.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.InActiveTabPageHeader">
      <summary>
        <para>A Close button is displayed in the active page. The Close button in the container&#39;s header is hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.InAllTabPageHeaders">
      <summary>
        <para>Close buttons are displayed in all pages. The Close button in the container&#39;s header is hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.InAllTabPagesAndTabControlHeader">
      <summary>
        <para>Close buttons are displayed in all pages and in the container&#39;s header.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.InTabControlHeader">
      <summary>
        <para>The Close button is displayed in the tab container&#39;s header</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.NoWhere">
      <summary>
        <para>Close buttons are not displayed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.ClosingBehavior">
      <summary>
        <para>Contains values that specify how panels are closed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ClosingBehavior.Default">
      <summary>
        <para>When applied to a <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> via the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosingBehavior"/> property, acts as the <see cref="F:DevExpress.Xpf.Docking.ClosingBehavior.HideToClosedPanelsCollection"/> option.When applied to a <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> via the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ClosingBehavior"/> property, gets a <see cref="T:DevExpress.Xpf.Docking.ClosingBehavior"/> enumeration value of its parent <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> .</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ClosingBehavior.HideToClosedPanelsCollection">
      <summary>
        <para>When a panel is closed, it&#39;s hidden and moved to the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanels"/> collection. The panel can be accessed via the Closed Panels bar (see <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanelsBarVisibility"/>).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ClosingBehavior.ImmediatelyRemove">
      <summary>
        <para>When a panel is closed, it&#39;s hidden. No reference to the closed panel is kept.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.ContentItem">
      <summary>
        <para>An abstract class for objects capable of displaying content.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.ContentItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.ContentItem"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.ContentItem.ActivateOnFocusing">
      <summary>
        <para>Gets or sets whether the content item is activated on getting logical or keyboard focus. This is a dependency property.</para>
      </summary>
      <value>Any of the <see cref="T:DevExpress.Xpf.Docking.ActivateOnFocusing"/> enumeration values.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ContentItem.ActivateOnFocusingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.ContentItem.ActivateOnFocusing"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.ContentItem.Content">
      <summary>
        <para>Gets or sets the item&#39;s content. This is a dependency property.</para>
      </summary>
      <value>An object that specifies the item&#39;s content.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ContentItem.ContentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.ContentItem.ContentTemplate">
      <summary>
        <para>Gets or sets the template used to display the item&#39;s <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/>. This is a dependency property.</para>
      </summary>
      <value>The corresponding data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ContentItem.ContentTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.ContentItem.ContentTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.ContentItem.ContentTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses the item&#39;s <see cref="P:DevExpress.Xpf.Docking.ContentItem.ContentTemplate"/> based on custom logic.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ContentItem.ContentTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.ContentItem.ContentTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.ContentItem.FocusContentOnActivating">
      <summary>
        <para>Gets or sets whether the <see cref="T:DevExpress.Xpf.Docking.ContentItem"/>&#39;s content should be focused on <see cref="T:DevExpress.Xpf.Docking.ContentItem"/> activation.</para>
      </summary>
      <value>true, if the <see cref="T:DevExpress.Xpf.Docking.ContentItem"/>&#39;s content should be focused on <see cref="T:DevExpress.Xpf.Docking.ContentItem"/> activation; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ContentItem.FocusContentOnActivatingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.ContentItem.FocusContentOnActivating"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.ContentItem.IsDataBound">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ContentItem.IsDataBoundProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.ContentItem.IsDataBound"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.DefaultMenuItemNames">
      <summary>
        <para>Contains names of the context menu items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.AutoHide">
      <summary>
        <para>Returns &quot;MenuItemAutoHide&quot;. Corresponds to the menu item used to enable the auto-hide functionality for the item/panel.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.BeginCustomization">
      <summary>
        <para>Returns &quot;MenuItemBeginCustomization&quot;. Corresponds to the menu item used to enter the customization mode.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.CaptionImageLocation">
      <summary>
        <para>Returns &quot;MenuItemCaptionImageLocation&quot;. Corresponds to the menu item used to set whether caption images of the group&#39;s items are shown before or after the text.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.CaptionImageLocationAfterText">
      <summary>
        <para>Returns &quot;MenuItemAfterText&quot;. Corresponds to the sub menu item used to set caption images of the group items to be displayed after the text.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.CaptionImageLocationBeforeText">
      <summary>
        <para>Returns &quot;MenuItemBeforeText&quot;. Corresponds to the sub menu item used to set caption images of the group&#39;s items to be displayed before the text.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.CaptionLocation">
      <summary>
        <para>Returns &quot;MenuItemCaptionLocation&quot;. Corresponds to the menu item used to set the location of the group&#39;s caption and items.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.CaptionLocationBottom">
      <summary>
        <para>Returns &quot;MenuItemBottom&quot;. Corresponds to the sub menu item used to set the group&#39;s caption and items to be displayed at the bottom.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.CaptionLocationLeft">
      <summary>
        <para>Returns &quot;MenuItemLeft&quot;. Corresponds to the sub menu item used to set the group&#39;s caption and items to be displayed to the left.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.CaptionLocationRight">
      <summary>
        <para>Returns &quot;MenuItemRight&quot;. Corresponds to the sub menu item used to set the group&#39;s caption and items to be displayed to the right.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.CaptionLocationTop">
      <summary>
        <para>Returns &quot;MenuItemTop&quot;. Corresponds to the sub menu item used to set the group&#39;s caption and items to be displayed at the top.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.Close">
      <summary>
        <para>Returns &quot;MenuItemClose&quot;. Corresponds to the menu item used to hide the tabbed panel.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.CloseAllButThis">
      <summary>
        <para>Returns &quot;MenuItemCloseAllButThis&quot;. Corresponds to the menu item used to close all tabbed panels except the selected one.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.ClosedPanels">
      <summary>
        <para>Returns &quot;MenuItemClosedPanels&quot;. Corresponds to the menu item used to toggle the visibility of the bar that displays closed panels.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.ClosedPanelsSeparator">
      <summary>
        <para>Returns &quot;ClosedPanelsSeparator&quot;. Corresponds to the separator before the DocumentPanel&#39;s Closed Panels context menu section.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.CollapseGroup">
      <summary>
        <para>Returns &quot;MenuItemCollapseGroup&quot;. Corresponds to the menu item used to collapse the group.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.CustomizationOperationsSeparator">
      <summary>
        <para>Returns &quot;CustomizationOperationsSeparator&quot;. Corresponds to the separator before the DocumentPanel&#39;s Customization Operations context menu section.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.Dockable">
      <summary>
        <para>Returns &quot;MenuItemDockable&quot;. Corresponds to the menu item used to dock a floating panel.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.DocumentHostOperationsSeparator">
      <summary>
        <para>Returns &quot;DocumentHostOperationsSeparator&quot;. Corresponds to the separator before the DocumentPanel&#39;s Document Host Operations context menu section.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.EndCustomization">
      <summary>
        <para>Returns &quot;MenuItemEndCustomization&quot;. Corresponds to the menu item used to exit the customization mode.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.ExpandGroup">
      <summary>
        <para>Returns &quot;MenuItemExpandGroup&quot;. Corresponds to the menu item used to expand the group.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.Floating">
      <summary>
        <para>Returns &quot;MenuItemFloating&quot;. Corresponds to the menu item used to make a docked panel floating.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.GroupBorderStyle">
      <summary>
        <para>Returns &quot;MenuItemStyle&quot;. Corresponds to the menu item that allows users to select the group border style.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.GroupBorderStyleGroup">
      <summary>
        <para>Returns &quot;MenuItemStyleGroup&quot;. Corresponds to the sub menu item that allows users to select the Group border style (the container is displayed with borders and caption).</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.GroupBorderStyleGroupBox">
      <summary>
        <para>Returns &quot;MenuItemStyleGroupBox&quot;. Corresponds to the sub menu item that allows users to select the GroupBox border style (the container is displayed with borders and title bar).</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.GroupBorderStyleNoBorder">
      <summary>
        <para>Returns &quot;MenuItemStyleNoBorder&quot;. Corresponds to the sub menu item that allows users to select the No Border border style (the container has no borders).</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.GroupBorderStyleTabbed">
      <summary>
        <para>Returns &quot;MenuItemStyleTabbed&quot;. Corresponds to the sub menu item that allows users to select the Tabbed border style (child items are represented as tabs).</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.GroupItems">
      <summary>
        <para>Returns &quot;MenuItemGroupItems&quot;. Corresponds to the menu item used to group the items selected in the customization window.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.GroupOrientation">
      <summary>
        <para>Returns &quot;MenuItemGroupOrientation&quot;. Corresponds to the menu item used to set whether the group orientation is horizontal or vertical.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.GroupOrientationHorizontal">
      <summary>
        <para>Returns &quot;MenuItemHorizontal&quot;. Corresponds to the sub menu item used to set the group orientation to horizontal.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.GroupOrientationVertical">
      <summary>
        <para>Returns &quot;MenuItemVertical&quot;. Corresponds to the sub menu item used to set the group orientation to vertical.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.Hide">
      <summary>
        <para>Returns &quot;MenuItemHide&quot;. Corresponds to the menu item used to hide the panel.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.HideCustomizationWindow">
      <summary>
        <para>Returns &quot;MenuItemHideCustomizationWindow&quot;. Corresponds to the menu item used to hide the customization window.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.HideItem">
      <summary>
        <para>Returns &quot;MenuItemHideItem&quot;. Corresponds to the menu item used to hide the item/group.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.MoveToNextTabGroup">
      <summary>
        <para>Returns &quot;MenuItemMoveToNextTabGroup&quot;. Corresponds to the menu item used to move the tab to the next group.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.MoveToPreviousTabGroup">
      <summary>
        <para>Returns &quot;MenuItemMoveToPreviousTabGroup&quot;. Corresponds to the menu item used to move the tab to the previous group.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.NewHorizontalTabbedGroup">
      <summary>
        <para>Returns &quot;MenuItemNewHorizontalTabbedGroup&quot;. Corresponds to the menu item used to create a new horizontally arranged group and move the tab to this group.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.NewVerticalTabbedGroup">
      <summary>
        <para>Returns &quot;MenuItemNewVerticalTabbedGroup&quot;. Corresponds to the menu item used to create a new vertically arranged group and move the tab to this group.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.PinTab">
      <summary>
        <para>Returns &quot;MenuItemPinTab&quot;. Corresponds to the menu item used to pin the tabbed panel.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.Rename">
      <summary>
        <para>Returns &quot;MenuItemRename&quot;. Corresponds to the menu item used to rename the item/group.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.RestoreItem">
      <summary>
        <para>Returns &quot;MenuItemRestoreItem&quot;. Corresponds to the menu item used to restore the hidden item/group.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.ShowCaption">
      <summary>
        <para>Returns &quot;MenuItemShowCaption&quot;. Corresponds to the menu item used to toggle the visibility of the item/group caption.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.ShowCaptionImage">
      <summary>
        <para>Returns &quot;MenuItemShowCaptionImage&quot;. Corresponds to the menu item used to toggle the visibility of the image next to the item caption.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.ShowControl">
      <summary>
        <para>Returns &quot;MenuItemShowControl&quot;. Corresponds to the menu item used to toggle the visibility of the control displayed by the item.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.ShowCustomizationWindow">
      <summary>
        <para>Returns &quot;MenuItemShowCustomizationWindow&quot;. Corresponds to the menu item used to invoke the customization window.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.TabOperationsSeparator">
      <summary>
        <para>Returns &quot;TabOperationsSeparator&quot;. Corresponds to the separator before the DocumentPanel&#39;s Tab Operations context menu section.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DefaultMenuItemNames.Ungroup">
      <summary>
        <para>Returns &quot;MenuItemUngroup&quot;. Corresponds to the menu item used to ungroup the items selected in the customization window.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.DockController">
      <summary>
        <para>Provides methods to perform docking operations on panels.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockController.#ctor(DevExpress.Xpf.Docking.DockLayoutManager)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.DockController"/> class with the specified <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/>.</para>
      </summary>
      <param name="container">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object, whose dock functionality will be controlled by the created <see cref="T:DevExpress.Xpf.Docking.DockController"/>.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockController.DockAsDocument(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.DockType)">
      <summary>
        <para>Docks the specified item as a tab into an existing or new <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>.</para>
      </summary>
      <param name="item">An item to be docked as a tab into a DocumentGroup.</param>
      <param name="target">An item to which or next to which the first item is docked. This item can be a <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> or <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> object.</param>
      <param name="type">Specifies the way the first item is docked to the second item.</param>
      <returns>true, if the operation is a success; otherwise, false.</returns>
    </member>
    <member name="T:DevExpress.Xpf.Docking.DockControllerBase">
      <summary>
        <para>The base class for DockControllers that provide methods to perform docking operations on panels.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.#ctor(DevExpress.Xpf.Docking.DockLayoutManager)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.DockController"/> class with the specified <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/>.</para>
      </summary>
      <param name="container">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object, whose dock functionality will be controlled by the created DockController.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.Activate(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Activates the specified item.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to be activated.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.Activate(DevExpress.Xpf.Docking.BaseLayoutItem,System.Boolean)">
      <summary>
        <para>Activates the specified item.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendant to be activated.</param>
      <param name="focus">true, to move focus to the item; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockControllerBase.ActiveItem">
      <summary>
        <para>Gets or sets the active item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object which is the active item.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.AddDocumentGroup(DevExpress.Xpf.Layout.Core.DockType)">
      <summary>
        <para>Creates a <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>, and docks it to the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container (root group).</para>
      </summary>
      <param name="type">A <see cref="T:DevExpress.Xpf.Layout.Core.DockType"/> value that specifies the side of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container where the DocumentGroup is docked.</param>
      <returns>The created  <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.AddDocumentPanel(DevExpress.Xpf.Docking.DocumentGroup)">
      <summary>
        <para>Adds a new <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> to the specified <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> object to which a new panel is added.</param>
      <returns>The created <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.AddDocumentPanel(DevExpress.Xpf.Docking.DocumentGroup,System.Uri)">
      <summary>
        <para>Adds a new <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> to the specified DocumentGroup and loads the contents of a Window, Page or UserControl defined in the specified XAML into the <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/>.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> object to which a new <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is added.</param>
      <param name="uri">The uniform resource identifier (URI) of the XAML that defines a Window, Page or UserControl to be loaded into the created <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/>.</param>
      <returns>The created <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.AddDocumentPanel(System.Windows.Point,System.Windows.Size)">
      <summary>
        <para>Adds an empty floating <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/>.</para>
      </summary>
      <param name="floatLocation">A <see cref="T:System.Windows.Point"/> object specifying the position of the left top corner of the created <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/>.</param>
      <param name="floatSize">A <see cref="T:System.Windows.Size"/> object specifying the height and width of the created <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/>.</param>
      <returns>The created <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.AddDocumentPanel(System.Windows.Point,System.Windows.Size,System.Uri)">
      <summary>
        <para>Adds a new floating <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> and loads the contents of a Window, Page or UserControl defined in the specified XAML into the <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/>.</para>
      </summary>
      <param name="floatLocation">A <see cref="T:System.Windows.Point"/> object specifying a position of the left top corner of the created DocumentPanel.</param>
      <param name="floatSize">A <see cref="T:System.Windows.Size"/> object specifying the height and width of the created <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/>.</param>
      <param name="uri">A <see cref="T:System.Uri"/> object specifying the uniform resource identifier (URI) of the XAML that defines a Window, Page or UserControl to be loaded into the created <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/>.</param>
      <returns>The created <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.AddItem(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.DockType)">
      <summary>
        <para>Adds a newly created item to the specified target item. This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <param name="item">A newly created item to be docked to another item.</param>
      <param name="target">An item to be docked to.</param>
      <param name="type">A DockType value that specifies the dock type.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.AddPanel(DevExpress.Xpf.Layout.Core.DockType)">
      <summary>
        <para>Creates a <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> and docks it at the specified side of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container (root group).</para>
      </summary>
      <param name="type">A DockType value that specifies the side of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container where the <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> is docked.</param>
      <returns>The created <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.AddPanel(System.Windows.Point,System.Windows.Size)">
      <summary>
        <para>Creates a floating panel with the specified size and displays it at the specified location.</para>
      </summary>
      <param name="floatLocation">A Point structure that specifies the position at which the panel is displayed. The position is relative to the top left corner of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container (root group).</param>
      <param name="floatSize">A Size structure that specifies the created panel&#39;s size.</param>
      <returns>The created <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.Close(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Closes the specified item.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be closed.</param>
      <returns>true, if the item is successfully closed; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.CloseAllButThis(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Closes all items except the specified one within this item&#39;s container.</para>
      </summary>
      <param name="item">The only <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> element to remain opened within its parent container.</param>
      <returns>true, if all but the specified items were successfully closed; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockControllerBase.Container">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container whose dock functionality is controlled by the current DockController.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.CreateCommand``1(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Creates the specified dock command for the specified item/pane.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendant with which the created command is associated.</param>
      <typeparam name="T"></typeparam>
      <returns>The created command.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.CreateNewDocumentGroup(DevExpress.Xpf.Docking.DocumentPanel,System.Windows.Controls.Orientation)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> and moves the specified <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> to it.</para>
      </summary>
      <param name="document">A <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> to be added to the created group.</param>
      <param name="orientation">A <see cref="T:System.Windows.Controls.Orientation"/> value that specifies whether the created <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> is positioned on the right or bottom of the DocumentPanel&#39;s previous group.</param>
      <returns>true, if a <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> has been created; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.CreateNewDocumentGroup(DevExpress.Xpf.Docking.LayoutPanel,System.Windows.Controls.Orientation)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> and moves the specified <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> to it.</para>
      </summary>
      <param name="document">A <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> to be added to the created group.</param>
      <param name="orientation">A <see cref="T:System.Windows.Controls.Orientation"/> value that specifies whether the created <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> is positioned on the right or bottom of the DocumentPanel&#39;s previous group.</param>
      <returns>true, if a <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> has been created; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.Dock(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Docks the specified item. This method is in effect for newly created, floating, auto-hidden or closed(hidden) items.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be docked.</param>
      <returns>true, if the item has been successfully docked; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.Dock(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.DockType)">
      <summary>
        <para>Docks the first item to the second item using the specified dock type.</para>
      </summary>
      <param name="item">The item to be docked.</param>
      <param name="target">The item to which the first item is docked.</param>
      <param name="type">A DockType value that specifies how the first item is docked.</param>
      <returns>true, if the item has been successfully docked; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.Float(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Makes the specified item floating.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that shall be made floating.</param>
      <returns>A <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> object that when created, displays the item in the floating state.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.Hide(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Enables the auto-hide functionality for the item/panel and hides it at a corresponding edge of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to which the auto-hide feature is applied.</param>
      <returns>true, if the auto-hide functionality has been enabled for the item/pane; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.Hide(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.AutoHideGroup)">
      <summary>
        <para>Enables the auto-hide functionality for the item/panel and hides it within the specified <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> group.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to which the auto-hide feature is applied.</param>
      <param name="target">An <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> object where the panel is hidden.</param>
      <returns>true, if the auto-hide functionality has been enabled for the item/pane; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.Hide(DevExpress.Xpf.Docking.BaseLayoutItem,System.Windows.Controls.Dock)">
      <summary>
        <para>Enables the auto-hide functionality for the item/panel and hides it at the specified edge of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to which the auto-hide feature is applied.</param>
      <param name="dock">A <see cref="T:System.Windows.Controls.Dock"/> value that specifies the side of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container where the panel is hidden.</param>
      <returns>true, if the auto-hide functionality has been enabled for the item/pane; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.Insert(DevExpress.Xpf.Docking.LayoutGroup,DevExpress.Xpf.Docking.BaseLayoutItem,System.Int32)">
      <summary>
        <para>Inserts the specified item into the specified group at a specific position.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> into which the specified item is inserted.</param>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be inserted into the group.</param>
      <param name="index">An integer value that specifies the position among the group&#39;s children at which the item must be inserted.</param>
      <returns>true, if the item has been inserted into the group; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.MoveToDocumentGroup(DevExpress.Xpf.Docking.DocumentPanel,System.Boolean)">
      <summary>
        <para>Moves the specified <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> to the previous or next <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>.</para>
      </summary>
      <param name="document">A <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> object to be moved to another <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>.</param>
      <param name="next">trueto move the panel to the next <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>; falseto move the panel to the previous <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>.</param>
      <returns>true, if the <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> has been moved to another <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.MoveToDocumentGroup(DevExpress.Xpf.Docking.LayoutPanel,System.Boolean)">
      <summary>
        <para>Moves the specified <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> to the previous or next <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>.</para>
      </summary>
      <param name="document">A <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> object to be moved to another <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>.</param>
      <param name="next">true to move the panel to the next <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>; false to move the panel to the previous <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>.</param>
      <returns>true, if the <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> has been moved to another <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.RemoveItem(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Removes the specified item. This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <param name="item">An item to be removed.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.RemovePanel(DevExpress.Xpf.Docking.LayoutPanel)">
      <summary>
        <para>Removes any connection of the specified panel with the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/>.</para>
      </summary>
      <param name="panel">The <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> object that is removed from the DockLayoutManager.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.Rename(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Starts dock item renaming.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to be renamed.</param>
      <returns>true, if item renaming has been initiated; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockControllerBase.Restore(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Restores a closed (hidden) panel at its previous dock position.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be restored.</param>
      <returns>A Boolean value that indicates that the panel has been restored successfully.</returns>
    </member>
    <member name="T:DevExpress.Xpf.Docking.DockingDocumentUIService">
      <summary>
        <para>Allows you to show documents in docked <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/>s.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockingDocumentUIService.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.DockingDocumentUIService"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockingDocumentUIService.ActualLayoutGroup">
      <summary>
        <para>Gets the actual <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> associated with the service.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> object that represents the <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> associated with the service.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockingDocumentUIService.LayoutGroup">
      <summary>
        <para>Gets or sets a <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> that is used for representing documents. This is a dependency property.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> object that is used for representing documents.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockingDocumentUIService.LayoutGroupProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockingDocumentUIService.LayoutGroup"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockingDocumentUIService.LayoutPanelStyle">
      <summary>
        <para>Gets or sets the style applied to a document&#39;s container (a <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> object). This is a dependency property.</para>
      </summary>
      <value>A Style object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockingDocumentUIService.LayoutPanelStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockingDocumentUIService.LayoutPanelStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockingDocumentUIService.LayoutPanelStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a style applied to a document&#39;s container (a <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> object) . This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.StyleSelector descendant that applies a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockingDocumentUIService.LayoutPanelStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockingDocumentUIService.LayoutPanelStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.DockingStyle">
      <summary>
        <para>Contains values that specify the dock behavior of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockingStyle.Default">
      <summary>
        <para>The default docking style, based on the Visual Studio 2008 dock behavior.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockingStyle.VS2010">
      <summary>
        <para>Emulates the docking capabilities found in Microsoft Visual Studio 2010 (including changing docking hints and restricting <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/>s from being docked anywhere other than <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>s).</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.DockingViewStyle">
      <summary>
        <para>Lists the values that specify the docking items&#39; view style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockingViewStyle.Default">
      <summary>
        <para>Dock layout items do not merge their borders.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockingViewStyle.Light">
      <summary>
        <para>Dock layout items merge their borders.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.DockLayoutManager">
      <summary>
        <para>Represents a container for dock and layout items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.Activate(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Activates the specified item. For a <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> object, this method focuses the associated control.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be activated.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ActiveDockItem">
      <summary>
        <para>Gets or sets the active dock item.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that is the active dock item.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ActiveDockItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ActiveDockItem"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ActiveLayoutItem">
      <summary>
        <para>Gets or sets the active layout item. Setting this property doesn&#39;t move keyboard focus.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that specifies the active layout item.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ActiveLayoutItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ActiveLayoutItem"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ActiveMDIItem">
      <summary>
        <para>Gets or sets the active MDI child panel. This property is in effect when the assigned item represents an MDI child panel (DocumentPanel) within a DocumentGroup, and the group&#39;s <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> property is set to MDI.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the active MDI child panel.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ActiveMDIItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ActiveMDIItem"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowAeroSnap">
      <summary>
        <para>Gets or sets whether floating panels can be can be resized using the Aero Snap feature. This property is in effect in the Desktop floating mode. This is a dependency property.</para>
      </summary>
      <value>true, if floating panels can be can be resized using the Aero Snap feature; otherwise, false. The default is true.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AllowAeroSnapProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowAeroSnap"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowCustomization">
      <summary>
        <para>Gets or sets whether Customization Mode can be invoked.
This is a dependency property.</para>
      </summary>
      <value>true if Customization Mode can be invoked; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AllowCustomizationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowCustomization"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowDockItemRename">
      <summary>
        <para>Gets or sets whether dock items can be renamed.
This is a dependency property.</para>
      </summary>
      <value>true if dock items can be renamed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AllowDockItemRenameProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowDockItemRename"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowDocumentSelector">
      <summary>
        <para>Gets or sets whether the Document Selector feature is enabled.
This is a dependency property.</para>
      </summary>
      <value>true if the Document Selector feature is enabled; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AllowDocumentSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowDocumentSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowFloatGroupTransparency">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AllowFloatGroupTransparencyProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowFloatGroupTransparency"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowLayoutItemRename">
      <summary>
        <para>Gets or sets whether layout items can be renamed.
This is a dependency property.</para>
      </summary>
      <value>true if layout items can be renamed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AllowLayoutItemRenameProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowLayoutItemRename"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowMergingAutoHidePanels">
      <summary>
        <para>Gets or sets whether auto-hide groups can be merged.
This is a dependency property.</para>
      </summary>
      <value>true, if auto-hide groups can be merged; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AllowMergingAutoHidePanelsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowMergingAutoHidePanels"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AutoHideExpandMode">
      <summary>
        <para>Gets or sets how an auto-hidden panel is expanded.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.Base.AutoHideExpandMode"/> value that specifies the way an auto-hidden panel is expanded.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AutoHideExpandModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.AutoHideExpandMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AutoHideGroups">
      <summary>
        <para>Provides access to the collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects, containing auto-hidden panels. Allows you to create auto-hidden panels in XAML.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.AutoHideGroupCollection"/> collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects, containing auto-hidden panels.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AutoHideGroupsCheckInterval">
      <summary>
        <para>Gets or sets time interval with which the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> checks whether it should close the opened auto-hide panel. This is a dependency property.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AutoHideGroupsCheckIntervalProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.AutoHideGroupsCheckInterval"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AutoHideMode">
      <summary>
        <para>Gets or sets the mode that specifies how auto-hidden panels are displayed relative to other panels when expanded. This is a dependency property.</para>
      </summary>
      <value>An DevExpress.Xpf.Docking.Base.AutoHideMode enumeration value that specifies how auto-hidden panels are displayed relative to other panels when expanded. The default is Default.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AutoHideModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.AutoHideMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.BeforeItemAdded">
      <summary>
        <para>Fires before an item is added to the current <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.BeforeItemAddedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.BeforeItemAdded"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.BeginCustomization">
      <summary>
        <para>Invokes Customization Mode and opens the Customization Window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.BringToFront(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Makes the specified floating item topmost.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be brought to the front.</param>
      <returns>A Boolean value that specifies whether the result is successful.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanels">
      <summary>
        <para>Provides access to closed panels.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.ClosedPanelCollection"/> object that contains closed panels</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanelsBarPosition">
      <summary>
        <para>Gets or sets the Closed Panels bar&#39;s position. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.LayoutControl.Dock"/> enumerator value specifying the closed panels bar&#39;s position.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanelsBarPositionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanelsBarPosition"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanelsBarVisibility">
      <summary>
        <para>Gets or sets the visibility state for the Closed Panels bar.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility"/> value that specifies the visibility state for the Closed Panels bar.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanelsBarVisibilityProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanelsBarVisibility"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.CloseFloatWindowsOnManagerUnloading">
      <summary>
        <para>Gets or sets whether the float panels are closed when the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> is unloaded from visual tree. This is a dependency property.</para>
      </summary>
      <value>true to close floating panels; otherwise, false. The default is false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.CloseFloatWindowsOnManagerUnloadingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.CloseFloatWindowsOnManagerUnloading"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.CloseMenu">
      <summary>
        <para>Closes the active context menu.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosingBehavior">
      <summary>
        <para>Gets or sets the way in which a panel is closed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.ClosingBehavior"/> value that specifies how a panel is closed.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.Collapse(System.Boolean)">
      <summary>
        <para>Minimizes the expanded auto-hidden panel.</para>
      </summary>
      <param name="immediately">true, to minimize the panel without a sliding animation effect; otherwise, false, to minimize the panel with a sliding animation effect.</param>
      <returns>true if the panel is successfully minimized; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ContextMenuCustomizations">
      <summary>
        <para>A collection of modification actions to be performed on context menus for dock panels.</para>
      </summary>
      <value>A BarManagerActionCollection object that contains modification actions.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.CustomizationFormVisibleChanged">
      <summary>
        <para>Fires after the Customization Window has been displayed or hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.CustomizationFormVisibleChangedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.CustomizationFormVisibleChanged"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DecomposedItems">
      <summary>
        <para>Gets the collection of <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/>s that have been automatically removed from the layout when all their children are closed. This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>The collection of <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/>s that have been removed from the layout.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DefaultAutoHidePanelCaptionImage">
      <summary>
        <para>Gets or sets the image displayed within a dock panel&#39;s header when the panel in the auto-hide state, and if no caption and image are explicitly assigned to the panel.
This is a dependency property.</para>
      </summary>
      <value>An ImageSource object that specifies the associated image.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DefaultAutoHidePanelCaptionImageProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.DefaultAutoHidePanelCaptionImage"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DefaultTabPageCaptionImage">
      <summary>
        <para>Gets or sets the image displayed within a dock panel&#39;s tab when the panel belongs to a tabbed group, and if no caption and image are explicitly assigned to the panel.
This is a dependency property.</para>
      </summary>
      <value>An ImageSource object that specifies the associated image.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DefaultTabPageCaptionImageProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.DefaultTabPageCaptionImage"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DestroyLastDocumentGroup">
      <summary>
        <para>Gets or sets if the last group existent within the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> should be destroyed on closing its children.</para>
      </summary>
      <value>true if the last group existent within the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> should be destroyed on closing its children; otherwise, false. The default is false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DestroyLastDocumentGroupProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.DestroyLastDocumentGroup"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DisposeOnWindowClosing">
      <summary>
        <para>Gets or sets if the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object should be disposed on its parent window closing.
This is a dependency property.</para>
      </summary>
      <value>true if the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object should be disposed on its parent window closing; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DisposeOnWindowClosingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.DisposeOnWindowClosing"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DockController">
      <summary>
        <para>Gets the controller that provides methods to perform docking operations on panels.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.DockController"/> object that provides methods to perform docking operations on panels.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DockingStyle">
      <summary>
        <para>Gets or sets the docking style.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.DockingStyle"/> value that is the current docking style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockingStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.DockingStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivated">
      <summary>
        <para>Fires after a dock item has been activated.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivatedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivated"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivating">
      <summary>
        <para>Fires before a dock item is activated, and allows you to prevent this action.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivatingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivating"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemClosed">
      <summary>
        <para>Fires after a dock item has been closed (hidden).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemClosedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemClosed"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemClosing">
      <summary>
        <para>Fires before a dock item is closed (hidden), and allows you to prevent this action.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemClosingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemClosing"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemCollapsed">
      <summary>
        <para>Fires after a visible auto-hidden dock panel has slid away.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemCollapsedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemCollapsed"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDocking">
      <summary>
        <para>Fires before a dock item is dragged over dock hints, and allows you to prevent dock zones from being displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDockingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDocking"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDragging">
      <summary>
        <para>Fires repeatedly while a dock panel is being dragged.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDraggingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDragging"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemEndDocking">
      <summary>
        <para>Fires after a dock item has been dropped, and allows you to prevent this action.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemEndDockingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemEndDocking"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemExpanded">
      <summary>
        <para>Fires after a hidden auto-hidden dock panel has slid out.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemExpandedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemExpanded"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemHidden">
      <summary>
        <para>Fires after a dock item has been made auto-hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemHiddenEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemHidden"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemHiding">
      <summary>
        <para>Fires before a dock item is auto-hidden, and allows you to prevent this action.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemHidingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemHiding"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemRestored">
      <summary>
        <para>Fires after a dock item has been restored from the closed (hidden) state.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemRestoredEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemRestored"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemRestoring">
      <summary>
        <para>Fires before a dock item is restored from the closed (hidden) state, and allows you to prevent this action.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemRestoringEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemRestoring"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemStartDocking">
      <summary>
        <para>Fires when a docking operation starts, and allows you to prevent this operation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemStartDockingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemStartDocking"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockLayoutManagerProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.DockLayoutManager"/> dependency property.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted">
      <summary>
        <para>Occurs immediately after a dock operation within the current <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> is completed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompletedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting">
      <summary>
        <para>Occurs whenever a docking operation within the current <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> is performed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStartingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.EnableWin32Compatibility">
      <summary>
        <para>Gets or sets whether auto-hide and floating panels should be displayed over a WindowsFormsHost element. This is a dependency property.</para>
      </summary>
      <value>true, if auto-hide panels should be displayed over a WindowsFormsHost element; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.EnableWin32CompatibilityProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.EnableWin32Compatibility"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.EndCustomization">
      <summary>
        <para>Finishes layout customization.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.ExtendSelectionToParent">
      <summary>
        <para>Selects the currently selected item&#39;s parent.</para>
      </summary>
      <returns>true if the item&#39;s parent has been selected; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.FloatGroups">
      <summary>
        <para>Provides access to floating groups of panels. Allows you to create floating panels in XAML.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.FloatGroupCollection"/> object which is a collection of floating groups.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.FloatingDocumentContainer">
      <summary>
        <para>Gets or sets the container for floating <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> objects. This is a dependency property.</para>
      </summary>
      <value>A FloatingDocumentContainer enumerator value that specifies the container for floating <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> objects.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.FloatingDocumentContainerProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.FloatingDocumentContainer"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.FloatingMode">
      <summary>
        <para>Gets or sets how floating panels can be dragged, within or outside the boundaries of the current window.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.FloatingMode"/> value that specifies how floating panels can be dragged.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.FloatingModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.FloatingMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.GetDockLayoutManager(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.DockLayoutManager"/> attached property from a given object.</para>
      </summary>
      <param name="obj">An object whose DockLayoutManager property value must be returned.</param>
      <returns>A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object associated with the specified object</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.GetLayoutItem(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItem"/> attached property from a given object.</para>
      </summary>
      <param name="obj">An object whose LayoutItem property value must be returned.</param>
      <returns>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object associated with the specified object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.GetUIScope(System.Windows.DependencyObject)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <param name="obj"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.HandleHwndHostMouseEvents">
      <summary>
        <para>Gets or sets whether the DockLayoutManager should handle the mouse events ocurring in layout items&#39; nested HwndHosts.</para>
      </summary>
      <value>true, to handle mouse events; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.HandleHwndHostMouseEventsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.HandleHwndHostMouseEvents"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.HiddenItems">
      <summary>
        <para>Provides access to a collection of hidden Layout Items.</para>
      </summary>
      <value>A DevExpress.Xpf.Docking.HiddenItemsCollection that stores hidden layout items.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.HideCustomizationForm">
      <summary>
        <para>Hides the Customization Window when Customization Mode is enabled.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomization">
      <summary>
        <para>Gets whether Customization Mode is enabled.
This is a dependency property.</para>
      </summary>
      <value>true if Customization Mode is enabled; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationChanged">
      <summary>
        <para>Fires when Customization Mode is enabled or disabled.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationChangedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationChanged"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationFormVisible">
      <summary>
        <para>Gets whether the Customization Window is visible.</para>
      </summary>
      <value>true if the Customization Window is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomization"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.IsDarkTheme">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.IsDarkThemeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.IsDarkTheme"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.IsRenaming">
      <summary>
        <para>Gets whether an item is being renamed.
This is a dependency property.</para>
      </summary>
      <value>true if an item is being renamed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.IsRenamingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.IsRenaming"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.IsSynchronizedWithCurrentItem">
      <summary>
        <para>Gets or sets whether the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> synchronizes its the currently selected child item with the current item in the System.ComponentModel.ICollectionView assigned to the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSource"/>. This is a dependency property.</para>
      </summary>
      <value>true if a <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> is synchronized with the currently selected child item; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.IsSynchronizedWithCurrentItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.IsSynchronizedWithCurrentItem"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.ItemIsVisibleChanged">
      <summary>
        <para>Fires when the item&#39;s IsVisible property is changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ItemIsVisibleChangedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.ItemIsVisibleChanged"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.Items">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemSelectorMenuCustomizations">
      <summary>
        <para>A collection of modification actions to be performed on selector menus for dock panels.</para>
      </summary>
      <value>A BarManagerActionCollection object that contains menu modification actions.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize layout items for the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/>.
This is a dependency property.</para>
      </summary>
      <value>The source of objects to be visualized as layout items.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSource"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemTemplate">
      <summary>
        <para>Gets or sets a DataTemplate used to render items stored in the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSource"/> collection.</para>
      </summary>
      <value>A DataTemplate used to render items in the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSource"/> collection.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ItemTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemTemplateSelector">
      <summary>
        <para>Gets or sets a DataTemplateSelector object that provides a way to choose a DataTemplate to render items stored in the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSource"/> collection.</para>
      </summary>
      <value>A DataTemplateSelector object that provides a way to choose a DataTemplate to render items stored in the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSource"/> collection.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutControlItemContextMenuCustomizations">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutControlItemCustomizationMenuCustomizations">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutController">
      <summary>
        <para>Gets the controller that provides methods to perform layout operations on layout items.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.LayoutController"/> object that provides methods to perform layout operations on items.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivated">
      <summary>
        <para>Fires after a layout item has been activated.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivatedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivated"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivating">
      <summary>
        <para>Fires when a layout item is about to be activated.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivatingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivating"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemEndRenaming">
      <summary>
        <para>Fires when layout item renaming is completed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemEndRenamingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemEndRenaming"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemHidden">
      <summary>
        <para>Fires when a layout item is hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemHiddenEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemHidden"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemMoved">
      <summary>
        <para>Fires after a layout item has been moved to a new position.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemMovedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemMoved"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItem"/> dependency property.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemRestored">
      <summary>
        <para>Fires when a hidden layout item is restored (added to the layout).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemRestoredEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemRestored"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanged">
      <summary>
        <para>Fires after the layout item&#39;s selection state has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChangedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanged"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanging">
      <summary>
        <para>Fires when the layout item&#39;s selection state is about to be changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChangingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanging"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSizeChanged">
      <summary>
        <para>Fires after a layout item&#39;s width/height has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSizeChangedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSizeChanged"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemStartRenaming">
      <summary>
        <para>Fires when layout item renaming is initiated.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemStartRenamingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemStartRenaming"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutRoot">
      <summary>
        <para>Gets or sets a root group for items (panels and other groups).
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> object that represents the root group.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutRootProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutRoot"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.LogicalTreeStructure">
      <summary>
        <para>Get or sets a value that specifies how the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> stores its logical structure.</para>
      </summary>
      <value>Any of the <see cref="T:DevExpress.Xpf.Docking.LogicalTreeStructure"/> enumeration values.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LogicalTreeStructureProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.LogicalTreeStructure"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ManualClosedPanelsBarVisibility">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.MDIController">
      <summary>
        <para>Gets the controller that provides methods to perform operations on MDI panels.</para>
      </summary>
      <value>A DevExpress.Xpf.Docking.MDIController object that provides methods to perform operations on MDI panels.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.MDIItemActivated">
      <summary>
        <para>Fires when an MDI child document has been activated.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.MDIItemActivatedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.MDIItemActivated"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.MDIItemActivating">
      <summary>
        <para>Fires before an MDI child panel is activated.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.MDIItemActivatingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.MDIItemActivating"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.MDIMergeStyle">
      <summary>
        <para>Gets or sets if and when the merge mechanism is invoked by the DockLayoutManager.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Xpf.Bars.MDIMergeStyle"/> enumerator value that specifies if and when the merge mechanism is invoked.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.MDIMergeStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.MDIMergeStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.Merge">
      <summary>
        <para>Allows you to customize menus and bars when the merging mechanism is invoked.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.MergeEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.Merge"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.MinimizedItems">
      <summary>
        <para>Provides access to a collection of minimized float panels.</para>
      </summary>
      <value>A <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/>&lt;<see cref="T:System.Object"/>,&gt; collection of minimized float panels.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.OwnsFloatWindows">
      <summary>
        <para>Gets or sets whether float windows are automatically minimized/restored along with their parent <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/>.
This is a dependency property.</para>
      </summary>
      <value>true, if float windows are automatically minimized/restored along with their parent <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/>; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.OwnsFloatWindowsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.OwnsFloatWindows"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.RedrawContentWhenResizing">
      <summary>
        <para>Gets or sets whether the current <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/>&#39;s content should be dynamically redrawn upon resizing. This is a dependency property.</para>
      </summary>
      <value>true if the current <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/>&#39;s content should be dynamically redrawn upon resizing; otherwise, false. Default is true.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.RedrawContentWhenResizingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.RedrawContentWhenResizing"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.Rename(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Starts item renaming.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to be renamed.</param>
      <returns>true if item renaming has been initiated; otherwise, false.</returns>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.RequestUniqueName">
      <summary>
        <para>Allows you to provide unique names for layout items, whose names conflict with existing names.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.RequestUniqueNameEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.RequestUniqueName"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.RestoreLayoutFromStream(System.IO.Stream)">
      <summary>
        <para>Restores the layout of dock panes from a stream.</para>
      </summary>
      <param name="stream">A stream from which the layout of dock panes is restored.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.RestoreLayoutFromXml(System.String)">
      <summary>
        <para>Restores the layout of dock panes from an XML file.</para>
      </summary>
      <param name="path">An XML file that contains the layout of dock panes.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SaveLayoutToStream(System.IO.Stream)">
      <summary>
        <para>Saves the layout of dock panes to a stream.</para>
      </summary>
      <param name="stream">A stream to which the layout is stored.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SaveLayoutToXml(System.String)">
      <summary>
        <para>Saves the layout of dock panes to an XML file.</para>
      </summary>
      <param name="path">An XML file to which the layout is stored.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SelectItem(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Selects the specified layout item, in Customization Mode.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents a layout item to be selected.</param>
      <returns>true if the item was selected; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SelectItem(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.SelectionMode)">
      <summary>
        <para>Selects the specified <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> in Customization Mode with specified selection options.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to be selected.</param>
      <param name="mode">A DevExpress.Xpf.Layout.Core.SelectionMode enumeration value specifying selection option.</param>
      <returns>true if the specified <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> is successfully selected; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SetDockLayoutManager(System.Windows.DependencyObject,DevExpress.Xpf.Docking.DockLayoutManager)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.DockLayoutManager"/> attached property for a given object.</para>
      </summary>
      <param name="obj">An object for which the DockLayoutManager attached property is set.</param>
      <param name="value">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object to set for the specified object</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SetLayoutItem(System.Windows.DependencyObject,DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItem"/> attached property for a given object.</para>
      </summary>
      <param name="obj">An object for which the LayoutItem attached property is set.</param>
      <param name="value">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to set for the specified object.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SetUIScope(System.Windows.DependencyObject,DevExpress.Xpf.Layout.Core.IUIElement)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <param name="obj"></param>
      <param name="value"></param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowContentWhenDragging">
      <summary>
        <para>Gets or sets whether the dock panels display their content while end-users drag them. This is a dependency property.</para>
      </summary>
      <value>true, if panels display their content while dragging; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowContentWhenDraggingProperty">
      <summary>
        <para>Identifies the DockLayoutManager.ShowContentWhenDragging](xref:DevExpress.Xpf.Docking.DockLayoutManager.ShowContentWhenDragging) dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.ShowContextMenu(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Displays a context menu with the dock commands related to the specified item.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> for which a context menu is to be displayed.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.ShowCustomizationForm">
      <summary>
        <para>Displays the Customization Window when Customization Mode is enabled.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowFloatWindowsInTaskbar">
      <summary>
        <para>Gets or sets whether float windows for this <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> should display their own thumbnails within the Microsoft Windows Taskbar.
This is a dependency property.</para>
      </summary>
      <value>true if float windows for this <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> should display their own thumbnails within the Microsoft Windows Taskbar; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowFloatWindowsInTaskbarProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowFloatWindowsInTaskbar"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowingDockHints">
      <summary>
        <para>Allows you to hide and disable individual dock hints.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowingDockHintsEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowingDockHints"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowingMenu">
      <summary>
        <para>Fires before showing a context menu, and allows it to be customized.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowingMenuEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowingMenu"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItems">
      <summary>
        <para>Gets or sets whether invisible items are displayed within a layout.
This is a dependency property.</para>
      </summary>
      <value>A Nullable Boolean value that specifies whether invisible items are displayed within the layout.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItemsChanged">
      <summary>
        <para>Fires when the value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItems"/> property is changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItemsChangedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItemsChanged"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItemsInCustomizationForm">
      <summary>
        <para>Gets or sets if invisible <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/>s should be displayed in the Customization Mode.</para>
      </summary>
      <value>true if invisible <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/>s should be displayed in the Customization Mode; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItemsInCustomizationFormProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItemsInCustomizationForm"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItemsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItems"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.ShowItemSelectorMenu(System.Windows.UIElement,DevExpress.Xpf.Docking.BaseLayoutItem[])">
      <summary>
        <para>Displays a panel selector menu, used to activate an auto-hidden panel or a child panel within a <see cref="T:DevExpress.Xpf.Docking.TabbedGroup"/> or a <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> within a <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>.</para>
      </summary>
      <param name="source">A visual element for which the menu is invoked.</param>
      <param name="items">An array of <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> objects for which menu items in the menu will be created. Clicking on any menu item will activate the corresponding <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowMaximizedDocumentCaptionInWindowTitle">
      <summary>
        <para>Gets or sets whether the caption of a maximized <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is displayed within the window&#39;s title. This property is in effect in <see cref="F:DevExpress.Xpf.Docking.MDIStyle.MDI"/> mode.</para>
      </summary>
      <value>true if the caption of a maximized <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is displayed within the window&#39;s title; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowMaximizedDocumentCaptionInWindowTitleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowMaximizedDocumentCaptionInWindowTitle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.UIScopeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.UIScope"/> dependency property.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.UnMerge">
      <summary>
        <para>Allows you to undo bars customizations performed via the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.Merge"/> event.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.UnMergeEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.UnMerge"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ViewStyle">
      <summary>
        <para>Gets or sets a value that specifies how the layout items display their borders.</para>
      </summary>
      <value>A DockingViewStyle enumeration value.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ViewStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ViewStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.WindowTitleFormat">
      <summary>
        <para>Gets or sets the format string used to format the window&#39;s title.
This is a dependency property.</para>
      </summary>
      <value>The format string used to format the window&#39;s title.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.WindowTitleFormatProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.WindowTitleFormat"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.DockLayoutManagerLinker">
      <summary>
        <para>Provides methods for link operations.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManagerLinker.Link(DevExpress.Xpf.Docking.DockLayoutManager,DevExpress.Xpf.Docking.DockLayoutManager)">
      <summary>
        <para>Links two DockLayoutManager instances.</para>
      </summary>
      <param name="first">The first DockLayoutManager instance.</param>
      <param name="second">The second DockLayoutManager instance.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DockLayoutManagerLinker.Unlink(DevExpress.Xpf.Docking.DockLayoutManager,DevExpress.Xpf.Docking.DockLayoutManager)">
      <summary>
        <para>Unlinks two linked DockLayoutManager instances.</para>
      </summary>
      <param name="first">The first DockLayoutManager instance.</param>
      <param name="second">The second DockLayoutManager instance.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.DockOperation">
      <summary>
        <para>Provides members to label different docking operation types.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockOperation.Close">
      <summary>
        <para>A target Dock Item is being closed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockOperation.Dock">
      <summary>
        <para>A target Dock Item is being docked to a <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/>&#39;s edge or another Dock Item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockOperation.Float">
      <summary>
        <para>A docked Dock Item is made floating.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockOperation.Hide">
      <summary>
        <para>A Dock Item is made auto-hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockOperation.Move">
      <summary>
        <para>A Dock Item is being moved.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockOperation.Reorder">
      <summary>
        <para>A target Dock Item is being re-arranged within its current container.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockOperation.Resize">
      <summary>
        <para>A Dock Item is being resized.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DockOperation.Restore">
      <summary>
        <para>A closed Dock Item is being restored (see the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanels"/> property to learn more).</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.DocumentGroup">
      <summary>
        <para>Represents child panels (<see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> objects) using either a tabbed or MDI UI.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentGroup.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentGroup.ClosePageButtonShowMode">
      <summary>
        <para>Gets or sets whether Close buttons are displayed in individual tab pages and the tab container&#39;s header. 
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.ClosePageButtonShowMode"/> value that specifies the display mode for Close buttons.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DocumentGroup.ClosePageButtonShowModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.ClosePageButtonShowMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentGroup.GetPinLocation(System.Windows.DependencyObject)">
      <summary>
        <para>Returns a value indicating where the pinned tabbed <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is located.</para>
      </summary>
      <param name="obj">An object for which the PinLocation attached property&#39;s value must be returned.</param>
      <returns>A value that specifies where the pinned tabbed DocumentPanel is located.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentGroup.GetPinned(System.Windows.DependencyObject)">
      <summary>
        <para>Returns a value indicating whether the tabbed <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is pinned.</para>
      </summary>
      <param name="obj">An object for which the Pinned attached property&#39;s value must be returned.</param>
      <returns>true, if the tabbed <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is pinned; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentGroup.GetShowPinButton(System.Windows.DependencyObject)">
      <summary>
        <para>Returns a value indicating whether the Pin button on the tabbed <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is visible.</para>
      </summary>
      <param name="obj">An object for which the ShowPinButton attached property&#39;s value must be returned.</param>
      <returns>true, if the tabbed <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is pinned; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentGroup.GroupBorderStyle">
      <summary>
        <para>This property is not supported by the <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> class. Use the <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> property instead.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentGroup.IsDropDownButtonVisible">
      <summary>
        <para>Gets whether the DropDown button is displayed within the DocumentGroup&#39;s header.</para>
      </summary>
      <value>true if the DropDown button is displayed within the DocumentGroup&#39;s header; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentGroup.IsRestoreButtonVisible">
      <summary>
        <para>Gets whether the DocumentGroup displays the Restore button.
This is a dependency property.</para>
      </summary>
      <value>true if the DocumentGroup displays the Restore button; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle">
      <summary>
        <para>Gets or sets how the <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> presents its child panels, as floaing windows, or using the tabbed UI.
This is a dependency property.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Xpf.Docking.MDIStyle"/> value that specifies how the <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> presents its child panels, as floaing windows, or using the tabbed UI.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DocumentGroup.MDIStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DocumentGroup.PinLocationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.PinLocation"/> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DocumentGroup.PinnedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.Pinned"/> dependency property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentGroup.SetPinLocation(System.Windows.DependencyObject,DevExpress.Xpf.Layout.Core.TabHeaderPinLocation)">
      <summary>
        <para>Set a value indicating where the pinned tabbed <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is located.</para>
      </summary>
      <param name="obj">An object for which the PinLocation attached property&#39;s value is set.</param>
      <param name="value">A value that specifies where the pinned tabbed <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is located.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentGroup.SetPinned(System.Windows.DependencyObject,System.Boolean)">
      <summary>
        <para>Set a value indicating whether the tabbed <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is pinned.</para>
      </summary>
      <param name="obj">An object for which the Pinned attached property&#39;s value is set.</param>
      <param name="value">true, if the tabbed <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is pinned; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentGroup.SetShowPinButton(System.Windows.DependencyObject,System.Boolean)">
      <summary>
        <para>Set a value indicating whether the Pin button on the tabbed <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is visible.</para>
      </summary>
      <param name="obj">An object for which the ShowPinButton attached property&#39;s value is set.</param>
      <param name="value">true, if the Pin button on the tabbed <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is visible; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentGroup.ShowDropDownButton">
      <summary>
        <para>Gets or sets whether the DropDown button is visible within the current DocumentGroup.</para>
      </summary>
      <value>true if the DropDown button is visible within the current DocumentGroup; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DocumentGroup.ShowPinButtonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.ShowPinButton"/> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentGroup.ShowRestoreButton">
      <summary>
        <para>Gets or sets whether the Restore button is displayed within the DocumentGroup&#39;s title, allowing an end-user to restore a maximized panel to its normal state.
This is a dependency property.</para>
      </summary>
      <value>true if the Restore button is displayed within the DocumentGroup&#39;s title; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentGroup.ShowWhenEmpty">
      <summary>
        <para>Gets or sets whether the <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> is displayed when it is empty.</para>
      </summary>
      <value>true, if the <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> is displayed when it is empty; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DocumentGroup.ShowWhenEmptyProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.ShowWhenEmpty"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentGroup.TabbedGroupDisplayMode">
      <summary>
        <para>Contains values that specify the DocumentPanel header displaying mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.TabbedGroupDisplayMode"/> enumeration value.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DocumentGroup.TabbedGroupDisplayModeProperty">
      <summary>
        <para>Identifies the <see cref="F:DevExpress.Xpf.Docking.DocumentGroup.TabbedGroupDisplayModeProperty"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.DocumentPanel">
      <summary>
        <para>DocumentPanel objects represent child panels in a <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentPanel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentPanel.ActivateCommand">
      <summary>
        <para>Gets the command that activates the current <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/>.</para>
      </summary>
      <value>The command that activates the current <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/>.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentPanel.EnableMouseHoverWhenInactive">
      <summary>
        <para>Gets or sets whether the current <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> handles its child controls&#39; events in the inactive state. This is a dependency property.</para>
      </summary>
      <value>true if the current <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> handles its child controls&#39; events in the inactive state; otherwise, false. The default is false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DocumentPanel.EnableMouseHoverWhenInactiveProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.EnableMouseHoverWhenInactive"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentPanel.GetMDILocation(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDILocation"/> attached property from a given object.</para>
      </summary>
      <param name="dObj">An object whose MDILocation property&#39;s value must be returned.</param>
      <returns>The value of the MDILocation attached property for the specified object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentPanel.GetMDISize(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDISize"/> attached property from a given object.</para>
      </summary>
      <param name="dObj">An object whose MDISize property&#39;s value must be returned.</param>
      <returns>The value of the MDISize attached property for the specified object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentPanel.GetMDIState(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDIState"/> attached property from a given object.</para>
      </summary>
      <param name="dObj">An object whose MDIState property&#39;s value must be returned.</param>
      <returns>The value of the MDIState attached property for the specified object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentPanel.GetRestoreBounds(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.RestoreBounds"/> attached property from a given object.</para>
      </summary>
      <param name="dObj">An object whose RestoreBounds property&#39;s value must be returned.</param>
      <returns>The value of the RestoreBounds attached property for the specified object.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentPanel.MDILocation">
      <summary>
        <para>Gets or sets the location of an MDI child panel. This property is in effect when the group&#39;s <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> property is set to MDI. This is an attached property.</para>
      </summary>
      <value>A Point value that specifies the location of an MDI child panel</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DocumentPanel.MDILocationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDILocation"/> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentPanel.MDISize">
      <summary>
        <para>Gets or sets the size of an MDI child panel. This property is in effect when the group&#39;s <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> property is set to MDI. This is an attached property.</para>
      </summary>
      <value>A Size structure that specifies the size of an MDI child panel.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DocumentPanel.MDISizeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDISize"/> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentPanel.MDIState">
      <summary>
        <para>Gets or sets a value that indicates whether a <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is minimized, maximized, or normal. This property is in effect in MDI Mode.</para>
      </summary>
      <value>An enumeration value that indicates whether a <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is minimized, maximized, or normal.
The default value is MDIState.Normal.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DocumentPanel.MDIStateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDIState"/> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.DocumentPanel.RestoreBoundsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.RestoreBounds"/> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.DocumentPanel.SerializableMDIBounds">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentPanel.SetMDILocation(System.Windows.DependencyObject,System.Windows.Point)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDILocation"/> attached property for a given object.</para>
      </summary>
      <param name="dObj">An object for which the MDILocation attached property is set.</param>
      <param name="value">The value for the MDILocation attached property.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentPanel.SetMDISize(System.Windows.DependencyObject,System.Windows.Size)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDISize"/> attached property for a given object.</para>
      </summary>
      <param name="dObj">An object for which the MDISize attached property is set.</param>
      <param name="value">The value for the MDISize attached property.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentPanel.SetMDIState(System.Windows.DependencyObject,DevExpress.Xpf.Docking.MDIState)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDIState"/> attached property for a given object.</para>
      </summary>
      <param name="dObj">An object for which the MDIState attached property is set.</param>
      <param name="value">The value for the MDIState attached property.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.DocumentPanel.SetRestoreBounds(System.Windows.DependencyObject,System.Windows.Rect)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.RestoreBounds"/> attached property for a given object.</para>
      </summary>
      <param name="dObj">An object for which the RestoreBounds attached property is set.</param>
      <param name="value">The value for the RestoreBounds attached property.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.EmptySpaceItem">
      <summary>
        <para>Adds whitespace to a UI.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.EmptySpaceItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.EmptySpaceItem"/> class with the specified settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.FixedItem">
      <summary>
        <para>An ancestor for fixed items available via the Customization Form (the Empty Space Item, Label, Splitter and Separator).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FixedItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.FixedItem"/> class.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.FixedItemStyle">
      <summary>
        <para>Identifies the type a <see cref="T:DevExpress.Xpf.Docking.FixedItem"/> object.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FixedItemStyle.EmptySpace">
      <summary>
        <para>Represents an empty space item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FixedItemStyle.Label">
      <summary>
        <para>Represents a label.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FixedItemStyle.Separator">
      <summary>
        <para>Represents a separator.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.FloatGroup">
      <summary>
        <para>A container for floating panels.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FloatGroup.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroup.ActualVisibility">
      <summary>
        <para>Gets a visibility state for the current <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/>.</para>
      </summary>
      <value>A System.Windows.Visibility enumerator value that indicates a visibility state for the current <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/>.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroup.BorderStyle">
      <summary>
        <para>Gets the FloatGroup&#39;s border style.
This is a dependency property.</para>
      </summary>
      <value>A FloatGroupBorderStyle value that identifies the group&#39;s border style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FloatGroup.BorderStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.BorderStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroup.FloatLocation">
      <summary>
        <para>Gets or sets the location of the <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> object, relative to the top left corner of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container.
This is a dependency property.</para>
      </summary>
      <value>A Point structure that specifies the location of the <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> object, relative to the top left corner of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FloatGroup.FloatLocationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.FloatLocation"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroup.FloatState">
      <summary>
        <para>Gets or sets whether the panel is in the normal, maximized or minimized state. This is an attached property.</para>
      </summary>
      <value>A DevExpress.Xpf.Docking.FloatState enumeration value that specifies whether the panel is in the normal, maximized or minimized state.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FloatGroup.FloatStateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.FloatState"/> attached property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FloatGroup.GetFloatState(System.Windows.DependencyObject)">
      <summary>
        <para>Returns the value of the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.FloatState"/> attached property for a specific object.</para>
      </summary>
      <param name="target">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.FloatGroup.FloatState"/> property value should be returned.</param>
      <returns>A FloatState enumeration value representing the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.FloatState"/> property value assigned to the specified DependencyObject. null (Nothing in VB) if no value is assigned.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FloatGroup.GetSizeToContent(System.Windows.DependencyObject)">
      <summary>
        <para>Returns the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.SizeToContent"/> property value for a specific object.</para>
      </summary>
      <param name="target">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.FloatGroup.SizeToContent"/> property value should be returned.</param>
      <returns>An Object representing the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.SizeToContent"/> property value assigned to the specified DependencyObject. null (Nothing in VB) if no value is assigned.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FloatGroup.GetWindowTaskbarIcon(System.Windows.DependencyObject)">
      <summary>
        <para>Returns the value of the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarIcon"/> attached property for a specific object.</para>
      </summary>
      <param name="dObj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarIcon"/> property value should be returned.</param>
      <returns>An ImageSource value representing the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarIcon"/> property value assigned to the specified DependencyObject. null (Nothing in VB) if no value is assigned.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FloatGroup.GetWindowTaskbarTitle(System.Windows.DependencyObject)">
      <summary>
        <para>Returns the value of the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarTitle"/> attached property for a specific object.</para>
      </summary>
      <param name="dObj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarTitle"/> property value should be returned.</param>
      <returns>A <see cref="T:System.String"/> value representing the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarTitle"/> property value assigned to the specified DependencyObject. null (Nothing in VB) if no value is assigned.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroup.IsMaximized">
      <summary>
        <para>Gets whether the FloatGroup displays a panel that is currently maximized to the window&#39;s size.
This is a dependency property.</para>
      </summary>
      <value>true if the FloatGroup displays a panel that is currently maximized to the window&#39;s size; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FloatGroup.IsMaximizedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.IsMaximized"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroup.IsOpen">
      <summary>
        <para>Gets or sets whether the current <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> object is visible.
This is a dependency property.</para>
      </summary>
      <value>true if the current <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> object is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FloatGroup.IsOpenProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.IsOpen"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroup.SerializableIsMaximized">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroup.SerializableZOrder">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FloatGroup.SetFloatState(System.Windows.DependencyObject,DevExpress.Xpf.Docking.FloatState)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.FloatState"/> attached property for a specific object.</para>
      </summary>
      <param name="target">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.FloatGroup.FloatState"/> attached property is to be set.</param>
      <param name="value">A new value of the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.FloatState"/> attached property for the specified object.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FloatGroup.SetSizeToContent(System.Windows.DependencyObject,System.Windows.SizeToContent)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.SizeToContent"/> attached property for a specific object.</para>
      </summary>
      <param name="target">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.FloatGroup.SizeToContent"/> attached property is to be set.</param>
      <param name="value">A new value of the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.SizeToContent"/> attached property for the specified object.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FloatGroup.SetWindowTaskbarIcon(System.Windows.DependencyObject,System.Windows.Media.ImageSource)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarIcon"/> attached property for a specific object.</para>
      </summary>
      <param name="dObj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarIcon"/> attached property is to be set.</param>
      <param name="value">A new value of the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarIcon"/> attached property for the specified object.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FloatGroup.SetWindowTaskbarTitle(System.Windows.DependencyObject,System.String)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarTitle"/> attached property for a specific object.</para>
      </summary>
      <param name="dObj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarTitle"/> attached property is to be set.</param>
      <param name="value">A new value of the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarTitle"/> attached property for the specified object.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroup.ShouldRestoreOnActivate">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>A Boolean value.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroup.SizeToContent">
      <summary>
        <para>Gets or sets whether a panel will automatically size itself to fit the size of its content. This is an attached property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.SizeToContent"/> enumeration value that specifies whether a panel will automatically size itself to fit the size of its content.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FloatGroup.SizeToContentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.SizeToContent"/> attached property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroup.WindowStyle">
      <summary>
        <para>Gets or sets the style applied to items within a specified FloatingPaneWindow container. This is a dependency property.</para>
      </summary>
      <value>The style applied to the FloatingPaneWindow.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FloatGroup.WindowStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarIcon">
      <summary>
        <para>Gets or sets an icon displayed for the current float group in the Windows Taskbar. This is an attached property.</para>
      </summary>
      <value>An ImageSource object that specifies the icon displayed for the current float group in the Windows Taskbar.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarIconProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarIcon"/> attached property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarTitle">
      <summary>
        <para>Gets or sets a title displayed for the current float group in the Windows Taskbar. This is an attached property.</para>
      </summary>
      <value>A string value that specifies the title displayed for the current float group in the Windows Taskbar.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarTitleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.FloatGroup.WindowTaskbarTitle"/> attached property.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.FloatGroupCollection">
      <summary>
        <para>Represents a collection of <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FloatGroupCollection.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.FloatGroupCollection"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FloatGroupCollection.AddRange(DevExpress.Xpf.Docking.FloatGroup[])">
      <summary>
        <para>Adds an array of <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> objects to the current collection.</para>
      </summary>
      <param name="items">An array of <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> objects to be added to the collection.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FloatGroupCollection.Dispose">
      <summary>
        <para>Disposes of all the items in the collection and releases all the allocated resources.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.FloatGroupCollection.Item(System.String)">
      <summary>
        <para>Provides access to a FloatGroup&#39;s items and their nested items by names.</para>
      </summary>
      <param name="name">A string that specifies the name of the item to be located.</param>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object with the specified name. null if no item is found.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.FloatGroupCollection.ToArray">
      <summary>
        <para>Returns float groups stored in the current collection as an array.</para>
      </summary>
      <returns>An array of <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> objects.</returns>
    </member>
    <member name="T:DevExpress.Xpf.Docking.FloatingMode">
      <summary>
        <para>Contains values that specify how floating panels can be dragged.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FloatingMode.Desktop">
      <summary>
        <para>Floating panels are allowed to be dragged throughout the desktop.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.FloatingMode.Window">
      <summary>
        <para>Floating panels are allowed to be dragged only within the current window.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.GroupBorderStyle">
      <summary>
        <para>Contains values that specify how a group&#39;s borders are rendered.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.GroupBorderStyle.Group">
      <summary>
        <para>A container is displayed with borders and caption.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.GroupBorderStyle.GroupBox">
      <summary>
        <para>A container is displayed with borders and title bar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.GroupBorderStyle.NoBorder">
      <summary>
        <para>A container has no borders.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.GroupBorderStyle.Tabbed">
      <summary>
        <para>Child items are represented as tabs.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.ImageLocation">
      <summary>
        <para>Contains values that specify how an image is displayed relative to an adjacent text region.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ImageLocation.AfterText">
      <summary>
        <para>An image is displayed after text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ImageLocation.BeforeText">
      <summary>
        <para>An image is displayed before text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.ImageLocation.Default">
      <summary>
        <para>The same as the <see cref="F:DevExpress.Xpf.Docking.ImageLocation.BeforeText"/> option.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.LabelItem">
      <summary>
        <para>A label displaying custom text.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LabelItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.LabelItem"/> class with the specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LabelItem.Content">
      <summary>
        <para>Gets or sets a <see cref="T:DevExpress.Xpf.Docking.LabelItem"/>&#39;s caption. This is a dependency property.</para>
      </summary>
      <value>An object that is a <see cref="T:DevExpress.Xpf.Docking.LabelItem"/>&#39;s caption.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LabelItem.ContentHorizontalAlignment">
      <summary>
        <para>Gets or sets a horizontal alignment for a Label Item&#39;s <see cref="P:DevExpress.Xpf.Docking.LabelItem.Content"/>. This is a dependency property.</para>
      </summary>
      <value>A System.Windows.HorizontalAlignment enumerator value.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LabelItem.ContentHorizontalAlignmentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LabelItem.ContentHorizontalAlignment"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LabelItem.ContentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LabelItem.Content"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LabelItem.ContentTemplate">
      <summary>
        <para>Gets or sets a DataTemplate object to visualize a <see cref="P:DevExpress.Xpf.Docking.LabelItem.Content"/> object.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> object that visualizes a <see cref="P:DevExpress.Xpf.Docking.LabelItem.Content"/> object.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LabelItem.ContentTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LabelItem.ContentTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LabelItem.ContentTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a <see cref="P:DevExpress.Xpf.Docking.LabelItem.ContentTemplate"/> used to visualize objects defined as a Label Item&#39;s <see cref="P:DevExpress.Xpf.Docking.LabelItem.Content"/>. This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LabelItem.ContentTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LabelItem.ContentTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LabelItem.ContentVerticalAlignment">
      <summary>
        <para>Gets or sets a vertical alignment for a Label Item&#39;s <see cref="P:DevExpress.Xpf.Docking.LabelItem.Content"/>. This is a dependency property.</para>
      </summary>
      <value>A System.Windows.VerticalAlignment enumerator value.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LabelItem.ContentVerticalAlignmentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LabelItem.ContentVerticalAlignment"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LabelItem.DesiredSizeInternalProperty">
      <summary>
        <para>For internal use only.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LabelItem.HasContent">
      <summary>
        <para>Gets if the current <see cref="T:DevExpress.Xpf.Docking.LabelItem"/> has a caption. This is a dependency property.</para>
      </summary>
      <value>true if the current <see cref="T:DevExpress.Xpf.Docking.LabelItem"/> has a caption; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LabelItem.HasContentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LabelItem.HasContent"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LabelItem.HasDesiredSizeProperty">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.LayoutControlItem">
      <summary>
        <para>An element of a Layout Group, capable of displaying a control with a label.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutControlItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.ActualCaptionMargin">
      <summary>
        <para>Gets the calculated outer indents for the item&#39;s caption.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> value that contains the outer indents of the item&#39;s caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.ActualCaptionMarginProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.ActualCaptionMargin"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.CaptionToControlDistance">
      <summary>
        <para>Gets or sets the distance between the item&#39;s caption and control.
This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the distance between the item&#39;s caption and control.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.CaptionToControlDistanceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.CaptionToControlDistance"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.ContentPresenter">
      <summary>
        <para>Retrieves the content presenter associated with the current <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> object. This is a dependency property.</para>
      </summary>
      <value>A UIElement that is the content presenter of this item.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.ContentPresenterProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.ContentPresenter"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.Control">
      <summary>
        <para>Gets or sets the control displayed by the current item.
This is a dependency property.</para>
      </summary>
      <value>A UIElement object that specifies the control displayed by the current item.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.ControlHorizontalAlignment">
      <summary>
        <para>Gets or sets the horizontal alignment of the control within the current <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/>.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.HorizontalAlignment"/> value that specifies the control&#39;s horizontal alignment.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.ControlHorizontalAlignmentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.ControlHorizontalAlignment"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.ControlProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.Control"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.ControlVerticalAlignment">
      <summary>
        <para>Gets or sets the vertical alignment of the control within the current <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/>.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.VerticalAlignment"/> value that specifies the control&#39;s vertical alignment.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.ControlVerticalAlignmentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.ControlVerticalAlignment"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.HasControl">
      <summary>
        <para>Gets whether a control is assigned to the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.Control"/> property.
This is a dependency property.</para>
      </summary>
      <value>true if the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.Control"/> property is initialized.; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.HasControlProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.HasControl"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.HasDesiredSizeProperty">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.ShowControl">
      <summary>
        <para>Gets or sets whether the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.Control"/> is visible.
This is a dependency property.</para>
      </summary>
      <value>true if the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.Control"/> is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.ShowControlProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.ShowControl"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.LayoutController">
      <summary>
        <para>Represents the object that provides methods to manage the layout of items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.#ctor(DevExpress.Xpf.Docking.DockLayoutManager)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.LayoutController"/> class.</para>
      </summary>
      <param name="container">A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object, whose layout functionality will be controlled by the created <see cref="T:DevExpress.Xpf.Docking.LayoutController"/>.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.Activate(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Activates the specified item. For a <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> object, the associated control is focused.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents a layout item to be activated.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.Activate(DevExpress.Xpf.Docking.BaseLayoutItem,System.Boolean)">
      <summary>
        <para>Activates the specified item, and optionally focuses the item&#39;s associated control.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the layout item to be activated.</param>
      <param name="focus">true to move focus to the control associated with the item; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutController.ActiveItem">
      <summary>
        <para>Gets the active layout item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendant that represents the active layout item.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.CancelRenaming">
      <summary>
        <para>Cancels the item renaming in progress.</para>
      </summary>
      <returns>true if item renaming has been canceled; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.ChangeGroupOrientation(DevExpress.Xpf.Docking.LayoutGroup,System.Windows.Controls.Orientation)">
      <summary>
        <para>Changes the <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/>&#39;s <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.Orientation"/> to the specified value.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> whose orientation is to be changed.</param>
      <param name="orientation">A <see cref="T:System.Windows.Controls.Orientation"/> value that specifies the new orientation for the group.</param>
      <returns>true if the group&#39;s orientation has been changed; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutController.Container">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container whose dock functionality is controlled by the current <see cref="T:DevExpress.Xpf.Docking.LayoutController"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.CreateCommand``1(DevExpress.Xpf.Docking.BaseLayoutItem[])">
      <summary>
        <para>Creates the specified layout command for the specified items.</para>
      </summary>
      <param name="items">An array of <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> objects with which the created command is associated.</param>
      <typeparam name="T"></typeparam>
      <returns>The created command.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.EndRenaming">
      <summary>
        <para>Finishes the item renaming in progress.</para>
      </summary>
      <returns>true if item renaming is finished; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutController.FixedItems">
      <summary>
        <para>Gets a collection of the items that are always available in the Customization Window.</para>
      </summary>
      <value>A collection of the items that are always available in the Customization Window.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.Group(DevExpress.Xpf.Docking.BaseLayoutItem[])">
      <summary>
        <para>Combines the specified adjacent items into a new group.</para>
      </summary>
      <param name="items">An array of adjacent items to be combined into a group.</param>
      <returns>true if the items have been combined into a group; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutController.HiddenItems">
      <summary>
        <para>Gets the collection of hidden items.</para>
      </summary>
      <value>A HiddenItemsCollection collection storing hidden items.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.Hide(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Hides the specified layout item.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be hidden.</param>
      <returns>true if the item has been successfully hidden; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.HideSelectedItems">
      <summary>
        <para>Hides the selected items.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutController.IsCustomization">
      <summary>
        <para>Gets whether Customization Mode is enabled.</para>
      </summary>
      <value>true if Customization Mode is enabled; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.Move(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.MoveType)">
      <summary>
        <para>Moves the specified item to the position next to another item or into another group.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be moved.</param>
      <param name="target">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to which (or next to which) the item is moved.</param>
      <param name="type">A MoveType value that specifies how the item is moved.</param>
      <returns>A Boolean value that specifies that the item has been successfully moved.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.Move(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.MoveType,System.Int32)">
      <summary>
        <para>Moves the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> next to another item or into another group at the precise position, specified by an insertIndex parameter.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to move.</param>
      <param name="target">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> in relation to which the specified element is moved.</param>
      <param name="type">A DevExpress.Xpf.Layout.Core.MoveType enumeration value specifying how the item is moved.</param>
      <param name="insertIndex">An integer value that specifies the zero-based index at which the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> should be inserted. If it is negative or exceeds the number of items within the target container, an exception is thrown.</param>
      <returns>true if the item has been sucsessfully moved; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.Rename(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Starts layout item renaming.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to be renamed.</param>
      <returns>true if item renaming has been initiated; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.Restore(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Restores the specified hidden item, adding it to the layout.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents a hidden item to be restored.</param>
      <returns>A Boolean value that indicates that the item has been successfully restored.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutController.Selection">
      <summary>
        <para>Gets a list of items that are selected in Customization Mode.</para>
      </summary>
      <value>A DevExpress.Xpf.Docking.Selection object that represents a list of selected <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> objects.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.SetGroupBorderStyle(DevExpress.Xpf.Docking.LayoutGroup,DevExpress.Xpf.Docking.GroupBorderStyle)">
      <summary>
        <para>Sets the specified style for a group.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> whose style is to be changed.</param>
      <param name="style">A <see cref="T:DevExpress.Xpf.Docking.GroupBorderStyle"/> value that specifies the new style.</param>
      <returns>true if the group&#39;s style has been changed; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutController.Ungroup(DevExpress.Xpf.Docking.LayoutGroup)">
      <summary>
        <para>Ungroups the specified group, moving its children to the group&#39;s parent.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> to be ungrouped.</param>
      <returns>true if the group has been ungouped; otherwise, false.</returns>
    </member>
    <member name="T:DevExpress.Xpf.Docking.LayoutGroup">
      <summary>
        <para>Represents a group of items, arranging them side by side (either horizontally or vertically) or using the tabbed UI (the tabbed UI is only supported when combining layout items).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Accept(DevExpress.Xpf.Layout.Core.IVisitor{DevExpress.Xpf.Docking.BaseLayoutItem})">
      <summary>
        <para>Invokes the Visit method of the specified visitor for each item that belongs to the current layout group.</para>
      </summary>
      <param name="visitor">An object implementing the DevExpress.Xpf.Layout.Core.IVisitor interface.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Accept(DevExpress.Xpf.Layout.Core.VisitDelegate{DevExpress.Xpf.Docking.BaseLayoutItem})">
      <summary>
        <para>Invokes the specified delegate for each item that belongs to the current layout group.</para>
      </summary>
      <param name="visit">A DevExpress.Xpf.Layout.Core.VisitDelegate delegate that will be invoked for the group&#39;s items.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ActualDockItemInterval">
      <summary>
        <para>Gets the actual distance between immediate child dock items.
This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the actual distance between immediate child dock items.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ActualDockItemIntervalProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ActualDockItemInterval"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ActualGroupTemplateSelector">
      <summary>
        <para>Gets an object that chooses the group&#39;s template based on custom logic.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ActualGroupTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ActualGroupTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ActualLayoutGroupInterval">
      <summary>
        <para>Gets the actual distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> objects.
This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the actual distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> objects.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ActualLayoutGroupIntervalProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ActualLayoutGroupInterval"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ActualLayoutItemInterval">
      <summary>
        <para>Gets the actual distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> objects.
This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the actual distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> objects.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ActualLayoutItemIntervalProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ActualLayoutItemInterval"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Add(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Adds the specified item as a child to the current group.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be added to the group.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Add(DevExpress.Xpf.Docking.BaseLayoutItem[])">
      <summary>
        <para>Adds multiple <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/>s as children to the current <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/>.</para>
      </summary>
      <param name="items"><see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/>s to be added to the current <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/>.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.AddRange(DevExpress.Xpf.Docking.BaseLayoutItem[])">
      <summary>
        <para>Adds the specified array of items to the group.</para>
      </summary>
      <param name="items">An array of items to be added to the group.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.AllowExpand">
      <summary>
        <para>Gets or sets whether a group can be expanded/collapsed.
This is a dependency property.</para>
      </summary>
      <value>true if a group can be expanded/collapsed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.AllowExpandProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.AllowExpand"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.AllowSplitters">
      <summary>
        <para>Gets or sets whether item resizing is enabled for the group&#39;s children.
This is a dependency property.</para>
      </summary>
      <value>A Nullable Boolean value that specifies whether item resizing is enabled.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.AllowSplittersProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.AllowSplitters"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.AutoScrollOnOverflow">
      <summary>
        <para>Gets or sets how the tab headers are scrolled while selecting tabs when tab headers could not fit into the header panel. This is a dependency property.</para>
      </summary>
      <value>Any of the <see cref="T:DevExpress.Xpf.Docking.AutoScrollOnOverflow"/> enumeration values.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.AutoScrollOnOverflowProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.AutoScrollOnOverflow"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.BeginInit">
      <summary>
        <para>Starts the <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/>&#39;s initialization. Initialization occurs at runtime.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.CaptionOrientation">
      <summary>
        <para>Gets or sets the orientation of the group&#39;s header.
This is a dependency property.</para>
      </summary>
      <value>An Orientation value that specifies the orientation of the group&#39;s header.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.CaptionOrientationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.CaptionOrientation"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Clear">
      <summary>
        <para>Removes child items from the group.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ControlItemsHost">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code. This is a dependency property.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ControlItemsHostProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ControlItemsHost"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.DestroyContentOnTabSwitching">
      <summary>
        <para>Gets or sets if a tab item&#39;s content is destroyed when another tab item is selected. This is a dependency property.</para>
      </summary>
      <value>true, if a tab item&#39;s content is destroyed when another tab item is selected; otherwise, false. The default value is true.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.DestroyContentOnTabSwitchingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.DestroyContentOnTabSwitching"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.DestroyOnClosingChildren">
      <summary>
        <para>Gets or sets whether the current group is destroyed when removing all its children.
This is a dependency property.</para>
      </summary>
      <value>true if the current group is destroyed when removing all its children; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.DestroyOnClosingChildrenProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.DestroyOnClosingChildren"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.DockItemInterval">
      <summary>
        <para>Gets or sets the distance between immediate child dock items.
This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the distance between immediate child dock items.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.DockItemIntervalProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.DockItemInterval"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.EndInit">
      <summary>
        <para>Ends the <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/>&#39;s initialization.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.Expanded">
      <summary>
        <para>Gets or sets whether the group is expanded.
This is a dependency property.</para>
      </summary>
      <value>true if the group is expanded; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ExpandedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.Expanded"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.FixedMultiLineTabHeaders">
      <summary>
        <para>Gets or sets whether, if tabs are displayed in multiple rows, the currently selected tab&#39;s row always holds its initial position, or automatically moves to the bottom.</para>
      </summary>
      <value>true, if the currently selected tab&#39;s row always holds its initial position; otherwise, false. The default is false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.FixedMultiLineTabHeadersProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.FixedMultiLineTabHeaders"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.GetChildrenCount">
      <summary>
        <para>Gets the number of all nested child items.</para>
      </summary>
      <returns>An integer value that specifies the total number of nested child items owned by the LayoutGroup.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.GroupBorderStyle">
      <summary>
        <para>Gets or sets the group&#39;s border style. This option is in effect when the LayoutGroup is used to combine layout items, rather than dock items.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.GroupBorderStyle"/> value that specifies the group&#39;s border style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.GroupBorderStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.GroupBorderStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.GroupTemplate">
      <summary>
        <para>Gets or sets the template used to render the LayoutGroup.
This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Windows.DataTemplate"/>A data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.GroupTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.GroupTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.GroupTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses the group&#39;s template based on custom logic.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.GroupTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.GroupTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.HasAccent">
      <summary>
        <para>Gets or sets whether the group is marked with a special flag (has a special accent) that makes the group painted with different outer indents.
This is a dependency property.</para>
      </summary>
      <value>A Nullable Boolean type that specifies whether the group is marked with a special flag that affects its painting.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.HasAccentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.HasAccent"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.HasNotCollapsedItemsProperty">
      <summary>
        <para>For internal use only.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.HasSingleItem">
      <summary>
        <para>Gets whether the group owns a single item.
This is a dependency property.</para>
      </summary>
      <value>A Boolean value that specifies whether the group owns a single item.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.HasSingleItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.HasSingleItem"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.HasVisibleItems">
      <summary>
        <para>Gets whether the current group contains items whose Visibility property is set to Visibility.Visible.
This is a dependency property.</para>
      </summary>
      <value>true if the current group contains items whose Visibility property is set to Visibility.Visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.HasVisibleItemsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.HasVisibleItems"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Insert(System.Int32,DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Inserts the specified item at the specified position.</para>
      </summary>
      <param name="index">An integer value that specifies the postion at which the item is inserted.</param>
      <param name="item">A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be inserted.</param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.IsAnimated">
      <summary>
        <para>Gets whether an animation is in progress.
This is a dependency property.</para>
      </summary>
      <value>true if an animation is in progress; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.IsAnimatedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.IsAnimated"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.IsExpanded">
      <summary>
        <para>Gets whether the group is actually expanded.
This is a dependency property.</para>
      </summary>
      <value>true if the group is actually expanded; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.IsExpandedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.IsExpanded"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.IsLayoutRoot">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.IsLayoutRootProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.IsLayoutRoot"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.IsScrollNextButtonVisible">
      <summary>
        <para>Gets whether the Scroll Next button is visible.</para>
      </summary>
      <value>true if the Scroll Next button is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.IsScrollPrevButtonVisible">
      <summary>
        <para>Gets whether the Scroll Prev button is visible.</para>
      </summary>
      <value>true if the Scroll Prev button is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.IsSplittersEnabled">
      <summary>
        <para>Gets whether item resizing is actually currently enabled for the LayoutGroup&#39;s children.
This is a dependency property.</para>
      </summary>
      <value>true if item resizing is actually currently enabled for the LayoutGroup&#39;s children; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.IsSplittersEnabledProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.IsSplittersEnabled"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.Item(System.Int32)">
      <summary>
        <para>Provides indexed access to items in the collection.</para>
      </summary>
      <param name="index">An integer value that specifies the index of the item to be returned.</param>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object at the specified index.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.Item(System.String)">
      <summary>
        <para>Provides access to items in the collection by name.</para>
      </summary>
      <param name="name">A string value that species the item&#39;s name. This value matches the Name property value of the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object.</param>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object representing the item with the specified name.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemCaptionTemplate">
      <summary>
        <para>Gets or sets the template used to visualize captions of objects stored as elements in the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSource"/> collection.
This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object that specifies the corresponding template.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemCaptionTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemCaptionTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemCaptionTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize captions of objects stored as elements in the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSource"/> collection. This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemCaptionTemplate"/> based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemCaptionTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemCaptionTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemContentTemplate">
      <summary>
        <para>Gets or sets the template used to visualize contents of objects stored as elements in the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsSource"/> collection.
This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object that specifies the corresponding template.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemContentTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemContentTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemContentTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize contents of objects stored as elements in the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsSource"/> collection. This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemContentTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemContentTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.Items">
      <summary>
        <para>Provides access to child items.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItemCollection"/> object that contains child items.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsAppearance">
      <summary>
        <para>Gets or sets the settings used to customize the appearance of captions for the group&#39;s children.
This is a dependency property.</para>
      </summary>
      <value>A DevExpress.Xpf.Docking.Appearance object that specifies the corresponding appearance settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemsAppearanceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsAppearance"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsInternal">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize groups, panels and layout items for the current <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> container. This is a dependency property.</para>
      </summary>
      <value>A source of objects to be visualized as panels, groups and layout items.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemsSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsSource"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemStyle">
      <summary>
        <para>Gets or sets the style applied to items within a specified <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> container.
This is a dependency property.</para>
      </summary>
      <value>A Style object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemTemplate">
      <summary>
        <para>Gets or sets a DataTemplate used to render items stored in the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsSource"/> collection.</para>
      </summary>
      <value>A DataTemplate used to render items in the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsSource"/> collection.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemTemplateSelector">
      <summary>
        <para>Gets or sets a DataTemplateSelector object that provides a way to choose a DataTemplate to render items stored in the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsSource"/> collection.</para>
      </summary>
      <value>A DataTemplateSelector object that provides a way to choose a DataTemplate to render items stored in the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsSource"/> collection.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemTemplateSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.LastChildFill">
      <summary>
        <para>Gets or sets whether the last <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/>&#39;s child should stretch to occupy the whole group. This is a dependency property.</para>
      </summary>
      <value>true, if the last <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/>&#39;s child should stretch to occupy the whole group; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.LastChildFillProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.LastChildFill"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.LayoutGroup.LayoutChanged">
      <summary>
        <para>Fires when the layout of items within the current group is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.LayoutGroupInterval">
      <summary>
        <para>Gets or sets the distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> objects.
This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> objects.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.LayoutGroupIntervalProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.LayoutGroupInterval"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.LayoutItemInterval">
      <summary>
        <para>Gets or sets the distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> objects.
This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> objects.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.LayoutItemIntervalProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.LayoutItemInterval"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.Orientation">
      <summary>
        <para>Gets or sets whether child items are arranged horizontally or vertically within the group.
This is a dependency property.</para>
      </summary>
      <value>An <see cref="T:System.Windows.Controls.Orientation"/> value that specifies whether child items are arranged horizontally or vertically within the group.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.OrientationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.Orientation"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Remove(DevExpress.Xpf.Docking.BaseLayoutItem)">
      <summary>
        <para>Removes the specified item from the collection.</para>
      </summary>
      <param name="item">The item to be removed from the collection.</param>
      <returns>true if the item is successfully removed; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.ScrollNext">
      <summary>
        <para>Scrolls forward through tab headers corresponding to the group&#39;s child items. This property is in effect if the current group represents child items as tabs.</para>
      </summary>
      <returns>true if the tab headers have been scrolled; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutGroup.ScrollPrev">
      <summary>
        <para>Scrolls backward through tab headers corresponding to the group&#39;s child items. This property is in effect if the current group represents child items as tabs.</para>
      </summary>
      <returns>true if the tab headers have been scrolled; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.SelectedItem">
      <summary>
        <para>Gets the selected child item within the current group (mostly, this property is used when the current group represents child items as tabs).
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the currently selected tab.</value>
    </member>
    <member name="E:DevExpress.Xpf.Docking.LayoutGroup.SelectedItemChanged">
      <summary>
        <para>Fires when a new child item is selected within the current group. This event is supported for LayoutGroups representing its children as tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.SelectedItemChangedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.LayoutGroup.SelectedItemChanged"/> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.SelectedItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.SelectedItem"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.SelectedTabIndex">
      <summary>
        <para>Gets or sets the index of the active child item. This property is supported for groups representing its children as tabs.
This is a dependency property.</para>
      </summary>
      <value>An integer value that represents the index of the active tab.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.SelectedTabIndexProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.SelectedTabIndex"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.SelectionOnTabRemoval">
      <summary>
        <para>Gets or sets which tab item should be selected after deleting (closing) the current tab item. This is a dependency property.</para>
      </summary>
      <value>Any of the <see cref="T:DevExpress.Xpf.Docking.SelectionOnTabRemoval"/> enumeration values.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.SelectionOnTabRemovalProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.SelectionOnTabRemoval"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.SerializableSelectedTabPageIndex">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ShowScrollNextButton">
      <summary>
        <para>Allows you to hide the Scroll Next button within the group&#39;s header. This property is in effect if the scroll buttons have been enabled via the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderLayoutType"/> property.</para>
      </summary>
      <value>true if the Scroll Next button is visible within the group&#39;s header; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ShowScrollPrevButton">
      <summary>
        <para>Allows you to hide the Scroll Prev button within the group&#39;s header. This property is in effect if the scroll buttons have been enabled via the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderLayoutType"/> property.</para>
      </summary>
      <value>true if the Scroll Prev button is visible within the group&#39;s header; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ShowTabHeaders">
      <summary>
        <para>Gets or sets whether tab headers are shown. This is a dependency property.</para>
      </summary>
      <value>true, if tab headers are shown; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ShowTabHeadersProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ShowTabHeaders"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabContentCacheMode">
      <summary>
        <para>Gets or sets whether tab contents are cached all at once when the group is displayed, each tab content is cached when the tab is selected, or tab contents are not cached. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Core.TabContentCacheMode"/> enumeration value that specifies how tab contents are cached. The default is None.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabContentCacheModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabContentCacheMode"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderCanScrollNext">
      <summary>
        <para>Gets whether it&#39;s possible to scroll forward through tab headers corresponding to the group&#39;s child items. This property is in effect if the current group represents child items as tabs.
This is a dependency property.</para>
      </summary>
      <value>true if scrolling forward through tab headers is possible.; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderCanScrollNextProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderCanScrollNext"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderCanScrollPrev">
      <summary>
        <para>Gets whether it&#39;s possible to scroll backward through tab headers corresponding to the group&#39;s child items. This property is in effect if the current group represents child items as tabs.This is a dependency property.</para>
      </summary>
      <value>true if scrolling backward through tab headers is possible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderCanScrollPrevProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderCanScrollPrev"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderHasScroll">
      <summary>
        <para>Gets whether the group&#39;s header displays scroll buttons used to scroll through the tab headers corresponding to the group&#39;s child items. This property is in effect if the current group represents child items as tabs.
This is a dependency property.</para>
      </summary>
      <value>true if the group&#39;s header displays scroll buttons used to scroll through the tab headers corresponding to the group&#39;s child items; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderHasScrollProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderHasScroll"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderLayoutType">
      <summary>
        <para>Gets or sets how the LayoutGroup displays the tab headers corresponding to the group&#39;s child items. This property is in effect if the current group represents child items as tabs.
This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Layout.Core.TabHeaderLayoutType"/> value that specifies how the LayoutGroup displays the tab headers corresponding to the group&#39;s child items.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderLayoutTypeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderLayoutType"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderMaxScrollIndex">
      <summary>
        <para>Gets the maximum tab header scroll position. This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>An integer value that specifies the maximum tab header scroll position.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderMaxScrollIndexProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderMaxScrollIndex"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeadersAutoFill">
      <summary>
        <para>Gets or sets whether the tab headers, corresponding to the group&#39;s child items, must be automatically stretched to fill the empty space in a tab row. This property is in effect if the current group represents child items as tabs.
This is a dependency property.</para>
      </summary>
      <value>A Boolean value which specifies whether the tab headers must be automatically stretched to fill the empty space in a tab row.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeadersAutoFillProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeadersAutoFill"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderScrollIndex">
      <summary>
        <para>Gets the index that defines the tab header scroll position. This member supports the internal infrastructure, and is not intended to be used directly from your code.
This is a dependency property.</para>
      </summary>
      <value>An integer value that specifies the tab header scroll position.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderScrollIndexProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderScrollIndex"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabItemContainerStyle">
      <summary>
        <para>Gets or sets a style applied to the part of the <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> containing tab headers. This is a dependency property.</para>
      </summary>
      <value>A Style object applied to the part of the <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> containing tab headers.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabItemContainerStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabItemContainerStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabItemContainerStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a style applied to the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabItemContainerStyle"/> property. This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.StyleSelector descendant that applies a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabItemContainerStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabItemContainerStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.VisiblePages">
      <summary>
        <para>Gets the collection of visible child items that are displayed in the current group, as tabs.</para>
      </summary>
      <value>A collection of visible tab child items.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutGroup.VisiblePagesCount">
      <summary>
        <para>Gets the number of items in the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.VisiblePages"/> collection.
This is a dependency property.</para>
      </summary>
      <value>An integer value that specifies the number of items in the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.VisiblePages"/> collection.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutGroup.VisiblePagesCountProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.VisiblePagesCount"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.LayoutPanel">
      <summary>
        <para>Represents a dock panel.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutPanel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ActualTabBackgroundColor">
      <summary>
        <para>Gets the current color of the <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/>&#39;s tab header.</para>
      </summary>
      <value>A Color value that is the current color of the <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/>&#39;s tab header.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.ActualTabBackgroundColorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.ActualTabBackgroundColor"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.AllowDockToDocumentGroup">
      <summary>
        <para>Gets or sets if the current <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> can be docked to <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>s. This is a dependency property.</para>
      </summary>
      <value>true if the current <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> can be docked to <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>s; otherwise, false. Default is true.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.AllowDockToDocumentGroupProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.AllowDockToDocumentGroup"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.AutoHidden">
      <summary>
        <para>Gets or sets whether the panel is currently auto-hidden. This is a dependency property.</para>
      </summary>
      <value>true, if the panel is currently auto-hidden; otherwise, false. The default is false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.AutoHiddenProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.AutoHidden"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.AutoHideExpandState">
      <summary>
        <para>Gets or sets whether the current auto-hidden panel is hidden, visible or expanded. This is a dependency property.</para>
      </summary>
      <value>An DevExpress.Xpf.Docking.Base.AutoHideExpandState enumeration value that specifies whether the current auto-hidden panel is hidden, visible or expanded.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.AutoHideExpandStateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.AutoHideExpandState"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ContentPresenter">
      <summary>
        <para>Retrieves the content presenter associated with the current <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> object.
This is a dependency property.</para>
      </summary>
      <value>A UIElement that is the content presenter of this panel.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.ContentPresenterProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.ContentPresenter"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.Control">
      <summary>
        <para>Gets a UIElement that has been assigned to the <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/> property.
This is a dependency property.</para>
      </summary>
      <value>The UIElement object that has been assigned to the <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/> property. null if any other object has been assigned to the <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/> property.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.ControlProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.Control"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.DockItemState">
      <summary>
        <para>Gets whether the panel is docked, auto-hidden, floating, or closed. This is a dependency property.</para>
      </summary>
      <value>A DevExpress.Xpf.Docking.DockItemState enumeration value that specifies whether the panel is docked, auto-hidden, floating, or closed.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.DockItemStateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.DockItemState"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.FloatState">
      <summary>
        <para>Gets whether the panel is in the normal, maximized or minimized state. This is a dependency property.</para>
      </summary>
      <value>A DevExpress.Xpf.Docking.FloatState enumeration value that specifies whether the panel is in the normal, maximized or minimized state.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.FloatStateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.FloatState"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutPanel.GetMDIMergeStyle(System.Windows.DependencyObject)">
      <summary>
        <para></para>
      </summary>
      <param name="target"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.HasBorder">
      <summary>
        <para>Gets whether the panel&#39;s border is visible.
This is a dependency property.</para>
      </summary>
      <value>true if the panel&#39;s border is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.HasBorderProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.HasBorder"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.HorizontalScrollBarVisibility">
      <summary>
        <para>Gets or sets a horizontal scroll bar&#39;s visibility mode. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.ScrollBarVisibility"/> value that specifies the scroll bar&#39;s visibility.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.HorizontalScrollBarVisibilityProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.HorizontalScrollBarVisibility"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.IsCollapseButtonVisibleProperty">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.IsExpandButtonVisibleProperty">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.IsHideButtonVisibleProperty">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.IsMaximized">
      <summary>
        <para>Gets whether the <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> is maximized. This property is in effect for floating panels.</para>
      </summary>
      <value>true, if the <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> is maximized; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.IsMaximizedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.IsMaximized"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.IsMinimized">
      <summary>
        <para>Gets whether a layout panel is minimized. This is a dependency property.</para>
      </summary>
      <value>true, if a layout panel is minimized; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.IsMinimizedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.IsMinimized"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.IsPinButtonVisible">
      <summary>
        <para>Gets whether the Pin button is visible.</para>
      </summary>
      <value>true if the Pin button is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.Layout">
      <summary>
        <para>Gets a <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> object that has been assigned to the <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/> property.
This is a dependency property.</para>
      </summary>
      <value>The  <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> object that has been assigned to the <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/> property. null if any other object has been assigned to the <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/> property.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.LayoutProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.Layout"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.MDIMergeStyle">
      <summary>
        <para>Gets or sets if and when the merging mechanism is invoked for the current panel.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Xpf.Bars.MDIMergeStyle"/> enumerator value that specifies if and when the merge mechanism is invoked for the current panel.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.MDIMergeStyleProperty">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.SerializableFloatingBounds">
      <summary>
        <para>Gets or sets the position and size of a floating <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> after it had been closed.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Rect"/> structure that stores the position and size of a floating <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> after it had been closed.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.SerializableFloatingOffset">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.SerializableIsMaximized">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutPanel.SetMDIMergeStyle(System.Windows.DependencyObject,DevExpress.Xpf.Bars.MDIMergeStyle)">
      <summary>
        <para></para>
      </summary>
      <param name="target"></param>
      <param name="value"></param>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ShowBorder">
      <summary>
        <para>Gets or sets whether the panel&#39;s border is visible.
This is a dependency property.</para>
      </summary>
      <value>true if the panel&#39;s border is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.ShowBorderProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.ShowBorder"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ShowCollapseButton">
      <summary>
        <para>Gets or sets whether the Collapse button is displayed when the current panel is auto-hidden. This property is in effect in the In-line auto-hide mode. This is a dependency property.</para>
      </summary>
      <value>true, if the Collapse button is displayed when the current panel is auto-hidden; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.ShowCollapseButtonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.ShowCollapseButton"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ShowExpandButton">
      <summary>
        <para>Gets or sets whether the Expand button is displayed when the current panel is auto-hidden. This property is in effect in the In-line auto-hide mode. This is a dependency property.</para>
      </summary>
      <value>true, if the Expand button is displayed when the current panel is auto-hidden; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.ShowExpandButtonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.ShowExpandButton"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ShowHideButton">
      <summary>
        <para>Gets or sets whether the Hide button is displayed when the current panel is auto-hidden. This property is in effect in the In-line auto-hide mode. This is a dependency property.</para>
      </summary>
      <value>true, if the Hide button is displayed when the current panel is auto-hidden; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.ShowHideButtonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.ShowHideButton"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ShowInDocumentSelector">
      <summary>
        <para>Gets or sets whether the current panel is listed in the Document Selector.</para>
      </summary>
      <value>true, if the current panel is listed in the Document Selector; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.ShowInDocumentSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.ShowInDocumentSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ShowMaximizeButton">
      <summary>
        <para>Gets or sets whether the maximize button is shown within the <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/>. This property is supported for floating panels.</para>
      </summary>
      <value>true, if the maximize button is displayed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ShowMinimizeButton">
      <summary>
        <para>Gets or sets whether the minimize button is shown in the <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/>. This property is supported for floating panels. This is a dependency property.</para>
      </summary>
      <value>true, if the minimize button is shown; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ShowPinButton">
      <summary>
        <para>Gets or sets whether the Pin button is visible.</para>
      </summary>
      <value>true if the Pin button is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ShowRestoreButton">
      <summary>
        <para>Gets of sets whether the restore button is displayed within the <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/>. This property is supported for floating panels.</para>
      </summary>
      <value>true, if the restore button is displayed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.TabBackgroundColor">
      <summary>
        <para>Gets or sets the background color for the <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/>&#39;s tab header.</para>
      </summary>
      <value>A Color value that is the background color for the <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/>&#39;s tab header.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.TabBackgroundColorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.TabBackgroundColor"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.Uri">
      <summary>
        <para>Gets a System.Uri object used to assign the current <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/>&#39;s content. This is a dependency property.</para>
      </summary>
      <value>A System.Uri object used to assign the current <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/>&#39;s content.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.UriProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.Uri"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutPanel.VerticalScrollBarVisibility">
      <summary>
        <para>Gets or sets a vertical scroll bar&#39;s visibility mode. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.ScrollBarVisibility"/> value that specifies the scroll bar&#39;s visibility.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutPanel.VerticalScrollBarVisibilityProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutPanel.VerticalScrollBarVisibility"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.LayoutSplitter">
      <summary>
        <para>Provides runtime item resizing.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.LayoutSplitter.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.LayoutSplitter"/> class with the specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.LayoutSplitter.Orientation">
      <summary>
        <para>Gets a <see cref="T:DevExpress.Xpf.Docking.LayoutSplitter"/>&#39;s orientation. This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Control.Orientation enumerator value that specifies a <see cref="T:DevExpress.Xpf.Docking.LayoutSplitter"/>&#39;s orientation.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LayoutSplitter.OrientationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutSplitter.Orientation"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.LogicalTreeStructure">
      <summary>
        <para>Lists values that specify the logical tree structure.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LogicalTreeStructure.Default">
      <summary>
        <para>The <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> stores its logical tree of layout items as a tree that reflects the actual item hierarchy.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.LogicalTreeStructure.Optimized">
      <summary>
        <para>The <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> stores its layout panels as logical children.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.MDIState">
      <summary>
        <para>Enumerates available states for panels in a <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> in MDI mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.MDIState.Maximized">
      <summary>
        <para>A child MDI window is maximized.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.MDIState.Minimized">
      <summary>
        <para>A child MDI window is minimized.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.MDIState.Normal">
      <summary>
        <para>A child MDI window is in its normal state.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.MDIStyle">
      <summary>
        <para>Contains values that specify how a <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> represents its children.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.MDIStyle.Default">
      <summary>
        <para>The same option as <see cref="F:DevExpress.Xpf.Docking.MDIStyle.Tabbed"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.MDIStyle.MDI">
      <summary>
        <para>A DocumentGroup&#39;s children are represented as floating windows, that can float within the DocumentGroup&#39;s boundaries.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.MDIStyle.Tabbed">
      <summary>
        <para>A DocumentGroup is rendered as a tab container, where children are represented as tabs.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.RestoreLayoutOptions">
      <summary>
        <para>Contains options that control the restoration of dock and layout items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.RestoreLayoutOptions"/> class.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutControlItemsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutControlItems"/> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutGroupsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutGroups"/> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewPanelsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewPanels"/> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.RestoreLayoutOptions.DockLayoutManagerRestoreOffsetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.DockLayoutManagerRestoreOffset"/> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.RestoreLayoutOptions.FloatPanelsRestoreOffsetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.FloatPanelsRestoreOffset"/> dependency property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.GetAddNewLayoutControlItems(System.Windows.DependencyObject)">
      <summary>
        <para>Returns the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutControlItems"/> property value for a specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutControlItems"/> property value should be returned.</param>
      <returns>A Boolean value specifying the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutControlItems"/> property value assigned to the specified DependencyObject. null (Nothing in VB) if no value is assigned.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.GetAddNewLayoutGroups(System.Windows.DependencyObject)">
      <summary>
        <para>Returns the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutGroups"/> property value for a specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutGroups"/> property value should be returned.</param>
      <returns>A Boolean value specifying the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutGroups"/> property value assigned to the specified DependencyObject. null (Nothing in VB) if no value is assigned.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.GetAddNewPanels(System.Windows.DependencyObject)">
      <summary>
        <para>Returns the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewPanels"/> property value for a specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewPanels"/> property value should be returned.</param>
      <returns>A Boolean value specifying the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewPanels"/> property value assigned to the specified DependencyObject. null (Nothing in VB) if no value is assigned.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.GetDockLayoutManagerRestoreOffset(System.Windows.DependencyObject)">
      <summary>
        <para>Returns the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.DockLayoutManagerRestoreOffset"/> property value for a specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.DockLayoutManagerRestoreOffset"/> property value should be returned.</param>
      <returns>A Point structure specifying the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.DockLayoutManagerRestoreOffset"/> property value assigned to the specified DependencyObject. null (Nothing in VB) if no value is assigned.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.GetFloatPanelsRestoreOffset(System.Windows.DependencyObject)">
      <summary>
        <para>Returns the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.FloatPanelsRestoreOffset"/> property value for a specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.FloatPanelsRestoreOffset"/> property value should be returned.</param>
      <returns>A Point structure specifying the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.FloatPanelsRestoreOffset"/> property value assigned to the specified DependencyObject. null (Nothing in VB) if no value is assigned.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.GetRemoveOldLayoutControlItems(System.Windows.DependencyObject)">
      <summary>
        <para>Returns the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutControlItems"/> property value for a specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutControlItems"/> property value should be returned.</param>
      <returns>A Boolean value specifying the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutControlItems"/> property value assigned to the specified DependencyObject. null (Nothing in VB) if no value is assigned.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.GetRemoveOldLayoutGroups(System.Windows.DependencyObject)">
      <summary>
        <para>Returns the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutGroups"/> property value for a specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutGroups"/> property value should be returned.</param>
      <returns>A Boolean value specifying the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutGroups"/> property value assigned to the specified DependencyObject. null (Nothing in VB) if no value is assigned.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.GetRemoveOldPanels(System.Windows.DependencyObject)">
      <summary>
        <para>Returns the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldPanels"/> property value for a specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldPanels"/> property value should be returned.</param>
      <returns>A Boolean value specifying the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldPanels"/> property value assigned to the specified DependencyObject. null (Nothing in VB) if no value is assigned.</returns>
    </member>
    <member name="F:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutControlItemsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutControlItems"/> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutGroupsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutGroups"/> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldPanelsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldPanels"/> dependency property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.SetAddNewLayoutControlItems(System.Windows.DependencyObject,System.Boolean)">
      <summary>
        <para>Sets a new <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutControlItems"/> property value for the specific object.</para>
      </summary>
      <param name="obj">A DependencyObject that will receive a new <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutControlItems"/> property value.</param>
      <param name="value">A Boolean value assigned to the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutControlItems"/> property.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.SetAddNewLayoutGroups(System.Windows.DependencyObject,System.Boolean)">
      <summary>
        <para>Sets a new <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutGroups"/> property value for the specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutGroups"/> property is to be set.</param>
      <param name="value">A Boolean value assigned to the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewLayoutGroups"/> property.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.SetAddNewPanels(System.Windows.DependencyObject,System.Boolean)">
      <summary>
        <para>Sets a new <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewPanels"/> property value for the specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewPanels"/> property is to be set.</param>
      <param name="value">A Boolean value assigned to the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.AddNewPanels"/> property.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.SetDockLayoutManagerRestoreOffset(System.Windows.DependencyObject,System.Windows.Point)">
      <summary>
        <para>Sets a new <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.DockLayoutManagerRestoreOffset"/> property value for the specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.DockLayoutManagerRestoreOffset"/> property is to be set.</param>
      <param name="value">A Boolean value assigned to the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.DockLayoutManagerRestoreOffset"/> property.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.SetFloatPanelsRestoreOffset(System.Windows.DependencyObject,System.Windows.Point)">
      <summary>
        <para>Sets a new <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.FloatPanelsRestoreOffset"/> property value for the specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.FloatPanelsRestoreOffset"/> property is to be set.</param>
      <param name="value">A Boolean value assigned to the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.FloatPanelsRestoreOffset"/> property.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.SetRemoveOldLayoutControlItems(System.Windows.DependencyObject,System.Boolean)">
      <summary>
        <para>Sets a new <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutControlItems"/> property value for the specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutControlItems"/> property is to be set.</param>
      <param name="value">A Boolean value assigned to the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutControlItems"/> property.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.SetRemoveOldLayoutGroups(System.Windows.DependencyObject,System.Boolean)">
      <summary>
        <para>Sets a new <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutGroups"/> property value for the specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutGroups"/> property is to be set.</param>
      <param name="value">A Boolean value assigned to the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldLayoutGroups"/> property.</param>
    </member>
    <member name="M:DevExpress.Xpf.Docking.RestoreLayoutOptions.SetRemoveOldPanels(System.Windows.DependencyObject,System.Boolean)">
      <summary>
        <para>Sets a new <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldPanels"/> property value for the specific object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldPanels"/> property is to be set.</param>
      <param name="value">A Boolean value assigned to the <see cref="P:DevExpress.Xpf.Docking.RestoreLayoutOptions.RemoveOldPanels"/> property.</param>
    </member>
    <member name="T:DevExpress.Xpf.Docking.SelectionOnTabRemoval">
      <summary>
        <para>Lists the values that specify which tab is selected when closing the current tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.SelectionOnTabRemoval.Following">
      <summary>
        <para>The following tab is selected when the current tab is closed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.SelectionOnTabRemoval.Preceding">
      <summary>
        <para>The preceding tab is selected when the current tab is closed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.SelectionOnTabRemoval.PreviousSelection">
      <summary>
        <para>The previously selected tab is selected when the current tab is closed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Docking.SeparatorItem">
      <summary>
        <para>A visual separator between neighboring items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.SeparatorItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.SeparatorItem"/> class with the specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.SeparatorItem.Orientation">
      <summary>
        <para>Gets a <see cref="T:DevExpress.Xpf.Docking.SeparatorItem"/>&#39;s orientation. This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Control.Orientation enumerator value that specifies a <see cref="T:DevExpress.Xpf.Docking.SeparatorItem"/>&#39;s orientation.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.SeparatorItem.OrientationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.SeparatorItem.Orientation"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.TabbedDocumentUIService">
      <summary>
        <para>Allows you to show documents in tabbed <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/>s.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.TabbedDocumentUIService.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.TabbedDocumentUIService"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.TabbedDocumentUIService.ActualDocumentGroup">
      <summary>
        <para>Gets the actual <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> associated with the service.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> object that represents the <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> associated with the service.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.TabbedDocumentUIService.DocumentGroup">
      <summary>
        <para>Gets or sets a <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> that is used for representing documents. This is a dependency property.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> object that is used for representing documents.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.TabbedDocumentUIService.DocumentGroupProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.TabbedDocumentUIService.DocumentGroup"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.TabbedDocumentUIService.DocumentPanelStyle">
      <summary>
        <para>Gets or sets the style applied to a document&#39;s container (a <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> object). This is a dependency property.</para>
      </summary>
      <value>A Style object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.TabbedDocumentUIService.DocumentPanelStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.TabbedDocumentUIService.DocumentPanelStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.TabbedDocumentUIService.DocumentPanelStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a style applied to a document&#39;s container (a <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> object) . This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.StyleSelector descendant that applies a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.TabbedDocumentUIService.DocumentPanelStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.TabbedDocumentUIService.DocumentPanelStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.TabbedDocumentUIService.UseActiveDocumentGroupAsDocumentHost">
      <summary>
        <para>Specifies whether the active DocumentGroup should be used as a document host. This is a dependency property.</para>
      </summary>
      <value>true, if the active DocumentGroup should be used as a document host; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.TabbedDocumentUIService.UseActiveDocumentGroupAsDocumentHostProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.TabbedDocumentUIService.UseActiveDocumentGroupAsDocumentHost"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.TabbedGroup">
      <summary>
        <para>Represents a tabbed group of dock panels (<see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> objects).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Docking.TabbedGroup.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Docking.TabbedGroup"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Docking.TabbedGroup.FloatState">
      <summary>
        <para>Gets whether the group is in the normal, maximized or minimized state. This is a dependency property.</para>
      </summary>
      <value>A DevExpress.Xpf.Docking.FloatState enumeration value that specifies whether the group is in the normal, maximized or minimized state.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.TabbedGroup.FloatStateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.TabbedGroup.FloatState"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.TabbedGroup.IsMaximized">
      <summary>
        <para>Gets whether the current group is maximized. This is a dependency property.</para>
      </summary>
      <value>true, if the current group is maximized; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.TabbedGroup.IsMaximizedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.TabbedGroup.IsMaximized"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.TabbedGroup.SerializableIsMaximized">
      <summary>
        <para>Gets whether the current group is maximized.</para>
      </summary>
      <value>true, if the current group is maximized; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Docking.TabbedGroup.ShowTabForSinglePage">
      <summary>
        <para>Gets or sets whether the only dock panel within the <see cref="T:DevExpress.Xpf.Docking.TabbedGroup"/> should display its tab.</para>
      </summary>
      <value>true if the only dock panel within the <see cref="T:DevExpress.Xpf.Docking.TabbedGroup"/> should display its tab; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Docking.TabbedGroup.ShowTabForSinglePageProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.TabbedGroup.ShowTabForSinglePage"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Docking.TabbedGroupDisplayMode">
      <summary>
        <para>Lists the <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>&#39;s displaying modes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.TabbedGroupDisplayMode.ContentOnly">
      <summary>
        <para>Hides <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> headers and borders.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Docking.TabbedGroupDisplayMode.Default">
      <summary>
        <para>Displays DocumentPanel headers and borders.</para>
      </summary>
    </member>
  </members>
</doc>