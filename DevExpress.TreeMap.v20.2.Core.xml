<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.TreeMap.v20.2.Core</name>
  </assembly>
  <members>
    <member name="N:DevExpress.TreeMap">
      <summary>
        <para>Contains all common classes that WinForms TreeMap Control, WPF TreeMap Control, and WinForms Sunburst Control require to function.</para>
      </summary>
    </member>
    <member name="T:DevExpress.TreeMap.IHierarchicalItem">
      <summary>
        <para>The base interface for the treemap and sunburst items.</para>
      </summary>
    </member>
    <member name="P:DevExpress.TreeMap.IHierarchicalItem.Children">
      <summary>
        <para>Returns an item&#39;s child items.</para>
      </summary>
      <value>A collection of items that implement the IHierarchicalItem interface.</value>
    </member>
    <member name="P:DevExpress.TreeMap.IHierarchicalItem.DataSourceColor">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.TreeMap.IHierarchicalItem.Label">
      <summary>
        <para>Returns a label for the item.</para>
      </summary>
      <value>An object that represents the item label.</value>
    </member>
    <member name="P:DevExpress.TreeMap.IHierarchicalItem.Tag">
      <summary>
        <para>Returns the object that contains data related to the item.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> that contains data about the chart element.</value>
    </member>
    <member name="P:DevExpress.TreeMap.IHierarchicalItem.Value">
      <summary>
        <para>Returns the item value.</para>
      </summary>
      <value>A floating-point number that defines the item value.</value>
    </member>
    <member name="T:DevExpress.TreeMap.ISunburstItem">
      <summary>
        <para>The base interface for sunburst items.</para>
      </summary>
    </member>
    <member name="T:DevExpress.TreeMap.ITreeMapItem">
      <summary>
        <para>The interface that should be implemented by a class which can be used as a tree map item.</para>
      </summary>
    </member>
  </members>
</doc>