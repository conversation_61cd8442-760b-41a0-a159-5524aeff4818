<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.XtraPdfViewer.v20.2</name>
  </assembly>
  <members>
    <member name="N:DevExpress.XtraPdfViewer">
      <summary>
        <para>Contains classes used by the PDF Viewer control in Windows Forms applications.</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraPdfViewer.Commands">
      <summary>
        <para>Contains commands that correspond to end-user actions in a PDF Viewer.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfCheckableCommand">
      <summary>
        <para>A command that corresponds to a fixed value of a specific setting.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfCheckableCommand.CreateContextMenuBarItem(DevExpress.XtraBars.BarManager)">
      <summary>
        <para>Supports the context menu initialization for commands with a fixed value of a specific setting.</para>
      </summary>
      <param name="barManager">A <see cref="T:DevExpress.XtraBars.BarManager"/> that maintains the application bars and individual bar items.</param>
      <returns>A <see cref="T:DevExpress.XtraBars.BarItem"/> element.</returns>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfCheckableCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfCheckableCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfCheckableCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfCheckableCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfCopyCommand">
      <summary>
        <para>Copies the selected document content to the Clipboard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfCopyCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfCopyCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfCopyCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfCopyCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfCopyCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfCopyCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfCopyCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfCopyCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfDeleteAnnotationCommand">
      <summary>
        <para>Deletes a selected annotation.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfDeleteAnnotationCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfDeleteAnnotationCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfDeleteAnnotationCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfDeleteAnnotationCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfDeleteAnnotationCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfDeleteAnnotationCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfEnlargePageThumbnailsCommand">
      <summary>
        <para>Enlarges the page thumbnail size.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfEnlargePageThumbnailsCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfEnlargePageThumbnailsCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfEnlargePageThumbnailsCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfEnlargePageThumbnailsCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfEnlargePageThumbnailsCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfEnlargePageThumbnailsCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfExportFormDataCommand">
      <summary>
        <para>Exports PDF form data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfExportFormDataCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfExportFormDataCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfExportFormDataCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfExportFormDataCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfExportFormDataCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfExportFormDataCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfExportFormDataCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfExportFormDataCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfExportFormDataCommand.UpdateUIState(DevExpress.Utils.Commands.ICommandUIState)">
      <summary>
        <para>Anticipates a command&#39;s execution, by assigning a new status to the command in the context of its current applicability to the application user interface.</para>
      </summary>
      <param name="state">An object implementing the <see cref="T:DevExpress.Utils.Commands.ICommandUIState"/> interface that defines the current status of a command in the overall context of the application user interface.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfFindTextCommand">
      <summary>
        <para>Finds the specified text in a document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfFindTextCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfFindTextCommand"/> class with the specified PDF Viewer.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> object.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfFindTextCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfFindTextCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfFindTextCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfFindTextCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfFindTextCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfFindTextCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfHandToolCommand">
      <summary>
        <para>Activates the Hand Tool to scroll the document by dragging its surface.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfHandToolCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfHandToolCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfHandToolCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfHandToolCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfHandToolCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfHandToolCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfHandToolCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfHandToolCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfImportFormDataCommand">
      <summary>
        <para>Imports PDF form data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfImportFormDataCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfImportFormDataCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfImportFormDataCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfImportFormDataCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfImportFormDataCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfImportFormDataCommand"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfImportFormDataCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfImportFormDataCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfImportFormDataCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfImportFormDataCommand.UpdateUIState(DevExpress.Utils.Commands.ICommandUIState)">
      <summary>
        <para>Anticipates a command&#39;s execution, by assigning a new status to the command in the context of its current applicability to the application user interface.</para>
      </summary>
      <param name="state">An object implementing the <see cref="T:DevExpress.Utils.Commands.ICommandUIState"/> interface that defines the current status of a command in the overall context of the application user interface.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfMarqueeZoomCommand">
      <summary>
        <para>Activates the Marquee Zoom tool that is used to change the zoom level of a document or zoom in a particular area of a page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfMarqueeZoomCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfMarqueeZoomCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfMarqueeZoomCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfMarqueeZoomCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfMarqueeZoomCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfMarqueeZoomCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfMarqueeZoomCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfMarqueeZoomCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfNextPageCommand">
      <summary>
        <para>Navigates to the next document page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfNextPageCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfNextPageCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfNextPageCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfNextPageCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfNextPageCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfNextPageCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfNextPageCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfNextPageCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfNextViewCommand">
      <summary>
        <para>Navigates to the next position in the document, based on the viewing history.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfNextViewCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfNextViewCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfNextViewCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfNextViewCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfNextViewCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfNextViewCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfNextViewCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfNextViewCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfOpenFileCommand">
      <summary>
        <para>Opens a document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfOpenFileCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOpenFileCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOpenFileCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOpenFileCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOpenFileCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOpenFileCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOpenFileCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOpenFileCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCollapseTopLevelCommand">
      <summary>
        <para>Expands or collapses top-level outlines.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCollapseTopLevelCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCollapseTopLevelCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCollapseTopLevelCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCollapseTopLevelCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCollapseTopLevelCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCollapseTopLevelCommand"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCollapseTopLevelCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCollapseTopLevelCommand.UpdateUIState(DevExpress.Utils.Commands.ICommandUIState)">
      <summary>
        <para>Anticipates a command&#39;s execution, by assigning a new status to the command in the context of its current applicability to the application user interface.</para>
      </summary>
      <param name="state">An object implementing the <see cref="T:DevExpress.Utils.Commands.ICommandUIState"/> interface that defines the current status of a command in the overall context of the application user interface.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCurrentCommand">
      <summary>
        <para>Expands current outlines located in the navigation panel of a PDF viewer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCurrentCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCurrentCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCurrentCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCurrentCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCurrentCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCurrentCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesGotoCommand">
      <summary>
        <para>Goes to a bookmark within a PDF document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfOutlinesGotoCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesGotoCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesGotoCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesGotoCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesGotoCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfImportFormDataCommand"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesGotoCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesHideAfterUseCommand">
      <summary>
        <para>Hides the PDF navigation panel after clicking on the bookmark item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfOutlinesHideAfterUseCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesHideAfterUseCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesHideAfterUseCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesHideAfterUseCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesHideAfterUseCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintCommand">
      <summary>
        <para>The base class for the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintSectionsCommand"/> and <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintPagesCommand"/> commands.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintPagesCommand">
      <summary>
        <para>Prints only pages to which selected bookmarks are linked in the document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintPagesCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintPagesCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintPagesCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintPagesCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintPagesCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintSectionsCommand">
      <summary>
        <para>Prints document sections that correspond to selected bookmarks.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintSectionsCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintSectionsCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintSectionsCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintSectionsCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintSectionsCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeCommand">
      <summary>
        <para>Changes node text size within the navigation pane to small, medium or large.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeCommand.CreateContextMenuBarItem(DevExpress.XtraBars.BarManager)">
      <summary>
        <para>Supports the context menu initialization for a command that changes node text size within the navigation pane of a PDF VIewer.</para>
      </summary>
      <param name="barManager">A <see cref="T:DevExpress.XtraBars.BarManager"/> that maintains the application bars and individual bar items.</param>
      <returns>A <see cref="T:DevExpress.XtraBars.BarItem"/> element.</returns>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToLargeCommand">
      <summary>
        <para>Changes the outlines node text size within the navigation page to large.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToLargeCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToLargeCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToLargeCommand.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToLargeCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToMediumCommand">
      <summary>
        <para>Changes the outlines node text size within the navigation pane to medium.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToMediumCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToMediumCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToMediumCommand.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToMediumCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToSmallCommand">
      <summary>
        <para>Changes outlines node text size within the navigation pane to small.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToSmallCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToSmallCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToSmallCommand.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToSmallCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesWrapLongLinesCommand">
      <summary>
        <para>Wraps long lines in the outline node text of the PDF viewer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfOutlinesWrapLongLinesCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesWrapLongLinesCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesWrapLongLinesCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesWrapLongLinesCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesWrapLongLinesCommand"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfOutlinesWrapLongLinesCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfPreviousPageCommand">
      <summary>
        <para>Navigates to the previous document page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfPreviousPageCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfPreviousPageCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfPreviousPageCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfPreviousPageCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfPreviousPageCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfPreviousPageCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfPreviousPageCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfPreviousPageCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfPreviousViewCommand">
      <summary>
        <para>Navigates to the previous position in the document, based on the viewing history.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfPreviousViewCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfPreviousViewCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfPreviousViewCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfPreviousViewCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfPreviousViewCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfPreviousViewCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfPreviousViewCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfPreviousViewCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfPrintFileCommand">
      <summary>
        <para>Runs the system Print dialog to adjust the print settings and send the current document to a printer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfPrintFileCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfPrintFileCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfPrintFileCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfPrintFileCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfPrintFileCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfPrintFileCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfPrintFileCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfPrintFileCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfReducePageThumbnailsCommand">
      <summary>
        <para>Reduces the page thumbnail size.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfReducePageThumbnailsCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfReducePageThumbnailsCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfReducePageThumbnailsCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfReducePageThumbnailsCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfReducePageThumbnailsCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfReducePageThumbnailsCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfRotatePageClockwiseCommand">
      <summary>
        <para>Rotates the document clockwise through 90 degrees.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfRotatePageClockwiseCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfRotatePageClockwiseCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfRotatePageClockwiseCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfRotatePageClockwiseCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfRotatePageClockwiseCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfRotatePageClockwiseCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfRotatePageClockwiseCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfRotatePageClockwiseCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfRotatePageCounterclockwiseCommand">
      <summary>
        <para>Rotates the document counterclockwise through 90 degrees.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfRotatePageCounterclockwiseCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfRotatePageCounterclockwiseCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfRotatePageCounterclockwiseCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfRotatePageCounterclockwiseCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfRotatePageCounterclockwiseCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfRotatePageCounterclockwiseCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfRotatePageCounterclockwiseCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfRotatePageCounterclockwiseCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfSaveAsFileCommand">
      <summary>
        <para>Save the PDF to a new file.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSaveAsFileCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSaveAsFileCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSaveAsFileCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSaveAsFileCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSaveAsFileCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSaveAsFileCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSaveAsFileCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSaveAsFileCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSaveAsFileCommand.UpdateUIState(DevExpress.Utils.Commands.ICommandUIState)">
      <summary>
        <para>Anticipates a command&#39;s execution, by assigning a new status to the command in the context of its current applicability to the application user interface.</para>
      </summary>
      <param name="state">An object implementing the <see cref="T:DevExpress.Utils.Commands.ICommandUIState"/> interface that defines the current status of a command in the overall context of the application user interface.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfSelectAllCommand">
      <summary>
        <para>Selects all content in a document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSelectAllCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSelectAllCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSelectAllCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSelectAllCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSelectAllCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSelectAllCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSelectAllCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSelectAllCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSelectAllCommand.UpdateUIState(DevExpress.Utils.Commands.ICommandUIState)">
      <summary>
        <para>Anticipates a command&#39;s execution, by assigning a new status to the command in the context of its current applicability to the application user interface.</para>
      </summary>
      <param name="state">An object implementing the <see cref="T:DevExpress.Utils.Commands.ICommandUIState"/> interface that defines the current status of a command in the overall context of the application user interface.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfSelectToolCommand">
      <summary>
        <para>Activates the Select Tool to select the document content.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSelectToolCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSelectToolCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSelectToolCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSelectToolCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSelectToolCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSelectToolCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSelectToolCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSelectToolCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfSetActualSizeZoomModeCommand">
      <summary>
        <para>Restores the default zoom factor value, showing the document in its actual size.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSetActualSizeZoomModeCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSetActualSizeZoomModeCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetActualSizeZoomModeCommand.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetActualSizeZoomModeCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfSetFitVisibleZoomModeCommand">
      <summary>
        <para>Adjusts the document zoom factor value to contain the visible part of the page content within the application window&#39;s dimensions</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSetFitVisibleZoomModeCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSetFitVisibleZoomModeCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetFitVisibleZoomModeCommand.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetFitVisibleZoomModeCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfSetFitWidthZoomModeCommand">
      <summary>
        <para>Adjusts the document zoom factor value to adjust the page width to the application window&#39;s dimensions.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSetFitWidthZoomModeCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSetFitWidthZoomModeCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetFitWidthZoomModeCommand.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetFitWidthZoomModeCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfSetPageLevelZoomModeCommand">
      <summary>
        <para>Adjusts the document zoom factor value to attain a page size appropriate to the application window&#39;s dimensions.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSetPageLevelZoomModeCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSetPageLevelZoomModeCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetPageLevelZoomModeCommand.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetPageLevelZoomModeCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfSetPageNumberCommand">
      <summary>
        <para>Sets the page number and navigates to the page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSetPageNumberCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSetPageNumberCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSetPageNumberCommand.CanExecute">
      <summary>
        <para>Determines whether the command can be executed in its current state.</para>
      </summary>
      <returns>true, if the command can be executed; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSetPageNumberCommand.CreateDefaultCommandUIState">
      <summary>
        <para>Creates an object defining the command state for the current command.</para>
      </summary>
      <returns>An <see cref="T:DevExpress.Utils.Commands.ICommandUIState"/> interface that defines the command state.</returns>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetPageNumberCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSetPageNumberCommand.ForceExecute(DevExpress.Utils.Commands.ICommandUIState)">
      <summary>
        <para>Performs a command action regardless of a command state.</para>
      </summary>
      <param name="state">An object which implements the <see cref="T:DevExpress.Utils.Commands.ICommandUIState"/> interface.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetPageNumberCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSetPageNumberCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetPageNumberCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfSetZoomCommand">
      <summary>
        <para>This class is now obsolete.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSetZoomCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSetZoomCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetZoomCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfSetZoomCommand.ForceExecute(DevExpress.Utils.Commands.ICommandUIState)">
      <summary>
        <para>Performs a command action regardless of a command state.</para>
      </summary>
      <param name="state">An object which implements the <see cref="T:DevExpress.Utils.Commands.ICommandUIState"/> interface.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetZoomCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSetZoomCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfSetZoomCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfSetZoomModeCommand">
      <summary>
        <para>The base for classes that provide custom document zoom factor modes.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfShowAnnotationPropertiesCommand">
      <summary>
        <para>Runs the Annotation Properties dialog for a selected annotation.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfShowAnnotationPropertiesCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfShowAnnotationPropertiesCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfShowAnnotationPropertiesCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfShowAnnotationPropertiesCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfShowAnnotationPropertiesCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfShowAnnotationPropertiesCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfShowDocumentPropertiesCommand">
      <summary>
        <para>Runs the Document Properties dialog that provides detailed information about the current document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfShowDocumentPropertiesCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfShowDocumentPropertiesCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfShowDocumentPropertiesCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfShowDocumentPropertiesCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfShowDocumentPropertiesCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfShowDocumentPropertiesCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfShowDocumentPropertiesCommand.UpdateUIState(DevExpress.Utils.Commands.ICommandUIState)">
      <summary>
        <para>Anticipates a command&#39;s execution, by assigning a new status to the command in the context of its current applicability to the application user interface.</para>
      </summary>
      <param name="state">An object implementing the <see cref="T:DevExpress.Utils.Commands.ICommandUIState"/> interface that defines the current status of a command in the overall context of the application user interface.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfShowExactZoomListCommand">
      <summary>
        <para>Adjust the document zoom factor to match one of the predefined static values.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfShowExactZoomListCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfShowExactZoomListCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfShowExactZoomListCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfShowExactZoomListCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfShowExactZoomListCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfShowExactZoomListCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfShowExactZoomListCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfShowExactZoomListCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfTextCommentCommand">
      <summary>
        <para>The base class for each text comment command.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfTextHighlightCommand">
      <summary>
        <para>Highlights a selected text or activates the text highlight tool if the text is not selected.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfTextHighlightCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfTextHighlightCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfTextHighlightCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfTextHighlightCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfTextHighlightCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfTextHighlightCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfTextHighlightCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfTextHighlightCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfTextStrikethroughCommand">
      <summary>
        <para>Strikethroughs a selected text or activates the text strikethrough tool if the text is not selected.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfTextStrikethroughCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfTextStrikethroughCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfTextStrikethroughCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfTextStrikethroughCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfTextStrikethroughCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfTextStrikethroughCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfTextStrikethroughCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfTextStrikethroughCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfTextUnderlineCommand">
      <summary>
        <para>Underlines a selected text or activates the text underline tool if the text is not selected.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfTextUnderlineCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfTextUnderlineCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfTextUnderlineCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfTextUnderlineCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfTextUnderlineCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfTextUnderlineCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfTextUnderlineCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfTextUnderlineCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfThumbnailsPrintPagesCommand">
      <summary>
        <para>Prints pages that correspond to the selected thumbnails.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfThumbnailsPrintPagesCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfThumbnailsPrintPagesCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfThumbnailsPrintPagesCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfThumbnailsPrintPagesCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfThumbnailsPrintPagesCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfThumbnailsPrintPagesCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommand">
      <summary>
        <para>The base class for each PDF Viewer command.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfViewerCommand.CreateContextMenuBarItem(DevExpress.XtraBars.BarManager)">
      <summary>
        <para>Supports the context menu initialization in a PDF Viewer.</para>
      </summary>
      <param name="barManager">A <see cref="T:DevExpress.XtraBars.BarManager"/> that maintains the application bars and individual bar items.</param>
      <returns>A <see cref="T:DevExpress.XtraBars.BarItem"/> element.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfViewerCommand.ForceExecute(DevExpress.Utils.Commands.ICommandUIState)">
      <summary>
        <para>Forces the application user interface to ignore the current applicability of a command, and trigger its execution despite the risks.</para>
      </summary>
      <param name="state">An object implementing the <see cref="T:DevExpress.Utils.Commands.ICommandUIState"/> interface that defines the current status of a command in the overall context of the application user interface.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId">
      <summary>
        <para>Lists the values that identify the PDF Viewer commands.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.#ctor(System.Int32)">
      <summary>
        <para>Initializes a new instance of the PdfViewerCommandId class with the specified value.</para>
      </summary>
      <param name="value">A System.Int32 value.</param>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.ClearFilterComments">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.CollapseAllCommentTree">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.Copy">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfCopyCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.DeleteAnnotation">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfDeleteAnnotationCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.EnlargePageThumbnails">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfEnlargePageThumbnailsCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.Equals(DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId)">
      <summary>
        <para>Determines whether or not the specified object is equal to the current <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> instance.</para>
      </summary>
      <param name="other">The object to compare with the current object.</param>
      <returns>true, if the specified object is equal to the current <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> instance; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.Equals(System.Object)">
      <summary>
        <para>Determines whether or not the specified object is equal to the current <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> instance.</para>
      </summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>true, if the specified object is equal to the current <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> instance; otherwise false.</returns>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.ExpandAllCommentTree">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.ExpandCurrentOutline">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCurrentCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.ExportFormData">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfExportFormDataCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.FilterCommentsByAuthor">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.FilterCommentsByCheckmarkStatus">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.FilterCommentsByNoneStatus">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.FilterCommentsByStatus">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.FilterCommentsByType">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.FilterCommentsHideAll">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.FindText">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfFindTextCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.GetHashCode">
      <summary>
        <para>Gets the hash code (a number) that corresponds to the value of the current <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> object.</para>
      </summary>
      <returns>An integer value specifying the hash code for the current object.</returns>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.GotoOutline">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesGotoCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.HandTool">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfHandToolCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.ImportFormData">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfImportFormDataCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.MarqueeZoom">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfMarqueeZoomCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.NextPage">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfNextPageCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.NextView">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfNextViewCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.None">
      <summary>
        <para>An undefined command.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.OpenFile">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOpenFileCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.OutlinePrintPages">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintPagesCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.OutlinePrintSections">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesPrintSectionsCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.OutlinesExpandCollapseTopLevel">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesExpandCollapseTopLevelCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.OutlinesTextSizeToLarge">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToLargeCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.OutlinesTextSizeToMedium">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToMediumCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.OutlinesTextSizeToSmall">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesTextSizeToSmallCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.OutlinesWrapLongLines">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesWrapLongLinesCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.OutlineViewerHideAfterUse">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfOutlinesHideAfterUseCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.PreviousPage">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfPreviousPageCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.PreviousView">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfPreviousViewCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.PrintFile">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfPrintFileCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.ReducePageThumbnails">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfReducePageThumbnailsCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.RotatePageClockwise">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfRotatePageClockwiseCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.RotatePageCounterclockwise">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfRotatePageCounterclockwiseCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SaveAsFile">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSaveAsFileCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SelectAll">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSelectAllCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SelectTool">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSelectToolCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SetActualSizeZoomMode">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSetActualSizeZoomModeCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SetFitVisibleZoomMode">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSetFitVisibleZoomModeCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SetFitWidthZoomMode">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSetFitWidthZoomModeCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SetPageLevelZoomMode">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSetPageLevelZoomModeCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SetPageNumber">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfSetPageNumberCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SetZoom">
      <summary>
        <para>This command&#39;s ID is not used.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.ShowAnnotationProperties">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfShowAnnotationPropertiesCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.ShowDocumentProperties">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfShowDocumentPropertiesCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.ShowExactZoomList">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfShowExactZoomListCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SortCommentsByAuthor">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SortCommentsByCheckmarkStatus">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SortCommentsByDate">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SortCommentsByPage">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.SortCommentsByType">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.StickyNote">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.TextHighlight">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfTextHighlightCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.TextStrikethrough">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfTextStrikethroughCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.TextUnderline">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfTextUnderlineCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.ThumbnailsPrintPages">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfThumbnailsPrintPagesCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.Zoom10">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom10Command"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.Zoom100">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom100Command"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.Zoom125">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom125Command"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.Zoom150">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom150Command"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.Zoom200">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom200Command"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.Zoom25">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom25Command"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.Zoom400">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom400Command"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.Zoom50">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom50Command"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.Zoom500">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom500Command"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.Zoom75">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom75Command"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.ZoomIn">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoomInCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId.ZoomOut">
      <summary>
        <para>Identifies the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoomOutCommand"/>.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandRepository">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandRepository.CreateCommand(DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId)">
      <summary>
        <para>For internal use. Use the  <see cref="M:DevExpress.XtraPdfViewer.IPdfViewerCommandFactoryService.CreateCommand(DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId)"/> method instead to create a custom command of the PDF Viewer.</para>
      </summary>
      <param name="id">A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member specifying a command to create.</param>
      <returns>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommand"/> object that is the PDF Viewer command.</returns>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfZoom100Command">
      <summary>
        <para>Sets the zoom factor value to show the original document size.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfZoom100Command.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom100Command"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom100Command.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom100Command.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of a menu caption corresponding to <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom100Command"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfZoom10Command">
      <summary>
        <para>Sets the zoom factor value to 10 percent.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfZoom10Command.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom10Command"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom10Command.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom10Command.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of a menu caption corresponding to <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom10Command"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfZoom125Command">
      <summary>
        <para>Sets the zoom factor value to 125 percent of the original size.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfZoom125Command.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom125Command"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom125Command.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom125Command.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of a menu caption corresponding to <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom125Command"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfZoom150Command">
      <summary>
        <para>Sets the zoom factor value to 150 percent of the original document size.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfZoom150Command.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom150Command"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom150Command.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom150Command.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of a menu caption corresponding to <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom150Command"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfZoom200Command">
      <summary>
        <para>Sets the zoom factor value to 200 percent of the original document size.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfZoom200Command.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom200Command"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom200Command.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom200Command.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of a menu caption corresponding to <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom200Command"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfZoom25Command">
      <summary>
        <para>Sets the zoom factor value to 25 percent of the original document size.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfZoom25Command.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom25Command"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom25Command.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom25Command.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of a menu caption corresponding to <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom25Command"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfZoom400Command">
      <summary>
        <para>Sets the zoom factor value to 400 percent of the original document size.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfZoom400Command.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom400Command"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom400Command.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom400Command.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of a menu caption corresponding to <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom400Command"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfZoom500Command">
      <summary>
        <para>Sets the zoom factor value to 500 percent of the original document size.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfZoom500Command.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom500Command"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom500Command.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom500Command.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of a menu caption corresponding to <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom500Command"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfZoom50Command">
      <summary>
        <para>Sets the zoom factor value to 50 percent of the original document size.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfZoom50Command.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom50Command"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom50Command.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom50Command.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of a menu caption corresponding to <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom50Command"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfZoom75Command">
      <summary>
        <para>Sets the zoom factor value to 75 percent of the original document size.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfZoom75Command.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom75Command"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom75Command.Id">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoom75Command.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of a menu caption corresponding to <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoom75Command"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfZoomCommand">
      <summary>
        <para>The base class for each PDF zoom command.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfZoomInCommand">
      <summary>
        <para>Increases the document zoom factor by ten percent.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfZoomInCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoomInCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoomInCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoomInCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoomInCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoomInCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoomInCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoomInCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Commands.PdfZoomOutCommand">
      <summary>
        <para>Decreases the document zoom factor by ten percent.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Commands.PdfZoomOutCommand.#ctor(DevExpress.XtraPdfViewer.PdfViewer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoomOutCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">A <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> that is the command owner.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoomOutCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoomOutCommand.Id">
      <summary>
        <para>Gets the ID of the specified <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoomOutCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member that is the command identifier.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoomOutCommand.ImageName">
      <summary>
        <para>Gets the name of the icon associated with the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfZoomOutCommand"/>.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the image name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Commands.PdfZoomOutCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of the menu caption of a command.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value that is the resource string identifier.</value>
    </member>
    <member name="N:DevExpress.XtraPdfViewer.Extensions">
      <summary>
        <para>Defines extension methods for the WinForms PDF Viewer.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Extensions.PdfViewerBarItemLinkCollectionExtensions">
      <summary>
        <para>Defines extension methods for <see cref="T:DevExpress.XtraBars.BarItemLinkCollection"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Extensions.PdfViewerBarItemLinkCollectionExtensions.GetPdfViewerBarItemLink(DevExpress.XtraBars.BarItemLinkCollection,DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId)">
      <summary>
        <para>Retrieves a menu item by its command&#39;s identifying information.</para>
      </summary>
      <param name="collection">A target collection of <see cref="T:DevExpress.XtraBars.BarItemLink"/> items.</param>
      <param name="commandId">One of the <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> enumeration values indicating the target item&#39;s command ID.</param>
      <returns>A target BarItemLink object.</returns>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.IPdfViewerCommandFactoryService">
      <summary>
        <para>A service which is used to create PDF Viewer commands.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.IPdfViewerCommandFactoryService.CreateCommand(DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId)">
      <summary>
        <para>Enables you to create a custom command for use in the PDF Viewer.</para>
      </summary>
      <param name="commandId">A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommandId"/> member specifying a command to create.</param>
      <returns>A <see cref="T:DevExpress.XtraPdfViewer.Commands.PdfViewerCommand"/> object that is the PDF Viewer command.</returns>
    </member>
    <member name="N:DevExpress.XtraPdfViewer.Localization">
      <summary>
        <para>Provides means to localize the user interface elements of a PDF Viewer.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerLocalizer">
      <summary>
        <para>Provides the means to localize the user interface elements of a PDF Viewer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerLocalizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerLocalizer.Active">
      <summary>
        <para>Specifies a localizer object providing localization of a PDF Viewer at runtime.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> descendant, used to localize the user interface at runtime.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerLocalizer.CreateResXLocalizer">
      <summary>
        <para>For internal use. Returns a Localizer object storing resources based on the thread&#39;s language and regional settings (culture).</para>
      </summary>
      <returns>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object, storing resources based on the thread&#39;s language and regional settings (culture).</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerLocalizer.GetString(DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId)">
      <summary>
        <para>Returns a localized string for the given string identifier.</para>
      </summary>
      <param name="id">An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value, identifying the string to localize.</param>
      <returns>A <see cref="T:System.String"/> value, corresponding to the specified identifier.</returns>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerResLocalizer">
      <summary>
        <para>A default localizer to translate the PDF Viewer&#39;s resources.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerResLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerResLocalizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerResLocalizer.GetLocalizedString(DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId)">
      <summary>
        <para>Gets the string, localized by the current <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerResLocalizer"/>, for the specified user interface element.</para>
      </summary>
      <param name="id">An <see cref="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId"/> enumeration value, specifying the user interface element whose caption (text) is to be localized.</param>
      <returns>A <see cref="T:System.String"/> value, specifying the text to be displayed within the specified user interface element.</returns>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerResLocalizer.Language">
      <summary>
        <para>Returns the name of the language currently used by this localizer object.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the language of the user interface localization.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId">
      <summary>
        <para>Contains strings that correspond to the PDF Viewer user interface captions that are subject to localization.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.BarCaption">
      <summary>
        <para>&quot;PDF Viewer&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.ClearFilterCommentsCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CollapseAllCommentTreeCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandCopyCaption">
      <summary>
        <para>&quot;Copy&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandCopyDescription">
      <summary>
        <para>&quot;Copy&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandEnlargePageThumbnailsCaption">
      <summary>
        <para>&quot;Enlarge Page Thumbnails&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandEnlargePageThumbnailsDescription">
      <summary>
        <para>&quot;Enlarge Page Thumbnails&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandExportFormDataCaption">
      <summary>
        <para>&quot;Export&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandExportFormDataDescription">
      <summary>
        <para>&quot;Export form data to the file.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandFindTextCaption">
      <summary>
        <para>&quot;Find&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandFindTextDescription">
      <summary>
        <para>&quot;Find text.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandHandToolCaption">
      <summary>
        <para>&quot;Hand Tool&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandHandToolDescription">
      <summary>
        <para>&quot;Hand Tool&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandImportFormDataCaption">
      <summary>
        <para>&quot;Import&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandImportFormDataDescription">
      <summary>
        <para>&quot;Import form data from the file.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandMarqueeZoomCaption">
      <summary>
        <para>&quot;Marquee Zoom&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandMarqueeZoomDescription">
      <summary>
        <para>&quot;Marquee Zoom&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandNextPageCaption">
      <summary>
        <para>&quot;Next&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandNextPageDescription">
      <summary>
        <para>&quot;Show next page.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandNextViewCaption">
      <summary>
        <para>&quot;Next View&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandNextViewDescription">
      <summary>
        <para>&quot;Next View&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOpenFileCaption">
      <summary>
        <para>&quot;Open&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOpenFileDescription">
      <summary>
        <para>&quot;Open a PDF file.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOutlinesCollapseTopLevelCaption">
      <summary>
        <para>&quot;Collapse Top-Level Bookmarks&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOutlinesExpandCurrentCaption">
      <summary>
        <para>&quot;Expand Current Bookmark&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOutlinesExpandTopLevelCaption">
      <summary>
        <para>&quot;Expand Top-Level Bookmarks&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOutlinesGotoCaption">
      <summary>
        <para>&quot;Go to Bookmark&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOutlinesHideAfterUseCaption">
      <summary>
        <para>&quot;Hide After Use&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOutlinesPrintPagesCaption">
      <summary>
        <para>&quot;Print Page(s)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOutlinesPrintSectionsCaption">
      <summary>
        <para>&quot;Print Section(s)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOutlinesTextSizeCaption">
      <summary>
        <para>&quot;Text Size&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOutlinesTextSizeToLargeCaption">
      <summary>
        <para>&quot;Large&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOutlinesTextSizeToMediumCaption">
      <summary>
        <para>&quot;Medium&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOutlinesTextSizeToSmallCaption">
      <summary>
        <para>&quot;Small&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandOutlinesWrapLongLinesCaption">
      <summary>
        <para>&quot;Wrap Long Bookmarks&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandPreviousPageCaption">
      <summary>
        <para>&quot;Previous&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandPreviousPageDescription">
      <summary>
        <para>&quot;Show previous page.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandPreviousViewCaption">
      <summary>
        <para>&quot;Previous View&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandPreviousViewDescription">
      <summary>
        <para>&quot;Previous View&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandPrintFileCaption">
      <summary>
        <para>&quot;Print&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandPrintFileDescription">
      <summary>
        <para>&quot;Print file.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandPrintPagesCaption">
      <summary>
        <para>&quot;Print Pages...&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandReducePageThumbnailsCaption">
      <summary>
        <para>&quot;Reduce Page Thumbnails&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandReducePageThumbnailsDescription">
      <summary>
        <para>&quot;Reduce Page Thumbnails&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandRotatePageClockwiseCaption">
      <summary>
        <para>&quot;Rotate Clockwise&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandRotatePageCounterclockwiseCaption">
      <summary>
        <para>&quot;Rotate Counterclockwise&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandSaveAsFileCaption">
      <summary>
        <para>&quot;Save As&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandSaveAsFileDescription">
      <summary>
        <para>&quot;Save the PDF file.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandSelectAllCaption">
      <summary>
        <para>&quot;Select All&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandSelectAllDescription">
      <summary>
        <para>&quot;Select All&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandSelectToolCaption">
      <summary>
        <para>&quot;Select Tool&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandSelectToolDescription">
      <summary>
        <para>&quot;Select Tool&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandSetActualSizeZoomModeCaption">
      <summary>
        <para>&quot;Actual Size&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandSetFitVisibleZoomModeCaption">
      <summary>
        <para>&quot;Fit Visible&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandSetFitWidthZoomModeCaption">
      <summary>
        <para>&quot;Fit Width&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandSetPageLevelZoomModeCaption">
      <summary>
        <para>&quot;Zoom to Page Level&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandSetPageNumberCaption">
      <summary>
        <para>&quot;Page Number&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandSetPageNumberDescription">
      <summary>
        <para>&quot;Change the page number and navigate to the page.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandSetPageNumberLabelFormat">
      <summary>
        <para>&quot;of {0}&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandShowDocumentProperties">
      <summary>
        <para>&quot;Document properties&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandViewExactZoomListCaption">
      <summary>
        <para>&quot;Zoom&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandViewExactZoomListDescription">
      <summary>
        <para>&quot;Change the zoom level of the PDF document.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoom100Caption">
      <summary>
        <para>&quot;100%&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoom10Caption">
      <summary>
        <para>&quot;10%&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoom125Caption">
      <summary>
        <para>&quot;125%&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoom150Caption">
      <summary>
        <para>&quot;150%&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoom200Caption">
      <summary>
        <para>&quot;200%&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoom25Caption">
      <summary>
        <para>&quot;25%&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoom400Caption">
      <summary>
        <para>&quot;400%&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoom500Caption">
      <summary>
        <para>&quot;500%&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoom50Caption">
      <summary>
        <para>&quot;50%&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoom75Caption">
      <summary>
        <para>&quot;75%&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoomInCaption">
      <summary>
        <para>&quot;Zoom In&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoomInDescription">
      <summary>
        <para>&quot;Zoom in to get a close-up view of the PDF document.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoomInShortcutCaption">
      <summary>
        <para>&quot;(Ctrl &#0043; Plus)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoomOutCaption">
      <summary>
        <para>&quot;Zoom Out&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoomOutDescription">
      <summary>
        <para>&quot;Zoom out to see more of the page at a reduces size.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommandZoomOutShortcutCaption">
      <summary>
        <para>&quot;(Ctrl &#0043; Minus)&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommentBarCaption">
      <summary>
        <para>&quot;Comment&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommentStatusAcceptedCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommentStatusCancelledCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommentStatusCompletedCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommentStatusNoneCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.CommentStatusRejectedCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DeleteAnnotationCommandCaption">
      <summary>
        <para>&quot;Delete&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DeleteAnnotationCommandDescription">
      <summary>
        <para>&quot;Delete a selected annotation.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DesignerActionListCreateAllBarsTransaction">
      <summary>
        <para>&quot;Create PDF Viewer Bars&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DesignerActionListCreateBarsTransaction">
      <summary>
        <para>&quot;Create Bars&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DesignerActionListCreateBarTransaction">
      <summary>
        <para>&quot;Create &quot;{0}&quot; Bar&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DesignerActionListCreateRibbonTransaction">
      <summary>
        <para>&quot;Create Ribbon&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DesignerActionMethodCreateAllBars">
      <summary>
        <para>&quot;Create All Bars&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DesignerActionMethodCreateBar">
      <summary>
        <para>&quot;Create &quot;{0}&quot; Bar&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DesignerActionMethodCreateBars">
      <summary>
        <para>&quot;Create Bars&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DesignerActionMethodCreateRibbon">
      <summary>
        <para>&quot;Create Ribbon&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DesignerActionMethodDockInParentContainer">
      <summary>
        <para>&quot;Dock in parent container&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DesignerActionMethodLoadPdf">
      <summary>
        <para>&quot;Load PDF file...&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DesignerActionMethodUndockInParentContainer">
      <summary>
        <para>&quot;Undock in parent container&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.DesignerActionMethodUnloadPdf">
      <summary>
        <para>&quot;Unload PDF file&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.EditCommentAcceptedBy">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.EditCommentCancelledBy">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.EditCommentCompletedBy">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.EditCommentMenuItemCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.EditCommentNullValuePrompt">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.EditCommentRejectedBy">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.ExpandAllCommentTreeCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FileRibbonGroupCaption">
      <summary>
        <para>&quot;File&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FilterCommentsByAuthorCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FilterCommentsByCheckmarkStatusCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FilterCommentsByNoneStatusCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FilterCommentsByStatusCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FilterCommentsByTypeCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FilterCommentsCountCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FilterCommentsHideAllCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FindControlCaseSensitive">
      <summary>
        <para>&quot;Case Sensitive&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FindControlWholeWordsOnly">
      <summary>
        <para>&quot;Whole Words Only&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FindRibbonGroupCaption">
      <summary>
        <para>&quot;Find&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FormDataBarCaption">
      <summary>
        <para>&quot;Interactive Form&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FormDataFileFilter">
      <summary>
        <para>&quot;FDF Files (.fdf)|.fdf|XFDF Files (.xfdf)|.xfdf|XML Files (.xml)|.xml|TXT Files (.txt)|.txt&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.FormDataRibbonPageGroupCaption">
      <summary>
        <para>&quot;Form Data&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageAddCommandConstructorError">
      <summary>
        <para>&quot;Cannot find a constructor with a PdfViewer type parameter in the {0} class&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageClipboardError">
      <summary>
        <para>&quot;Unable to copy data to the Clipboard.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageCurrentPageNumberOutOfRange">
      <summary>
        <para>&quot;The current page number should be greater than 0.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageDocumentClosing">
      <summary>
        <para>&quot;Do you want to save the changes to the document before closing it?&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageDocumentClosingCaption">
      <summary>
        <para>&quot;Document Closing&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageDocumentIsProtected">
      <summary>
        <para>&quot;{0} is protected&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageErrorCaption">
      <summary>
        <para>&quot;Error&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageExportError">
      <summary>
        <para>&quot;An error occurred while exporting the document&#39;s form data.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageImportError">
      <summary>
        <para>&quot;Unable to import the specified data into the document form.\r\n{0}\r\n Please ensure that the provided data meets the {1} specification.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageIncorrectMaxPrintingDpi">
      <summary>
        <para>&quot;The maximum printing DPI value should be greater than or equal to 0.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageIncorrectNavigationPaneVisibility">
      <summary>
        <para>&quot;The current navigation pane visibility cannot be set to PdfNavigationPaneVisibility.Default.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageIncorrectPageNumber">
      <summary>
        <para>&quot;The page number should be greater than 0, and less than or equal to the document page count.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageIncorrectPassword">
      <summary>
        <para>&quot;The password is incorrect. Please make sure that Caps Lock is not enabled.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageLoadingError">
      <summary>
        <para>&quot;Unable to load the PDF document because the following file is not available or it is not a valid PDF document.\r\n{0}\r\nPlease ensure that the application can access this file and that it is valid, or specify a different file.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessagePrintError">
      <summary>
        <para>&quot;Unable to print the document. Please contact your system administrator.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageSaveAsError">
      <summary>
        <para>&quot;{0}\r\nYou don&#39;t have a permission to save in this location.\r\nContact the administrator to obtain the permission.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageSaveAttachmentError">
      <summary>
        <para>&quot;Cannot save the attachment with the following name: {0}&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageSearchCaption">
      <summary>
        <para>&quot;Find&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageSearchFinished">
      <summary>
        <para>&quot;Finished searching throughout the document. No more matches were found.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageSearchFinishedNoMatchesFound">
      <summary>
        <para>&quot;Finished searching throughout the document. No matches were found.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageSecurityWarningCaption">
      <summary>
        <para>&quot;Security Warning&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageSecurityWarningFileAttachmentOpening">
      <summary>
        <para>&quot;The document is trying to access an embedded resource:\r\n&#39;{0}&#39;\r\nDo you want to open it?&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.MessageSecurityWarningUriOpening">
      <summary>
        <para>&quot;The document is trying to access an external resource by using the following URI (uniform resource identifier): &#39;{0}&#39;\r\nDo you want to open it?&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.NavigationPaneAttachmentsPageCaption">
      <summary>
        <para>&quot;Attachments&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.NavigationPaneCommentsPageCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.NavigationPaneOutlinesPageCaption">
      <summary>
        <para>&quot;Bookmarks&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.NavigationPaneThumbnailsPageCaption">
      <summary>
        <para>&quot;Page Thumbnails&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.NavigationRibbonGroupCaption">
      <summary>
        <para>&quot;Navigation&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.NoteCommentRibbonPageGroupCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.PageSize">
      <summary>
        <para>&quot;{0:0.00} x {1:0.00} in&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.PDFFileFilter">
      <summary>
        <para>&quot;PDF Files (.pdf)|*.pdf&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.PRNFileFilter">
      <summary>
        <para>&quot;Printable Files (.prn)|&#0042;.prn|All Files (.&#0042;)|&#0042;.&#0042;&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.RemoveCommentMenuItemCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.SelectionRibbonGroupCaption">
      <summary>
        <para>&quot;Selection&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.SetCommentStatusMenuItemCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.ShowAnnotationPropertiesCommandCaption">
      <summary>
        <para>&quot;Properties...&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.ShowAnnotationPropertiesCommandDescription">
      <summary>
        <para>&quot;Shows the annotation properties dialog for a selected annotation.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.SortCommentsByAuthorCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.SortCommentsByCheckmarkStatusCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.SortCommentsByDateCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.SortCommentsByPageCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.SortCommentsByTypeCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.SortCommentsCheckedStatusCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.SortCommentsUncheckedStatusCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.StickyNoteCommandCaption">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.StickyNoteCommandDescription">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.TextCommentRibbonPageGroupCaption">
      <summary>
        <para>&quot;Text&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.TextHighlightCommandCaption">
      <summary>
        <para>&quot;Highlight&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.TextHighlightTextCommandDescription">
      <summary>
        <para>&quot;Highlight text.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.TextStrikethroughCommandCaption">
      <summary>
        <para>&quot;Strikethrough&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.TextStrikethroughCommandDescription">
      <summary>
        <para>&quot;Strikethrough text.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.TextUnderlineCommandCaption">
      <summary>
        <para>&quot;Underline&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.TextUnderlineCommandDescription">
      <summary>
        <para>&quot;Underline text.&quot;</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.Localization.XtraPdfViewerStringId.ZoomRibbonGroupCaption">
      <summary>
        <para>&quot;Zoom&quot;</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfAnnotationChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.AnnotationChanged"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfAnnotationChangedEventArgs.Annotation">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfAnnotationChangedEventHandler">
      <summary>
        <para></para>
      </summary>
      <param name="sender"></param>
      <param name="e"></param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfAnnotationCreatedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.AnnotationCreated"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfAnnotationCreatedEventArgs.Annotation">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfAnnotationCreatedEventHandler">
      <summary>
        <para></para>
      </summary>
      <param name="sender"></param>
      <param name="e"></param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfAnnotationCreatingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.AnnotationCreating"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfAnnotationCreatingEventArgs.Builder">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfAnnotationCreatingEventHandler">
      <summary>
        <para></para>
      </summary>
      <param name="sender"></param>
      <param name="e"></param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfAnnotationDeletingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.AnnotationDeleting"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfAnnotationDeletingEventArgs.Annotation">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfAnnotationDeletingEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.AnnotationDeleting"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfAnnotationDeletingEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfAnnotationGotFocusEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.AnnotationGotFocus"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfAnnotationGotFocusEventArgs.Annotation">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfAnnotationGotFocusEventHandler">
      <summary>
        <para></para>
      </summary>
      <param name="sender"></param>
      <param name="e"></param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfAnnotationLostFocusEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.AnnotationLostFocus"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfAnnotationLostFocusEventArgs.Annotation">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfAnnotationLostFocusEventHandler">
      <summary>
        <para></para>
      </summary>
      <param name="sender"></param>
      <param name="e"></param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfCancelSaveOperationException">
      <summary>
        <para>An exception thrown after canceling the Save operation in a <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfCancelSaveOperationException.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.PdfCancelSaveOperationException"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfContentMarginMode">
      <summary>
        <para>Lists the values specifying the mode for calculating the outer border width displayed by a <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> around document pages.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfContentMarginMode.Dynamic">
      <summary>
        <para>The width of a page&#39;s outer border is variable and depends on the skin that is currently applied to a <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfContentMarginMode.Static">
      <summary>
        <para>The page border width is constant regardless of the skin that is currently applied to a <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfContinueSearchFrom">
      <summary>
        <para>Lists values used to specify where to continue the search in a PDF document.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfContinueSearchFrom.CurrentPage">
      <summary>
        <para>The PDF Viewer continues its search from the current page.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfContinueSearchFrom.LastSearchResult">
      <summary>
        <para>The PDF Viewer continues its search from its last search result.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfCurrentPageChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.CurrentPageChanged"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfCurrentPageChangedEventArgs.PageCount">
      <summary>
        <para>Gets the document page count.</para>
      </summary>
      <value>An integer value that is the total number of pages in the document.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfCurrentPageChangedEventArgs.PageNumber">
      <summary>
        <para>Gets the page number of the currently displayed page.</para>
      </summary>
      <value>An integer value that is the current page number.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfCurrentPageChangedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.CurrentPageChanged"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfCurrentPageChangedEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfCursorMode">
      <summary>
        <para>Lists values that specify the interaction mode for keyboard and cursor.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfCursorMode.Custom">
      <summary>
        <para>The PDF Viewer does not handle mouse and keyboard events. You can handle these events to create your custom interaction tool. To change a cursor, use the PdfViewer.CursorMode property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfCursorMode.HandTool">
      <summary>
        <para>This tool is used for navigation. The end-user can browse the document by moving the mouse while pressing the left button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfCursorMode.MarqueeZoom">
      <summary>
        <para>This tool is used to change the zoom level and navigate in a document. The end-user can increase the zoom level by simply clicking, decrease the zoom level by clicking while pressing the Ctrl key, or zoom in on a portion of a page by dragging the rectangle around it.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfCursorMode.SelectTool">
      <summary>
        <para>This tool is used for navigation in a document. You can also select text and images in a document to copy using a keyboard and mouse.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfCursorMode.StickyNoteTool">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfCursorMode.TextHighlightTool">
      <summary>
        <para>This tool is used to highlight a text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfCursorMode.TextStrikethroughTool">
      <summary>
        <para>This tool is used to strikethrough a text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfCursorMode.TextUnderlineTool">
      <summary>
        <para>This tool is used to underline a text.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfDocumentChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.DocumentChanged"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentChangedEventArgs.DocumentFilePath">
      <summary>
        <para>Gets the path to the document file.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the path to the document.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentChangedEventArgs.DocumentFileStream">
      <summary>
        <para>Indicates the file stream storing the document bytes.</para>
      </summary>
      <value>A <see cref="T:System.IO.Stream"/> value, identifying the file stream.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfDocumentChangedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.DocumentChanged"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfDocumentChangedEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfDocumentClosingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.DocumentClosing"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentClosingEventArgs.IsDocumentModified">
      <summary>
        <para>Gets a value indicating whether or not the current PDF has been modified.</para>
      </summary>
      <value>true if the document has been modified; otherwise false.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfDocumentClosingEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.DocumentClosing"/> event.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfDocumentClosingEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfDocumentProperties">
      <summary>
        <para>Provides information related to the currently opened document in the Document Properties dialog.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentProperties.Application">
      <summary>
        <para>Indicates the application that generated the document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentProperties.Author">
      <summary>
        <para>Indicates a document&#39;s author.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentProperties.CreationDate">
      <summary>
        <para>Gets the date of the PDF file creation.</para>
      </summary>
      <value>A <see cref="T:System.DateTimeOffset"/> structure that is the date of the PDF file creation.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentProperties.FileLocation">
      <summary>
        <para>Indicates the physical location of a document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentProperties.FileName">
      <summary>
        <para>Indicates a document&#39;s file name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the file name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentProperties.FilePath">
      <summary>
        <para>Indicates a path to the folder that contains the document file.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the path to the folder that contains the document file.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentProperties.FileSize">
      <summary>
        <para>Indicates the physical size of the document file.</para>
      </summary>
      <value>A long integer value.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentProperties.Keywords">
      <summary>
        <para>Indicates the keywords that describe the primary topics covered in a document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, providing a set of document keywords.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentProperties.ModificationDate">
      <summary>
        <para>Gets the date of the PDF file&#39;s last modification.</para>
      </summary>
      <value>A <see cref="T:System.DateTimeOffset"/> structure that is the date of the PDF file&#39;s last modification.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentProperties.Producer">
      <summary>
        <para>Indicates an application that produces a PDF.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the application name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentProperties.Subject">
      <summary>
        <para>Indicates the primary subject of a document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the document subject.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentProperties.Title">
      <summary>
        <para>Indicates the title of a document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the document title.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfDocumentProperties.Version">
      <summary>
        <para>Gets the version of a PDF document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that represents the PDF document version.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfFileAttachmentOpeningEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.FileAttachmentOpening"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfFileAttachmentOpeningEventArgs.FileAttachment">
      <summary>
        <para>Gets the file attachment stored in the Attachments tab of the PDF Viewer.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfFileAttachment"/> object that is the file attachment.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfFileAttachmentOpeningEventArgs.Handled">
      <summary>
        <para>Gets or sets a value that specifies whether the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.FileAttachmentOpening"/> event is handled; if handled, the Security Warning message box is hidden.</para>
      </summary>
      <value>true, if the Security Warning message box is hidden; otherwise false.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfFileAttachmentOpeningEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.FileAttachmentOpening"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event sender.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfFileAttachmentOpeningEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfFindDialogOptions">
      <summary>
        <para>Provides the text search options applied by an end-user in the Find Text dialog.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfFindDialogOptions.#ctor(System.String,System.Boolean,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.PdfFindDialogOptions"/> class with the specified settings.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value, specifying the search text. This value is assigned to the <see cref="P:DevExpress.XtraPdfViewer.PdfFindDialogOptions.Text"/> property.</param>
      <param name="caseSensitive">true, to take into account the letter case; otherwise false. This value is assigned to the <see cref="P:DevExpress.XtraPdfViewer.PdfFindDialogOptions.CaseSensitive"/> property.</param>
      <param name="wholeWords">true, if whole words should match the search criteria; otherwise false. This value is assigned to the <see cref="P:DevExpress.XtraPdfViewer.PdfFindDialogOptions.WholeWords"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfFindDialogOptions.CaseSensitive">
      <summary>
        <para>Gets a value that specifies whether or not to ignore the letter case when searching text in a PDF.</para>
      </summary>
      <value>true, to take into account the letter case; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfFindDialogOptions.Text">
      <summary>
        <para>Returns the search text.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the search text.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfFindDialogOptions.WholeWords">
      <summary>
        <para>Returns a value that indicates whether or not to take into account only whole words when searching text.</para>
      </summary>
      <value>true, if whole words should match the search criteria; otherwise false.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfFindDialogVisibilityChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.FindDialogVisibilityChanged"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfFindDialogVisibilityChangedEventArgs.Visible">
      <summary>
        <para>Indicates the visibility state of the Find dialog window.</para>
      </summary>
      <value>true if the Find dialog window is visible; otherwise false.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfFindDialogVisibilityChangedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.FindDialogVisibilityChanged"/> event.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfFindDialogVisibilityChangedEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfFormFieldFocusEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.FormFieldGotFocus"/> and <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.FormFieldLostFocus"/> events.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfFormFieldFocusEventArgs.FieldName">
      <summary>
        <para>Gets the name of the form field.</para>
      </summary>
      <value>A string that represents the form field name.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfFormFieldFocusEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.FormFieldGotFocus"/> and <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.FormFieldLostFocus"/> events.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfFormFieldFocusEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfFormFieldValueChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.FormFieldValueChanged"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfFormFieldValueChangedEventArgs.FieldName">
      <summary>
        <para>Gets the name of the form field in the PDF document.</para>
      </summary>
      <value>A string that represents the form field name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfFormFieldValueChangedEventArgs.NewValue">
      <summary>
        <para>Gets an object which represents the new value of the form field which has been assigned.</para>
      </summary>
      <value>An object of the System.Object class representing the form field new value.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfFormFieldValueChangedEventArgs.OldValue">
      <summary>
        <para>Gets the form field&#39;s previous value.</para>
      </summary>
      <value>An object of the <see cref="T:System.Object"/> class representing the previous value of the form field.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfFormFieldValueChangedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.FormFieldValueChanged"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="args">A <see cref="T:DevExpress.XtraPdfViewer.PdfFormFieldValueChangedEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfFormFieldValueChangingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.FormFieldValueChanging"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfFormFieldValueChangingEventArgs.FieldName">
      <summary>
        <para>Gets the name of the form field.</para>
      </summary>
      <value>A string that represents the form field name.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfFormFieldValueChangingEventArgs.NewValue">
      <summary>
        <para>Gets or sets a new value of the form field.</para>
      </summary>
      <value>An object representing the new value of the form field.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfFormFieldValueChangingEventArgs.OldValue">
      <summary>
        <para>Gets the form field&#39;s previous value.</para>
      </summary>
      <value>An object representing the previous value of the form field.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfFormFieldValueChangingEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.FormFieldValueChanging"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="args">A <see cref="T:DevExpress.XtraPdfViewer.PdfFormFieldValueChangingEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings">
      <summary>
        <para>Provides markup annotation tools settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings.Author">
      <summary>
        <para>Specifies the default author for all markup tools.</para>
      </summary>
      <value>A string that specifies the annotation&#39;s default author.</value>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings.PropertyChanged">
      <summary>
        <para>Occurs every time any of the <see cref="T:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings"/> class properties has changed its value.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings.StickyNoteColor">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings.StickyNoteDefaultSubject">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings.StickyNoteIconName">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings.TextHighlightColor">
      <summary>
        <para>Specifies the color for a text highlight tool.</para>
      </summary>
      <value>A  <see cref="T:System.Drawing.Color"/> value that specifies the text highlight tool&#39;s color.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings.TextHighlightDefaultSubject">
      <summary>
        <para>Specifies the default subject for a text highlight tool.</para>
      </summary>
      <value>A string that specifies the text highlight tool&#39;s default subject.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings.TextStrikethroughColor">
      <summary>
        <para>Specifies the color for a text strikethrough tool.</para>
      </summary>
      <value>A  <see cref="T:System.Drawing.Color"/> value that specifies the text strikethrough tool&#39;s color.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings.TextStrikethroughDefaultSubject">
      <summary>
        <para>Specifies the default subject for a text strikethrough tool.</para>
      </summary>
      <value>A string that specifies the text strikethrough tool&#39;s default subject.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings.TextUnderlineColor">
      <summary>
        <para>Specifies the color for a text underline tool.</para>
      </summary>
      <value>A  <see cref="T:System.Drawing.Color"/> value that specifies the text underline tool&#39;s color.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings.TextUnderlineDefaultSubject">
      <summary>
        <para>Specifies the default subject for a text underline tool.</para>
      </summary>
      <value>A string that specifies the text underline tool&#39;s default subject.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings.ToString">
      <summary>
        <para>Returns the textual representation of the <see cref="T:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings"/>.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value, which is the textual representation of <see cref="T:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings"/>.</returns>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfNavigationPanePage">
      <summary>
        <para>Lists the values specifying the selected page (Bookmarks, Thumbnails, and Attachments) displayed on the Navigation pane.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPanePage.Attachments">
      <summary>
        <para>The Attachments page is selected on the navigation pane.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPanePage.Bookmarks">
      <summary>
        <para>The Bookmarks page is selected on the navigation pane.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPanePage.Comments">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPanePage.Default">
      <summary>
        <para>When this option is used, the Bookmarks page is opened if a document contains bookmarks. Otherwise, the Thumbnails page is shown on the navigation pane.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPanePage.Thumbnails">
      <summary>
        <para>The Thumbnails page is selected on the navigation pane.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfNavigationPanePageVisibility">
      <summary>
        <para>Lists the values specifying the visibility of pages (Bookmarks, Thumbnails, and Attachments) displayed on the Navigation pane.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPanePageVisibility.All">
      <summary>
        <para>Attachments, Bookmarks (the page is shown if a document contains bookmarks), and Thumbnails pages are displayed on the Navigation pane.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPanePageVisibility.Attachments">
      <summary>
        <para>Only the Attachments page is displayed on the Navigation pane.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPanePageVisibility.Bookmarks">
      <summary>
        <para>Only the Bookmarks page is displayed on the Navigation pane. If a PDF document does not contain bookmarks the Navigation pane is hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPanePageVisibility.Comments">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPanePageVisibility.None">
      <summary>
        <para>The Navigation pane is hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPanePageVisibility.Thumbnails">
      <summary>
        <para>Only the Thumbnails page is displayed on the Navigation pane.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfNavigationPaneSelectedPageChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneSelectedPageChanged"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfNavigationPaneSelectedPageChangedEventArgs.PreviousSelectedPage">
      <summary>
        <para>Gets a previously selected page on a navigation pane.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfNavigationPanePage"/> enumeration value that represents a selected page on the navigation pane.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfNavigationPaneSelectedPageChangedEventArgs.SelectedPage">
      <summary>
        <para>Gets a currently selected page (bookmarks, thumbnails or attachments) on the navigation pane.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfNavigationPanePage"/> enumeration value that represents a selected page on the navigation pane.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfNavigationPaneSelectedPageChangedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneSelectedPageChanged"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfNavigationPaneSelectedPageChangedEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility">
      <summary>
        <para>Lists values specifying the visibility of the PDF navigation pane.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility.Collapsed">
      <summary>
        <para>The PDF navigation pane is collapsed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility.Default">
      <summary>
        <para>When this option is specified for the <see cref="P:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneInitialVisibility"/> property, it sets the <see cref="P:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneVisibility"/> property either to Visible (a document contains bookmarks) or to Collapsed (a document does not have bookmarks and attachments tab is shown in the collapsed state). 
This option is not supported for the <see cref="P:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneVisibility"/> property (the ArgumentOutOfRangeException is thrown).</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility.Expanded">
      <summary>
        <para>The PDF navigation pane is expanded.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility.Hidden">
      <summary>
        <para>The PDF navigation pane is hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility.Visible">
      <summary>
        <para>This navigation pane is displayed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibilityChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneVisibilityChanged"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibilityChangedEventArgs.NavigationPaneVisibility">
      <summary>
        <para>Gets the visible state of a navigation pane.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility"/> enumerator value defining the visibility of a navigation pane.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibilityChangedEventArgs.PreviousNavigationPaneVisibility">
      <summary>
        <para>Gets the previous value of the <see cref="P:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneVisibility"/> property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility"/> enumeration value defining the visibility of a navigation pane.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibilityChangedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneVisibilityChanged"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibilityChangedEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfOutlineNodeTextSize">
      <summary>
        <para>Lists values specifying the outline node text size in the PDF navigation pane.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfOutlineNodeTextSize.Large">
      <summary>
        <para>The text of outline nodes in the PDF navigation pane is large.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfOutlineNodeTextSize.Medium">
      <summary>
        <para>The text of outline nodes in the PDF navigation pane is medium.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfOutlineNodeTextSize.Small">
      <summary>
        <para>The text of outline nodes in the PDF navigation pane is small.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfOutlineViewerSettings">
      <summary>
        <para>Provides outline settings of the PDF viewer.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfOutlineViewerSettings.HideAfterUse">
      <summary>
        <para>Gets or sets a value which specifies whether the PDF outline pane is hidden after clicking on the outline item.</para>
      </summary>
      <value>true to hide the outline pane after clicking on the item (e.g., a bookmark); false to show the outline pane after use.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfOutlineViewerSettings.TextSize">
      <summary>
        <para>Gets or sets the text size of nodes located in the PDF outline viewer (navigation pane).</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfOutlineNodeTextSize"/> enumeration value which represents an outline nodes&#39; text size mode.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfOutlineViewerSettings.UseOutlinesForeColor">
      <summary>
        <para>Specifies whether to use document foreground colors for the outline node text in the navigation pane.</para>
      </summary>
      <value>true to apply document foreground colors to the outline node text; false to apply theme colors to the outline node text.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfOutlineViewerSettings.WrapLongLines">
      <summary>
        <para>Gets or sets a value which defines whether to wrap long lines in the outline node text of the PDF outline viewer.</para>
      </summary>
      <value>true to wrap long lines in the outline node text; false to show the outline node text unwrapped in the PDF outline viewer.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfPageSetupDialogShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.PageSetupDialogShowing"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfPageSetupDialogShowingEventArgs.FormLocation">
      <summary>
        <para>Gets or sets the point that specifies the screen coordinates of the form&#39;s upper-left corner.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> that represents the upper-left corner of the form in the screen coordinates.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfPageSetupDialogShowingEventArgs.FormSize">
      <summary>
        <para>Gets or sets the size of the PDF page setup dialog.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that specifies the setup dialog form&#39;s width and height, in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfPageSetupDialogShowingEventArgs.FormStartPosition">
      <summary>
        <para>Gets or sets the form start position at runtime.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.FormStartPosition"/> that is the start position of the form.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfPageSetupDialogShowingEventArgs.MinimumFormSize">
      <summary>
        <para>Gets the form&#39;s minimum size.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that specifies the setup dialog form&#39;s width and height, in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfPageSetupDialogShowingEventArgs.PrinterSettings">
      <summary>
        <para>Provides access to the PDF printer settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPrinterSettings"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfPageSetupDialogShowingEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.PageSetupDialogShowing"/> event.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfPageSetupDialogShowingEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfPopupMenuKind">
      <summary>
        <para>Lists the values specifying the kind of a popup menu that can be invoked in the PDF Viewer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfPopupMenuKind.BookmarkOptions">
      <summary>
        <para>Specifies a popup menu that can be invoked by clicking the Options drop-down button in the Bookmarks panel of the PDF Viewer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfPopupMenuKind.BookmarkTree">
      <summary>
        <para>Specifies a popup menu that can be invoked by right clicking the bookmark item in the bookmarks hierarchical tree on the Navigation pane of the PDF Viewer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfPopupMenuKind.CommentFilter">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfPopupMenuKind.CommentOptions">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfPopupMenuKind.CommentSort">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfPopupMenuKind.PageContent">
      <summary>
        <para>Specifies a popup menu that can be invoked by right clicking the page content of the PDF Viewer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfPopupMenuKind.Thumbnail">
      <summary>
        <para>Specifies a popup menu that can be invoked by right clicking the page thumbnail on the Navigation pane of the PDF Viewer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfPopupMenuKind.ThumbnailOptions">
      <summary>
        <para>Specifies a popup menu that can be invoked by clicking the Options drop-down button in the Page Thumbnails panel of the PDF Viewer.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfPopupMenuShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.PopupMenuShowing"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfPopupMenuShowingEventArgs.ItemLinks">
      <summary>
        <para>Gets the collection of popup menu links displayed when the menu is being invoked.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.BarItemLinkCollection"/> object that contains links to a popup menu.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfPopupMenuShowingEventArgs.Menu">
      <summary>
        <para>Provides access to a popup menu that is being invoked.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.PopupMenu"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfPopupMenuShowingEventArgs.PopupMenuKind">
      <summary>
        <para>Gets the type of a particular popup menu shown for the PDF Viewer.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfPopupMenuKind"/> enumeration value which represents the kind of a popup menu shown for the PDF Viewer.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfPopupMenuShowingEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.PopupMenuShowing"/> event.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfPopupMenuShowingEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfReferencedDocumentOpeningEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.ReferencedDocumentOpening"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfReferencedDocumentOpeningEventArgs.DocumentFilePath">
      <summary>
        <para>Gets the path to the document.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the path to the document.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfReferencedDocumentOpeningEventArgs.OpenInNewWindow">
      <summary>
        <para>Gets a value that indicates whether or not to open the referenced document in a new window.</para>
      </summary>
      <value>true to open the referenced document in a new window; otherwise false.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfReferencedDocumentOpeningEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.ReferencedDocumentOpening"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfReferencedDocumentOpeningEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfScrollPositionChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.ScrollPositionChanged"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfScrollPositionChangedEventArgs.HorizontalPosition">
      <summary>
        <para>Specifies the horizontal position of the <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> toolbar.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value, specifying the horizontal position of the toolbar.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfScrollPositionChangedEventArgs.VerticalPosition">
      <summary>
        <para>Specifies the vertical position of the <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> toolbar.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value, specifying the vertical position of the toolbar.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfScrollPositionChangedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.ScrollPositionChanged"/> event.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfScrollPositionChangedEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfSelectionContent">
      <summary>
        <para>Provides information about the selected PDF content.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfSelectionContent.ContentType">
      <summary>
        <para>Indicates the type of the PDF content corresponding to a specific document point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfSelectionContentType"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfSelectionContent.Image">
      <summary>
        <para>Returns the selected image.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Bitmap"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfSelectionContent.Text">
      <summary>
        <para>Returns the selected text.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfSelectionContentType">
      <summary>
        <para>Lists the values specifying the type of the selected PDF content.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfSelectionContentType.Image">
      <summary>
        <para>The selected content is an image.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfSelectionContentType.None">
      <summary>
        <para>The selected content is not defined.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfSelectionContentType.Text">
      <summary>
        <para>The selected content is text.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfSelectionEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.SelectionStarted"/>, <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.SelectionContinued"/> and <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.SelectionEnded"/> events.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfSelectionEventArgs.DocumentPosition">
      <summary>
        <para>Returns the PDF coordinates of a hit point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfSelectionPerformedEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.SelectionStarted"/>, <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.SelectionContinued"/> and <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.SelectionEnded"/> events.</para>
      </summary>
      <param name="sender">The event sender.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfSelectionEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfUriOpeningEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.UriOpening"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfUriOpeningEventArgs.Handled">
      <summary>
        <para>Gets or sets a value that specifies whether the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.UriOpening"/> event is handled; if handled, the Security Warning message box is hidden.</para>
      </summary>
      <value>true, if the Security Warning message box is hidden; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfUriOpeningEventArgs.Uri">
      <summary>
        <para>Gets the opened document&#39;s URI (Uniform Resource Identifier).</para>
      </summary>
      <value>A <see cref="T:System.Uri"/> object that is the document URI.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfUriOpeningEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.UriOpening"/> event.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfUriOpeningEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfViewer">
      <summary>
        <para>Represents the PDF Viewer, which displays PDF files in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.About">
      <summary>
        <para>Invokes the About dialog.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.AcceptsTab">
      <summary>
        <para>Gets or sets a value indicating whether pressing the TAB key is processed by the PDF Viewer instead of moving the focus to the next control in the tab order.</para>
      </summary>
      <value>true, if a tab is accepted in the PDF viewer; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback)">
      <summary>
        <para>Adds the specified service to the service container.</para>
      </summary>
      <param name="serviceType">The type of service to add.</param>
      <param name="callback">A callback object that is used to create the service. This allows a service to be declared as available, but delays the creation of the object until the service is requested.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback,System.Boolean)">
      <summary>
        <para>Adds the specified service to the service container.</para>
      </summary>
      <param name="serviceType">The type of service to add.</param>
      <param name="callback">A callback object that is used to create the service. This allows a service to be declared as available, but delays the creation of the object until the service is requested.</param>
      <param name="promote">true, to promote this request to any parent service containers; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.AddService(System.Type,System.Object)">
      <summary>
        <para>Adds the specified service to the service container.</para>
      </summary>
      <param name="serviceType">The type of service to add.</param>
      <param name="serviceInstance">An instance of the service type to add. This object must implement or inherit from the type indicated by the serviceType parameter.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.AddService(System.Type,System.Object,System.Boolean)">
      <summary>
        <para>Adds the specified service to the service container.</para>
      </summary>
      <param name="serviceType">The type of service to add.</param>
      <param name="serviceInstance">An instance of the service type to add. This object must implement or inherit from the type indicated by the serviceType parameter.</param>
      <param name="promote">true, to promote this request to any parent service containers; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.AddStickyNote(DevExpress.Pdf.PdfDocumentPosition)">
      <summary>
        <para></para>
      </summary>
      <param name="position"></param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.AddStickyNote(DevExpress.Pdf.PdfDocumentPosition,System.String)">
      <summary>
        <para></para>
      </summary>
      <param name="position"></param>
      <param name="comment"></param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.AddStickyNote(DevExpress.Pdf.PdfDocumentPosition,System.String,System.Drawing.Color)">
      <summary>
        <para></para>
      </summary>
      <param name="position"></param>
      <param name="comment"></param>
      <param name="color"></param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.AllowCommentFiltering">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.AllowCommentSorting">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.AnnotationChanged">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.AnnotationCreated">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.AnnotationCreating">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.AnnotationDeleting">
      <summary>
        <para>Fires before an annotation is deleted from a page.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.AnnotationGotFocus">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.AnnotationLostFocus">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.ClearSelection">
      <summary>
        <para>Clears the current selection, making no document content being selected.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.CloseDocument">
      <summary>
        <para>Closes the current document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.CommentFilter">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.CommentSortMode">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.ContentMarginMode">
      <summary>
        <para>Specifies the mode for calculating the outer border width displayed by a <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> around document pages.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfContentMarginMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.ContentMinMargin">
      <summary>
        <para>Specifies the minimum width of the outer border displayed by a <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> around document pages.</para>
      </summary>
      <value>An integer value, specifying the minimum width of the page outer border.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.ContinueSearchFrom">
      <summary>
        <para>Gets or sets where the PDF Viewer continues its search after you scroll a document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfContinueSearchFrom"/> enumeration value. The default is CurrentPage.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.CopyToClipboard">
      <summary>
        <para>Copies the selected PDF content to the Clipboard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.CreateBars">
      <summary>
        <para>Creates a toolbar with actions specific to the <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.CreateBars(DevExpress.XtraPdfViewer.PdfViewerToolbarKind)">
      <summary>
        <para>Creates a set of toolbars with actions specific to the <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> control.</para>
      </summary>
      <param name="flags">The <see cref="T:DevExpress.XtraPdfViewer.PdfViewerToolbarKind"/> enumeration value specifying a set of toolbars that should be created.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.CreateBitmap(System.Int32,System.Int32)">
      <summary>
        <para>Exports a PDF page to a bitmap image.</para>
      </summary>
      <param name="pageNumber">An integer value, specifying the page number.</param>
      <param name="largestEdgeLength">An integer value, specifying the length of the image&#39;s largest dimension.</param>
      <returns>A <see cref="T:System.Drawing.Bitmap"/> object.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.CreateRibbon">
      <summary>
        <para>Creates a Ribbon tab with actions specific to the <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.CreateRibbon(DevExpress.XtraPdfViewer.PdfViewerToolbarKind)">
      <summary>
        <para>Creates a set of ribbon tabs with actions specific to the <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> control.</para>
      </summary>
      <param name="flags">The <see cref="T:DevExpress.XtraPdfViewer.PdfViewerToolbarKind"/> enumeration value specifying a set of ribbon tabs that should be created.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.CreateTiff(System.IO.Stream,System.Collections.Generic.IEnumerable{System.Int32},System.Single)">
      <summary>
        <para>Exports a PDF document to a TIFF image using a stream, page numbers and predefined resolution.</para>
      </summary>
      <param name="stream">A stream to which a TIFF image should be written.</param>
      <param name="pageNumbers">A list of page numbers.</param>
      <param name="imageDpi">The image&#39;s DPI.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.CreateTiff(System.IO.Stream,System.Int32)">
      <summary>
        <para>Exports a PDF document to a TIFF image using the document&#39;s stream and the image&#39;s largest edge length.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object that is the stream containing a PDF document.</param>
      <param name="largestEdgeLength">An integer value, specifying the length of the image&#39;s largest dimension.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.CreateTiff(System.IO.Stream,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})">
      <summary>
        <para>Exports a PDF document to a TIFF image using the document&#39;s stream, the image&#39;s largest edge length and page numbers.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object that is the stream containing a PDF document.</param>
      <param name="largestEdgeLength">An integer value, specifying the length of the image&#39;s largest dimension.</param>
      <param name="pageNumbers">A list of page numbers which implements the <see cref="T:System.Collections.Generic.IEnumerable`1"/>&lt;<see cref="T:System.Int32"/>,&gt; interface.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.CreateTiff(System.String,System.Collections.Generic.IEnumerable{System.Int32},System.Single)">
      <summary>
        <para>Exports a PDF document to a TIFF image using a stream, page numbers and predefined resolution.</para>
      </summary>
      <param name="fileName">The file name (including the full path) for the created Tiff image.</param>
      <param name="pageNumbers">A list of page numbers.</param>
      <param name="imageDpi">The image&#39;s DPI.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.CreateTiff(System.String,System.Int32)">
      <summary>
        <para>Exports a PDF document to a TIFF image using the document&#39;s file path and the image&#39;s largest edge length.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> which specifies the file name (including the full path) for the PDF document.</param>
      <param name="largestEdgeLength">An integer value, specifying the length of the image&#39;s largest dimension.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.CreateTiff(System.String,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})">
      <summary>
        <para>Exports a PDF document to a TIFF image using the document&#39;s file path, the image&#39;s largest edge length and page numbers.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> which specifies the file name (including the full path) for the PDF document.</param>
      <param name="largestEdgeLength">An integer value, specifying the length of the image&#39;s largest dimension.</param>
      <param name="pageNumbers">A list of page numbers which implements the <see cref="T:System.Collections.Generic.IEnumerable`1"/>&lt;<see cref="T:System.Int32"/>,&gt; interface.</param>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.CurrentPageChanged">
      <summary>
        <para>Occurs after the currently displayed page has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.CurrentPageNumber">
      <summary>
        <para>Specifies the number of the currently displayed page.</para>
      </summary>
      <value>An integer value, specifying the current page number.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.CursorMode">
      <summary>
        <para>Gets or sets a value that specifies the interaction mode for keyboard and cursor.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfCursorMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.DefaultDocumentDirectory">
      <summary>
        <para>Specifies the initial directory displayed by the Save As dialog and Open File dialog.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the path to the default directory.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.DetachStreamAfterLoadComplete">
      <summary>
        <para>Specifies whether or not the PDF Viewer allows closing the input stream after loading a PDF file.</para>
      </summary>
      <value>true to close the input stream after loading a file; otherwise false.</value>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.DocumentChanged">
      <summary>
        <para>Occurs when a <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> control loads another PDF document.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.DocumentClosing">
      <summary>
        <para>Occurs when closing a PDF document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.DocumentCreator">
      <summary>
        <para>If the document was converted to PDF from another format, specifies the name of the conforming product that created the original document from which it was converted.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.DocumentFilePath">
      <summary>
        <para>Gets or sets the path to the PDF file, which is currently opened in the <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> control.</para>
      </summary>
      <value>A string value containing the file path.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.DocumentProducer">
      <summary>
        <para>If the document was converted to PDF from another format, specifies the name of the conforming product that converted it to PDF.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.DocumentProperties">
      <summary>
        <para>Provides access to the basic information about a document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfDocumentProperties"/> object.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.EnsureVisibility(DevExpress.Pdf.PdfDocumentPosition)">
      <summary>
        <para>Tries to display the document position on the screen if the document position is found to be not visible in the <see cref="M:DevExpress.XtraPdfViewer.PdfViewer.IsVisible(DevExpress.Pdf.PdfDocumentPosition)"/> method.</para>
      </summary>
      <param name="documentPosition">A <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> object that specifies a specific document&#39;s position.</param>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.FileAttachmentOpening">
      <summary>
        <para>Occurs when an attachment is opening in the Attachments panel and allows you to manage the attachment opening behavior. For example, the attachment contents are not opened if e.Cancel is set to true. If you want to hide the Security Warning message box, set the <see cref="P:DevExpress.XtraPdfViewer.PdfFileAttachmentOpeningEventArgs.Handled"/> property to true.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.FindDialogOptions">
      <summary>
        <para>Provides the text search options assigned to the Find Text dialog.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfFindDialogOptions"/> object.</value>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.FindDialogVisibilityChanged">
      <summary>
        <para>Occurs when the visibility state of the Find dialog window has changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.FindText(System.String)">
      <summary>
        <para>Searches for the specified text in the current document with default parameters.</para>
      </summary>
      <param name="text">Specifies the text to find in the PDF document.</param>
      <returns>Contains information related to search results.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.FindText(System.String,DevExpress.Pdf.PdfTextSearchParameters)">
      <summary>
        <para>Searches for the specified text in the current document with the given parameters.</para>
      </summary>
      <param name="text">Specifies the text to find in the PDF document.</param>
      <param name="parameters">Specifies the search parameters.</param>
      <returns>Contains information related to search results.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.FindText(System.String,DevExpress.Pdf.PdfTextSearchParameters,System.Func{System.Int32,System.Boolean})">
      <summary>
        <para>Searches for the specified text in the current document with the given parameters and allows you to specify a delegate to terminate the text search.</para>
      </summary>
      <param name="text">Specifies the text to find in the PDF document.</param>
      <param name="parameters">Contains information related to search results.</param>
      <param name="terminate">A delegate that encapsulates a method used to terminate the text search on a specific page. The delegate should take the index of the currently processed page and return true to terminate the search; otherwise, false.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfTextSearchResults"/> object.</returns>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.FormFieldGotFocus">
      <summary>
        <para>Occurs when a form field receives input focus.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.FormFieldLostFocus">
      <summary>
        <para>Occurs when a form field loses input focus.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.FormFieldValueChanged">
      <summary>
        <para>Occurs after a form field value in the PDF document has been changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.FormFieldValueChanging">
      <summary>
        <para>Occurs when an end-user starts to edit a form field value.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.GetClientPoint(DevExpress.Pdf.PdfDocumentPosition)">
      <summary>
        <para>Returns the client point corresponding to the specified PDF coordinates.</para>
      </summary>
      <param name="documentPosition">A <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> object.</param>
      <returns>A <see cref="T:System.Drawing.PointF"/> structure.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.GetContentInfo(System.Drawing.PointF)">
      <summary>
        <para>Provides information about the PDF content corresponding to the specified client point.</para>
      </summary>
      <param name="clientPoint">A <see cref="T:System.Drawing.PointF"/> structure.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfDocumentContent"/> object.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.GetDocumentPosition(System.Drawing.PointF)">
      <summary>
        <para>Converts the specified point coordinates relative to the PDF Viewer&#39;s client area to the page coordinates.</para>
      </summary>
      <param name="clientPoint">A <see cref="T:System.Drawing.PointF"/> structure, specifying the coordinates of a point (in pixels) relative to the PDF Viewer&#39;s client area.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> object that returns the page coordinates and the page number.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.GetDocumentPosition(System.Drawing.PointF,System.Boolean)">
      <summary>
        <para>Converts the specified point coordinates relative to the PDF Viewer&#39;s client area (in pixels) to the page coordinates in a document.</para>
      </summary>
      <param name="clientPoint">A <see cref="T:System.Drawing.PointF"/> structure, specifying the coordinates of a point (in pixels) relative to the PDF Viewer&#39;s client area.</param>
      <param name="inPageBounds">A <see cref="T:System.Boolean"/> value that determines how the method interprets the point located outside page bounds in a document; if true - when the point is outside page bounds, the method returns the point coordinates that belong to the closest page in a document; false -  if the point is outside page bounds, this method returns null.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> object that returns the page coordinates and the page number in a document.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.GetPageSize(System.Int32)">
      <summary>
        <para>Returns the size (in inches) of the specified document page.</para>
      </summary>
      <param name="pageNumber">An integer value, specifying the document page index.</param>
      <returns>A <see cref="T:System.Drawing.SizeF"/> structure, specifying the page width and height in inches.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.GetSelectionContent">
      <summary>
        <para>Returns the selected content.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraPdfViewer.PdfSelectionContent"/> object.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.GetService``1">
      <summary>
        <para>Gets the specified service.</para>
      </summary>
      <typeparam name="T"></typeparam>
      <returns>A service object of the specified generic type, or a null reference (Nothing in Visual Basic) if there is no service object of this type.</returns>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.HandTool">
      <summary>
        <para>Specifies whether to browse a document (by using the Hand Tool), or select parts of its content (by using the Select Tool).</para>
      </summary>
      <value>true to enable browsing the document; false to enable selecting the document content.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.HasSelection">
      <summary>
        <para>Specifies whether or not any PDF content is selected.</para>
      </summary>
      <value>true if some content has been selected in the PDF; otherwise false.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.HideFindDialog">
      <summary>
        <para>Hides the text search user interface.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.HideFindDialog(System.Boolean)">
      <summary>
        <para>Hides the text search user interface, without showing the fade-out animation.</para>
      </summary>
      <param name="immediate">true, to omit animation when hiding the text search user interface; otherwise false.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.HighlightedFormFieldColor">
      <summary>
        <para>Gets or sets a color which is used to highlight form fields in a PDF document.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value that specifies the form field color to be highlighted.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.HighlightFormFields">
      <summary>
        <para>Gets or sets a value which indicates whether to use a color to highlight form fields in a PDF document.</para>
      </summary>
      <value>true to highlight form fields with a color; false to disable form field highlighting.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.HighlightSelectedText">
      <summary>
        <para>Highlights a selected text.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.HighlightSelectedText(System.Drawing.Color)">
      <summary>
        <para>Highlights a selected text with a specified color.</para>
      </summary>
      <param name="color">A <see cref="T:System.Drawing.Color"/> value that specifies the color used to highlight a selected text.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.HighlightSelectedText(System.String)">
      <summary>
        <para>Highlights a selected text and adds a comment to this text that will be shown in a pop-up window.</para>
      </summary>
      <param name="comment">A string that specifies the comment for a highlighted text. The comment will be shown in a pop-up window.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.HighlightSelectedText(System.String,System.Drawing.Color)">
      <summary>
        <para>Highlights a selected text with a color and adds a comment to this text that will be shown in a pop-up window.</para>
      </summary>
      <param name="comment">A string that specifies the comment for a highlighted text. The comment will be shown in a pop-up window.</param>
      <param name="color">A <see cref="T:System.Drawing.Color"/> value that specifies the color used to highlight a selected text.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.HorizontalScrollPosition">
      <summary>
        <para>Specifies the horizontal position of the <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> scroll bar.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value. This value must be in the 0 - 1 range.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.ImageCacheSize">
      <summary>
        <para>Specifies the size of the PDF image cache (in megabytes).</para>
      </summary>
      <value>An integer value, specifying the image cache size (in megabytes).</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.IsDocumentChanged">
      <summary>
        <para>Indicates whether the current document has been changed.</para>
      </summary>
      <value>true if changes have been made to the current document; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.IsDocumentOpened">
      <summary>
        <para>Gets a value indicating whether any document is currently being loaded into the <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/>.</para>
      </summary>
      <value>true if a document is currently opened; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.IsFindDialogVisible">
      <summary>
        <para>Indicates the visibility state of the Find dialog in a PDF Viewer.</para>
      </summary>
      <value>true if the Find dialog is currently visible; otherwise false.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.IsVisible(DevExpress.Pdf.PdfDocumentPosition)">
      <summary>
        <para>Indicates the current visibility of a specific <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/>.</para>
      </summary>
      <param name="documentPosition">A <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> object that contains information about a specific document&#39;s position.</param>
      <returns>true, if a specific PDF document position is visible; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.LoadDocument">
      <summary>
        <para>Invokes the &quot;Open...&quot; file dialog, creates a specific importer and loads the file.</para>
      </summary>
      <returns>true, if the document was loaded successfully; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.LoadDocument(System.IO.Stream)">
      <summary>
        <para>Loads a PDF document from the specified stream.</para>
      </summary>
      <param name="stream">A Stream class descendant.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.LoadDocument(System.String)">
      <summary>
        <para>Loads the PDF document from the specified path.</para>
      </summary>
      <param name="path">A string specifying the path to the PDF document.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.MarkupToolsSettings">
      <summary>
        <para>Provides access to the markup tools settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfMarkupToolsSettings"/> object which contains the settings of markup tools.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.MaxPrintingDpi">
      <summary>
        <para>Specifies the maximum DPI (dots per inch) value allowed for document printing.</para>
      </summary>
      <value>An integer value. The zero value indicates that the printing DPI is not limited.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.MaxZoomFactor">
      <summary>
        <para>Returns the maximum allowed zoom factor.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.MenuManager">
      <summary>
        <para>Specifies an object that controls the look and feel of the popup menus.</para>
      </summary>
      <value>An object that controls the look and feel of the popup menus.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.MinZoomFactor">
      <summary>
        <para>Returns the minimum allowed zoom factor.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneInitialSelectedPage">
      <summary>
        <para>Gets or sets a value that specifies a page (thumbnails, bookmarks or attachments) that will be selected on the navigation pane after loading a new PDF document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfNavigationPanePage"/> enumeration value that represents a selected page on the navigation pane.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneInitialVisibility">
      <summary>
        <para>Specifies the initial visible state of a navigation pane after loading a new PDF document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility"/> enumerator value defining the visibility of a navigation pane.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneMinWidth">
      <summary>
        <para>Returns the minimum width of a navigation pane.</para>
      </summary>
      <value>An integer value that determines the minimum navigation pane width.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.NavigationPanePageVisibility">
      <summary>
        <para>Specifies the visibility of pages (Bookmarks, Thumbnails, and Attachments) on the Navigation pane.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfNavigationPanePageVisibility"/> enumerator value that specifies the visibility of pages on the Navigation pane.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneSelectedPage">
      <summary>
        <para>Gets or sets a value that is used to select a current page (thumbnails, bookmarks or attachments) programmatically on the navigation pane. You can also obtain the currently selected page that is opened on the navigation pane.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfNavigationPanePage"/> enumeration value that represents a selected page on the navigation pane.</value>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneSelectedPageChanged">
      <summary>
        <para>Fires in response to changing the thumbnails, attachments, or bookmarks page on the navigation pane.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneVisibility">
      <summary>
        <para>Specifies the current visible state of a navigation pane.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfNavigationPaneVisibility"/> enumerator value defining the visibility of a navigation pane.</value>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneVisibilityChanged">
      <summary>
        <para>Occurs after the visibility of a navigation pane has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.NavigationPaneWidth">
      <summary>
        <para>Gets or sets the navigation pane width (where outlines are located).</para>
      </summary>
      <value>An integer value, specifying the width of a navigation pane.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.OutlineViewerSettings">
      <summary>
        <para>Provides access to the settings of the PDF outline viewer.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfOutlineViewerSettings"/> object which contains the settings to customize the PDF outline viewer.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.PageCount">
      <summary>
        <para>Returns the total number of pages in a document.</para>
      </summary>
      <value>An integer value, specifying the document page count.</value>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.PageSetupDialogShowing">
      <summary>
        <para>Occurs when displaying the Print dialog in the PDF Viewer.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.PasswordAttemptsLimit">
      <summary>
        <para>Specifies the maximum number of allowed attempts to enter the PDF file&#39;s security password.</para>
      </summary>
      <value>An integer value.</value>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.PasswordRequested">
      <summary>
        <para>Occurs when requesting a security password to open a protected PDF file.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.PopupMenuShowing">
      <summary>
        <para>Occurs after the popup menu of a PDF Viewer has been invoked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.Print">
      <summary>
        <para>Invokes a Print dialog and prints the current document using the print settings specified in this dialog window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.Print(DevExpress.Pdf.PdfPrinterSettings)">
      <summary>
        <para>Prints the current document using the specified PDF print settings without invoking the Print dialog.</para>
      </summary>
      <param name="pdfPrinterSettings">A <see cref="T:DevExpress.Pdf.PdfPrinterSettings"/> value, specifying the PDF printing options.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.Print(System.Drawing.Printing.PrinterSettings)">
      <summary>
        <para>Obsolete. Prints the current document using the specified print settings.</para>
      </summary>
      <param name="printerSettings">A <see cref="T:System.Drawing.Printing.PrinterSettings"/> object.</param>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.PrintPage">
      <summary>
        <para>Occurs when the document page is printed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.QueryPageSettings">
      <summary>
        <para>Occurs immediately before the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.PrintPage"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.ReadOnly">
      <summary>
        <para>Specifies whether document modifications are prohibited.</para>
      </summary>
      <value>true if the document is in a read-only state; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.ReferencedDocumentOpening">
      <summary>
        <para>Occurs when opening an embedded or external document by clicking its corresponding link in a PDF document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.RemoveService(System.Type)">
      <summary>
        <para>Removes the service of the specified type from the service container.</para>
      </summary>
      <param name="serviceType">The type of service to remove.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.RemoveService(System.Type,System.Boolean)">
      <summary>
        <para>Removes the service of the specified type from the service container.</para>
      </summary>
      <param name="serviceType">The type of service to remove.</param>
      <param name="promote">true, to promote this request to any parent service containers; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.RenderPageContentWithDirectX">
      <summary>
        <para>Gets or sets whether to render page content with DirectX.</para>
      </summary>
      <value>true, to enable DirectX page content rendering; false, to render page content with GDI/GDI+. The default is true.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.RotationAngle">
      <summary>
        <para>Specifies the rotation angle of a document in the <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/>.</para>
      </summary>
      <value>An integer value, specifying the rotation angle of document pages (in degrees). This value must be multiple of 90.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.SaveDocument">
      <summary>
        <para>Saves a document in its original format to its original location.</para>
      </summary>
      <returns>true if a document has been successfully saved; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.SaveDocument(System.IO.Stream)">
      <summary>
        <para>Saves the current PDF to the specified stream.</para>
      </summary>
      <param name="stream">A System.IO.Stream, specifying the document address.</param>
      <returns>true, if the document is saved successfully; false, if the document saving operation is cancelled by the user.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.SaveDocument(System.String)">
      <summary>
        <para>Saves the current PDF to the specified file path.</para>
      </summary>
      <param name="path">A System.String value, specifying the document location.</param>
      <returns>true, if the document is saved successfully; false, if the document saving operation is cancelled by the user.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.ScrollHorizontal(System.Int32)">
      <summary>
        <para>Scrolls the document horizontally by the specified number of units.</para>
      </summary>
      <param name="amount">An integer value, specifying the scrolling amount.</param>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.ScrollPositionChanged">
      <summary>
        <para>Occurs after changing the current scroll bar position in a <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.ScrollVertical(System.Int32)">
      <summary>
        <para>Scrolls the document vertically by the specified number of units.</para>
      </summary>
      <param name="amount">An integer value, specifying the scrolling amount.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.Select(DevExpress.Pdf.PdfDocumentPosition,DevExpress.Pdf.PdfDocumentPosition)">
      <summary>
        <para>Selects the document content located at the specified position.</para>
      </summary>
      <param name="startPosition">A <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> value, specifying the starting position in the document.</param>
      <param name="endPosition">A <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> value, specifying the ending position in the document.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.Select(System.Drawing.RectangleF)">
      <summary>
        <para>Selects the document content located in the specified rectangle.</para>
      </summary>
      <param name="clientRectangle">A <see cref="T:System.Drawing.RectangleF"/> structure that is the target rectangle.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.SelectAllText">
      <summary>
        <para>Selects all the text in the document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.SelectAnnotation(System.Int32,System.String)">
      <summary>
        <para>Selects an annotation on a page.</para>
      </summary>
      <param name="pageNumber">An integer value, specifying the page number.</param>
      <param name="annotationName">A <see cref="T:System.String"/> value that specifies the annotation name.</param>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.SelectionContinued">
      <summary>
        <para>Occurs after the selection of the PDF content has been continued.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.SelectionEnded">
      <summary>
        <para>Occurs after the selection of the PDF content has finished.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.SelectionStarted">
      <summary>
        <para>Occurs after the selection of the PDF content has started.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.SelectWord(DevExpress.Pdf.PdfDocumentPosition)">
      <summary>
        <para>Selects a word at the specified document position.</para>
      </summary>
      <param name="position">A <see cref="T:DevExpress.Pdf.PdfDocumentPosition"/> object that specifies the position of the word to select. If no word is found at the specified position, the selection is cleared.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.ShowDocumentClosingWarning">
      <summary>
        <para>Specifies whether to display a warning before the current document is closed.</para>
      </summary>
      <returns>true to display a warning before closing a document; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.ShowFindDialog">
      <summary>
        <para>Invokes the Find Text dialog.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.ShowFindDialog(DevExpress.XtraPdfViewer.PdfFindDialogOptions)">
      <summary>
        <para>Invokes the Find Text dialog with the text search options applied by an end-user.</para>
      </summary>
      <param name="findDialogOptions">A <see cref="T:DevExpress.XtraPdfViewer.PdfFindDialogOptions"/> object.</param>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.ShowImagePlaceholder">
      <summary>
        <para>Gets or sets a value which indicates whether to show placeholders for images in unsupported image formats.</para>
      </summary>
      <value>true to show image placeholders in unsupported image formats; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.ShowPrintStatusDialog">
      <summary>
        <para>Gets or sets  whether or not to show a print status dialog when printing a document.</para>
      </summary>
      <value>true, to show a print status dialog; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.ShowSavingProgressDialog">
      <summary>
        <para>Specifies whether or not to show the Saving a file... dialog.</para>
      </summary>
      <value>true, to display the dialog; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.StrikethroughSelectedText">
      <summary>
        <para>Strikethroughs a selected text.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.StrikethroughSelectedText(System.Drawing.Color)">
      <summary>
        <para>Strikethroughs a selected text with the specified color.</para>
      </summary>
      <param name="color">A <see cref="T:System.Drawing.Color"/> value that specifies the color used to strikethrough a text.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.StrikethroughSelectedText(System.String)">
      <summary>
        <para>Strikethroughs a selected text and adds a comment to this text that will be shown in a pop-up window.</para>
      </summary>
      <param name="comment">A string that specifies the comment for a strikethroughed text. The comment will be shown in a pop-up window.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.StrikethroughSelectedText(System.String,System.Drawing.Color)">
      <summary>
        <para>Strikethroughs a selected text with a color and adds a comment to this text that will be shown in a pop-up window.</para>
      </summary>
      <param name="comment">A string that specifies the comment for a strikethroughed text. The comment will be shown in a pop-up window.</param>
      <param name="color">A <see cref="T:System.Drawing.Color"/> value that specifies the color used to strikethrough a selected text.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.UnderlineSelectedText">
      <summary>
        <para>Underlines a selected text.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.UnderlineSelectedText(System.Drawing.Color)">
      <summary>
        <para>Underlines a selected text with a specified color.</para>
      </summary>
      <param name="color">A <see cref="T:System.Drawing.Color"/> value that specifies the color used to underline a selected text.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.UnderlineSelectedText(System.String)">
      <summary>
        <para>Underlines a selected text and adds a comment to this text that will be shown in a pop-up window.</para>
      </summary>
      <param name="comment">A string that specifies the comment for an underlined text. The comment will be shown in a pop-up window.</param>
    </member>
    <member name="M:DevExpress.XtraPdfViewer.PdfViewer.UnderlineSelectedText(System.String,System.Drawing.Color)">
      <summary>
        <para>Underlines a selected text with a color and adds a comment to this text that will be shown in a pop-up window.</para>
      </summary>
      <param name="comment">A string that specifies the comment for an underlined text. The comment will be shown in a pop-up window.</param>
      <param name="color">A <see cref="T:System.Drawing.Color"/> value that specifies the color used to underline a selected text.</param>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.UpdateUI">
      <summary>
        <para>Raised when changes occur which may affect the control&#39;s UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.UriOpening">
      <summary>
        <para>Occurs after clicking a hyperlink addressing an external URI (universal resource identifier).</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.VerticalScrollPosition">
      <summary>
        <para>Specifies the vertical position of the <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> scroll bar.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value. This value must be in the 0 - 1 range.</value>
    </member>
    <member name="E:DevExpress.XtraPdfViewer.PdfViewer.ZoomChanged">
      <summary>
        <para>Occurs after the current zoom factor has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.ZoomFactor">
      <summary>
        <para>Specifies the document&#39;s zoom factor.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value, specifying the document zoom factor (as a percentage).</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfViewer.ZoomMode">
      <summary>
        <para>Specifies the document zoom mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfZoomMode"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfViewerToolbarKind">
      <summary>
        <para>Lists the values specifying the kind of a PDF Viewer toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfViewerToolbarKind.All">
      <summary>
        <para>All PDF Viewer&#39;s toolbars.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfViewerToolbarKind.Comment">
      <summary>
        <para>The PDF Viewer&#39;s Comment toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfViewerToolbarKind.InteractiveForm">
      <summary>
        <para>The PDF Viewer&#39;s Interactive Form toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfViewerToolbarKind.Main">
      <summary>
        <para>The PDF Viewer&#39;s Main toolbar.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfZoomChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.ZoomChanged"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfZoomChangedEventArgs.ZoomFactor">
      <summary>
        <para>Indicates the current document zoom factor value.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value, specifying the zoom factor.</value>
    </member>
    <member name="P:DevExpress.XtraPdfViewer.PdfZoomChangedEventArgs.ZoomMode">
      <summary>
        <para>Indicates the current document zoom mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPdfViewer.PdfZoomMode"/> enumeration value, specifying the document zoom mode.</value>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfZoomChangedEventHandler">
      <summary>
        <para>Occurs after a document zoom factor value has changed.</para>
      </summary>
      <param name="sender">A <see cref="T:System.Object"/> value, specifying the event sender.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPdfViewer.PdfZoomChangedEventArgs"/> object that contains data related to the event.</param>
    </member>
    <member name="T:DevExpress.XtraPdfViewer.PdfZoomMode">
      <summary>
        <para>Lists the document zoom modes provided by a PDF Viewer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfZoomMode.ActualSize">
      <summary>
        <para>Sets the document zoom factor value to 100%.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfZoomMode.Custom">
      <summary>
        <para>The document zoom factor is defined by the <see cref="P:DevExpress.XtraPdfViewer.PdfViewer.ZoomFactor"/> property value.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfZoomMode.FitToVisible">
      <summary>
        <para>The document zoom factor is calculated to fit to the width of the visible content of a page. The region of the visible page content is defined by the bleed box (see section 14.11.2 of the PDF 32000-1:2008 standard). If the bleed box is smaller than the crop box, the FitToWidth and FitToVisible modes provide different results.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfZoomMode.FitToWidth">
      <summary>
        <para>Sets the document zoom factor value to fit to the width of the widest page in a document. If the bleed box is smaller than the crop box, the FitToWidth provides different result from the FitToVisible mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPdfViewer.PdfZoomMode.PageLevel">
      <summary>
        <para>Sets the document zoom factor value to fit to the widest or highest page in a document.</para>
      </summary>
    </member>
  </members>
</doc>